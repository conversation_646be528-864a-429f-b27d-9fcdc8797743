<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

class EnsureJsonEncodedTest extends TestCase
{
    public function test_it_returns_json_if_input_is_already_json_string()
    {
        $jsonString = '{"name":"<PERSON>","age":30}';
        $result = ensureJsonEncoded($jsonString);

        $this->assertJson($result);
        $this->assertEquals($jsonString, $result);
    }

    public function test_it_encodes_array_to_json()
    {
        $array = ['name' => 'John', 'age' => 30];
        $result = ensureJsonEncoded($array);

        $this->assertJson($result);
        $this->assertEquals(json_encode($array), $result);
    }

    public function test_it_encodes_non_json_string_to_json()
    {
        $string = 'Hello World';
        $result = ensureJsonEncoded($string);

        $this->assertJson($result);
        $this->assertEquals(json_encode($string), $result);
    }

    public function test_it_encodes_numeric_value_to_json()
    {
        $number = 100;
        $result = ensureJsonEncoded($number);

        $this->assertJson($result);
        $this->assertEquals(json_encode($number), $result);
    }

    public function test_it_encodes_boolean_value_to_json()
    {
        $bool = true;
        $result = ensureJsonEncoded($bool);

        $this->assertJson($result);
        $this->assertEquals(json_encode($bool), $result);
    }

    public function test_it_encodes_null_to_json()
    {
        $result = ensureJsonEncoded(null);

        $this->assertJson($result);
        $this->assertEquals(json_encode(null), $result);
    }
}
