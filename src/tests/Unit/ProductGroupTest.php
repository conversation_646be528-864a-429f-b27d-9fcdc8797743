<?php

namespace Tests\Unit;

use Exception;
use Faker\Factory as Faker;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Modules\Common\Models\Administrator;
use Modules\Product\Services\ProductService;
use Modules\Product\Services\ProductTypeService;
use Tests\TestCase;

class ProductGroupTest extends TestCase
{
    private ProductService $productService;

    use WithoutMiddleware;

    /**
     * @throws Exception
     */
    public function setUp(): void
    {
        $this->checkEnvironment();
        parent::setUp();

        $this->productService = new ProductService();
        $this->productTypeService = new ProductTypeService();
    }

    public function tearDown(): void
    {
        parent::tearDown();
    }

    public function testProductGroupCreate()
    {
        $faker = Faker::create();
        $name = $faker->word;
        $credentials = [
            "name" => $name,
            "description" => $faker->name,
            "created_by" => Administrator::DEFAULT_UNIT_TEST_USER_ID,
        ];

        $productGroupCreate = $this->productTypeService->create($credentials)->fresh();

        $this->assertEquals($productGroupCreate->name, $credentials['name']);
        $this->assertEquals($productGroupCreate->description, $credentials['description']);
        $this->assertEquals(true, $productGroupCreate->isActive());
        $this->assertEquals(false, $productGroupCreate->isDeleted());
        $this->assertNotEmpty($productGroupCreate->created_at);
        $this->assertNotEmpty($productGroupCreate->created_by);

        return $productGroupCreate;
    }

    /**
     * @depends testProductGroupCreate
     *
     * @param $productGroupCreate
     */
    public function testProductGroupUpdate($productGroupCreate)
    {
        $faker = Faker::create();
        $name = $faker->word;
        $credentials = [
            "name" => $name,
            "description" => $faker->name,
            "updated_by" => Administrator::DEFAULT_UNIT_TEST_USER_ID,
        ];

        $productGroupUpdate = $this->productTypeService->edit(
            $productGroupCreate->product_group_id,
            $credentials
        )->fresh();

        $this->assertEquals($productGroupUpdate->product_group_id, $productGroupCreate->product_group_id);
        $this->assertEquals($productGroupUpdate->name, $credentials['name']);
        $this->assertEquals($productGroupUpdate->description, $credentials['description']);
        $this->assertEquals(true, $productGroupUpdate->isActive());
        $this->assertEquals(false, $productGroupUpdate->isDeleted());
        $this->assertNotEmpty($productGroupUpdate->created_at);
        $this->assertNotEmpty($productGroupUpdate->created_by);

        $productGroupUpdate->productSettings->delete();
        $productGroupUpdate->delete();

        $productGroup = $productGroupUpdate->fresh();

        foreach ($productGroup->productSettings as $setting) {
            $this->assertEquals(true, $setting->isDeleted());
        }

        $this->assertEquals(false, $productGroup->isActive());
        $this->assertEquals(true, $productGroup->isDeleted());
        $this->assertNotEmpty($productGroup->disabled_at);
        $this->assertNotEmpty($productGroup->disabled_by);
    }
}

