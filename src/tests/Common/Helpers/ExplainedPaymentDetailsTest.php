<?php

declare(strict_types=1);

namespace Tests\Common\Helpers;

use PHPUnit\Framework\TestCase;

final class ExplainedPaymentDetailsTest extends TestCase
{
    private const DEFAULTS = [
        'principal' => 0,
        'interest' => 0,
        'penalty' => 0,
        'late_interest' => 0,
        'late_penalty' => 0,
        'taxes' => 0,
        'late_penalty_and_taxes' => 0,
        'profit' => 0,
    ];

    /**
     * @test
     */
    public function simpleJsonInput(): void
    {
        $this->assertSame([
            ...self::DEFAULTS,
            'late_penalty_and_taxes' => '0.00',
            'profit' => '0.00',
        ], getExplainedPaymentDetails('{ "foo": "bar" }'));
    }

    /**
     * @test
     */
    public function emptyInput(): void
    {
        $this->assertSame(self::DEFAULTS, getExplainedPaymentDetails(''));
    }

    /**
     * @test
     */
    public function invalidInput(): void
    {
        $this->assertSame([
            ...self::DEFAULTS,
            'late_penalty_and_taxes' => '0.00',
            'profit' => '0.00',
        ], getExplainedPaymentDetails('foo'));
    }

    /**
     * @test
     */
    public function invalidJsonInput(): void
    {
        $this->assertSame([
            ...self::DEFAULTS,
            'late_penalty_and_taxes' => '0.00',
            'profit' => '0.00',
        ], getExplainedPaymentDetails('{ "foo": bar }'));
    }

    /**
     * @test
     */
    public function nullInput(): void
    {
        $this->assertSame(self::DEFAULTS, getExplainedPaymentDetails(null));
    }

    /**
     * @test
     */
    public function regularInput(): void
    {
        $input = [
            'payLoanFees' => ['amount' => fake()->numberBetween(100)],
            'outstandingLatePenaltyAmount' => ['amount' => (string) fake()->numberBetween(100)],
            'outstandingLateInterestAmount' => ['amount' => fake()->numberBetween(100)],
            'outstandingPenaltyAmount' => ['amount' => (string) fake()->numberBetween(100)],
            'outstandingInterestAmount' => ['amount' => fake()->numberBetween(100)],
            'outstandingPrincipleAmount' => ['amount' => (string) fake()->numberBetween(100)],
        ];

        $expected = [
            'principal' => (int) $input['outstandingPrincipleAmount']['amount'],
            'interest' => (int) $input['outstandingInterestAmount']['amount'],
            'penalty' => (int) $input['outstandingPenaltyAmount']['amount'],
            'late_interest' => (int) $input['outstandingLateInterestAmount']['amount'],
            'late_penalty' => (int) $input['outstandingLatePenaltyAmount']['amount'],
            'taxes' => (int) $input['payLoanFees']['amount'],
        ];

        $this->assertSame(array_map('intToFloat', [
            ...$expected,
            'late_penalty_and_taxes' => $expected['late_penalty'] + $expected['taxes'],
            'profit' => $expected['late_interest'] + $expected['late_penalty'] + $expected['taxes'] + $expected['penalty'] + $expected['interest'],
        ]), getExplainedPaymentDetails(json_encode($input)));
    }

    /**
     * @test
     */
    public function jsonInJsonInput(): void
    {
        $input = [
            'payLoanFees' => ['amount' => fake()->numberBetween(100)],
            'outstandingLatePenaltyAmount' => ['amount' => (string) fake()->numberBetween(100)],
            'outstandingLateInterestAmount' => ['amount' => fake()->numberBetween(100)],
            'outstandingPenaltyAmount' => ['amount' => (string) fake()->numberBetween(100)],
            'outstandingInterestAmount' => ['amount' => fake()->numberBetween(100)],
            'outstandingPrincipleAmount' => ['amount' => (string) fake()->numberBetween(100)],
        ];

        $expected = [
            'principal' => (int) $input['outstandingPrincipleAmount']['amount'],
            'interest' => (int) $input['outstandingInterestAmount']['amount'],
            'penalty' => (int) $input['outstandingPenaltyAmount']['amount'],
            'late_interest' => (int) $input['outstandingLateInterestAmount']['amount'],
            'late_penalty' => (int) $input['outstandingLatePenaltyAmount']['amount'],
            'taxes' => (int) $input['payLoanFees']['amount'],
        ];

        $this->assertSame(array_map('intToFloat', [
            ...$expected,
            'late_penalty_and_taxes' => $expected['late_penalty'] + $expected['taxes'],
            'profit' => $expected['late_interest'] + $expected['late_penalty'] + $expected['taxes'] + $expected['penalty'] + $expected['interest'],
        ]), getExplainedPaymentDetails(json_encode(json_encode($input))));
    }
}
