<?php

namespace StikCredit\Calculators\Fees;

/**
 * @property int $feeId
 * @property string $title
 * @property string $dueDate
 * @property int $amount
 * @property int $paidAmount
 * @property int $outstandingAmount
 * @property int $toInstallment
 * @property string $feeType
 * @property string|bool $fromDate
 * @property int $extensionDays
 * @property int $priority
 */
class DefaultFee extends AbstractFee
{
    const TYPE_EXTENSION = 'extend';

    public function __construct(
        public int         $feeId,
        public string      $title,
        public string      $creator,
        public string      $dueDate,
        public int         $toInstallment,
        public int         $priority,
        public string      $feeType,
        public int         $extensionDays,

        /// default amounts
        public int         $amount,
        public int         $paidAmount = 0,
        public int         $outstandingAmount = 0,
        public int         $overdueAmount = 0,

        /// late amounts
        public int         $accruedLateAmount = 0,
        public int         $paidLateAmount = 0,
        public int         $outstandingLateAmount = 0,

        public string|bool $fromDate = false,

        public bool        $isDue = false,
        public bool        $isPaid = false,
        public ?string     $paidDate = null,
    )
    {
    }

    public function build(): DefaultFee
    {
        $this->outstandingAmount = $this->amount;
        $this->isDue = $this->isDue();

        return $this;
    }

    public function isDue(): bool
    {
        return $this->sqlDate($this->dueDate)->getTimestamp() <= $this->sqlDate()->getTimestamp();
    }

    public function isExtensionFee(): bool
    {
        return ($this->feeType == self::TYPE_EXTENSION);
    }
}
