<?php

namespace StikCredit\Calculators\Overdue;

use StikCredit\Calculators\Accrued\AccruedAmountsCollection;
use StikCredit\Calculators\DiContainer;
use StikCredit\Calculators\Fees\FeesCollection;
use StikCredit\Calculators\Outstanding\DefaultOutstanding;

class DefaultOverdue extends AbstractOverdue
{

    /**
     * @param int $index
     * @param int $overduePrincipleAmount
     * @param int $overdueInterestAmount
     * @param int $overduePenaltyAmount
     */
    public function __construct(
        public int $index,
        public int $overduePrincipleAmount = 0,
        public int $overdueInterestAmount = 0,
        public int $overduePenaltyAmount = 0,
        public int $overdueDays = 0,
    )
    {
    }

    public function addOverdueAmount(
        DefaultOutstanding $defaultOutstanding,
        int                $overdueDays = 0
    ): void
    {
        $this->overdueDays = $overdueDays;
        $this->overduePrincipleAmount = $defaultOutstanding->outstandingPrincipleAmount;
        $this->overdueInterestAmount = $defaultOutstanding->outstandingInterestAmount;
        $this->overduePenaltyAmount = $defaultOutstanding->outstandingPenaltyAmount;
    }

    public function getTotalOverdueAmount(): int
    {
        $installmentOverdueAmount = array_sum([
            $this->overduePrincipleAmount,
            $this->overdueInterestAmount,
            $this->overduePenaltyAmount
        ]);

        /**
         * @var FeesCollection $feesCollection
         * @var AccruedAmountsCollection $accruedAmountCollection
         */
        $accruedAmountCollection = DiContainer::instance()->get(AccruedAmountsCollection::class);
        $feesCollection = DiContainer::instance()->get(FeesCollection::class);
        $fees = $feesCollection->getFeesToInstallment($this->index);

        $feesAmount = $fees->sum('amount');
        $accruedLateAmount = $accruedAmountCollection->get($this->index)->getAccruedLateTotalAmount();

        return array_sum([
            $installmentOverdueAmount,
            $feesAmount,
            $accruedLateAmount
        ]);
    }

    public function getTotalInstallmentOverdueAmount(): int
    {
        $overdueAmount = array_sum([
            $this->overduePrincipleAmount,
            $this->overdueInterestAmount,
            $this->overduePenaltyAmount
        ]);

        return intval($overdueAmount);
    }
}
