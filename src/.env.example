APP_NAME=credit_hunter
APP_ENV=local
APP_KEY=base64:MZyVsV8zgolDoi9xllLTXCH81si1hrU6iNial3RTZbo=
APP_DEBUG=true
APP_URL=http://localhost:8000
APP_USER=dockerUser
APP_USER_ID=1000
APP_TZ=Europe/Sofia

PROJECT=stikcredit

LOG_CHANNEL=stack

DB_CONNECTION=pgsql
DB_HOST=postgres
DB_PORT=5432
DB_EXTERNAL_PORT=5432
DB_DATABASE=credit_hunter_db
DB_USERNAME=root
DB_PASSWORD=w1ld)H@nt8
POSTGRES_DEFAULT_USER=postgres
POSTGRES_DEFAULT_PASS=postgres!DB_2020
POSTGRES_TEST_DB=credit_hunter_db_test

BROADCAST_DRIVER=redis
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_STORE=redis
SESSION_CONNECTION=session

REDIS_HOST=redis
REDIS_CLIENT=phpredis
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_EXTERNAL_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1
REDIS_SESSION_DB=2
REDIS_API_DB=3
REDIS_DOCS_DB=4
REDIS_SMS_DB=5
REDIS_VIBER_DB=6
REDIS_EMAIL_DB=7
REDIS_REPORTS_DB=8
REDIS_WRITE_TIMEOUT=40

ELASTIC_BOOT_PASSWORD=elastic

ELASTICSEARCH_HOST=elasticsearch
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_SCHEME=http
ELASTICSEARCH_USER=elastic
ELASTICSEARCH_PASS=elastic

MAIL_SENDER="<EMAIL>"
MAIL_SENDER_NAME="Lendivo|STIK"
MAIL_TEST_EMAIL="yourmail"

MAIL_DRIVER=smtp
MAIL_HOST=smtp.eu.mailgun.org
MAIL_PORT=465

MAIL_USERNAME=
MAIL_FROM_ADDRESS=
MAIL_PASSWORD=

MAIL_ENCRYPTION=
MAILGUN_DOMAIN=
MAILGUN_SECRET=

API_ALLOWED_IP="Your IP"
API_HASH=U3Rpa0NyZWRpdGlzdGhlYmVzdHBsYWNldG93b3JrYW5kdGhlYm9zc2lzY29vbA==

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=
AWS_BUCKET=

NGINX_PORT=8000
NGINX_PORT_SSL=4430

SMS_SERVICE_TEST_PHONE=
SMS_SERVICE_MOBICA_URL=
SMS_SERVICE_MOBICA_USER=
SMS_SERVICE_MOBICA_PASS=

EASYPAY_PIN="12345"

WEBSITE=http://localhost:8080/
WEBSITE_TOKEN="a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"

TELESCOPE_ENABLED=false

VOLUME_PATH_PG=./data/staging/pg
VOLUME_PATH_REDIS=./data/staging/redis

SELF=.env

########### EASY PAY MICROSERVICE ###########
EASYPAY_MS_TOKEN=
EASYPAY_MS_URL=
# to get local IP: hostname -I | cut -d' ' -f1

#######################################################
### DIRECT SERVICE
DIRECT_SERVICE_HOST=
DIRECT_SERVICE_PORT=
DIRECT_SERVICE_USER=
DIRECT_SERVICE_PASW=

###
MAKE_ITEGROMAT_NEW_CLIENTS_URL=

SCP_USER=
SCP_HOST=

# SMART ITK (affiliate)
SMART_ITK_LEAD_CREATE_URL=
SMART_ITK_API_KEY=


# PDF LIB FOR LEGAL DOCS
WKHTML_PDF_BINARY=/usr/bin/wkhtmltopdf
WKHTML_IMG_BINARY=/usr/bin/wkhtmltoimage


# VERIFF
VERIF_MASTER_SIG_KEY="c6f3538a-2172-4704-bb29-0447b4c2f776"
VERIF_API_KEY="636bfdbe-4ce5-42fa-a5d6-610424fab289"
VERIF_API_URL="https://stationapi.veriff.com"

# MVR

SKIP_MVR_FOR_DEV=true
MVR_SERVICE_URL=
MVR_SERVICE_TOKEN=
