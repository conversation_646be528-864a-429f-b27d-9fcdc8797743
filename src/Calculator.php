<?php

namespace StikCredit\Calculators;

class Calculator
{
    private const ROUNDED_NUMBER = 2;
    private const ROUNDING_FACTOR = 10 ** self::ROUNDED_NUMBER;

    /**
     * @param float $value
     * @param float $percent
     * @param bool $increase
     *
     * @return float|int
     */
    private static function percentageIncreaseOrDecrease(
        float $value,
        float $percent,
        bool $increase = true
    ) {
        $increase = $increase === true ? 1 : -1;
        return (1 + $increase * $percent / 100) * $value;
    }

    /**
     * @param $number
     *
     * @return string
     */
    private static function roundUp($number): string
    {
        $number = ceil($number * self::ROUNDING_FACTOR) / self::ROUNDING_FACTOR;
        return number_format($number, self::ROUNDED_NUMBER, '.', '');
    }

    /**
     * @param float $amount
     * @param int $rounding
     *
     * @return float
     */
    public static function toEuro(float $amount, $rounding = PHP_ROUND_HALF_UP): float
    {
        return round($amount / config('currency.euroRate'), self::ROUNDED_NUMBER, $rounding);
    }

    /**
     * @param $number
     * @param int $precision
     * @param int $mode
     * @return float
     * @throws \Exception
     */
    public static function round($number, int $precision = 2, int $mode = PHP_ROUND_HALF_UP): float
    {
        // round to lower border
        // return bcdiv($number, 1, $precision);
        // round to higher border

        if (!is_numeric($number)) {
            throw new \Exception('Wrong type provided. Type of $value is ' . gettype($number));
        }

        if (!is_float($number)) {
            return $number;
        }

        return round($number, $precision, $mode);
    }

    public static function sub($firstNumber, $secondNumber, $scale = 2): float
    {
        $firstNumber = (string) $firstNumber;
        $secondNumber = (string) $secondNumber;
        return (float) bcsub($firstNumber, $secondNumber, $scale);
    }

    public static function add($firstNumber, $secondNumber, $scale = 2): float
    {
        $firstNumber = (string) $firstNumber;
        $secondNumber = (string) $secondNumber;
        return (float) bcadd($firstNumber, $secondNumber, $scale);
    }

    public static function subArray(array $numbers, $scale = 2)
    {
        $total = array_shift($numbers);
        foreach ($numbers as $number) {
            $total = bcsub($total, $number, $scale);
        }

        return $total;
    }

    public static function sum()
    {
        if (count($arguments = func_get_args())) {
            return bcsum($arguments);
        }

        return 0;
    }

    public static function sumArray(array $array)
    {
        return self::sum(...$array);
    }

    public static function divide($firstNumber, $secondNumber, $scale = 2)
    {
        return bcdiv($firstNumber, $secondNumber, $scale);
    }

    public static function multiply($firstNumber, $secondNumber, $scale = 2)
    {
        return bcmul($firstNumber, $secondNumber, $scale);
    }

    public static function equal($firstNumber, $secondNumber, $scale = 2): bool
    {
        $firstNumber = (string) $firstNumber;
        $secondNumber = (string) $secondNumber;
        return (0 == bccomp($firstNumber, $secondNumber, $scale));
    }

    public static function gt($firstNumber, $secondNumber, $scale = 2): bool
    {
        $firstNumber = (string) $firstNumber;
        $secondNumber = (string) $secondNumber;
        return (1 == bccomp($firstNumber, $secondNumber, $scale));
    }

    public static function gte($firstNumber, $secondNumber, $scale = 2): bool
    {
        $firstNumber = (string) $firstNumber;
        $secondNumber = (string) $secondNumber;
        $res = bccomp($firstNumber, $secondNumber, $scale);

        return (0 == $res || 1 == $res);
    }

    public static function lt($firstNumber, $secondNumber, $scale = 2): bool
    {
        $firstNumber = (string) $firstNumber;
        $secondNumber = (string) $secondNumber;
        return (-1 == bccomp($firstNumber, $secondNumber, $scale));
    }

    public static function lte($firstNumber, $secondNumber, $scale = 2): bool
    {
        $firstNumber = (string) $firstNumber;
        $secondNumber = (string) $secondNumber;
        $res = bccomp($firstNumber, $secondNumber, $scale);

        return (0 == $res || -1 == $res);
    }
}
