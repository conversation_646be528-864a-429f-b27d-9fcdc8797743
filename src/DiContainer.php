<?php

namespace StikCredit\Calculators;

class DiContainer
{
    public static ?DiContainer $instance = null;

    public static array $instances = [];

    public function destroy(): void
    {
        self::$instance = null;
        self::$instances = [];
    }

    public function bind(string $bindKey, object $classInstance): mixed
    {
        if (!isset(self::$instances[$bindKey])) {
            self::$instances[$bindKey] = $classInstance;
        }

        return self::$instances[$bindKey];
    }

    public function get(string $bindKey): mixed
    {
        if (!isset(self::$instances[$bindKey])) {
            throw new \Exception('No available item for key: ' . $bindKey);
        }

        return self::$instances[$bindKey];
    }

    public static function instance(): self
    {
        if (!is_null(self::$instance)) {
            return self::$instance;
        }
        self::$instance = new self();

        return self::$instance;
    }
}
