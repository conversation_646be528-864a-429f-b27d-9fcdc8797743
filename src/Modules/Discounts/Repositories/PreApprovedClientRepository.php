<?php

namespace Modules\Discounts\Repositories;

use Illuminate\Database\Eloquent\Model;
use Modules\Common\Repositories\BaseRepository;
use Modules\Discounts\Models\PreApprovedClient;

final class PreApprovedClientRepository extends BaseRepository
{
    public function __construct(
        private PreApprovedClient $preApprovedClient
    )
    {
    }

    public function getDbModel(): Model
    {
        return $this->preApprovedClient;
    }
}
