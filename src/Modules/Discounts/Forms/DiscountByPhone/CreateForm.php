<?php

declare(strict_types=1);

namespace Modules\Discounts\Forms\DiscountByPhone;

use <PERSON>\LaravelFormBuilder\Field;
use <PERSON>\LaravelFormBuilder\Form;
use Modules\Common\Models\Product;

final class CreateForm extends Form
{
    public const NAME = 'new_discount';
    protected $name = self::NAME;

    public function buildForm(): void
    {
        $this->add('phone', Field::TEXT, [
            'label' => __('table.Phone'),
        ])->add('product_ids', Field::SELECT, [
            'label' => __('table.Products'),
            'empty_value' => '',
            'choices' => ($product = new Product())->getSelectedOptions('name', $product->getKeyName()),
            'attr' => [
                'multiple' => 'true',
                'data-boostrap-selectpicker' => 'true',
            ],
        ])->add('discount', Field::TEXT, [
            'label' => __('table.ActiveDiscount'),
        ])->add('valid_from', Field::TEXT, [
            'label' => __('table.ValidFrom'),
            'attr' => ['data-date-picker' => 'true',],
        ])->add('valid_until', Field::TEXT, [
            'label' => __('table.ValidTill'),
            'attr' => ['data-date-picker' => 'true'],
        ]);
    }
}
