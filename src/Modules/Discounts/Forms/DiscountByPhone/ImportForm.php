<?php

declare(strict_types=1);

namespace Modules\Discounts\Forms\DiscountByPhone;

use <PERSON>\LaravelFormBuilder\Field;
use <PERSON>\LaravelFormBuilder\Form;

final class ImportForm extends Form
{
    public function buildForm(): void
    {
        $this->add('import', Field::FILE, [
            'label' => __('table.ImportDiscounts'),
        ])->add('valid_from', Field::TEXT, [
            'label' => __('table.ValidFrom'),
            'attr' => ['data-date-picker' => 'true',],
        ])->add('valid_until', Field::TEXT, [
            'label' => __('table.ValidTill'),
            'attr' => ['data-date-picker' => 'true'],
        ]);
    }
}
