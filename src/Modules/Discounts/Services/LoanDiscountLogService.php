<?php

declare(strict_types=1);

namespace Modules\Discounts\Services;

use InvalidArgumentException;
use Modules\Common\Models\Administrator;
use Modules\Discounts\Dtos\DiscountSourceDto;
use Modules\Discounts\Enums\DiscountSourceEnum;
use Modules\Discounts\Models\LoanDiscountLog;

final class LoanDiscountLogService
{

    /**
     * Finding out the source discount by comparing the requested value. If there are no compliance with the sources,
     * then the discount will be recognized as the agent’s discount.
     *
     * @param LoanDiscountLog $model
     * @param Administrator $admin
     * @param int $requestedDiscount
     * @param DiscountSourceDto[] $sources
     * @return void
     */
    public function fillByRequestedDiscount(
        LoanDiscountLog $model,
        Administrator $admin,
        int $requestedDiscount,
        array $sources,
    ): void {
        if (!$requestedDiscount) {
            return;
        }

        $model->discount = $requestedDiscount;

        foreach ($sources as $source) {
            if (!($source instanceof DiscountSourceDto)) {
                throw new InvalidArgumentException('The source must be an instance of DiscountSourceDto');
            }
            if ($source->discount === $requestedDiscount) {
                $model->source = $source->source;
                $model->source_id = $source->sourceId;
                $model->valid_from = $source->validFrom;
                $model->valid_to = $source->validTo;
                $model->is_manual = $source->isManual;

                return;
            }
        }

        $model->source = DiscountSourceEnum::Administrator;
        $model->source_id = $admin->getKey() ?? getAdminId();
        $model->is_manual = true;
    }
}
