<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'client_discount_actual',
            function ($table) {
                $table->bigIncrements('client_discount_actual_id');
                $table->integer('client_id')->unsigned()->index();
                $table->integer('product_id')->unsigned()->index();
                $table->decimal('percent', 11, 2);
                $table->timestamp('valid_from')->nullable()->index();
                $table->timestamp('valid_till')->nullable()->index();

                $table->foreign('client_id')->references('client_id')->on('client');
                $table->foreign('product_id')->references('product_id')->on('product');

                $table->tableCreateFields(false, true);
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'client_discount_actual',
            function (Blueprint $table) {
                $table->dropForeign('client_discount_actual_client_id_foreign');
                $table->dropForeign('client_discount_actual_product_id_foreign');
            }
        );

        Schema::dropIfExists('client_discount_actual');
    }
};
