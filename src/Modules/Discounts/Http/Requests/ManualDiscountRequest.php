<?php

namespace Modules\Discounts\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;

class ManualDiscountRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules()
    {
        return [
            'client_id' => 'required|regex:/\#([1-9][0-9]{0,7})[ ]/',
            'discountPercent' => 'required|numeric|between:0,100',
            'validPeriod' => 'required|regex:/([0-9]{2}\-[0-9]{2}\-[0-9]{4})[\s]\-[\s]([0-9]{2}\-[0-9]{2}\-[0-9]{4})/',
            'discountProduct' => 'required|array|min:1',
            'create_sale_task' => 'nullable|numeric|between:0,1',
        ];
    }
}
