<?php

declare(strict_types=1);

namespace Modules\Discounts\Http\Controllers;

use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Discounts\Application\Actions\DiscountByPhone\DeleteAction;
use Modules\Discounts\Application\Actions\DiscountByPhone\ExportAction;
use Modules\Discounts\Application\Actions\DiscountByPhone\ImportAction;
use Modules\Discounts\Application\Actions\DiscountByPhone\IndexAction;
use Modules\Discounts\Application\Actions\DiscountByPhone\StoreAction;
use Modules\Discounts\Http\Requests\DiscountByPhone\FilterRequest;
use Modules\Discounts\Http\Requests\DiscountByPhone\ImportRequest;
use Modules\Discounts\Http\Requests\DiscountByPhone\CreateRequest;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

final class DiscountByPhoneController extends BaseController
{
    public function index(
        FilterRequest $request,
        IndexAction $action
    ): View {
        return view(
            'discounts::discount-by-phone.index',
            $action->execute($request->validated(), $this->getPaginationLimit())
        );
    }

    public function export(
        FilterRequest $request,
        ExportAction $action,
    ): BinaryFileResponse {
        return $action->execute($request->validated())->download(
            'discounts_by_phones_' . time() . '.xlsx'
        );
    }

    public function destroy(int $id, DeleteAction $action): RedirectResponse
    {
        if ($action->execute($id)) {
            return $this->backSuccess('discounts::discount-by-phone.messages.SuccessfulDelete');
        }

        return $this->backError('discounts::discount-by-phone.messages.UnsuccessfulDelete');
    }

    public function create(CreateRequest $request, StoreAction $action): RedirectResponse
    {
        $action->execute($request->validated());

        return $this->backSuccess('discounts::discount-by-phone.messages.SuccessfulCreate');
    }

    public function import(ImportRequest $request, ImportAction $action): RedirectResponse
    {
        $action->execute($request->validated())->import($request->file('import'));

        return $this->backSuccess('discounts::discount-by-phone.messages.SuccessfulImport');
    }
}
