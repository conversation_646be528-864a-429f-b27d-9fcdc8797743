<?php

declare(strict_types=1);

namespace Modules\Sales\Exceptions;

use Exception;
use Modules\Common\Exceptions\ShouldBeReportedToUser;

final class AmountForRefinanceIsLargerThanProductMaxAmount extends Exception implements ShouldBeReportedToUser
{
    public function __construct(string $refinanceAmount, string $productMaxAmount)
    {
        parent::__construct(
            __(
                'Amount for refinance :refinanceAmount is larger than product max amount :productMaxAmount',
                compact('refinanceAmount', 'productMaxAmount')
            ),
            200
        );
    }
}
