<?php

namespace Modules\Sales\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Common\Models\SaleDecision;

/**
 * php artisan db:seed --class=\\Modules\\Sales\\Database\\Seeders\\AddNewSaleDecisionSeeder
 */
class AddNewSaleDecisionSeeder extends Seeder
{
    public function run(): void
    {
        $saleDecision = [
            'sale_decision_id' => SaleDecision::SALE_DECISION_ID_SELF_DESTROY,
            'name' => SaleDecision::SALE_DECISION_SELF_DESTROY,
            'type' => SaleDecision::SALE_DECISION_TYPE_FINAL,
            'active' => 1,
            'deleted' => 0
        ];

        SaleDecision::create($saleDecision);
    }
}
