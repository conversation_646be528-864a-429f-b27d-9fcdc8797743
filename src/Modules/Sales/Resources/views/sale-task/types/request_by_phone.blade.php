<tr>
    <td>{{ $saleTask->sale_task_id }}</td>
    <td>{{ __('sales::saleTaskType.' . $saleTask->sale_task_type_name) }}</td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td></td>
    <td>{{ $saleTask->client_phone}}</td>
    <td>{{ \Carbon\Carbon::parse($saleTask->created_at)->format('d.m.Y H:i:s') }}</td>
    <td class="timer secondsTd">{{ $saleTask->timer }}</td>
    <td>
        @if(!empty($saleTask->process_by))
            {{__('table.InReviewFrom')}}: {{ $saleTask->process_by}}
        @else
        @endif
    </td>
    <td>
        <x-btn-process
            url="{{ route('sales.processing', $saleTask->sale_task_id) }}"
            name="btnProcessName"
            title="{{ __('btn.Process') }}"
            disabled="{{ $disabled }}"
        />
    </td>
</tr>
