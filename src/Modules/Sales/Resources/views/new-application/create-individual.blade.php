@extends('layouts.app')

@section('content')
    <div class="row">
        <div class="col-lg-4">
            <!-- Client card modals -->
            @include('head::client-card.modals.sale_task.something_else')
            @include('head::client-card.modals.sale_task.call_later')
        </div>
    </div>

    {!! form_start($newLoanForm,['name' => 'newAppForm']) !!}
    <input type="hidden" name="attempt[start_at]" value="{{ \Carbon\Carbon::now() }}"/>

    @include('sales::new-application.includes.loan-type')

    <div id="newLoanApp">

        <div class="row mb-5">
            <div class="col-lg-4">
                <x-card>
                    <x-slot:title>{{__('Loan calculator')}}</x-slot:title>

                    <x-create-loan-calculator
                        :loans-for-refinance="$loansForRefinance??null"
                        :client-pin="request('pin')"
                        :phone="request('phone')"
                    />
                </x-card>

                @isset($guarantorForLoans)
                    <x-card>
                        <x-slot:title>{{__('messages.GuarantorLoans')}}</x-slot:title>
                        {{--                    <div id="guarantorLoans"></div>--}}
                        @php
                            /**
                        * @var \Modules\Common\Models\Loan $guarantorLoan
                         */
                        @endphp
                        <x-table>
                            <tr>
                                <th>{{__('table.LoanId')}}</th>
                                <th>{{__('table.CreatedAt')}}</th>
                                <th>{{__('table.AmountApproved')}}</th>
                                <th>{{__('table.Product')}}</th>
                                <th>{{__('table.OverdueDays')}}</th>
                                <th>{{__('table.OverdueAmount')}}</th>
                            </tr>
                            @foreach($guarantorForLoans as $guarantorLoan)
                                <tr>
                                    <td>{{$guarantorLoan->getKey()}}</td>
                                    <td>{{$guarantorLoan->created_at->format('d.m.Y')}}</td>
                                    <td>{{intToFloat($guarantorLoan->amount_approved)}}</td>
                                    <td>{{$guarantorLoan->product->trade_name}}</td>
                                    <td>{{$guarantorLoan->getCartonDb()['current_overdue_days']}}</td>
                                    <td>{{intToFloat($guarantorLoan->getCartonDb()['current_overdue_amount'])}}</td>
                                </tr>
                            @endforeach
                        </x-table>
                    </x-card>
                @endisset
            </div>
            <!-- End ./col-lg-4 -->

            <div class="col-lg-4">
                @include('sales::new-application.includes.client-data')
                @include('sales::new-application.includes.client-address')
            </div>
            <!-- End ./col-lg-4 -->

            <div class="col-lg-4">
                @include('sales::new-application.includes.contact-person')
                @include('sales::new-application.includes.client-guarantor')
                @include('sales::new-application.includes.consultants')
                @include('sales::new-application.includes.receive-money')

                <div class="form-group">
                    <button type="submit" class="btn btn-success btn-block" id="submitNewApp">
                        {{ __('sales::newApplication.apply') }}
                    </button>
                </div>

                @includeWhen(isset($saleTask), 'head::client-card.cards.boxSalesActions')
            </div>
            <!-- End ./col-lg-4 -->
        </div>
        <!-- End ./row -->
    </div>
    <!-- End ./newLoanApp -->

    {!! form_end($newLoanForm, false) !!}

    @include('sales::new-application.modals.inflated-loan-amount-modal')
    @include('sales::new-application.modals.refinanced-loans-amount-bigger-modal')
    @include('sales::new-application.modals.new-app-online-office-modal')
    <!-- Show errors modal -->
    <x-common::modal
        modal-id="errorModal"
        modal-title="{{__('messages.errorOccurred')}}"
        header-classes="alert-danger"
    >
        <div class="modal-body"></div>
    </x-common::modal>
@endsection

@push('scripts')
    <script>
        @isset($saleTask)
        $(document).ready(function () {
            let $completeSaleTask = '{{route('head.clientCard.completeSaleTask', $saleTask->getKey())}}';
            $(document).on('click', 'button.saleDecisionForm', (event) => {
                event.preventDefault();
                $(event.target).attr('disabled');
                $(event.target).addClass('disabled');

                axios
                    .post($completeSaleTask, {
                        sale_decision_id: $(event.target).val()
                    })
                    .then(resp => {
                        location.replace(resp.data.redirectTo);
                    })
                    .catch(error => {
                        alert(error);
                    });
            });
        });
        @endisset

        let $isWebOffice = '{{session('isWebOffice',false)}}';
        $(document).ready(function () {

            if ($isWebOffice) {
                $('input[name="client[email]"]').attr('required', 'required');
            }

            $('input[name="client_idcard[valid_date]"], input[name="client_idcard[issue_date]"]').focusout(function () {
                if ($(this).val() && $(this).val().indexOf('-') !== -1) {
                    var d = new Date($(this).val());
                    var day = d.getDate() > 9 ? d.getDate() : '0' + d.getDate();
                    var month = d.getMonth() + 1;
                    $(this).val(day + '.' + month + '.' + d.getFullYear());
                }
            })

            $(document).on('click', 'button#submitNewApp', function (event) {
                event.preventDefault();

                let $submitNewApp = $(this);
                let $newAppForm = $(this).parents('form').first();
                let $formData = $newAppForm.serialize();
                $($submitNewApp).attr('disabled', 'disabled');

                /// validate form-data
                $newAppForm.parsley().validate();

                /// submit form
                if ($newAppForm.parsley().isValid()) {
                    clearValidationErrors();

                    $.post($newAppForm.attr('action'), $formData, function (resp) {

                        let messagesArr = [];
                        if (
                            resp.hasOwnProperty('messages')) {
                            Object.keys(resp.messages).forEach(function (key, index) {
                                if (resp.messages[key][0] !== undefined) {
                                    messagesArr[key] = resp.messages[key][0];
                                }
                            });
                        }

                        if (resp.success == true) {

                            $(resp.modalSelector).find('a#printDocsBtn').attr('href', resp.href);
                            $(resp.modalSelector).find('a#printDocsBtn').attr('data-print-docs', resp.printDocs);
                            $(resp.modalSelector).find('a#backInSalesBtn').attr('href', resp.href);

                        } else if (Object.keys(messagesArr).length > 0) {

                            // SHOW MESSAGES UNDER FORM FIELDS
                            showValidationErrorsFromMsg(messagesArr);
                        }

                        let $msg = null;
                        if (
                            resp.success === false
                            && '#errorModal' === resp.modalSelector
                        ) {
                            $msg = 'Something goes wrong, drink a tequila shot, smoke a cigar and kick ass to your IT team';
                        }
                        if (resp.message) {
                            $msg = resp.message;
                        }


                        if (resp.modalSelector !== '') {
                            window.Dashboard.showModalById(resp.modalSelector, $msg);

                            $($submitNewApp).removeAttr('disabled');

                            return false;
                        }

                        location.replace(resp.href);

                    }).fail(function (resp) {
                        //// show validation errors with parsley
                        if (resp.responseJSON.errors) {
                            for (const key in resp.responseJSON.errors) {
                                if (resp.responseJSON.errors.hasOwnProperty(key)) {
                                    const errorMessage = resp.responseJSON.errors[key][0];

                                    let convertedKey = key.replace(/\.(\w+)/g, "[$1]");
                                    let parsleyErrorKey = key.replaceAll('.', '-');

                                    console.error('Field: ' + convertedKey, 'Error: ' + errorMessage);
                                    console.error($('*[name="' + convertedKey + '"]'));

                                    /// before show errors remove if already existing
                                    $('*[name="' + convertedKey + '"]').parsley().removeError(parsleyErrorKey);
                                    $('*[name="' + convertedKey + '"]').parsley().addError(parsleyErrorKey, {
                                        message: errorMessage,
                                        assert: parsleyErrorKey
                                    });

                                    $('*[name="' + convertedKey + '"]').focus();
                                }
                            }
                            $($submitNewApp).removeAttr('disabled');
                            return;
                        }

                        $msg = 'Something goes wrong, drink a tequila shot, smoke a cigar and kick ass to your IT team';
                        if (resp.message) {
                            $msg = resp.message;
                        }

                        window.Dashboard.showModalById('#errorModal', resp.responseJSON.message);

                        $($submitNewApp).removeAttr('disabled');
                    });
                } else {
                    $($submitNewApp).removeAttr('disabled');
                }
            });

        });

        function showValidationErrorsFromMsg(messages) {
            for (const [key, value] of Object.entries(messages)) {

                let inputName = key;
                if (key.includes('.')) {
                    let keyArr = key.split('.');
                    inputName = keyArr[0] + '[' + keyArr[1] + ']';
                }

                let input = $('input[name="' + inputName + '"]');
                let inputId = input.attr('data-parsley-id');

                input.addClass('parsley-error');

                let ul = $('<ul>', {
                    class: 'parsley-errors-list filled',
                    id: 'parsley-id-' + inputId,
                    'aria-hidden': false
                });

                let li = $('<li>', {
                    class: 'parsley-required',
                    text: value
                });

                ul.append(li);
                input.parent().after(ul);
            }
        }

        function clearValidationErrors() {
            $('.parsley-errors-list.filled').remove();
        }

    </script>
@endpush
