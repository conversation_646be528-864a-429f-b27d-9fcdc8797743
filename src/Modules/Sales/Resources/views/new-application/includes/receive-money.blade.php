<x-card>
    <x-slot:title>{{__('Receive money method')}}</x-slot:title>

    {!! form_row($newLoanForm->bank_account_id) !!}
    {!! form_row($newLoanForm->payment_method) !!}

    <div id="receive-money" class="d-none">
        {!! form_row($newLoanForm->iban) !!}
    </div>


    <!-- End ./receive-money -->

    {!! form_row($newLoanForm->comment) !!}


    <div class="form-group mt-4">
        <label for="skip_auto_process" class="control-label">
            <input type="hidden" name="loan[skip_auto_process]" value="0"/>
            <input type="checkbox" name="loan[skip_auto_process]" value="1" id="skip_auto_process"/>

            {{__('table.SkipAutoProcess')}}
        </label>
    </div>
    <!-- End ./form-group -->

    <div class="form-group mb-0">
        <label for="skip_ref_amount_check" class="control-label">
            <input type="hidden" name="loan[skip_ref_amount_check]" value="0"/>
            <input type="checkbox" name="loan[skip_ref_amount_check]" value="1" id="skip_ref_amount_check"/>
            {{__('table.SkipRefAmountCheck')}}
        </label>
    </div>
    <!-- End ./form-group -->

</x-card>


@push('scripts')
    <script>
        let $officePaymentMethods = @json($officePaymentMethods);
        $(document).ready(function () {
            let selectedPaymentAccountId = $('select[name="loan[bank_account_id]"]').val();
            showIbanFiled(selectedPaymentAccountId);

            /// refresh on change
            $(document).on('change', 'select[name="loan[bank_account_id]"]', function () {
                selectedPaymentAccountId = $(this).val();

                showIbanFiled(selectedPaymentAccountId);
            });

            function showIbanFiled(selectedPaymentAccountId) {

                if (!selectedPaymentAccountId) {
                    return false;
                }

                if (!$officePaymentMethods.hasOwnProperty(parseInt(selectedPaymentAccountId))) {
                    alert('Problem with payment_account/payment_method');
                    return false;
                }

                $('input[name="loan[payment_method]"]').val(parseInt($officePaymentMethods[selectedPaymentAccountId]));

                if (parseInt($officePaymentMethods[selectedPaymentAccountId]) === 1) {
                    $('div#receive-money').removeClass('d-none');
                    //$('div#bank').removeClass('d-none');
                    $('input[name="loan[iban]"]').attr('required', 'required');
                } else {
                    $('div#receive-money').addClass('d-none');
                    //$('div#bank').addClass('d-none');
                    $('input[name="loan[iban]"]').removeAttr('required');
                }
            }
        });
    </script>
@endpush
