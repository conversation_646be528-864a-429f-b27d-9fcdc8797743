<x-card>
    <x-slot:title>
        <i class="fa-duotone fa-address-card"></i>&nbsp;
        {{__('Contact person')}}
    </x-slot:title>
    @php
        $contactFields = collect($newLoanForm->getFields())->filter(function ($contactPersonField){return str_contains($contactPersonField->getName(),'contact');})
    @endphp
    @foreach($contactFields->chunk(2) as $contactPersonRows)
        @if($loop->index+1 <= 2)
            <h6>{{__('Contact person :seq', ['seq' => $loop->index+1])}}</h6>
            @foreach($contactPersonRows as $contactPersonField)
                {!! form_row($contactPersonField) !!}
            @endforeach
        @else
            <div class="contact-person-box">
                <h6>
                    {{__('Contact person :seq', ['seq' => $loop->index+1])}}
                    <span class="pull-right">
                    <button type="button" class="btn btn-sm btn-danger" name="removeContactPerson">
                        <i class="fa fa-trash"></i>
                    </button>
                </span>
                </h6>

                @foreach($contactPersonRows as $contactPersonField)
                    {!! form_row($contactPersonField) !!}
                @endforeach
            </div>
        @endif
    @endforeach

    <div class="form-group">
        <button type="button" name="addNewContactPerson" class="btn btn-sm btn-primary">
            <i class="fa fa-plus"></i>&nbsp;
            {{__('Add new contact person')}}
        </button>
    </div>
    <!-- End ./form-group -->
</x-card>

@push('scripts')
    <script>
        $(document).ready(function () {
            $(document).on('click', 'button[name="addNewContactPerson"]', function () {
                let $htmlTemplate = $('script#template-contact-person').html();
                let $countContactPerson = $('input.contact-form-person').length + 1;
                const elements = document.querySelectorAll('input[name*="contact["][name*="][name]"]');

                let $index = 1;
                if(!isNaN(parseInt(elements.length))){
                    $index = parseInt(elements.length) + 1;
                }

                $htmlTemplate = $htmlTemplate.replaceAll(
                    "contact[1][name]",
                    "contact[" + $index + "][name]"
                );

                $htmlTemplate = $htmlTemplate.replaceAll(
                    "contact[1][phone]",
                    "contact[" + $index + "][phone]"
                );

                $htmlTemplate = $htmlTemplate.replace(
                    " 1",
                    " " + $countContactPerson
                );

                $(this).parent('div.form-group:first').before($htmlTemplate);
            });

            $(document).on('click', 'button[name="removeContactPerson"]', function () {
                $(this).parents('div.contact-person-box:first').remove();
            });
        });
    </script>

    <!-- Template -->
    <script type="text/html" id="template-contact-person">
        <div class="contact-person-box">
            <h6>
                {{__('Contact person 1')}}
                <span class="pull-right">
                <button type="button" class="btn btn-sm btn-danger" name="removeContactPerson">
                    <i class="fa fa-trash"></i>
                </button>
            </span>
            </h6>
            {!! form_row($newLoanForm->{'contact[1][name]'},['value' => '']) !!}
            {!! form_row($newLoanForm->{'contact[1][phone]'},['value' => '']) !!}
        </div>
    </script>
@endpush
