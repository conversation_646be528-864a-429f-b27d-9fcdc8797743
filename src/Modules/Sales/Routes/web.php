<?php

use Illuminate\Support\Facades\Route;
use Modules\Sales\Http\Controllers\ClientsWithoutProfileController;
use Modules\Sales\Http\Controllers\ClientsWhoHaveNeverHadActiveLoans;
use Modules\Sales\Http\Controllers\SaleLoanController;
use Modules\Sales\Http\Controllers\TestController;
use Modules\Sales\Http\Controllers\VeriffStatsController;

Route::prefix('sales')->group(function () {
    Route::post('/sale-loan/update-loan', [SaleLoanController::class, 'updateLoan'])
        ->name('sales.sale-loan.update-loan')//http://localhost:8000/approve//loan-decision/update-loan
        ->defaults('description', 'Change application product settings')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'General')
        ->defaults('info_bubble', 'Може да променя параметри по заявката от бутон Промени заявка в клиентска карта');

    Route::get('test-something', [TestController::class, 'something'])->name('sales.testSomething');

    Route::get('/tmp-requests', 'TmpRequestController@list')
        ->name('sales.tmpRequests.list')
        ->defaults('description', 'View unfinished applications')
        ->defaults('module_name', 'User Settings')
        ->defaults('controller_name', 'General')
        ->defaults('info_bubble', 'Вижда страница Оперативни -> Заявки');

    Route::get('/tmp-requests/refresh', 'TmpRequestController@refresh')
        ->name('sales.tmpRequests.refresh')
        ->defaults('description', 'Ajax refresh of unfinished tmp requests from website');

    // Client without profile

    Route::get('/clients/without-profile', [ClientsWithoutProfileController::class, 'index'])
        ->name('sales.clients.without-profile')
        ->defaults('module_name', 'Sales')
        ->defaults('controller_name', 'Customers Without Profile')
        ->defaults('description', 'View page');

    Route::post(
        '/clients/without-profile/send-message/sms',
        [ClientsWithoutProfileController::class, 'sendSmsMessage']
    )
        ->name('sales.clients.without-profile.send-message.sms')
        ->defaults('module_name', 'Sales')
        ->defaults('controller_name', 'Customers Without Profile')
        ->defaults('description', 'Send SMS');

    Route::get('/clients/without-profile/export', [ClientsWithoutProfileController::class, 'export'])
        ->name('sales.clients.without-profile.export')
        ->defaults('module_name', 'Sales')
        ->defaults('controller_name', 'Customers Without Profile')
        ->defaults('description', 'Export data');

    // Clients who have never had active loans

    Route::get('/clients/who-have-never-had-active-loans', [ClientsWhoHaveNeverHadActiveLoans::class, 'index'])
        ->name('sales.clients.who-have-never-had-active-loans')
        ->defaults('description', 'View page')
        ->defaults('module_name', 'Sales')
        ->defaults('controller_name', 'Customers Never Had Loan')
        ->defaults('additional_info', 'Information about clients who have never had active loans');

    Route::post(
        '/clients/who-have-never-had-active-loans/send-message/sms',
        [ClientsWhoHaveNeverHadActiveLoans::class, 'sendSmsMessage']
    )
        ->name('sales.clients.who-have-never-had-active-loans.send-message.sms')
        ->defaults('description', 'Send SMS')
        ->defaults('module_name', 'Sales')
        ->defaults('controller_name', 'Customers Never Had Loan')
        ->defaults('additional_info', 'Send sms message to clients who have never had active loans');

    Route::post(
        '/clients/who-have-never-had-active-loans/send-message/email',
        [ClientsWhoHaveNeverHadActiveLoans::class, 'sendEmailMessage']
    )
        ->name('sales.clients.who-have-never-had-active-loans.send-message.email')
        ->defaults('module_name', 'Sales')
        ->defaults('controller_name', 'Customers Never Had Loan')
        ->defaults('description', 'Send email.');

    Route::get('/clients/who-have-never-had-active-loans/export', [ClientsWhoHaveNeverHadActiveLoans::class, 'export'])
        ->name('sales.clients.who-have-never-had-active-loans.export')
        ->defaults('module_name', 'Sales')
        ->defaults('controller_name', 'Customers Never Had Loan')
        ->defaults('description', 'Export data');

    Route::get('/veriff-stats', [VeriffStatsController::class, 'list'])
        ->name('sales.veriff-stats.list')
        ->defaults('module_name', 'Sales')
        ->defaults('controller_name', 'Veriff Page')
        ->defaults('description', 'View veriff stats page');
});
