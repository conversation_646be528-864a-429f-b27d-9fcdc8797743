<?php

use Illuminate\Support\Facades\Route;
use Modules\Sales\Http\Controllers\NewAppController;

Route::prefix('sales')->group(function () {
    Route::get('/new-application/set-office', [NewAppController::class, 'setOfficeId'])
        ->name('sales.setOfficeId');

    Route::get('/new-application/{saleTask}/from-sale-task', [NewAppController::class, 'newAppFromSaleTask'])
        ->name('sales.newAppFromSaleTask')
        ->defaults('description', 'Create application from sale task')
        ->defaults('module_name', 'Sales')
        ->defaults('controller_name', 'New Application Page')
        ->defaults('info_bubble', 'Отваря страница Нова заявка с попълнени данни на клиента. Използва се, когато се обработва задача за продажба');

    Route::get('/new-application', [NewAppController::class, 'newApplication'])
        ->name('sales.newApplication')
        ->defaults('description', 'View new application page')
        ->defaults('module_name', 'Sales')
        ->defaults('controller_name', 'New Application Page')
        ->defaults('info_bubble', 'Вижда страница Нова заявка');

    Route::get('/new-application-reset', [NewAppController::class, 'newApplicationReset'])
        ->name('sales.newApplicationReset');
        // ->defaults('description', 'New application page');

    Route::post('/new-application', [NewAppController::class, 'storeNewApplication'])
        ->name('sales.saveNewApplication');

    Route::post('/new-application/company', [NewAppController::class, 'storeNewCompanyApplication'])
        ->name('sales.storeNewCompanyApplication');

    Route::get('/check-client-from-mvr', [NewAppController::class, 'getClientDataFromMvrAjax'])
        ->name('sales.checkClientFromMvr')
        ->defaults('description', 'Fill client data from MVR')
        ->defaults('module_name', 'Sales')
        ->defaults('controller_name', 'New Application Page')
        ->defaults('info_bubble', 'Тегли справка от МВР с данните на клиента и ги попълва.');

    Route::get('/product-settings/{product}/{client?}', [NewAppController::class, 'getProductSettingsByProduct'])
        ->name('sales.getProductSettingsByProduct');

    Route::get('/get-client-loan-data', [NewAppController::class, 'getClientLoanData'])
        ->name('sales.getClientLoanData');

    Route::get('/calc-params', [NewAppController::class, 'calcLoanParams'])
        ->name('sales.calculateLoanParams');

    Route::get('/loans/{client}/{loanId?}', [NewAppController::class, 'getClientActiveLoans'])
        ->name('sales.getActiveLoans');

    Route::get('/get-products-by-office/{office}', [NewAppController::class, 'getProductsByOffice'])
        ->name('sales.getProductsByOffice');

    Route::post('/get-rest-after-refinance', [NewAppController::class, 'getRestAfterRefinance'])
        ->name('sales.getRestAfterRefinance');

    Route::get('/load-guarantor-by-pin', [NewAppController::class, 'getGuarantorDataByPin'])
        ->name('sales.getGuarantorDataByPin');
});
