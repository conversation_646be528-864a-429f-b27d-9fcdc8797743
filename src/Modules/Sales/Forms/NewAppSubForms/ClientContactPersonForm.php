<?php

namespace Modules\Sales\Forms\NewAppSubForms;

use Kris\LaravelFormBuilder\Form;

class ClientContactPersonForm extends Form
{
    public function buildForm(): void
    {

        $clientContacts = $this->getData('client_contacts', []) ?? [];
        $firstContactPerson = array_shift($clientContacts);

        $this->add('contact[1][name]', 'text', [
            'label' => __('table.Name'),
            'value' => $firstContactPerson['name'] ?? '',
            'attr' => [
                'class' => 'form-control contact-form-person',
                'maxlength' => 100,
                'data-parsley-pattern' => "/^[a-zA-Zа-яА-Я\s-]+$/i",
                "autocomplete" => "nope"
            ]
        ]);

        $this->add('contact[1][phone]', 'text', [
            'label' => __('table.Phone'),
            'value' => $firstContactPerson['phone'] ?? '',
            'attr' => [
                'maxlength' => 20,
                'data-parsley-trigger' => "keyup",
                'data-parsley-pattern' => config('validation.requestRules.commonPhoneParsley'),
                'data-parsley-pattern-message' => __('Невалиден телефонен номер.'),
                "autocomplete" => "nope"
            ]
        ]);

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        $secondContactPerson = array_shift($clientContacts);
        $this->add('contact[2][name]', 'text', [
            'label' => __('table.Name'),
            'value' => $secondContactPerson['name'] ?? '',
            'attr' => [
                'data-parsley-pattern' => "/^[a-zA-Zа-яА-Я\s-]+$/i",
                'maxlength' => 100,
                'class' => 'form-control contact-form-person',
                "autocomplete" => "nope"
            ]
        ]);

        $this->add('contact[2][phone]', 'text', [
            'label' => __('table.Phone'),
            'value' => $secondContactPerson['phone'] ?? '',
            'attr' => [
                'maxlength' => 20,
                'data-parsley-trigger' => "keyup",
                'data-parsley-pattern' => config('validation.requestRules.commonPhoneParsley'),
                'data-parsley-pattern-message' => __('Невалиден телефонен номер.'),
                "autocomplete" => "nope"
            ]
        ]);

        if (count($clientContacts) > 0) {
            $index = 3;
            foreach ($clientContacts as $clientContact) {
                if (!$clientContact['name'] && !$clientContact['phone']) {
                    continue;
                }

                $this->add("contact[{$index}][name]", 'text', [
                    'label' => __('table.Name'),
                    'value' => $clientContact['name'] ?? '',
                    'attr' => [
                        'data-parsley-pattern' => "/^[a-zA-Zа-яА-Я\s-]+$/i",
                        'maxlength' => 100,
                        'class' => 'form-control contact-form-person',
                        "autocomplete" => "nope"
                    ]
                ]);

                $this->add("contact[{$index}][phone]", 'text', [
                    'label' => __('table.Phone'),
                    'value' => $clientContact['phone'] ?? '',
                    'attr' => [
                        'maxlength' => 20,
                        'data-parsley-trigger' => "keyup",
                        'data-parsley-pattern' => config('validation.requestRules.commonPhoneParsley'),
                        'data-parsley-pattern-message' => __('Невалиден телефонен номер.'),
                        "autocomplete" => "nope"
                    ]
                ]);

                $index++;
            }
        }
    }
}
