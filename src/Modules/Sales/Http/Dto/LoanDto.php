<?php

namespace Modules\Sales\Http\Dto;

use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Http\Dto\SpatieDto;
use Modules\Discounts\Repositories\ClientDiscountActualRepository;
use Modules\Discounts\Repositories\DiscountByPhoneRepository;
use Modules\Head\Repositories\ClientRepository;

class LoanDto extends SpatieDto
{
    public function __construct(
        public ?int    $loan_id,
        public int     $office_id,
        public ?int    $administrator_id,
        public int     $payment_method_id,
        public int     $product_id,
        public int     $loan_sum,
        public int     $loan_period_days,
        public int     $discount_percent,
        public array   $refinanced_loan_ids,
        public ?array  $contactDtoArr = [],
        public ?array  $guarantorDtoArr = [],
        public ?string $ip = '',
        public ?string $browser = '',
        public ?string $locationData = null,
        public ?string $interest = null,
        public ?string $penalty = null,
        public ?string $iban = null,
        public ?int    $bank_account_id = null,
        public ?int    $consultant_id = null,
        public ?string $comment = null,
        public ?string $skip_auto_process = '0',
        public ?string $skip_ref_amount_check = '0',
        public ?LoanSourceEnum $source = null,
    ) {
    }

    public static function getFrom(array $array): self
    {
        $array['interest'] = $array['interest'] ?? null;
        $array['penalty'] = $array['penalty'] ?? null;
        $array['bank_account_id'] = $array['bank_account_id'] ?? null;
        $array['loan_period_days'] = $array['loan_period_days'] ?? $array['loan_period'];
        $array['payment_method_id'] = $array['payment_method_id'] ?? $array['payment_method'];
        $array['administrator_id'] = $array['administrator_id'] ?? getAdminId();
        $array['refinanced_loan_ids'] = $array['refinanced_loan_ids'] ?? ($array['refinanced_loans'] ?? []);
        $array['contactDtoArr'] = isset($array['contact']) ? self::contactDtoArr($array['contact']) : null;
        $array['guarantorDtoArr'] = isset($array['guarantor']) ? self::guarantorDtoArr($array['guarantor']) : null;

        return self::from($array);
    }

    /* @return ContactDto[] */
    public static function contactDtoArr(array $array): array
    {
        $contactDtos = [];
        foreach ($array as $i => $cData) {
            if (!$cData["name"]) {
                continue;
            }
            if (!isset($cData['seq_num'])) {
                $cData['seq_num'] = $i + 1;
            }

            $cData['pin'] = $cData['pin'] ?? null;
            $cData['email'] = $cData['email'] ?? null;

            $contactDtos[] = ContactDto::from($cData);
        }
        return $contactDtos;
    }

    /* @return GuarantorDto[] */
    public static function guarantorDtoArr(array $array): array
    {
        $res = [];
        $i = 1;
        foreach ($array as $seqNum => $guarantorData) {
            if (!$guarantorData['pin']) {
                continue;
            }
            if (!isset($guarantorData['seq_num'])) {
                //idea of dmitry arnaut to replace 12 digit seq_nums send by him from the front - with local counter value.
                $guarantorData['seq_num'] = $seqNum > 1000 ? $i : $seqNum;
            }
            $i++;
            $res[] = GuarantorDto::getFrom($guarantorData);
        }

        return $res;
    }

    public function setDiscount(string $pin, string $phone, int $productId): void
    {
        if ($this->discount_percent) {
            return;
        }

        $discountByPhone = (int) app(DiscountByPhoneRepository::class)->getPercentByPhoneAndProduct(
            $phone,
            $productId
        );
        $clientId = app(ClientRepository::class)->getIdByPin($pin);
        if ($clientId) {
            $clientDiscountActual = app(ClientDiscountActualRepository::class)
                ->getClientDiscountWithProduct($clientId, $productId);
            $discountByClient = (int) $clientDiscountActual?->percent;
        }
        $discount = max($discountByPhone, $discountByClient ?? 0);
        if ($discount > 0) {
            $this->discount_percent = $discount;
        }
    }
}
