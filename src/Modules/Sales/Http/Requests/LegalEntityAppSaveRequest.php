<?php

namespace Modules\Sales\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Interfaces\LoggableRequestInterface;
use Modules\Common\Models\PaymentMethod;

class LegalEntityAppSaveRequest extends BaseRequest implements LoggableRequestInterface
{
    public function rules(): array
    {
        $rules = [
            'clientMetaAction' => 'nullable|string|max:55',

            'client.first_name' => $this->getConfiguration('requestRules.firstName'),
            'client.first_name_latin' => 'nullable|string|max:255',
            'client.middle_name_latin' => 'nullable|string|max:255',
            'client.last_name_latin' => 'nullable|string|max:255',
            'client.foreigner' => 'nullable',

            /// client_idcard
            'client_idcard.pin' => 'required|numeric|min_digits:9|max_digits:13',
            'client_idcard.idcard_number' => 'required|string',
            'client_idcard.image' => 'nullable|string',

            /// id card address
            'client_idcard.address' => 'required|string|max:255',
            'client_idcard.city_id' => 'required|numeric|exists:city,city_id',

            'loan.office_id' => 'required|numeric',
            'loan.product_id' => 'required|numeric',
            'loan.loan_period' => 'required|numeric',
            'loan.loan_sum' => 'required|numeric',
            'loan.interest' => 'nullable|numeric',
            'loan.penalty' => 'nullable|numeric',
            'loan.discount_percent' => 'required|numeric|min:0|max:100',
            'loan.payment_method' => 'numeric',
            'loan.bank_account_id' => 'required|numeric',
            'loan.comment' => 'nullable|string|max:255',
            'loan.iban' => [
                'nullable',
                'required_if:payment_method_id,' . PaymentMethod::PAYMENT_METHOD_BANK,
                'min:20',
                'max:22',
                'regex:/([A-Z])\w+([0-9])\w+/',
            ],

            //// use representor_ because has conflict with client first_name and e.t.c
            'representor.representor_first_name' => 'nullable|string|max:255',
            'representor.representor_middle_name' => 'nullable|string|max:255',
            'representor.representor_last_name' => 'nullable|string|max:255',
            'representor.representor_phone' => 'nullable|string|max:255',
            'representor.representor_phone_additional' => 'nullable|string|max:255',
            'representor.representor_email' => 'nullable|string|max:255',

            'contact.*.name' => $this->getConfiguration('requestRules.nameNullable'),
            'contact.*.phone' => $this->getConfiguration('requestRules.phoneNullable'),
            'contact.*.contact_type_id' => 'nullable',
            'refinanced_loans' => 'nullable|array',
            'refinanced_loans.*' => 'exists:loan,loan_id',
        ];

        return $rules;
    }

    public function messages(): array
    {
        return [
            'client.first_name.required' => __('messages.Required'),
            'client.last_name.required' => __('messages.Required'),
            'client.address.required' => __('messages.Required'),
            'client_idcard.address.required' => __('messages.Required'),
            'client_idcard.idcard_issued_id.required' => __('messages.Required'),
            'client_idcard.idcard_number.required' => __('messages.Required'),
            'client_idcard.pin.is_valid_pin' => __('messages.Invalid'),
            'client_idcard.city_id.required' => __('messages.Required'),
            'client_idcard.issue_date.required' => __('messages.Required'),
            'client_idcard.valid_date.required' => __('messages.Required'),
            'loan.payment_method.required' => __('messages.Required'),
            'loan.payment_method.numeric' => __('messages.Invalid'),
            'client.phone.*.required' => __('messages.Required'),
            'client.phone.*.regex' => __('messages.SymbolNotAllowed'),
            'guarant.*.pin.is_valid_pin' => __('messages.Invalid'),
            'loan.iban.required' => __('messages.Required'),
            'loan.iban.regex' => __('messages.InvalidFormatIBAN'),
        ];
    }
}
