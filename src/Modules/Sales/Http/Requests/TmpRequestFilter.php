<?php

namespace Modules\Sales\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Common\Traits\DateBuilderTrait;

class TmpRequestFilter extends FormRequest
{
    use DateBuilderTrait;

    public function rules(): array
    {
        return [
            'tmp_request_id' => 'nullable|numeric',
            'product_id' => 'nullable|numeric',
            'amount_requested' => 'nullable|numeric',
            'period_requested' => 'nullable|numeric',
            'payment_method_id' => 'nullable|numeric',
            'phone' => 'nullable|string',
            'email' => 'nullable|string',
            'pin' => 'nullable|string',
            'idcard_number' => 'nullable|string',
            'clientFullName' => 'nullable|string',
            'ip' => 'nullable|string',
            'created_at' => [
                'nullable',
                'regex:' . $this->getDateValidationRegex()
            ],
        ];
    }

    public function getFilters(): array
    {
        $filters = $this->validated();
        if (
            !empty($filters['created_at'])
            && !preg_match("/(\d{2}-\d{2}-\d{4}) - (\d{2}-\d{2}-\d{4})/", $filters['created_at'])
        ) {
            $filters['created_at'] = $filters['created_at'] . ' - ' . $filters['created_at'];
        }
        return $filters;
    }
}
