<?php

declare(strict_types=1);

namespace Modules\Sales\Http\Requests\ClientsWhoHaveNeverHadActiveLoans;

use Modules\Common\Http\Requests\BaseRequest;

final class FilterRequest extends BaseRequest
{
    public function rules(): array
    {
        return [
            'client_id' => 'nullable|integer',
            'email' => 'nullable|string|email',
            'phone' => 'nullable|string',
            'total_count' => 'nullable|integer',
            'days_since_last' => 'nullable|integer',
            'first_created_at' => ['nullable', 'regex:' . $this->getDateValidationRegex()],
            'last_created_at' => ['nullable', 'regex:' . $this->getDateValidationRegex()],
        ];
    }
}
