<?php

namespace Modules\Sales\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;

class NewAppLoadClientRequest extends BaseRequest
{

    public function rules(): array
    {
        $rules = ['pin' => ['sometimes', 'numeric']];

        if (!$this->input('phone')) {
            $rules['pin'][] = 'exists:client,pin';
        }

        return [
            ...$rules,
            'newLoanType' => 'sometimes|string|in:individual,company',
            'idcard_number' => 'sometimes|string|exists:client_idcard,idcard_number',
            'phone' => 'sometimes|numeric',
        ];
    }

    public function messages(): array
    {
        return [
            'pin' => __('Requested pin (:pin) is not available on database', ['pin' => $this->get('pin')]),
        ];
    }
}
