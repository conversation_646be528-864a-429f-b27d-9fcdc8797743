<?php

namespace Modules\Sales\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Interfaces\LoggableRequestInterface;
use Modules\Common\Models\PaymentMethod;

final class ApplicationSaveRequest extends BaseRequest implements LoggableRequestInterface
{
    public function rules(): array
    {
        $rules = [
            'clientMetaAction' => 'nullable|string|max:55',

            'client.first_name' => $this->getConfiguration('requestRules.firstName'),
            'client.middle_name' => $this->getConfiguration('requestRules.middleNameNullable'),
            'client.last_name' => $this->getConfiguration('requestRules.lastName'),
            'client.first_name_latin' => 'nullable|string|max:255',
            'client.middle_name_latin' => 'nullable|string|max:255',
            'client.last_name_latin' => 'nullable|string|max:255',
            'client.email' => $this->getConfiguration('requestRules.emailNullable'),
            'client.phone' => $this->getConfiguration('requestRules.commonPhone'),
            'client.phone2' => $this->getConfiguration('requestRules.commonPhoneNullable'),
            'client.foreigner' => 'nullable',

            /// client_idcard
            'client_idcard.pin' => $this->getConfiguration('requestRules.pin'),
            'client_idcard.address_is_match' => 'required|string|in:yes,no',
            'client_idcard.idcard_number' => 'required|string',
            'client_idcard.idcard_issued_id' => 'required|numeric|max:100',
            'client_idcard.issue_date' => $this->getConfiguration('requestRules.issueDateNullable'),
            'client_idcard.valid_date' => $this->getConfiguration('requestRules.validDate'),
            'client_idcard.lifetime_idcard' => 'required|integer',
            'client_idcard.image' => 'nullable|string',

            /// id card address
            'client_idcard.address' => 'required|string|max:255',
            'client_idcard.city_id' => 'required|numeric|exists:city,city_id',

            /// current address
            'client_idcard.current_address' => 'nullable|string|max:255',
            'client_idcard.current_city_id' => 'nullable|numeric',

            'loan.office_id' => 'required|numeric',
            'loan.product_id' => 'required|numeric',
            'loan.loan_period' => 'required|numeric',
            'loan.loan_sum' => 'required|numeric',
            'loan.interest' => 'nullable|numeric',
            'loan.penalty' => 'nullable|numeric',
            'loan.discount_percent' => 'required|numeric|min:0|max:100',
            'loan.consultant_id' => 'nullable|numeric',
            'loan.payment_method' => 'required|numeric',
            'loan.bank_account_id' => 'required|numeric',
            'loan.comment' => 'nullable|string|max:255',
            'loan.skip_auto_process' => 'required|integer|in:0,1',
            'loan.skip_ref_amount_check' => 'required|integer|in:0,1',
            'attempt.start_at' => 'required|date',

            'representor.first_name' => 'nullable|string|max:255',
            'representor.middle_name' => 'nullable|string|max:255',
            'representor.last_name' => 'nullable|string|max:255',
            'representor.phone' => $this->getConfiguration('requestRules.commonPhoneNullable'),
            'representor.phone_additional' => $this->getConfiguration('requestRules.commonPhoneNullable'),
            'representor.email' => 'nullable|string|max:255',

            'contact.*.name' => $this->getConfiguration('requestRules.nameNullable') . '|required_with:contact.*.phone',
            'contact.*.phone' => $this->getConfiguration(
                    'requestRules.commonPhoneNullable'
                ) . '|required_with:contact.*.name|distinct',
            'contact.*.contact_type_id' => 'nullable',
            'refinanced_loans.*' => 'nullable|exists:loan,loan_id',

            //// guarantor validation rules
            'guarant.*.pin' => $this->getConfiguration('requestRules.pinNullable') . '|distinct',
            'guarant.*.first_name' => $this->getConfiguration('requestRules.firstNameNullable'),
            'guarant.*.middle_name' => $this->getConfiguration('requestRules.middleNameNullable'),
            'guarant.*.last_name' => $this->getConfiguration('requestRules.lastNameNullable'),
            'guarant.*.phone' => $this->getConfiguration('requestRules.commonPhoneNullable'),
            'guarant.*.guarant_type_id' => 'nullable|numeric',
            'guarant.*.idcard_number' => 'nullable|string',
            'guarant.*.idcard_issued_id' => 'nullable|numeric',
            'guarant.*.idcard_issue_date' => 'nullable|date|before_or_equal:' . date('d.m.Y'),
            'guarant.*.idcard_valid_date' => $this->getConfiguration('requestRules.validDate'),
            'guarant.*.address' => 'nullable|min:2|max:100',
            'guarant.*.currentAddress.city_id' => 'nullable|numeric',
            'guarant.*.currentAddress.address' => 'nullable|string|max:255',
        ];

        if (
            !empty($this->loan['payment_method'])
            && PaymentMethod::PAYMENT_METHOD_BANK == $this->loan['payment_method']
        ) {
            $rules['loan.iban'] = 'required|min:16|max:34|regex:/^([a-zA-Z0-9]{16,34})+$/';
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'client.first_name.required' => __('messages.Required'),
            'client.last_name.required' => __('messages.Required'),
            'client.address.required' => __('messages.Required'),
            'client_idcard.address.required' => __('messages.Required'),
            'client_idcard.idcard_issued_id.required' => __('messages.Required'),
            'client_idcard.idcard_number.required' => __('messages.Required'),
            'client_idcard.pin.is_valid_pin' => __('messages.Invalid PIN'),
            'client_idcard.city_id.required' => __('messages.Required city'),
            'client_idcard.issue_date.required' => __('messages.Required issue date'),
            'client_idcard.valid_date.required' => __('messages.Required valid date'),
            'client_idcard.valid_date.date' => __('messages.Invalid valid date'),
            'client_idcard.valid_date.after' => __('messages.IdCardDateAfterNow'),
            'loan.payment_method.required' => __('messages.Required payment_method'),
            'loan.payment_method.numeric' => __('messages.Invalid payment_method'),
            'client.phone.required' => __('messages.Required'),
            'client.phone.regex' => __('messages.Invalid client phone'),
            'client.phone2.regex' => __('messages.Invalid client phone'),
            'loan.iban.required' => __('messages.Required'),
            'loan.iban.regex' => __('messages.InvalidFormatIBAN'),

            //// guarantor validation messages
            'guarant.*.pin.is_valid_pin' => __('messages.Invalid PIN'),
            'guarant.*.pin.required' => __('messages.Required'),
            'guarant.*.pin.distinct' => __('messages.DistinctGuarantorPin'),
            'guarant.*.pin.*' => __('messages.Invalid'),

            'guarant.*.first_name.*' => __('messages.Invalid first name'),
            'guarant.*.middle_name.*' => __('messages.Invalid middle name'),
            'guarant.*.last_name.*' => __('messages.Invalid last name'),

            'guarant.*.phone.regex' => __('messages.OnlyNumbers'),
            'guarant.*.phone.*' => __('messages.Invalid phone'),

            'guarant.*.guarant_type_id.*' => __('messages.Invalid guarant type id'),

            'guarant.*.idcard_number.required' => __('messages.Required'),
            'guarant.*.idcard_number.distinct' => __('messages.DistinctGuarantorIdCardNumber'),
            'guarant.*.idcard_number.*' => __('messages.Invalid'),

            'guarant.*.idcard_issued_id.*' => __('sales::newApplication.guarantIssuedByProblem'),
            'guarant.*.idcard_issue_date.*' => __('messages.Invalid issue date'),
            'guarant.*.idcard_valid_date.*' => __('messages.Invalid valid to date'),
            'guarant.*.address.*' => __('messages.Invalid address'),

            /// contact person validation messages
            'contact.*.name.required_with' => __('messages.Required'),
            'contact.*.name.string' => __('messages.Required'),
            'contact.*.name.max' => __('messages.MaxInputLength', ['max' => 50]),

            'contact.*.phone.required_with' => __('messages.Required'),
            'contact.*.phone.regex' => __('messages.OnlyNumbers'),
            'contact.*.phone.distinct' => __('messages.DistinctContactPhone'),
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'client_idcard' => [
                ...$this->client_idcard,
                'address' => $this->sanitizeAddress($this->input('client_idcard.address')),
                'current_address' => $this->sanitizeAddress($this->input('client_idcard.current_address')),
            ],
        ]);
    }

    private function sanitizeAddress(?string $address): ?string
    {
        return $address ? trim(preg_replace('/\s+/', ' ', $address)) : $address;
    }
}
