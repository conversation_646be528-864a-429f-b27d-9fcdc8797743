<?php

namespace Modules\Sales\Http\Controllers;

use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Sales\Application\Actions\VeriffStatsListAction;
use Modules\Sales\Http\Requests\VeriffStatsFilterRequest;

class VeriffStatsController extends BaseController
{
    public function list(VeriffStatsFilterRequest $request, VeriffStatsListAction $action): View
    {
        return view(
            'sales::veriff-stats.list',
            $action->execute($request->validated(), $this->getPaginationLimit())
        );
    }
}