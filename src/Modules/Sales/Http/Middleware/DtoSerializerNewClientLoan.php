<?php

namespace Modules\Sales\Http\Middleware;

use Modules\Common\Enums\AddressTypeEnum;
use Modules\Common\Models\NotificationSetting;
use Modules\Sales\Http\Dto\AddressDto;
use Modules\Sales\Http\Dto\BankDto;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Dto\ClientRelationDto;
use Modules\Sales\Http\Dto\NotificationSettingDto;
use Modules\Sales\Http\Dto\RepresentativeDto;
use Modules\Sales\Http\Dto\EmploymentDto;
use Modules\Sales\Http\Dto\IdCardDto;
use Modules\Sales\Http\Dto\LoanDto;
use Modules\Sales\Http\Dto\PhoneDto;
use Modules\Sales\Http\Dto\PictureDto;

/**
 * @see \Modules\Head\Http\Middleware\ClientDtoSerializer
 */
class DtoSerializerNewClientLoan
{
    /*TODO: replace with REAL serializer*/
    public function createClientDto(array $data): ClientDto
    {
        return new ClientDto(
            $data["client_idcard"]["pin"],
            $data['client_idcard']['idcard_number'],
            $data['client']['first_name'],
            $this->getRelationDto($data),
            $data['client']['middle_name'] ?? null,
            $data['client']['last_name'] ?? null,
            $data['client']['first_name_latin'] ?? null,
            $data['client']['middle_name_latin'] ?? null,
            $data['client']['last_name_latin'] ?? null,
            $data['client']['legal_status'] ?? null,
//            null,//legal_status
            null,//citizenship_type
            null,//sex
            null,//legal_status_code
            null,//economy_sector_code
            null,//industry_code
            $data['client']['email'] ?? null,
        );
    }

    public function getRelationDto(array $array): ClientRelationDto
    {
        return ClientRelationDto::from([
            'idCardDto' => IdCardDto::getFromData($array['client_idcard']),
            'pictureDto' => PictureDto::getFrom($array['client_idcard'] ?? null),
            'employmentDto' => EmploymentDto::getFrom($array['client_employer'] ?? null),
            'loanDto' => $this->getLoanDto($array),
            'representativeDto' => RepresentativeDto::getFrom($array['representor'] ?? null),
            'bankDto' => $this->getBankDto($array),
            'addressDtoArr' => $this->getAddressDto($array),
            'phoneDtoArr' => $this->getPhoneDtos($array),
            'notificationSettingDtoArr' => $this->getNotificationSettingsDtos()
        ]);
    }

    /* @return PhoneDto[] */
    public function getPhoneDtos(array $data): array
    {
        if (isset($data['client']['phone']) && !is_array($data['client']['phone'])) {
            $phones = [$data['client']['phone']];
            if (isset($data['client']['phone2'])) {
                $phones[] = $data['client']['phone2'];
            }

            $data['client']['phone'] = $phones;
        }

        $phoneDtos = [];
        $phones = $data['client']['phone'] ?? [];
        foreach ($phones as $i => $num) {
            $phoneDtos[] = new PhoneDto($num, $i + 1);
        }
        return $phoneDtos;
    }

    public function getBankDto(array $data): ?BankDto
    {
        return isset($data['loan']['iban']) ?
            new BankDto($data['loan']['iban'], null, $data['loan']['bank_id'] ?? null)
            : null;
    }

    public function getLoanDto(array $data): LoanDto
    {
        $d = $data['loan'];
        $d['contact'] = $data['contact'] ?? null;
        $d['guarantor'] = $data['guarant'] ?? null;
        $d['refinanced_loans'] = $data['refinanced_loans'] ?? [];

        return LoanDto::getFrom($d);
    }

    /**
     * @see \Modules\Head\Http\Middleware\ClientDtoSerializer::getAddressDto
     * @param array $data
     * @return AddressDto[]
     */
    public function getAddressDto(array $data): array
    {
        $id_card_address = $data['client_idcard'];
        $id_card_address['type'] = AddressTypeEnum::IdCard->value;
        $addresses = [AddressDto::from($id_card_address)];

        if (isset($id_card_address['address_is_match']) && $id_card_address['address_is_match'] === 'yes') {
            $id_card_address['type'] = AddressTypeEnum::Current->value;
            $addresses[] = AddressDto::from($id_card_address);

            return $addresses;
        }

        $current_address = [
            'city_id' => $id_card_address['current_city_id'] ?? null,
            'address' => $id_card_address['current_address'] ?? null,
            'type' => AddressTypeEnum::Current->value,
        ];
        $addresses[] = AddressDto::from($current_address);

        return $addresses;
    }


    /* @return NotificationSettingDto[] */
    public function getNotificationSettingsDtos(): array
    {
        $defaults = [];
        foreach (NotificationSetting::notificationDefaultValue() as $type => $channels) {
            foreach ($channels as $channel) {
                $defaults[] = new NotificationSettingDto($type, $channel, NotificationSetting::NOTIFICATION_SETTING_DEFAULT_VALUE);
            }
        }
        return $defaults;
    }
}
