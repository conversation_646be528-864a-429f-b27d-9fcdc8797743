<?php

namespace Modules\Sales\Tests\Integration\Domain\Entities\Contact;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Modules\Common\Database\Seeders\Test\Payday30LoanSeeder;
use Modules\Common\Models\LoanContactActual;
use Modules\Common\Models\LoanContactHistory;
use Modules\Sales\Domain\Entities\Contact\LoanContacts as Sut;
use Modules\Sales\Domain\Entities\Loan\LoanForUpdate;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Dto\ContactDto;
use Tests\TestCase;

class LoanContactsRepoTest extends TestCase
{
    use DatabaseTransactions;

    private ClientDto $clientDto;
    private array $existingNameData;

    public function setUp(): void
    {
        parent::setUp();
        $this->dtos = [
            ContactDto::from([
                'name'=>"<PERSON> Ka<PERSON>",
                'phone'=>"0896667701",
                'pin'=>null,
                'email'=>null,
                'contact_type_id'=>1,
                'seq_num'=>1
            ]),
            ContactDto::from([
                'name'=>"Boby Oneal",
                'phone'=>"0896667702",
                'pin'=>null,
                'email'=>null,
                'contact_type_id'=>1,
                'seq_num'=>2
            ])
        ];
    }

    public function testSettingTwoNewContacts()
    {
        $this->seed(Payday30LoanSeeder::class);
        $loan = LoanForUpdate::loadSelf(1);
        /** @var Sut $sut */
        $sut = app(Sut::class);
        $sut->build($loan, $this->dtos);
        $this->assertCount(2, LoanContactActual::where(['loan_id'=>1])->get());
        $this->assertEquals(1, $sut->first()->dbModel()->loan_id);
    }

    public function testAddingToHistory()
    {
        $this->seed(Payday30LoanSeeder::class);
        DB::table('contact')->insert([
            ['contact_id'=>1, 'name'=>'lala1','phone'=>'34536436735'],
            ['contact_id'=>2, 'name'=>'lala2','phone'=>'4536436735'],
            ['contact_id'=>3, 'name'=>'lala3','phone'=>'45364367354'],
        ]);
        DB::table('loan_contact_actual')->insert([
            ['loan_contact_actual_id'=>1, 'client_id'=>1, 'loan_id'=>1, 'contact_id'=>1,'seq_num'=>1,'contact_type_id'=>1],
            ['loan_contact_actual_id'=>2, 'client_id'=>1,'loan_id'=>1, 'contact_id'=>2,'seq_num'=>2,'contact_type_id'=>1],
            ['loan_contact_actual_id'=>3, 'client_id'=>1,'loan_id'=>1, 'contact_id'=>3,'seq_num'=>3,'contact_type_id'=>1]
        ]);
        $loan = LoanForUpdate::loadSelf(1);
        /** @var Sut $sut */
        $sut = app(Sut::class);
        $sut->build($loan, $this->dtos);
        $this->assertCount(2, LoanContactActual::where(['loan_id'=>1])->get());
        $this->assertCount(3, LoanContactHistory::where(['loan_id'=>1])->get());
    }

    public function testNotChangingExisting()
    {
        $this->seed(Payday30LoanSeeder::class);
        DB::table('contact')->insert([
            ['contact_id'=>1, 'name'=>'lala1','phone'=>'34536436735'],
            ['contact_id'=>2, 'name'=>'lala2','phone'=>'4536436735'],
            ['contact_id'=>3, 'name'=>'lala3','phone'=>'45364367354'],
        ]);
        DB::table('loan_contact_actual')->insert([
            ['loan_contact_actual_id'=>1, 'client_id'=>1, 'loan_id'=>1, 'contact_id'=>1,'seq_num'=>1,'contact_type_id'=>1],
            ['loan_contact_actual_id'=>2, 'client_id'=>1, 'loan_id'=>1, 'contact_id'=>2,'seq_num'=>2,'contact_type_id'=>1],
            ['loan_contact_actual_id'=>3, 'client_id'=>1, 'loan_id'=>1, 'contact_id'=>3,'seq_num'=>3,'contact_type_id'=>1]
        ]);
        $loan = LoanForUpdate::loadSelf(1);
        /** @var Sut $sut */
        $sut = app(Sut::class);
        $dtos = $this->dtos;
        $dtos[] = new ContactDto(
            'lala3','45364367354', null,null,3, 1
        );
        $sut->build($loan, $dtos);
        $this->assertCount(3, LoanContactActual::where(['loan_id'=>1])->get());
        $this->assertNotNull(LoanContactActual::where(['name'=>"James Karter"]));
        $this->assertNotNull(LoanContactActual::where(['name'=>"lala3"]));
        $this->assertCount(2, LoanContactHistory::where(['loan_id'=>1])->get());
    }

    public function testLoadingContacts()
    {
        $this->seed(Payday30LoanSeeder::class);
        DB::table('contact')->insert([
            ['contact_id'=>1, 'name'=>'lala','phone'=>'34536436735'],
            ['contact_id'=>2, 'name'=>'lalal','phone'=>'4536436735'],
            ['contact_id'=>3, 'name'=>'lalala','phone'=>'45364367354'],
        ]);
        DB::table('loan_contact_actual')->insert([
            ['loan_contact_actual_id'=>1, 'client_id'=>1, 'loan_id'=>1, 'contact_id'=>1,'seq_num'=>1,'contact_type_id'=>1],
            ['loan_contact_actual_id'=>2, 'client_id'=>1, 'loan_id'=>1, 'contact_id'=>2,'seq_num'=>2,'contact_type_id'=>1],
            ['loan_contact_actual_id'=>3, 'client_id'=>1, 'loan_id'=>1, 'contact_id'=>3,'seq_num'=>3,'contact_type_id'=>1]
        ]);
        $loan = LoanForUpdate::loadSelf(1);
        $sut = app(Sut::class);
        /** @var Sut $sut */
        $sut->build($loan, null);
        foreach ($sut as $i=>$clientContact){
            $this->assertEquals($i+1, $clientContact->dbModel()->seq_num);
        }
        $this->assertCount(3, LoanContactActual::where(['loan_id'=>1])->get());
        $this->assertCount(0, LoanContactHistory::where(['loan_id'=>1])->get());
    }
}