<?php

namespace Modules\Sales\Tests\Integration\Domain\Entities\Client;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Modules\Common\Database\Seeders\Test\ClientSeeder;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Providers\TestDataProvider;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Entities\Client\Picture as Sut;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Tests\TestCase;

class PictureRepoTest extends TestCase
{
    use DatabaseTransactions;

    private ClientDto $clientDto;
    private array $existingNameData;

    public function setUp(): void
    {
        parent::setUp();
        $this->clientDto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
    }

    public function testSettingNewIdCard()
    {
        $this->seed(ClientSeeder::class);
        $dto = $this->clientDto->relationDto->pictureDto;
        $dbClient = DbClient::where(['client_id'=>1])->first();
        $client = $this->createMock(Client::class);
        $client->method('dbModel')->willReturn($dbClient);
        /** @var Sut $sut */
        $sut = app(Sut::class);
        $sut->build($client, $dto);
        $this->assertEquals($dto->image, $sut->dbModel()->base64);
    }


    public function testSkippingExistingWhenNothingChanged()
    {
        $this->seed(ClientSeeder::class);
        $dto = $this->clientDto->relationDto->pictureDto;
        $arr = $dto->toArray();
        unset($arr['image']);
        $arr['base64'] = $dto->image;
        $arr['client_id'] = 1;
        $arr['client_picture_id'] = 1;
        DB::table('client_picture')->insert($arr);
        $dbClient = DbClient::where(['client_id'=>1])->first();
        $client = $this->createMock(Client::class);
        $client->method('dbModel')->willReturn($dbClient);
        $sut = app(Sut::class);
        $sut->build($client, $dto);
        $this->assertEquals(1, $sut->dbModel()->client_picture_id);
        $this->assertEquals("SomeLongBase64String", $sut->dbModel()->base64);
    }

    public function testSkippingExistingWhenMvrExists()
    {
        $this->seed(ClientSeeder::class);
        $dto = $this->clientDto->relationDto->pictureDto;
        $arr = $dto->toArray();
        $arr['base64'] = $dto->image;
        unset($arr['image']);
        $arr['client_picture_id'] = 1;
        DB::table('client_picture')->insert($arr);
        $dbClient = DbClient::where(['client_id'=>1])->first();
        $client = $this->createMock(Client::class);
        $client->method('dbModel')->willReturn($dbClient);
        $sut = app(Sut::class);
        $sut->build($client, $dto);
        $this->assertEquals(1, $sut->dbModel()->client_picture_id);
        $this->assertEquals("SomeLongBase64String", $sut->dbModel()->base64);
    }

    public function testNotSkippingExistingWhenChangedPicture()
    {
        $this->seed(ClientSeeder::class);
        $dto = $this->clientDto->relationDto->pictureDto;
        $arr = $dto->toArray();
        $arr['base64'] = $dto->image;
        unset($arr['image']);
        $arr['client_id'] = 1;
        $arr['client_picture_id'] = 1;
        DB::table('client_picture')->insert($arr);
        $dbClient = DbClient::where(['client_id'=>1])->first();
        $client = $this->createMock(Client::class);
        $client->method('dbModel')->willReturn($dbClient);
        $sut = app(Sut::class);

        $dto->image = 'CHANGED_IMAGE';
        $sut->build($client, $dto);
        $this->assertNotEquals(1, $sut->dbModel()->client_picture_id);
        $this->assertEquals($dto->image, $sut->dbModel()->base64);
    }

}