<?php

namespace Modules\Sales\Tests\Integration\Domain\Entities\Client;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Modules\Common\Database\Seeders\Test\ClientSeeder;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Providers\TestDataProvider;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Entities\Client\IdCard as Sut;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Tests\TestCase;

class IdCardRepoTest extends TestCase
{
    use DatabaseTransactions;

    private ClientDto $clientDto;
    private array $existingNameData;

    public function setUp(): void
    {
        parent::setUp();
        $this->clientDto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
    }

    public function testSettingNewIdCard()
    {
        $this->seed(ClientSeeder::class);
        $dto = $this->clientDto->relationDto->idCardDto;
        $dbClient = DbClient::where(['client_id'=>1])->first();
        $client = $this->createMock(Client::class);
        $client->method('dbModel')->willReturn($dbClient);
        $sut = app(Sut::class);
        $sut->build($client, $dto);
        $this->assertEquals($dto->idcard_number, $sut->dbModel()->idcard_number);
    }


    public function testSkippingExistingWhenNothingChanged()
    {
        $this->seed(ClientSeeder::class);
        $dto = $this->clientDto->relationDto->idCardDto;
        $arr = $dto->toArray();
        unset($arr['image'], $arr['post_code']);
        $arr['client_id'] = 1;
        $arr['client_idcard_id'] = 1;
        $arr['idcard_number'] = '111';
        $dto->idcard_number = '111';
        DB::table('client_idcard')->insert($arr);
        $dbClient = DbClient::where(['client_id'=>1])->first();
        $client = $this->createMock(Client::class);
        $client->method('dbModel')->willReturn($dbClient);
        $sut = app(Sut::class);
        $sut->build($client, $dto);
        $this->assertEquals(1, $sut->dbModel()->client_idcard_id);
        $this->assertEquals($dto->idcard_number, $sut->dbModel()->idcard_number);
    }

    public function testNotSkippingExistingWhenChangedName()
    {
        $this->seed(ClientSeeder::class);
        $dto = $this->clientDto->relationDto->idCardDto;
        $arr = $dto->toArray();
        unset($arr['image'], $arr['post_code']);
        $arr['client_id'] = 1;
        $arr['client_idcard_id'] = 1;
        $arr['idcard_number'] = '111';
        DB::table('client_idcard')->insert($arr);
        $dbClient = DbClient::where(['client_id'=>1])->first();
        $client = $this->createMock(Client::class);
        $client->method('dbModel')->willReturn($dbClient);
        $sut = app(Sut::class);
        $sut->build($client, $dto);
        $this->assertNotEquals(1, $sut->dbModel()->client_employer_id);
        $this->assertEquals($dto->idcard_number, $sut->dbModel()->idcard_number);
    }
}