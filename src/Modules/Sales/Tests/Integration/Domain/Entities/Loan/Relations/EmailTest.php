<?php

namespace Modules\Sales\Tests\Integration\Domain\Entities\Loan\Relations;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Database\Seeders\Test\Payday30LoanSeeder;
use Modules\Common\Models\LoanEmail as DbModel;
use Modules\Head\Repositories\Loan\LoanEmailRepository as Repo;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Entities\Loan\LoanForUpdate;
use Modules\Sales\Domain\Entities\Loan\Relations\Email as Sut;
use Tests\TestCase;

class EmailTest extends TestCase
{
    use DatabaseTransactions;

    public function setUp(): void
    {
        parent::setUp();
        $this->seed(Payday30LoanSeeder::class);
    }

    public function testBuildingOne()
    {
        $client = Client::selfLoad(1, true);
        $dbClientEmail = $client->relations()->email()->dbModel();
        $sut = new Sut(new DbModel(), new Repo());
        $sut->build(LoanForUpdate::loadSelf(1), $dbClientEmail);
        $this->assertEquals($dbClientEmail->client_email_id, $sut->dbModel()->client_email_id);
    }
}