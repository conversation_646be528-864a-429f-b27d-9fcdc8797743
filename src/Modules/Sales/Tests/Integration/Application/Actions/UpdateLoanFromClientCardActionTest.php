<?php

namespace Integration\Application\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\WithoutEvents;
use Illuminate\Support\Facades\DB;
use Modules\Common\Database\Seeders\Test\ProcessedLoanSeeder;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Document;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Models\Email;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Product;
use Modules\Common\Models\OfficeProduct;
use Modules\Common\Models\Sms;
use Modules\Docs\Database\Seeders\DocumentTemplateSeeder;
use Modules\Docs\Database\Seeders\ProductDocumentTemplateSeeder;
use Modules\Docs\Jobs\DocsPackGeneratingJob;
use Modules\Docs\Services\DocumentService;
use Modules\Sales\Application\Actions\UpdateLoanFromClientCardAction as Sut;
use Modules\Sales\Domain\Events\NewLoanDocsGenerated;
use Modules\Sales\Domain\Events\NewLoanHasArrived;
use Modules\Sales\Http\Dto\LoanDto;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class UpdateLoanFromClientCardActionTest extends TestCase
{
    use DatabaseTransactions;
    //use WithoutEvents;

    public function testChangeSumForOnlineLoan()
    {
        //Event::fake(); //disabled because I want to check real process here
        //Queue::fake();
        $this->actingAs(Administrator::find(2));
        $this->seed([
            ProcessedLoanSeeder::class
        ]);
        /** @var Loan $dbLoan */
        $dbLoan = Loan::find(ProcessedLoanSeeder::LOAN_ID);
        $sut = app()->make(Sut::class);
        $dto = LoanDto::from([
            'loan_id' => ProcessedLoanSeeder::LOAN_ID,
            'office_id' => $dbLoan->office_id,
            'payment_method_id' => $dbLoan->payment_method_id,
            'product_id' => $dbLoan->product_id,
            'loan_sum' => 20000,//$dbLoan->amount_requested,
            'loan_period_days' => $dbLoan->period_requested,
            'discount_percent' => $dbLoan->discount_percent,
            'refinanced_loan_ids' => [],
            'administrator_id' => $dbLoan->administrator_id
        ]);
        //checking initial values
        $this->assertEquals(1000, $dbLoan->amount_requested);
        $this->assertCount(0, Document::all());
        $this->assertCount(0, Sms::all());
        $this->assertCount(0, Email::all());
        /** @var Loan $newDbLoan */
        $newDbLoan = $sut->execute($dto)[0];
        //check that change did happen
        $this->assertEquals(20000, $newDbLoan->amount_requested);
        //check that loan status was reverted to new
        $this->assertEquals(LoanStatus::NEW_STATUS_ID, $newDbLoan->loan_status_id);
        //check that event was fired and job pushed to queue
        //Event::assertDispatched(NewLoanHasArrived::class);
        //Queue::assertPushedOn('docs', DocsPackGeneratingJob::class);

        //check that documents were created
        $this->assertCount(7, Document::all());
        //Event::assertDispatched(NewLoanDocsGenerated::class);
        //TODO: find out why sms count is 0
        //$this->assertCount(1, Sms::all());
        $this->assertCount(1, Email::all());
    }

    public function testChangeSumForOfficeLoan()
    {
        $this->seed(ProcessedLoanSeeder::class);
        $dbLoan = Loan::find(ProcessedLoanSeeder::LOAN_ID);
        $officeProduct = OfficeProduct::where(['office_id' => 2, 'product_id' => 1])->first();
        if(! $officeProduct) {
                DB::table('office_product')->insert([
                    ['office_id' => 2, 'product_id' => 1],
                ]);
        }
        $dbLoan->office_id = 2;
        $dbLoan->save();
        $sut = app()->make(Sut::class);
        $dto = LoanDto::from([
            'loan_id' => ProcessedLoanSeeder::LOAN_ID,
            'office_id' => $dbLoan->office_id,
            'payment_method_id' => $dbLoan->payment_method_id,
            'product_id' => $dbLoan->product_id,
            'loan_sum' => 20000,//$dbLoan->amount_requested,
            'loan_period_days' => $dbLoan->period_requested,
            'discount_percent' => $dbLoan->discount_percent,
            'refinanced_loan_ids' => [],
            'administrator_id' => $dbLoan->administrator_id
        ]);

        /** @var Loan $newDbLoan */
        $newDbLoan = $sut->execute($dto)[0];
        $this->assertEquals(LoanStatus::SIGNED_STATUS_ID, $newDbLoan->loan_status_id);
    }
}
