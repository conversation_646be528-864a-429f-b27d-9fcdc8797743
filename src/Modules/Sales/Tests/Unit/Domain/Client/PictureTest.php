<?php

namespace Modules\Sales\Tests\Unit\Domain\Client;

use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Models\Client as DbClient;
use Modules\Head\Repositories\ClientPictureRepository;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Entities\Client\Picture;
use Modules\Sales\Domain\Exceptions\ClientRelations\NewClientPictureBuiltWithoutDto;
use Modules\Sales\Domain\Exceptions\ClientRelations\PictureNotSaved;
use Modules\Sales\Http\Dto\PictureDto;
use Modules\Common\Models\ClientPicture;
use Tests\TestCase;

class PictureTest extends TestCase
{
    private PictureDto $dto;

    const CURRENT_CLIENT_ID = 12;
    const OTHER_CLIENT_ID = 13;
    const TEST_PICTURE = 'draaawing';


    public function setUp(): void
    {
        parent::setUp();
        $this->dto = PictureDto::getFrom(['image'=>self::TEST_PICTURE]);
        $this->client = $this->createMock(Client::class);
        $dbClient = $this->createMock(DbClient::class);
        $dbClient->method('getKey')->willReturn(self::CURRENT_CLIENT_ID);
        $this->client->method('dbModel')->willReturn($dbClient);
    }

    public function testSettingNewPicture()
    {
        $repo = $this->createMock(ClientPictureRepository::class);
        $repo->method('getLastByClient')->willReturn(null);
        $repo->method('save')->willReturn($this->createMock(ClientPicture::class));
        $sut = new Picture(new ClientPicture(), $repo);
        $sut->build($this->client, $this->dto);
        $this->assertEquals(self::TEST_PICTURE, $sut->dbModel()->base64);
    }

    public function testPictureNotSaved()
    {
        $repo = $this->createMock(ClientPictureRepository::class);
        $repo->method('getLastByClient')->willReturn(null);
        $repo->method('save')->willReturn(null);
        $sut = new Picture(new ClientPicture(), $repo);
        $this->expectException(PictureNotSaved::class);
        $sut->build($this->client, $this->dto);
    }
}