<?php

namespace Modules\Sales\Tests\Unit\Domain\Client;

use Modules\Common\Models\Client as DbClient;
use Modules\Common\Models\ClientAddress as DbClientAddress;
use Modules\Head\Repositories\ClientAddressRepository;
use Modules\Sales\Domain\Entities\Client\Address;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Exceptions\ClientRelations\NewClientAddressBuiltWithoutDto;
use Modules\Sales\Http\Dto\AddressDto;
use Tests\TestCase;

class AddressTest extends TestCase
{
    const CURRENT_CLIENT_ID = 12;
    private AddressDto $addressDto;

    public function setUp(): void
    {
        parent::setUp();
        $this->addressDto = new AddressDto(
            5156,
            "Ул.Съединение, бл.83, вх.2, ет.7, ап.44",
            "1234BG",
            DbClientAddress::TYPE_ID_CARD
        );
    }

    /**
     * @todo rewrite
     */
    public function testSettingCompletelyNewAddress()
    {
        $addrRepo = $this->createMock(ClientAddressRepository::class);
        $addrRepo->method('save')->willReturn(new DbClientAddress());
        $addrRepo->method('getLatestByClientIdAndType')->willReturn(null);
        $dbClient = $this->createMock(DbClient::class);
        $dbClient->method('getKey')->willReturn(self::CURRENT_CLIENT_ID);
        $client = $this->createMock(Client::class);
        $client->method('dbModel')->willReturn($dbClient);
        $sut = new Address($addrRepo, new DbClientAddress());
        $sut->build($client, $this->addressDto);
        $this->assertEquals($this->addressDto->address, $sut->dbModel()->address);
    }
}