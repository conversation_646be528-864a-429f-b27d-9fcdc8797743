<?php

namespace Modules\Sales\Tests\Unit\Domain\Loan;

use Illuminate\Foundation\Testing\WithoutEvents;
use Illuminate\Support\Carbon;
use Modules\Admin\Repositories\SettingRepository;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\LoanType;
use Modules\Common\Models\Office as DbOffice;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\Product as DbProduct;
use Modules\Common\Models\Setting;
use Modules\Discounts\Repositories\ClientDiscountActualRepository;
use Modules\Head\Repositories\Loan\LoanParamsHistoryRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Sales\Domain\Entities\Admin;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Entities\Contact\LoanContacts;
use Modules\Sales\Domain\Entities\Guarantor\LoanGuarantors;
use Modules\Sales\Domain\Entities\Loan\ActiveLoans;
use Modules\Sales\Domain\Entities\Loan\Installments;
use Modules\Sales\Domain\Entities\Loan\NewLoan;
use Modules\Sales\Domain\Entities\Loan\Relations\LoanMeta;
use Modules\Sales\Domain\Entities\Loan\Relations\ProductSetting;
use Modules\Sales\Domain\Entities\Loan\Relations\Relations;
use Modules\Sales\Domain\Entities\Loan\SalesRefinancedLoans;
use Modules\Sales\Domain\Entities\Office\ClientOffice;
use Modules\Sales\Domain\Entities\Office\Office;
use Modules\Sales\Domain\Entities\Product\Product;
use Modules\Sales\Domain\Entities\Task;
use Modules\Sales\Domain\Exceptions\ClientHasUnprocessedLoans;
use Modules\Sales\Domain\Exceptions\ExcessiveLoanAmount;
use Modules\Sales\Domain\Exceptions\IncorrectPaymentMethod;
use Modules\Sales\Domain\Exceptions\LoanNotSaved;
use Modules\Sales\Domain\Exceptions\NewLoanNotCoveringRefinance;
use Modules\Sales\Http\Dto\LoanDto;
use PHPUnit\Framework\MockObject\Stub\ReturnValueMap;
use Tests\TestCase;

class NewLoanTest extends TestCase
{
    use WithoutEvents;

    const CURRENT_CLIENT_ID = 12;
    const WRONG_OFFICE_ID = 999;
    const WRONG_PAYMENT_METHOD_ID = 999;

    private LoanRepository $loanRepo;
    private SalesRefinancedLoans $refLoans;
    private LoanDto $loanDto;

    public function setUp(): void
    {
        parent::setUp();
        $this->refLoans = $this->createMock(SalesRefinancedLoans::class);
        $this->activeLoans = $this->createMock(ActiveLoans::class);
        $this->setting = $this->createMock(Setting::class);
        $this->settingRepository = $this->createMock(SettingRepository::class);
        $this->loanRepo = $this->createMock(LoanRepository::class);
        $dbOffice = $this->createMock(DbOffice::class);
        $this->office = $this->createMock(Office::class);
        $this->office->method('dbModel')->willReturn($dbOffice);
        $this->office->method('loadById')->willReturn($this->office);
        $product = new DbProduct();
        $product->product_id = 1;
        $product->product_type_id = 1;
        $this->product = $this->createMock(Product::class);
        $this->product->method('dbModel')->willReturn($product);
        $this->product->method('loadById')->willReturn($this->product);
        $this->discountRepo = $this->createMock(ClientDiscountActualRepository::class);
        $this->contacts = $this->createMock(LoanContacts::class);
        $this->installments = $this->createMock(Installments::class);
        $this->guarantors = $this->createMock(LoanGuarantors::class);
        $this->relations = $this->createMock(Relations::class);
        $this->loanRepo->method('save')->willReturn(new DbLoan());
        $this->currentDate = $this->createMock(CurrentDate::class);
        $this->currentDate->method('now')->willReturn(Carbon::parse('2022-10-20 12:00:00'));
        $this->loanRepo->method('getProductById')->willReturn($product);
        $dbClient = $this->createMock(DbClient::class);
        $dbClient->method('getKey')->willReturn(self::CURRENT_CLIENT_ID);
        $dbClient->method('getFullName')->willReturn('xxx');
        $dbClient->method('getAttribute')->willReturn('123');
        $dbClient->method('__get')->willReturn(
            new ReturnValueMap([
                ['birth_date', 1987-12-02],
            ])
        );
        $this->client = $this->createMock(Client::class);
        $this->client->method('dbModel')->willReturn($dbClient);
        $this->clientOffice = $this->createMock(ClientOffice::class);
        $this->productSetting = $this->createMock(ProductSetting::class);
        $this->loanMeta = $this->createMock(LoanMeta::class);
        $this->admin = $this->createMock(Admin::class);
        $this->admin->method('getMaxDiscount')->willReturn(100);
        $this->task = $this->createMock(Task::class);
        $this->loanHistRepo = $this->createMock(LoanParamsHistoryRepository::class);
        $this->loanDto = LoanDto::getFrom([
            'loan_id' => 1,
            'office_id' => 1,
            'administrator_id' => 1,
            'payment_method_id' => 1,
            'product_id' => 1,
            'loan_sum' => 10000,
            'loan_period_days' => 30,
            'discount_percent' => 0,
            'refinanced_loans' => null,
            'contact' => null,
            'guarantor' => null
        ]);
    }

    public function testSettingNewLoan()
    {
        $sut = $this->createSut($this->settingRepository);
        $sut->build($this->loanDto, $this->client);
        $dbLoan = $sut->dbModel();
        $this->assertEquals($this->loanDto->loan_sum, $dbLoan->amount_approved);
    }

    public function testDuplicatedLoan()
    {
        $this->loanRepo->expects($this->any())
            ->method('clientHasUnprocessedLoans')
            ->willReturn(true);
        $sut = $this->createSut($this->settingRepository);
        $this->expectException(ClientHasUnprocessedLoans::class);
        $sut->build($this->loanDto, $this->client);
    }

    public function testWrongPaymentMethod()
    {
        $dto = clone($this->loanDto);
        $dto->payment_method_id = self::WRONG_PAYMENT_METHOD_ID;
        $sut = $this->createSut($this->settingRepository);
        $this->expectException(IncorrectPaymentMethod::class);
        $sut->build($dto, $this->client);
    }

    public function getSettingRepository(): SettingRepository
    {
        $setting = $this->createMock(SettingRepository::class);
        $setting->expects($this->any())->method('getSetting')->willReturn(
            (new Setting())->fill(['default_value' => '10000'])
        );

        return $setting;
    }

    public function testExcessiveLoanAmount()
    {
        $dto = clone($this->loanDto);
        $dto->payment_method_id = PaymentMethod::PAYMENT_METHOD_CASH;
        $dto->office_id = DbOffice::OFFICE_ID_BLAGOEVGRAD;
        $dto->loan_sum = 9999900;
        $this->activeLoans->expects($this->any())
            ->method('getProjectedDebtSum')
            ->willReturn(2.0);
        $this->activeLoans->expects($this->any())
            ->method('build')
            ->willReturn($this->activeLoans);
        $this->client->method('activeLoans')->willReturn($this->activeLoans);
        $sut = $this->createSut($this->getSettingRepository());
        $this->expectException(ExcessiveLoanAmount::class);
        $sut->build($dto, $this->client);
    }

    public function testNewLoanNotCoveringRefinance()
    {
        $dto = clone($this->loanDto);
        $dto->loan_sum = 1000;
        $this->refLoans->expects($this->any())
            ->method('isEmpty')
            ->willReturn(false);
        $this->refLoans->repaymentSum = 1001.00;
        $sut = $this->createSut($this->getSettingRepository());
        $this->expectException(NewLoanNotCoveringRefinance::class);
        $sut->build($dto, $this->client);
    }

    public function testLoanTypeShouldBeRefinance()
    {
        $dto = clone($this->loanDto);
        $dto->loan_sum = 90000;
        $this->refLoans->repaymentSum = 60000;
        $sut = $this->createSut($this->getSettingRepository());
        $sut->build($dto, $this->client);
        $this->assertEquals(30000, $sut->dbModel()->amount_rest);
        $this->assertEquals(LoanType::LOAN_TYPE_ID_REFINANCING, $sut->dbModel()->loan_type_id);
    }

    public function testLoanTypeShouldBeNormal()
    {
        $dto = clone($this->loanDto);
        $dto->loan_sum = 10000;
        $this->refLoans->repaymentSum = 0.00;
        $sut = $this->createSut($this->getSettingRepository());
        $sut->build($dto, $this->client);
        $this->assertEquals(10000, $sut->dbModel()->amount_rest);
        $this->assertEquals(LoanType::LOAN_TYPE_ID_NORMAL, $sut->dbModel()->loan_type_id);
    }

    public function testLoanNotSaved()
    {
        $refLoans = $this->createMock(SalesRefinancedLoans::class);
        $loanRepo = $this->createMock(LoanRepository::class);
        $product = new DbProduct();
        $product->product_id = 1;
        $product->product_type_id = 1;
        $loanRepo->method('getProductById')->willReturn($product);
        $sut = $this->createSut($this->getSettingRepository(), $refLoans, $loanRepo);
        $this->expectException(LoanNotSaved::class);
        $sut->build($this->loanDto, $this->client);
    }

    public function testSigningOfficeLoan()
    {
        $dto = clone($this->loanDto);
        $dto->office_id = DbOffice::OFFICE_ID_BLAGOEVGRAD;
        $this->loanRepo->method('changeStatus')->will(
            $this->returnCallback(
                function (DbLoan $dbLoan) {
                    $dbLoan->loan_status_id = LoanStatus::SIGNED_STATUS_ID;

                    return $dbLoan;
                }
            )
        );
        $sut = $this->createSut($this->getSettingRepository());
        $sut->build($dto, $this->client);
        $this->assertEquals(LoanStatus::SIGNED_STATUS_ID, $sut->dbModel()->loan_status_id);
    }

    private function createSut(
        SettingRepository     $settingRepository,
        ?SalesRefinancedLoans $refLoans = null,
        ?LoanRepository       $loanRepo = null
    ): NewLoan
    {
        if (!$refLoans) {
            $refLoans = $this->refLoans;
        }
        if (!$loanRepo) {
            $loanRepo = $this->loanRepo;
        }

        return new NewLoan(
            $refLoans,
            $this->setting,
            $this->installments,
            $this->relations,
            $loanRepo,
            $this->office,
            $this->admin,
            $this->discountRepo,
            $this->currentDate,
            $this->product,
            $this->productSetting,
            $this->clientOffice,
            $this->loanMeta,
            $settingRepository,
            $this->task,
            $this->loanHistRepo
        );
    }
}
