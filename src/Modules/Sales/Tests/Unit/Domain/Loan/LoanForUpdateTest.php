<?php

namespace Modules\Sales\Tests\Unit\Domain\Loan;

use Modules\Admin\Repositories\SettingRepository;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\LoanType as DbLoanType;
use Modules\Common\Models\Office as DbOffice;
use Modules\Common\Models\PaymentMethod as DbPaymentMethod;
use Modules\Common\Models\Product as DbProduct;
use Modules\Common\Models\Setting;
use Modules\Discounts\Repositories\ClientDiscountActualRepository;
use Modules\Head\Repositories\Loan\LoanParamsHistoryRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Sales\Domain\Entities\Admin;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Entities\Loan\ActiveLoans;
use Modules\Sales\Domain\Entities\Loan\Installments;
use Modules\Sales\Domain\Entities\Loan\LoanForUpdate;
use Modules\Sales\Domain\Entities\Loan\Relations\LoanMeta;
use Modules\Sales\Domain\Entities\Loan\Relations\ProductSetting;
use Modules\Sales\Domain\Entities\Loan\Relations\Relations;
use Modules\Sales\Domain\Entities\Loan\SalesRefinancedLoans;
use Modules\Sales\Domain\Entities\Office\ClientOffice;
use Modules\Sales\Domain\Entities\Office\Office;
use Modules\Sales\Domain\Entities\Product\Product;
use Modules\Sales\Domain\Entities\Task;
use Modules\Sales\Domain\Exceptions\ExcessiveLoanAmount;
use Modules\Sales\Domain\Exceptions\LoanHasIncorrectStatus;
use Modules\Sales\Domain\Exceptions\LoanIsProcessedByDifferentAdmin;
use Modules\Sales\Domain\Exceptions\LoanNotFound;
use Modules\Sales\Domain\Exceptions\LoanNotSaved;
use Modules\Sales\Http\Dto\LoanDto;
use PHPUnit\Framework\MockObject\Stub\ReturnValueMap;
use Tests\TestCase;

class LoanForUpdateTest extends TestCase
{
    const CURRENT_CLIENT_ID = 12;
    const WRONG_OFFICE_ID = 999;
    const WRONG_PAYMENT_METHOD_ID = 999;

    private SalesRefinancedLoans $refLoans;
    private LoanDto $loanDto;

    public function setUp(): void
    {
        parent::setUp();
        $dbProduct = $this->createMock(DbProduct::class);
        $dbProduct->method('getKey')->willReturn(1);
        $this->product = $this->createMock(Product::class);
        $this->product->method('dbModel')->willReturn($dbProduct);
        $this->product->method('loadById')->willReturn($this->product);
        $this->client = $this->createMock(Client::class);
        $dbOffice = $this->createMock(DbOffice::class);
        $dbOffice->method('getKey')->willReturn(1);
        $this->office = $this->createMock(Office::class);
        $this->office->method('dbModel')->willReturn($dbOffice);
        $this->office->method('loadById')->willReturn($this->office);
        $this->office->method('allowedToUpdate')->willReturn(true);
        $this->admin = $this->createMock(Admin::class);
        $this->admin->method('getMaxDiscount')->willReturn(100);
        $this->refLoans = $this->createMock(SalesRefinancedLoans::class);
        $this->activeLoans = $this->createMock(ActiveLoans::class);
        $this->discountRepo = $this->createMock(ClientDiscountActualRepository::class);
        $this->currentDate = $this->createMock(CurrentDate::class);
        $this->relations = $this->createMock(Relations::class);
        $this->productSetting = $this->createMock(ProductSetting::class);
        $this->clientOffice = $this->createMock(ClientOffice::class);
        $this->loanMeta = $this->createMock(LoanMeta::class);
        $this->task = $this->createMock(Task::class);
        $this->loanParamsRepo = $this->createMock(LoanParamsHistoryRepository::class);

        $this->loanDto = LoanDto::getFrom([
            'loan_id' => 10015,
            'product_id' => 1,
            'office_id' => 1,
            'loan_period' => 10,
            'loan_sum' => 85000,
            'discount_percent' => 6,
            'administrator_id' => 1,
            'payment_method_id' => 1,
        ]);
        $this->installments = $this->createMock(Installments::class);
        $this->setting = $this->createMock(Setting::class);

        $this->settingRepository = $this->createMock(SettingRepository::class);
        $this->settingRepository->expects($this->any())->method('getSetting')->willReturn(
            (new Setting())->fill(['default_value' => '10000'])
        );
    }

    public function testUpdatingNonexistentLoanCausesException()
    {
        $sut = $this->createSutFromLoanRepo(
            $this->getLoanRepoMock(),
        );
        $this->expectException(LoanNotFound::class);
        $sut->loadById($this->loanDto->loan_id);
        $sut->update($this->loanDto);
    }

    public function testStatusNotProcessingCausesException()
    {
        $repo = $this->getLoanRepoMock($this->loanDto->loan_id, 'new');
        $sut = $this->createSutFromLoanRepo($repo);
        $dbLoan = $repo->getById($this->loanDto->loan_id);
        $client = $this->createMock(Client::class);
        $dbClient = new DbClient();
        $dbClient->birth_date = '1987-12-02';
        $client->method("dbModel")->willReturn($dbClient);
        $this->expectException(LoanHasIncorrectStatus::class);
        $sut->buildFromExisting($client, $dbLoan);
        $sut->update($this->loanDto);
    }

    public function testWrongAdminId()
    {
        $dto = clone($this->loanDto);
        $dto->office_id = DbOffice::OFFICE_ID_NOVI_PAZAR_1;
        $dto->loan_sum = 9999;
        $dto->administrator_id = 2;
        $repo = $this->getLoanRepoMock($dto->loan_id, 'processing', DbPaymentMethod::PAYMENT_METHOD_CASH);
        $sut = $this->createSutFromLoanRepo($repo);
        $dbLoan = $repo->getById($dto->loan_id);
        $client = $this->createMock(Client::class);
        $dbClient = new DbClient();
        $dbClient->birth_date = '1987-12-02';
        $client->method("dbModel")->willReturn($dbClient);
        $sut->buildFromExisting($client, $dbLoan);
        $this->expectException(LoanIsProcessedByDifferentAdmin::class);
        $sut->update($dto);
    }

    public function testExcessiveLoanAmount()
    {
        $this->loanDto->office_id = DbOffice::OFFICE_ID_NOVI_PAZAR_1;
        $this->loanDto->loan_sum = 99990000;
        $this->activeLoans->expects($this->any())
            ->method('getProjectedDebtSum')
            ->willReturn(2.0);
        $this->activeLoans->method('build')->willReturn($this->activeLoans);
        $repo = $this->getLoanRepoMock($this->loanDto->loan_id, 'processing', DbPaymentMethod::PAYMENT_METHOD_CASH);
        $sut = $this->createSutFromLoanRepo($repo);
        $dbLoan = $repo->getById($this->loanDto->loan_id);
        $client = $this->createMock(Client::class);
        $client->method('activeLoans')->willReturn($this->activeLoans);
        $dbClient = new DbClient();
        $dbClient->birth_date = '1987-12-02';
        $client->method("dbModel")->willReturn($dbClient);
        $sut->buildFromExisting($client, $dbLoan);
        $this->expectException(ExcessiveLoanAmount::class);
        $sut->update($this->loanDto);
    }

    public function testLoanNotSaved()
    {
        $repo = $this->getLoanRepoMock(
            $this->loanDto->loan_id,
            'processing',
            DbPaymentMethod::PAYMENT_METHOD_CASH,
            DbLoanType::LOAN_TYPE_ID_NORMAL,
            false
        );
        $sut = $this->createSutFromLoanRepo($repo);
        $dbLoan = $repo->getById($this->loanDto->loan_id);
        $client = $this->createMock(Client::class);
        $dbClient = new DbClient();
        $dbClient->birth_date = '1987-12-02';
        $client->method("dbModel")->willReturn($dbClient);
        $sut->buildFromExisting($client, $dbLoan);
        $this->expectException(LoanNotSaved::class);
        $sut->update($this->loanDto);
    }

    private function createSutFromLoanRepo(LoanRepository $loanRepo)
    {
        return new LoanForUpdate(
            $this->refLoans,
            $this->setting,
            $this->installments,
            $this->relations,
            $loanRepo,
            $this->office,
            $this->admin,
            $this->discountRepo,
            $this->currentDate,
            $this->product,
            $this->productSetting,
            $this->clientOffice,
            $this->loanMeta,
            $this->settingRepository,
            $this->task,
            $this->loanParamsRepo
        );
    }

    private function getLoanRepoMock(
        ?int   $id = null,
        string $status = 'processing',
        int    $paymentMethodId = 2,
        int    $loanTypeId = 1,
        bool   $saved = true
    ): LoanRepository
    {
        $loanRepo = $this->createMock(LoanRepository::class);
        if (!$id) {
            $loanRepo
                ->method('getById')
                ->willReturn(null);

            return $loanRepo;
        }
        $dbLoan = $this->createMock(DbLoan::class);
        $dbLoan->method('getAttribute')
            ->willReturn(
                new ReturnValueMap([
                    ['administrator_id', 1],
                    ['client_id', 1],
                    ['payment_method_id', $paymentMethodId],
                    ['loan_type_id', $loanTypeId],
                    ['office_id', 1],
                    ['product_id', 1],
                ])
            );
        $dbLoan->method('__get')->willReturn(
            new ReturnValueMap([
                ['administrator_id', 1],
                ['client_id', 1],
                ['payment_method_id', $paymentMethodId],
                ['loan_type_id', $loanTypeId],
                ['office_id', 1],
                ['product_id', 1],
                ['amount_approved', $this->loanDto->loan_sum],
                ['amount_rest', $this->loanDto->loan_sum],
                ['period_approved', $this->loanDto->loan_period_days],
                ['loan_status_id', LoanStatus::STATUS_IDS[$status]],
            ])
        );
        $dbLoan->method('hasStatus')->will(
            $this->returnValueMap([
                [3, $status == 'processing'],
                [1, $status == 'new']
            ])
        );
        $dbLoan->method('getStatus')->willReturn($status);
        $dbLoan->method('getKey')->willReturn($id);
        $loanRepo->method('getById')->willReturn($dbLoan);
        $dbLoan->method('setAttribute')->willReturn($dbLoan);
        if (!$saved) {
            $dbLoan->method('isDirty')->willReturn(true);
        }
        $loanRepo->method('save')->willReturn($saved ? $dbLoan : null);

        return $loanRepo;
    }
}
