<?php

namespace Modules\Sales\Application\Actions;

use Modules\Common\Models\Loan as DbLoan;
use Modules\Communication\Services\CommunicationCommentService;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Http\Dto\ClientDto;

final readonly class NewAppAction
{
    public function __construct(
        private Client $client,
        private CommunicationCommentService $commentService,
    ) {
    }

    public function execute(ClientDto $clientDto): Client
    {
//        $clientDto->relationDto->loanDto->setDiscount($clientDto->pin, ); TODO: implement automatic discount application?

        $client = $this->client->processNewApplication($clientDto);

        if ($clientDto->relationDto->loanDto->comment) {
            $this->createComComment(
                $clientDto->relationDto->loanDto->comment,
                $client->dbModel()->getKey(),
                $client->dbLoan()->getKey()
            );
        }

        return $client;
    }

    private function createComComment(string $comment, int $clientId, int $loanId): void
    {
        $this->commentService->create([
            'client_id' => $clientId,
            'loan_id' => $loanId,
            'text' => $comment,
            'administrator_id' => getAdminId(),
        ]);
    }

    public function getDbLoan(): DbLoan
    {
        return $this->client->dbLoan();
    }

    public function getClient(): Client
    {
        return $this->client;
    }
}
