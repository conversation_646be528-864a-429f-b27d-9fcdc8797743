<?php

namespace Modules\Sales\Application\Actions\RefinanceSlider;

use Illuminate\Support\Collection;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Common\Models\ProductSetting;
use Modules\Head\Repositories\LoanRepository;
use Modules\Product\Repository\ProductRepository;
use Modules\Sales\Domain\Entities\Loan\NewLoan;
use Modules\Sales\Exceptions\AmountForRefinanceIsLargerThanProductMaxAmount;
use Modules\Sales\Http\Dto\RefinanceSliderDto;

class RefinanceSliderDataAction
{
    public function __construct(
        private readonly ProductRepository $productRepository,
        private readonly LoanRepository    $loanRepository
    ) {}

    public function execute(RefinanceSliderDto $refinanceSliderDto): array
    {
        $product = $this->productRepository->getProductById($refinanceSliderDto->productId);
        if (!$product) {
            $message = __('Product not found');
            throw new \Exception($message, 200);
        }

        $isWeb = ($refinanceSliderDto->loanOfficeId == Office::OFFICE_ID_WEB);

        if (
            !empty($refinanceSliderDto->loanOfficeId)
            && $isWeb
            && !empty($refinanceSliderDto->pin)
        ) {
            $client = Client::where('pin', $refinanceSliderDto->pin)->first();
            $loans = $this->loanRepository->getActiveLoans($client);
        } else {
            $loans = $this->loanRepository->getByLoanIds($refinanceSliderDto->loanIds);
        }


        $refinanceAmount = $this->getRefinanceAmount($loans, $isWeb);
        $productMaxAmount = $product->getSettingValueByKey(ProductSetting::MAX_AMOUNT_KEY);
        $productMaxAmount = floatToInt($productMaxAmount);

        if ($productMaxAmount <= $refinanceAmount) {
            throw new AmountForRefinanceIsLargerThanProductMaxAmount(amount($refinanceAmount), amount($productMaxAmount));
        }

        $data['status'] = true;
        $data['productId'] = $product->getKey();
        $data['originRefinanceAmount'] = intToFloat($refinanceAmount);

        // if no slider amount OR amount < refinance amount => refinance amount + 100.00 for client
        if (empty($refinanceSliderDto->loanSum) || $refinanceSliderDto->loanSum < $refinanceAmount) {
            $data['refinanceAmount'] = intToFloat($refinanceAmount + NewLoan::MIN_REF_AMOUNT);
        } else {
            $data['refinanceAmount'] = intToFloat($refinanceSliderDto->loanSum);
        }

        return $data;
    }

    protected function getRefinanceAmount(Collection $loans, bool $isWeb = false): int
    {
        $refinanceAmount = 0;

        $loans->each(function (Loan $loan) use (&$refinanceAmount, $isWeb) {
            $refinanceAmount += $loan->getAmountForRefinance($isWeb);
        });

        return $refinanceAmount;
    }
}
