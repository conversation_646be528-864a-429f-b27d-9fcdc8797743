<?php

namespace Modules\Sales\Application\Actions;

use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Entities\Loan\LoanForUpdate;
use Modules\Sales\Domain\Entities\Loan\Repositories\AddressRepository;
use Modules\Sales\Http\Dto\ClientDto;

final readonly class SaveEditedClientDataAction
{
    public function __construct(private Client $client)
    {
    }

    public function execute(ClientDto $dto, ?int $loanId = null): Client
    {
        $client = $this->client->processWithoutLoan($dto);

        if ($loanId) {
            $this->updateLoanAddresses($client, $loanId);
        }

        return $client;
    }

    public function updateLoanAddresses(Client $client, int $loanId): void
    {
        app(AddressRepository::class)->createAllByLoanAndClient(LoanForUpdate::loadSelf($loanId), $client);
    }
}
