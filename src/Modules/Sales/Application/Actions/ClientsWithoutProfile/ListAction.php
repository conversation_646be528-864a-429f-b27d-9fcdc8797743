<?php

declare(strict_types=1);

namespace Modules\Sales\Application\Actions\ClientsWithoutProfile;

use Kris\LaravelFormBuilder\Facades\FormBuilder;
use Modules\Sales\Forms\ClientsWithoutProfile\FiltersForm;
use Modules\Sales\Forms\ClientsWithoutProfile\SmsMessageForm;
use Modules\Sales\Repositories\ClientWithoutProfileRepository;

final readonly class ListAction
{
    public function __construct(private ClientWithoutProfileRepository $repository)
    {
    }

    public function execute(array $filters, int $perPage): array
    {
        $paginator = $this->repository->getPaginator($filters, $perPage);
        return [
            'filterForm' => FiltersForm::create(),
            'smsMessageForm' => FormBuilder::create(SmsMessageForm::class, data: compact('filters')),
            'clients' => $paginator,
            'countOfFilteredClients' => $paginator->total(),
        ];
    }
}
