<?php

namespace Modules\Sales\Services;

use Illuminate\Support\Facades\URL;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\RawRequest;
use Illuminate\Http\Request;
use Throwable;

class RawRequestService
{
    public ?RawRequest $rawRequest = null;

    public function __construct(private readonly Request $request)
    {
    }

    public function createRequest(array $requestData, string $type = 'manual'): void
    {
        $rawRequest = new RawRequest();
        $rawRequest->type = $type;
        $rawRequest->url = URL::full();
        $rawRequest->ip = $requestData['ip'] ?? $this->request->ip();
        $rawRequest->browser = $requestData['browser'] ?? $this->request->server('HTTP_USER_AGENT');
        $rawRequest->request = $requestData['requestString'];
        $rawRequest->response = null;
        $rawRequest->token = $requestData['token'] ?? null;
        $rawRequest->token2 = $requestData['token2'] ?? null;

        if (!empty($requestData['client_id'])) {
            $rawRequest->client_id = $requestData['client_id'];
        }

        if ($rawRequest->save()) {
            $this->rawRequest = $rawRequest;
        }
    }

    public function saveResponse(string|array|Throwable $response, bool $success = false): void
    {
        if (!$this->rawRequest) {
            return;
        }

        if ($response instanceof Throwable) {
            $success = false;
            $response = [
                'error' => $response->getMessage(),
                'location' => $response->getFile() . '::' . $response->getLine(),
                'trace' => $response->getTrace()
            ];
        }

        if (is_array($response)) {
            $response = json_encode($response, JSON_PRETTY_PRINT);
        }

        $this->rawRequest->response = $response;
        $this->rawRequest->success = $success;
        $this->rawRequest->save();

        $this->rawRequest = null;
    }

    public function updateResponseWithClientAndLoan(
        Client $client,
        ?Loan  $loan
    ): void
    {

        $arr = [
            'client_id' => $client->getKey(),
            'loan_id' => $loan?->getKey() ?? null
        ];

        $this->saveResponse(json_encode($arr, JSON_PRETTY_PRINT), true);
    }
}
