<?php

namespace Modules\Sales\ModelFilters\ClientsWhoHaveNeverHadActiveLoans;

use Modules\Common\ModelFilters\ModelFilterAbstract;

final class LastCreatedAtFilter extends ModelFilterAbstract
{

    public function handle(mixed $filterValue): void
    {
        $dates = getDatesForFilter($filterValue);
        $dateFrom = $dates['from'];
        $dateTo = $dates['to'];

        if (!empty($dateFrom) && !empty($dateTo)) {
            $this->query->whereBetween('last_created_at', [
                $dateFrom,
                $dateTo,
            ]);
        }
    }
}
