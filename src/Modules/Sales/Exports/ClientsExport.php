<?php

namespace Modules\Sales\Exports;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Modules\Common\Models\Client;

class ClientsExport implements FromCollection
{
    /**
     * @var Collection
     */
    private Collection $clients;
    public Builder $builder;

    /**
     * ClientsExport constructor.
     *
     * @param Collection $clients
     */
    public function __construct(Collection $clients)
    {
        $this->clients = $clients;
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return $this->clients;
    }

    public function formatRow(Client $row): array
    {
        switch (true) {
            case $row->isActive():
                $status = __('table.Active');
                break;
            case $row->isDeleted():
                $status = __('table.Deleted');
                break;
            case $row->isBlocked():
                $status = __('table.Blocked');
                break;
            default:
                $status = null;
                break;
        }

        return [
            $row->first_name,
            $row->middle_name,
            $row->last_name,
            $row->phone,
            $row->email,
            $status,
            $row->created_at,
            $row->getCreateAdmin(),
            $row->updated_at,
            $row->getUpdateAdmin(),
            $row->getClientBlockDeleteReason()->reason->name ?? '',
            $row->getClientBlockDeleteReason()->comment ?? '',
            '',
        ];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            __('table.Name'),
            __('table.MiddleName'),
            __('table.LastName'),
            __('table.Phone'),
            __('table.Email'),
            __('table.Status'),
            __('table.CreatedAt'),
            __('table.CreatedBy'),
            __('table.UpdatedAt'),
            __('table.UpdatedBy'),
            __('table.Reason'),
            __('table.Comment'),
            __('table.TotalLoans'),
        ];
    }
}
