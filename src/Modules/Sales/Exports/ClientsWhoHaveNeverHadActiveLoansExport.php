<?php

namespace Modules\Sales\Exports;

use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Modules\Common\Models\Client;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

final class ClientsWhoHaveNeverHadActiveLoansExport implements FromQuery, ShouldAutoSize, WithHeadings, WithMapping, WithCustomChunkSize
{
    use Exportable;

    public function __construct(private readonly Builder $query)
    {
    }

    public function query(): Builder
    {
        return $this->query;
    }

    public function chunkSize(): int
    {
        return 100;
    }

    public function headings(): array
    {
        return [
            __('table.ClientId'),
            __('table.Phone'),
            __('table.Email'),
            __('table.DateOfFirstRequest'),
            __('table.DateOfLastRequest'),
            __('table.NumberOfRequests'),
            __('table.DaysSinceLastRequest'),
        ];
    }

    /**
     * @param Client $row
     */
    public function map($row): array
    {
        return [
            $row->client_id,
            $row->phone,
            $row->email,
            $row->first_created_at ? CarbonImmutable::parse($row->first_created_at)->format('d/m/Y') : null,
            $row->last_created_at ? CarbonImmutable::parse($row->last_created_at)->format('d/m/Y') : null,
            $row->total_count,
            $row->days_since_last,
        ];
    }
}
