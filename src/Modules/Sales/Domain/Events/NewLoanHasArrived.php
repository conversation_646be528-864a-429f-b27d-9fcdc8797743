<?php

namespace Modules\Sales\Domain\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\Common\Models\Loan;
use Modules\Sales\Domain\Entities\Loan\NewLoan;
use Modules\Sales\Http\Dto\LoanDto;

/**
 * Used in:
 * - NewLoan::dispatchNewLoanHasArrived() - when we create new loan
 */
class NewLoanHasArrived
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(public readonly Loan $loan, public ?LoanDto $loanDto = null) {}
}
