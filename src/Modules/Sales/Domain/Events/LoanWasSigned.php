<?php

namespace Modules\Sales\Domain\Events;

use App\Providers\EventServiceProvider;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\Api\Http\Dto\SignContractDto;
use Modules\Common\Models\Loan;

/** @see EventServiceProvider */
class LoanWasSigned
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(public Loan $loan, public ?SignContractDto $dto = null){}
}
