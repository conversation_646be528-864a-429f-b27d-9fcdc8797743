<?php

namespace Modules\Sales\Domain\Events;

use App\Providers\EventServiceProvider;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\Common\Models\Client as DbClient;
use Modules\Head\Application\Listeners\Statistics\Client\CreateInitialClientStatsListener;
use Modules\Sales\Domain\Entities\Client\Client;

/**
 * @see Client::save()
 * @see EventServiceProvider
 * @see CreateInitialClientStatsListener::handle()
 */
class NewClientHasRegistered
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(public readonly DbClient $client) {}
}
