<?php

namespace Modules\Sales\Domain\Exceptions;

use Modules\Common\Domain\Exceptions\DomainException;
use Modules\Sales\Exceptions\AmountForRefinanceIsLargerThanProductMaxAmount as ParentException;
use Modules\Common\Exceptions\ShouldBeReportedToUser;

final class AmountForRefinanceIsLargerThanProductMaxAmount extends DomainException implements ShouldBeReportedToUser
{
    public function __construct(string $refinanceAmount, string $productMaxAmount)
    {
        $parent = new ParentException($refinanceAmount, $productMaxAmount);
        $this->baseMessage = $parent->getMessage();
        parent::__construct(get_defined_vars());
    }
}
