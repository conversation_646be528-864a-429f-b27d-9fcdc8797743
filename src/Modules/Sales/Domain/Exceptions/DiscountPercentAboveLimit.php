<?php

namespace Modules\Sales\Domain\Exceptions;

use Modules\Common\Domain\Exceptions\DomainException;


class DiscountPercentAboveLimit extends DomainException
{
    public function __construct(int $discount, int $limit)
    {
        $this->baseMessage = __('exceptions.discount of :discount is above limit of :limit', [
            'discount' => $discount,
            'limit' => $limit
        ]);
        parent::__construct(get_defined_vars());
    }
}
