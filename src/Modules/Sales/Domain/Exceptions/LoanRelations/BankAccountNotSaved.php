<?php

namespace Modules\Sales\Domain\Exceptions\LoanRelations;

use Modules\Common\Domain\Exceptions\DomainException;

class BankAccountNotSaved extends DomainException
{
    public function __construct(int $clientBankAccountId)
    {
        $this->baseMessage = sprintf('LoanBankAccount not saved from ClientBankAccount: %d', $clientBankAccountId);
        parent::__construct(get_defined_vars());
    }
}
