<?php

namespace Modules\Sales\Domain\Entities\Client;

use Illuminate\Support\Facades\Log;
use Modules\Common\Domain\DomainModelInterface;
use Modules\Common\Models\BaseModel;
use Modules\Common\Models\NotificationSetting as DbModel;
use Modules\Communication\Repositories\NotificationSettingRepository as Repo;
use Modules\Sales\Domain\Exceptions\NotificationSettingNotSaved;
use Modules\Sales\Http\Dto\NotificationSettingDto;

class NotificationSetting implements DomainModelInterface
{
    private Client $client;

    public function __construct(private readonly Repo $repo, private DbModel $dbModel)
    {
    }

    public function build(Client $client, NotificationSettingDto $dto): self
    {
        return $this->setClient($client)->setFromDto($dto);
    }

    public function buildFromExisting(Client $client, DbModel $dbModel)
    {
        $this->dbModel = $dbModel;
        return $this->setClient($client);
    }

    public function setClient(Client $client): self
    {
        $this->client = $client;
        return $this;
    }

    public function setFromDto(NotificationSettingDto $dto): self
    {
        $dto->client_id = $this->client->dbModel()->getKey();
        $existing = $this->repo->getByData($dto->client_id, $dto->type, $dto->channel);

        if ($existing && $existing->value === $dto->value) {
            $this->dbModel = $existing;
            return $this;
        }

        if ($existing) {
            $this->dbModel = $existing;
            $this->dbModel->value = $dto->value;
            return $this->save($existing);
        }

        $this->dbModel->client_id = $dto->client_id;
        $this->dbModel->type = $dto->type;
        $this->dbModel->channel = $dto->channel;
        $this->dbModel->value = $dto->value;

        return $this->save();
    }

    public function save(?DbModel $existing = null): self
    {
        if (
            $this->dbModel->isDirty()
            && !$this->repo->save($this->dbModel, $existing)
        ) {
            throw new NotificationSettingNotSaved();
        }
        return $this;
    }

    public function dbModel(): BaseModel|DbModel
    {
        return $this->dbModel;
    }
}
