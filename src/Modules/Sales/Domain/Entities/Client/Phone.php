<?php

namespace Modules\Sales\Domain\Entities\Client;

use Modules\Common\Domain\DomainModelInterface;
use Modules\Common\Models\ClientPhone;
use Modules\Common\Models\ClientPhone as DbModel;
use Modules\Head\Repositories\ClientPhoneRepository as Repo;
use Modules\Sales\Domain\Exceptions\ClientRelations\PhoneNotSaved;
use Modules\Sales\Http\Dto\PhoneDto as Dto;

class Phone implements DomainModelInterface
{
    private Client $client;

    public function __construct(
        private DbModel $dbModel,
        private readonly Repo $repo
    ){}

    public function build(Client $client, Dto $dto): self
    {
        return $this->setClient($client)
            ->setDbModel($dto)
            ->save();
    }

    public function buildFromExisting(Client $client, DbModel $dbModel): self
    {
        $this->dbModel = $dbModel;

        return $this->setClient($client);
    }

    private function setClient(Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    private function setDbModel(Dto $dto): self
    {
        $dbClient = $this->client->dbModel();
        $this->dbModel->setRelation(DbModel::class, $dbClient);
        $this->dbModel->client_id = $dbClient->getKey();
        $this->dbModel->number = $dto->number;
        $this->dbModel->seq_num = (ClientPhone::whereClientId($dbClient->getKey())->count() + 1);

        return $this;
    }

    private function save(): self
    {
        if (!$this->repo->save($this->dbModel)) {
            throw new PhoneNotSaved();
        }

        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
