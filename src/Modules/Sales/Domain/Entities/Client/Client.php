<?php

namespace Modules\Sales\Domain\Entities\Client;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Modules\Approve\Domain\Exceptions\ClientNotFound;
use Modules\Common\Domain\AggregateRootInterface;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Domain\EloquentCollectionWrapper;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Models\ClientEmployer;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\MvrReport;
use Modules\Common\Models\Office;
use Modules\Common\Traits\PinTrait;
use Modules\Head\Repositories\ClientRepository;
use Modules\Sales\Domain\Entities\Loan\ActiveLoans;
use Modules\Sales\Domain\Entities\Loan\LoanForUpdate;
use Modules\Sales\Domain\Entities\Loan\NewLoan;
use Modules\Sales\Domain\Events\NewClientHasRegistered;
use Modules\Sales\Domain\Exceptions\ClientHasUnreceivedEasyPayMoney;
use Modules\Sales\Domain\Exceptions\ClientIsBlocked;
use Modules\Sales\Domain\Exceptions\ClientNotSaved;
use Modules\Sales\Domain\Exceptions\ClientRelations\NewClientAddressBuiltWithoutDto;
use Modules\Sales\Domain\Exceptions\EmailMissingForOnlineLoan;
use Modules\Sales\Http\Dto\AddressDto;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Dto\EmploymentDto;
use Modules\Sales\Http\Dto\LoanDto;
use Modules\Sales\Services\ClientCheckerService;
use Modules\ThirdParty\Repositories\MvrReportRepository;

class Client extends DomainModel implements AggregateRootInterface
{
    use PinTrait;

    private DbClient $dbModel;
    private EloquentCollectionWrapper|Phone $phones;
    private EloquentCollectionWrapper|NotificationSetting $notificationSettings;
    private ?MvrReport $mvrReport = null;
    private ?ClientDto $clientDto = null;

    public function __construct(
        private ClientRepository $clientRepo,
        private NewLoan $loan,
        private Relations $relations,
        protected ActiveLoans $activeLoans
    ) {
    }

    // Used at:
    // - NewAppController - New Application (Admin)
    // - RegistrationStepTwo - Api
    // - RegistrationStepThree - Api
    //
    //CREATE or UPDATE Client + CREATE Loan
    public function processNewApplication(ClientDto $clientDto): self
    {
        return $this->build($clientDto)
            ->buildActiveLoans()
            ->buildRelations($clientDto, $clientDto->relationDto?->loanDto?->iban)
            ->buildLoan($clientDto->relationDto->loanDto);
    }

    //CREATE or UPDATE Client WITHOUT Loan
    public function processWithoutLoan(ClientDto $clientDto): self
    {
        return $this->build($clientDto)
            ->buildActiveLoans()
            ->buildRelations($clientDto);
    }

    // Used at:
    // - request from profile - api
    //
    //CREATING A LOAN WITHOUT CHANGING THE trusted CLIENT
    public function createLoanForExistingClient(DbClient $dbClient, LoanDto $loanDto, ?ClientDto $clientDto = null): self
    {
        return $this->buildFromExisting($dbClient)
            ->buildActiveLoans()
            ->buildRelations($clientDto, $loanDto->iban)
            ->buildLoan($loanDto);
    }

    public function generateLoanStub(LoanDto $loanDto): DbLoan
    {
        return $this->loan->buildStub($loanDto, $this);
    }

    //UPDATING PHONES, always expect full array
    public function updatePhonesForExistingClient(int $clientId, array $phoneDtoArr): Collection
    {
        return $this->loadById($clientId)
            ->relations->phones()
            ->build($this, $phoneDtoArr)
            ->getDbCollection();
    }


    /**
     * UPDATING Addresses, expects array of AddressDto
     *
     * @param int $clientId
     * @param AddressDto[]|array $addressDtosArr
     * @param int|null $loanId
     * @return void
     * @throws ClientNotFound
     * @throws NewClientAddressBuiltWithoutDto
     * @throws \Throwable
     */
    public function updateAddressesForExistingClient(int $clientId, array $addressDtosArr, ?int $loanId = null): void
    {
        $client = $this->loadById($clientId);

        app(\Modules\Sales\Domain\Entities\Client\Repositories\AddressRepository::class)
            ->findOrCreateAll($client, $addressDtosArr);

        if ($loanId) {
            $loan = LoanForUpdate::loadSelf($loanId);

            app(\Modules\Sales\Domain\Entities\Loan\Repositories\AddressRepository::class)
                ->createAllByLoanAndClient($loan, $client);
        }
    }

    //CREATING of UPDATING employment
    public function updateEmploymentForExistingClient(int $clientId, EmploymentDto $dto): ClientEmployer
    {
        return $this->loadById($clientId)
            ->buildRelations(null)
            ->relations->employment()
            ->build($this, $dto)
            ->dbModel();
    }

    public function updateNotificationSettings(int $clientId, array $dtos): Collection
    {
        return $this->loadById($clientId)
            ->relations->notificationSettings()
            ->build($this, $dtos)
            ->getDbCollection();
    }

    public function updateFromApi(int $clientId, ?string $email, ?array $settings)
    {
        $this->loadById($clientId);
        if ($email) {
            $this->setEmail($email)->save();
            $this->relations->email()->build($this);
        }
        if ($settings) {
            $this->relations->notificationSettings()->build($this, $settings);
        }

        return $this;
    }

    /********************END OF ACCESS POINTS ****************************/

    private function build(ClientDto $clientDto): self
    {
        return $this->setClientDto($clientDto)
            ->setDbClientByPin($clientDto->pin)
            ->setMvrReport()
            ->setIdCardNumber()
            ->setPhone()
            ->setEmail()
            ->setLegalStatus()
            ->setCitizenshipType()
            ->setLegalStatusCode()
            ->setBirthDate()
            ->setGender()
            ->setEconomySectorCode()
            ->setIndustryCode()
            ->setNames()
            ->setLatinNames()
            ->setCcrSyncFlag()
            ->save();
    }

    private function setDbClientByPin(string $pin): self
    {
        $client = $this->clientRepo->getByPin($pin);
        if ($client) {
            return $this->buildFromExisting($client);
        }
        $this->dbModel = new DbClient();
        $this->dbModel->pin = $pin;
        $this->dbModel->new = 1;

        return $this;
    }

    private function setClientDto(ClientDto $clientDto): self
    {
        $this->clientDto = $clientDto;
        return $this;
    }

    private function setMvrReport(): self
    {
        $this->mvrReport = app(MvrReportRepository::class)->get($this->dbModel->pin);
        return $this;
    }

    public static function loadSelf(int $id): self
    {
        return app()->make(self::class)->loadById($id);
    }

    public function loadById(int $id): self
    {
        $dbClient = $this->clientRepo->getById($id);
        if (!$dbClient || !$dbClient->exists) {
            throw new ClientNotFound($id);
        }
        return $this->buildFromExisting($dbClient);
    }

    public static function selfLoad(int $id, $withRelations = false): self
    {
        /** @var Client $client */
        $client = app(Client::class);
        $client->loadById($id);

        return $withRelations ? $client->buildRelations(null) : $client;
    }

    public function buildFromExisting(DbClient $dbClient): self
    {
        /// on create new application we have rule for blocked clients
        if ($dbClient->isBlocked()) {
            throw new ClientIsBlocked($dbClient->pin);
        }

        // TODO: wtf?
        if ($dbClient->isDeleted()) {
            $this->clientRepo->undelete($dbClient);
        }

        $this->dbModel = $dbClient;

        return $this->setMvrReport();
    }

    /*******************END OF BUILDERS******************************/

    private function setIdCardNumber(): self
    {
        $idCardNumber = $this->clientDto?->idcard_number;
        $idCardDto = $this->clientDto?->relationDto?->idCardDto;
        if (
            $this->clientDto->legal_status !== DbClient::LS_COMP &&
            $this->clientDto->relationDto?->loanDto?->source !== LoanSourceEnum::WEBSITE
        ) {
            $this->relations->idCard()?->checkExpirationDate($idCardDto);
        }
        $this->dbModel->idcard_number = $idCardNumber;

        return $this;
    }

    private function setPhone(): self
    {
        $phoneDtos = $this->clientDto?->relationDto?->phoneDtoArr;
        $this->dbModel->phone = isset($phoneDtos[0]) ? $phoneDtos[0]->number : null;

        return $this;
    }

    private function setEmail(?string $email = null): self
    {
        if(! $email) {
            $email = $this->clientDto?->email;
        }
        $loanDto = $this->clientDto?->relationDto?->loanDto;
        if(! $email && $loanDto?->office_id === Office::OFFICE_ID_WEB){
            throw new EmailMissingForOnlineLoan();
        }
        $this->dbModel->email = $email;

        return $this;
    }

    private function setLegalStatus(): self
    {
        $dto = $this->clientDto?->relationDto?->representativeDto;
        $this->dbModel->legal_status = $dto ? DbClient::LS_COMP : DbClient::LS_INDV;

        return $this;
    }

    private function setCitizenshipType(): self
    {
        if (in_array($this->clientDto->citizenship_type, [DbClient::CT_LOCAL, DbClient::CT_FOREIGNER])) {
            $this->dbModel->citizenship_type = $this->clientDto->citizenship_type;

            return $this;
        }

        if ($this->clientDto->legal_status === DbClient::LS_COMP) {
            $this->dbModel->citizenship_type = DbClient::CT_UNKNOWN;

            return $this;
        }
        if ($this->isValidPin($this->dbModel->pin)) {
            $this->dbModel->citizenship_type = DbClient::CT_LOCAL;

            return $this;
        }
        if ($this->isValidPin($this->dbModel->pin, true)) {
            $this->dbModel->citizenship_type = DbClient::CT_FOREIGNER;

            return $this;
        }
        $this->dbModel->citizenship_type = DbClient::CT_UNKNOWN;

        return $this;
    }

    private function setLegalStatusCode(): self
    {
        $legalStatusCode = $this->clientDto?->legal_status_code;
        if($legalStatusCode){
            $this->dbModel->legal_status_code = $legalStatusCode;
            return $this;
        }
        if ($this->dbModel->legal_status !== DbClient::LS_INDV) {
            $this->dbModel->legal_status_code = DbClient::LS_CODE_MERCHANT;

            return $this;
        }
        $this->dbModel->legal_status_code = $this->dbModel->citizenship_type === DbClient::CT_LOCAL
            ? DbClient::LS_CODE_LOCAL_INVD
            : DbClient::LS_CODE_FOREIGN_INVD;

        return $this;
    }

    private function setBirthDate(?Carbon $birthDate = null): self
    {
        if (! $birthDate) {
            $parsedData = $this->getAgeAndSex($this->dbModel->pin);
            $birthDate = $parsedData['birth_date'] ? Carbon::parse($parsedData['birth_date']) : null;
        }

        if (! $birthDate) {
            $birthDate = $this->mvrReport?->getData()?->birthDate;
        }

        if (! $birthDate) {
            return $this;
        }

        $this->dbModel->birth_date = $birthDate;

        return $this;
    }

    private function setGender(?string $gender = null): self
    {
        $this->dbModel->gender = $gender;
        if ($gender) {
            return $this;
        }

        $parsedData = $this->getAgeAndSex($this->dbModel->pin);
        if (!empty($parsedData['sex'])) {
            $this->dbModel->gender = ($parsedData['sex'] == 2 ? 'male' : 'female');
        }


        return $this;
    }

    private function setEconomySectorCode(): self
    {
        if ($this->clientDto?->economy_sector_code) {
            $this->dbModel->economy_sector_code = $this->clientDto->economy_sector_code;
            return $this;
        }
        $this->dbModel->economy_sector_code = $this->dbModel->legal_status === DbClient::LS_INDV
            ? DbClient::ES_CODE_INVD
            : DbClient::ES_CODE_COMP;

        return $this;
    }

    private function setIndustryCode(): self
    {
        if ($this->clientDto?->industry_code) {
            $this->dbModel->industry_code = $this->clientDto->industry_code;
            return $this;
        }
        $this->dbModel->industry_code = $this->dbModel->legal_status === DbClient::LS_INDV
            ? DbClient::IND_CODE_INVD
            : DbClient::IND_CODE_COMP;

        return $this;
    }

    public function setNames(): self
    {
        $this->dbModel->first_name = $this->clientDto->first_name;
        $this->dbModel->middle_name = $this->clientDto->middle_name;
        $this->dbModel->last_name = $this->clientDto->last_name;

        return $this;
    }

    public function setLatinNames(): self
    {
        $this->dbModel->first_name_latin = $this->clientDto->first_name_latin;
        $this->dbModel->middle_name_latin = $this->clientDto->middle_name_latin;
        $this->dbModel->last_name_latin = $this->clientDto->last_name_latin;

        return $this;
    }

    private function setCcrSyncFlag(): self
    {
        if(! $this->dbModel->exists) {
            return $this;
        }

        foreach (DbClient::CCR_BORR_TRIGER_PROPS as $attribute) {
            if ($this->dbModel->isDirty($attribute)) {
                $this->dbModel->need_ccr_sync = 1;
                $this->dbModel->set_need_ccr_sync_at = now();

                return $this;
            }
        }

        return $this;
    }

    private function save(): self
    {
        $isNew = ! $this->dbModel()->exists;
        if (!$this->clientRepo->save($this->dbModel)) {
            throw new ClientNotSaved();
        }
        NewClientHasRegistered::dispatchIf($isNew, $this->dbModel);
        return $this;
    }

    private function buildRelations(?ClientDto $clientDto, ?string $iban = null): self
    {
        $this->relations->build($this, $clientDto, $iban);

        return $this;
    }

    private function buildLoan(LoanDto $loanDto): self
    {
        if (ClientCheckerService::hasUnreceivedEasyPayMoney(
            $this->dbModel->client_id,
            $loanDto->office_id ?? Office::OFFICE_ID_WEB,
        )) {
            throw new ClientHasUnreceivedEasyPayMoney($this->dbModel->pin);
        }

        $this->loan = $this->loan->build($loanDto, $this);

        return $this;
    }

    public function buildActiveLoans(): self
    {
        $this->activeLoans->build($this);
        return $this;
    }

    /***************** GETTERS *********************/

    public function dbModel(): DbClient
    {
        return $this->dbModel;
    }

    public function dbLoan(): DbLoan
    {
        return $this->loan->dbModel();
    }

    public function mvrReport(): ?MvrReport
    {
        return $this->mvrReport;
    }

    public function relations(): Relations
    {
        return $this->relations;
    }

    public function activeLoans(): ActiveLoans
    {
        return $this->activeLoans;
    }
}
