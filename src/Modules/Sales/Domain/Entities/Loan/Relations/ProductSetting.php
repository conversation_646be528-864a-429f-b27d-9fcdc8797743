<?php

namespace Modules\Sales\Domain\Entities\Loan\Relations;

use Modules\Common\Domain\DomainModelInterface;
use Modules\Common\Domain\LoanInterface;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanProductSetting as DbModel;
use Modules\Product\Repository\ProductSettingRepository as Repo;
use Modules\Common\Models\ProductSetting as DbProductSetting;
use Modules\Sales\Domain\Exceptions\LoanRelations\ProductSettingNotSaved;

class ProductSetting implements DomainModelInterface
{
    private LoanInterface $loan;

    public function __construct(private DbModel $dbModel, private Repo $repo)
    {
    }

    public function build(LoanInterface $loan): self
    {
        return $this->setLoan($loan)
            ->setDbModel()
            ->setData()
            ->save();
    }

    private function setLoan(LoanInterface $loan): self
    {
        $this->loan = $loan;
        $this->dbModel->setRelation(Loan::class, $loan->dbModel());

        return $this;
    }

    private function setDbModel(): self
    {
        $existing = $this->loan->dbModel()->loanProductSetting;
        if ($existing) {
            $this->dbModel = $existing;
        }
        return $this;
    }

    private function setData(): self
    {
        $l = $this->loan->dbModel();
        $currentProductSettings = $this->repo->getCurrentProductSettings($l->product_id);

        $this->dbModel->setAttribute('loan_id', $l->loan_id)
            ->setAttribute('product_id', $l->product_id)
            ->setAttribute('period', $currentProductSettings[DbProductSetting::PERIOD_KEY] ?? null)
            ->setAttribute('settings', $currentProductSettings);
        return $this;
    }

    private function save(): self
    {
        if (!$this->repo->saveLoanProductSetting($this->dbModel)) {
            throw new ProductSettingNotSaved();
        }

        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
