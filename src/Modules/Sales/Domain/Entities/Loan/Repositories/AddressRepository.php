<?php

declare(strict_types=1);

namespace Modules\Sales\Domain\Entities\Loan\Repositories;

use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Domain\LoanInterface;
use Modules\Common\Enums\AddressTypeEnum;
use Modules\Common\Models\ClientAddress;
use Modules\Common\Models\LoanAddress;
use Modules\Common\Services\LoanAddressService;
use Modules\Head\Repositories\ClientAddressRepository;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Entities\Loan\Relations\Address;
use Modules\Sales\Domain\Entities\Client\Address as ClientAddressEntity;
use Modules\Sales\Domain\Entities\Loan\Relations\AddressCollection;

final readonly class AddressRepository
{
    public function __construct(
        private LoanAddressService $service,
        private ClientAddressRepository $clientAddressRepo,
    ) {
    }

    public function createAllByLoanAndClient(LoanInterface $loan, Client $client): AddressCollection
    {
        /** @var ClientAddress[]|Collection $clientAddresses */
        $clientAddresses = $this->clientAddressRepo->getLatestOnesByClientId($client->dbModel()->getKey());
        $addresses = [];

        foreach ($clientAddresses as $clientAddress) {
            $loanAddress = $this->service->updateOrCreateLoanAddressLink(
                $loan->dbModel(), AddressTypeEnum::from($clientAddress->type), $clientAddress
            );

            if (! $loanAddress) {
                continue;
            }

            $addresses[] = $this->createFromExisting($loanAddress);
        }

        return new AddressCollection($addresses);
    }

    /**
     * @param LoanInterface $loan
     * @param array $clientAddresses
     * @return AddressCollection
     */
    public function createAllFromClientAddresses(LoanInterface $loan, array $clientAddresses): AddressCollection
    {
        $addresses = [];

        foreach ($clientAddresses as $clientAddress) {
            $addresses[] = $this->createFromClientAddress($loan, $clientAddress);
        }

        return new AddressCollection($addresses);
    }

    public function createFromClientAddress(LoanInterface $loan, ClientAddressEntity $clientAddress): Address
    {
        return new Address(
            $this->service->updateOrCreateLoanAddressLink(
                $loan->dbModel(),
                AddressTypeEnum::from($clientAddress->dbModel()->type),
                $clientAddress->dbModel(),
            )
        );
    }

    public function createFromExisting(LoanAddress $loanAddress): Address
    {
        return new Address($loanAddress);
    }
}
