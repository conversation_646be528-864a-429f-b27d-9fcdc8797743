<?php

namespace Modules\Sales\Domain\Entities\Loan;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Modules\Api\Domain\Exceptions\LoanAlreadySigned;
use Modules\Api\Http\Dto\SignContractDto;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Domain\AggregateRootInterface;
use Modules\Common\Domain\LoanInterface;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\Loan as DbModel;
use Modules\Common\Models\LoanParamsHistory;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\LoanStatusHistory;
use Modules\Common\Models\Office as DbOffice;
use Modules\Common\Models\PaymentMethod as DbPaymentMethod;
use Modules\Common\Models\SaleDecision;
use Modules\Common\Models\SaleTask;
use Modules\Sales\Domain\Entities\Bank\ClientBank;
use Modules\Sales\Domain\Entities\Client\Client;
use Modules\Sales\Domain\Entities\Loan\Relations\BankAccount;
use Modules\Sales\Domain\Events\LoanParamsUpdated;
use Modules\Sales\Domain\Exceptions\LoanHasIncorrectStatus;
use Modules\Sales\Domain\Exceptions\LoanIsProcessedByDifferentAdmin;
use Modules\Sales\Domain\Exceptions\LoanNotFound;
use Modules\Sales\Domain\Exceptions\LoanNotSaved;
use Modules\Sales\Domain\Exceptions\OfficeAdminNotAllowedToUpdateLoan;
use Modules\Sales\Domain\Exceptions\UnidentifiedAdmin;
use Modules\Sales\Http\Dto\BankDto;
use Modules\Sales\Http\Dto\LoanDto;
use Modules\Sales\Services\ClientCheckerService;
use Modules\Sales\Services\SaleService;

class LoanForUpdate extends NewLoan implements LoanInterface, AggregateRootInterface
{
    const DEFAULT_HIDE_TIME = 5; // min

    public static function loadSelf(int $id): self
    {
        /** @var LoanForUpdate $loanForUpdate */
        $loanForUpdate = app(LoanForUpdate::class);
        return $loanForUpdate->loadById($id);
    }

    public function loadById(int $id): self
    {
        $dbModel = $this->repo->getById($id);
        if (!$dbModel) {
            throw new LoanNotFound($id);
        }
        $client = Client::selfLoad($dbModel->getAttribute('client_id'));
        return $this->buildFromExisting($client, $dbModel);
    }

    public function buildFromExisting(Client $client, DbModel $dbModel): self
    {
        return $this
            ->setDbModel($client, $dbModel)
            ->setClient($client);
    }

    protected function setDbModel(Client $client, ?DbModel $dbModel = null): self
    {
        $this->dbModel = $dbModel;
        return $this;
    }

    public function update(LoanDto $loanDto): self
    {
        if (
            !empty($loanDto->iban)
            && !empty($this->dbModel->client_id)
            && ClientCheckerService::hasAnotherIbanUsers($this->dbModel->client_id, $loanDto->iban)
        ) {
            throw new \Exception('Този IBAN вече се използва от друг клиент.');
        }

        return $this
            ->setLoanDto($loanDto)
            ->setOffice()
            ->setAdmin($this->loanDto->administrator_id)
            ->setProduct()
            ->setProductType()
            ->setPaymentMethodId()
            ->setBankAccountId($loanDto->bank_account_id) // payment account
            ->updateBankAccount($loanDto->iban ?? null) // real bank accounts: client and loan
            ->setAmounts()
            ->setRefinanceData() // loan_type & amount_rest

            ->setDiscountPercent($this->loanDto->discount_percent)
            ->setPeriods()
            ->setInterestPercent()
            ->setPenaltyPercent()
            ->setGraceDays()
            ->setInstallmentModifier()
            ->setInstallments()
            ->setStatus()
            ->saveLoanParamsHistory()
            ->updateInstallments()
            ->save(true)
            ->saveRefLoans()
            ->buildProductSetting()
            ->buildClientOffice()
            ->buildLoanMeta()
            ->closeSaleTasks();
    }

    public function nullateApproveTasks(): self
    {
        $dbLoan = $this->dbModel;

        // when loan was signed and agent changed it we need to nullate its approve tasks
        // since they were related to old loan params
        if (!empty($dbLoan->getSignInDateTime())) {
            $dbLoan->moveApproveTasksToLoanHistory();
            $dbLoan->setAttribute('approve_tasks', null);
            $dbLoan->saveQuietly();
        }

        return $this;
    }

    private function closeSaleTasks(): self
    {
        $task = SaleTask::where('loan_id', $this->loanDto->loan_id)
            ->where('status', SaleTask::SALE_TASK_STATUS_PROCESSING)
            ->first();

        if ($task) {
            app(SaleService::class)->storeSaleAttempt(
                $task,
                [
                    'sale_decision_id' => SaleDecision::SALE_DECISION_ID_CHANGE_LOAN_PARAMS,
                    'sale_decision_reason_id' => 'other',
                    'comment' => 'Сменени параметри по заявка'
                ]
            );
        }

        return $this;
    }

    public function sign(?SignContractDto $dto = null): self
    {
        if ($this->cancelLoanIfClientUseIbanOfOther($dto)) {
            return $this;
        }

        $loan = $this->dbModel;
        if ($loan->hasStatus(LoanStatus::SIGNED_STATUS_ID)) {
            throw new LoanAlreadySigned($loan->loan_id);
        }
        if (!$loan->hasStatus(LoanStatus::NEW_STATUS_ID)) {
            throw new LoanHasIncorrectStatus($loan->getKey(), $loan->getStatus());
        }

        // only for online
        // by deafult we're hinding loan for 5 min, so the background processes could be done
        // and reset it when we get Credit Limit
        // also if we're re-sign contract, we need to delete prev.auto process record
        $skipTill = $loan->isOnlineLoan() ? now()->addMinutes(self::DEFAULT_HIDE_TIME) : null;
        $loan->updateSkipTill($skipTill, 'sign contract');

        $autoProcessRow = $loan->getLastAutoProcessRow();
        if (!empty($autoProcessRow->auto_process_id)) {
            $autoProcessRow->active = 0;
            $autoProcessRow->deleted = 1;
            $autoProcessRow->deleted_at = now();
            $autoProcessRow->deleted_by = 1;
            $autoProcessRow->saveQuietly();
        }

        $this->dbModel->loan_status_id = LoanStatus::SIGNED_STATUS_ID;
        $this->dbModel->last_status_update_date = Carbon::now();

        return $this
            ->save()
            ->closeExistingTasks()
            ->dispatchLoanWasSigned($dto);
    }

    private function cancelLoanIfClientUseIbanOfOther(?SignContractDto $dto): bool
    {
        if (
            empty($this->dbModel->office_id)
            || $this->dbModel->office_id != DbOffice::OFFICE_ID_WEB
        ) {
            return false;
        }

        if (
            empty($this->dbModel->payment_method_id)
            || $this->dbModel->payment_method_id != DbPaymentMethod::PAYMENT_METHOD_BANK
        ) {
            return false;
        }


        $iban = null;
        if (empty($dto->iban)) {
            $lba = $this->relations()?->bankAccount()?->dbModel();
            if (empty($lba->client_bank_account_id)) {
                return false;
            }

            $ba = $lba->clientBankAccount;
            if (empty($ba->iban)) {
                return false;
            }

            $iban = $ba->iban;
        } else {
            $iban = $dto->iban;
        }


        if (
            !empty($iban)
            && ClientCheckerService::hasAnotherIbanUsers($this->dbModel->client->getKey(), $iban)
        ) {
            $decisionDto = DecisionDto::from([
                'loan_id' => $this->dbModel->getKey(),
                'admin_id' => 1,
                'decision' => ApproveDecision::APPROVE_DECISION_CANCELED,
                'decision_reason' => ApproveDecisionReason::APPROVE_DECISION_REASON_USE_IBAN_OF_OTHER,
            ]);
            $this->cancelLoanAction->execute($decisionDto);

            return true;
        }

        return false;
    }


    protected function setAdmin(?int $adminId): self
    {
        if (!$adminId) {
            throw new UnidentifiedAdmin();
        }
        $processedBy = $this->dbModel->getAttribute('administrator_id');
        if (!empty($processedBy) && $processedBy !== $adminId) {
            throw new LoanIsProcessedByDifferentAdmin($processedBy);
        }
        if (!$this->office->allowedToUpdate($adminId)) {
            throw new OfficeAdminNotAllowedToUpdateLoan();
        }

        // we should set it to null, to be redirected to approve page after loan change
        $this->dbModel->administrator_id = null;

        return $this;
    }

    protected function setStatus(): self
    {
        // commented this, because we need ability to change loan in sale! where loan is not SIGNED, and stay as NEW
        // $office = $this->dbModel->office;
        // if ($office->isSelfApproved()) {
        //     if (!$this->dbModel->hasStatus(LoanStatus::PROCESSING_STATUS_ID)) {
        //         throw new LoanHasIncorrectStatus($this->dbModel->getKey(), $this->dbModel->getStatus());
        //     }
        // }

        return $this->dbModel->wasImportantPropertyChanged()
            ? parent::setStatus()
            : $this;
    }

    protected function buildClientOffice(): self
    {
        return $this->dbModel->wasChanged('office_id')
            ? parent::buildClientOffice()
            : $this;
    }

    protected function saveLoanParamsHistory(): self
    {
        if ($this->dbModel->isDirty(LoanParamsHistory::CHANGES_TO_TRACK)) {
            $this->loanParamsHistoryRepo->createFromLoan($this->dbModel);

            $this->dbModel->setAttribute('loan_changed_at', Carbon::now());

            if ($this->dbModel->isOnlineLoan()) {
                //used in Head/Resources/views/card/items/boxSalesCollectApproveOthers/approve.blade.php:24
                Session::put('loanChanged', 1);
            }
        }

        return $this;
    }

    protected function save(bool $changedLoanParams = false): self
    {
        $loanIsBeforeApprove = in_array(
            $this->dbModel->loan_status_id,
            [LoanStatus::NEW_STATUS_ID, LoanStatus::SIGNED_STATUS_ID]
        );


        $statusWasProcessing = $this->dbModel->getOriginal('loan_status_id') === LoanStatus::PROCESSING_STATUS_ID;
        $statusRevertedBeforeApprove = $statusWasProcessing && $loanIsBeforeApprove; // approve level
        $loanChangedBeforeApprove = $changedLoanParams && $loanIsBeforeApprove; // sale level


        // in case when loan status is new and agent process sale task
        // and change loan params we change it from new to new and not create this record
        // we need it for later calculations
        if (
            $changedLoanParams
            && !empty($this->dbModel->loan_id)
            && !empty($this->dbModel->office_id)
            && $this->dbModel->office_id == 1
            && !$this->dbModel->wasChanged('loan_status_id')
            && $this->dbModel->loan_status_id == LoanStatus::NEW_STATUS_ID
        ) {
            $lsh = new LoanStatusHistory();
            $lsh->loan_id = $this->dbModel->loan_id;
            $lsh->loan_status_id = LoanStatus::NEW_STATUS_ID;
            $lsh->date = now();
            $lsh->administrator_id = 1;
            $lsh->active = 1;
            $lsh->deleted = 0;
            $lsh->created_at = now();
            $lsh->save();
        }


        DB::transaction(function () {
            if (!$this->repo->save($this->dbModel)) {
                throw new LoanNotSaved();
            }

            if ($this->dbModel->wasChanged('discount_percent')) {
                $this->saveLoanDiscountLog();
            }
        });


        // regenerate: loan/client stats, docs: DOC_PACK_NEW_LOAN, email, sms
        if ($statusRevertedBeforeApprove || $loanChangedBeforeApprove) {
            $this->actualizeRefinanceSnapshot();

            LoanParamsUpdated::dispatch($this->dbModel, null);
        }


        return $this;
    }

    protected function updateBankAccount(?string $iban = null): self
    {
        if (empty($iban)) {
            return $this;
        }

        // Create Client Bank Account
        $bankDto = new BankDto($iban);
        $client = $this->dbModel->client;
        $clientBankDomainObj = app(ClientBank::class)->buildFromRealClient($client, $bankDto);

        // create Loan Bank Account
        app(BankAccount::class)->build($this, $clientBankDomainObj->dbModel());

        return $this;
    }
}
