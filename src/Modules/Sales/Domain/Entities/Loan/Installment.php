<?php

namespace Modules\Sales\Domain\Entities\Loan;

use Carbon\Carbon;
use Illuminate\Support\Facades\App;
use Modules\Common\Domain\DomainModelInterface;
use Modules\Common\Domain\LoanInterface;
use Modules\Common\Models\BaseModel;
use Modules\Common\Models\Installment as DbModel;
use Modules\Head\Repositories\InstallmentRepository as Repo;
use Modules\Sales\Domain\Exceptions\LoanRelations\InstallmentNotSaved;
use StikCredit\Calculators\Calculator;
use StikCredit\Calculators\InstallmentCalculator;
use StikCredit\Calculators\Installments\DefaultInstallment;

class Installment implements DomainModelInterface
{
    private float $unpaidPrincipal = 0;
    private Carbon $prevDueDate;
    private ?Installment $prevInstallment = null;
    private Carbon $repaymentDate;
    private Carbon $dueDate;
    private float $interest = 0;
    private float $penalty = 0;
    private float $lateInterest = 0;
    private float $latePenalty = 0;
    private LoanInterface $loan;

    public function __construct(
        private DbModel $dbModel,
        private Repo    $repo
    )
    {
    }

    public function buildFromExisting(LoanInterface $loan, DbModel $dbModel): self
    {
        $this->dbModel = $dbModel;
        return $this->setLoan($loan);
    }

    public function buildFromCalculator(LoanInterface $loan, DefaultInstallment $calcInst): self
    {
        return $this->setLoan($loan)
            ->setDbModel($calcInst)
            ->fillFromCalc($calcInst)
            ->save();
    }

    private function setLoan(LoanInterface $loan): self
    {
        $this->loan = $loan;
        return $this;
    }

    private function setDbModel(DefaultInstallment $calcInst): self
    {
        $existing = $this->repo->getByLoanIdAndSeq($this->loan->dbModel()->getKey(), $calcInst->index);
        if ($existing) {
            $this->dbModel = $existing;
        }
        return $this;
    }

    private function fillFromCalc(DefaultInstallment $calcInst): self
    {
        $dbLoan = $this->loan->dbModel();

        $this->dbModel->client_id = $dbLoan->getAttribute('client_id');
        $this->dbModel->loan_id = $dbLoan->getAttribute('loan_id');
        $this->dbModel->seq_num = $calcInst->index;
        $this->dbModel->due_date = $calcInst->dueDate;

        $this->dbModel->accrued_total_amount = intToFloat($calcInst->principal + $calcInst->accrued_interest + $calcInst->accrued_penalty);
        $this->dbModel->accrued_interest = intToFloat($calcInst->accrued_interest);
        $this->dbModel->accrued_penalty = intToFloat($calcInst->accrued_penalty);

        $this->dbModel->total_amount = intToFloat($calcInst->installmentAmount);
        $this->dbModel->principal = intToFloat($calcInst->principal);
        $this->dbModel->interest = intToFloat($calcInst->interest);
        $this->dbModel->penalty = intToFloat($calcInst->penaltyAmount);

        $this->dbModel->overdue_days = $calcInst->overdueDays;
        $this->dbModel->status = DbModel::INSTALLMENT_STATUS_SCHEDULED;
        $this->dbModel->active = 1;

        $this->dbModel->rest_principal = $this->dbModel->principal;
        $this->dbModel->rest_interest = $this->dbModel->interest;
        $this->dbModel->rest_penalty = $this->dbModel->penalty;

        return $this;
    }

    private function save(): self
    {
        $installment = $this->repo->save($this->dbModel);
        if (empty($installment->installment_id)) {
            throw new InstallmentNotSaved();
        }

        return $this;
    }

    /***************** DEBT CALCULATIONS *************************/

    public function setCalculated()
    {
        $this->setPreviousInstallment()
            ->setDueDate()
            ->setPrevDueDate()
            ->setUnpaidPrincipal()
            ->setRepaymentDate()
            ->setInterest()
            ->setPenalty()
            ->setLateInterest()
            ->setLatePenalty();
    }

    public function setPreviousInstallment(): self
    {
        $dbInstallment = $this->dbModel->getPreviousInstallment();
        if ($dbInstallment) {
            /** @var Installment $previous */
            $previous = app(Installment::class);
            $this->prevInstallment = $previous->buildFromExisting($this->loan, $dbInstallment);
        }
        return $this;
    }

    public function setDueDate(): self
    {
        $this->dueDate = Carbon::parse($this->dbModel->due_date);
        return $this;
    }

    public function setPrevDueDate(): self
    {
        $this->prevDueDate = $this->prevInstallment
            ? Carbon::parse($this->prevInstallment->dbModel()->due_date)
            : Carbon::parse($this->loan->dbModel()->created_at);
        return $this;
    }

    public function setRepaymentDate($repaymentDate = null): self
    {
        $this->repaymentDate = $repaymentDate ?: Carbon::today();
        return $this;
    }

    public function setUnpaidPrincipal(): self
    {
        $this->unpaidPrincipal = Calculator::sub($this->dbModel->principal, $this->dbModel->paid_principal);
        return $this;
    }

    public function isOnTimeRepayment(): bool
    {
        return $this->repaymentDate->lte($this->dueDate) && $this->repaymentDate->gt($this->prevDueDate);
    }

    public function isLateRepayment(): bool
    {
        return $this->repaymentDate->gt($this->dueDate);
    }

    public function setInterest(): self
    {
        $this->interest = 0;
        if ($this->isOnTimeRepayment()) {
            $this->interest = InstallmentCalculator::calcAccruedInterest(
                $this->repaymentDate,
                $this->dbModel->due_date,
                $this->prevDueDate,
                $this->dbModel->interest
            );
        }
        if ($this->isLateRepayment()) {
            $this->interest = $this->dbModel->rest_interest;
        }
        return $this;
    }

    public function setPenalty(): self
    {
        $this->penalty = 0;
        if ($this->isOnTimeRepayment()) {
            $this->penalty = InstallmentCalculator::calcAccruedInterest(
                $this->repaymentDate,
                $this->dbModel->due_date,
                $this->prevDueDate,
                $this->dbModel->penalty
            );
        }
        if ($this->isLateRepayment()) {
            $this->penalty = $this->dbModel->rest_penalty;
        }
        return $this;
    }

    public function setLateInterest(): self
    {
        $this->lateInterest = 0;
        if ($this->isOnTimeRepayment() && $this->dbModel->status === DbModel::INSTALLMENT_STATUS_LATE) {
            $this->lateInterest = Calculator::round($this->dbModel->getRestLateInterest());
        }

        if ($this->isLateRepayment()) {
            $this->lateInterest = Calculator::sub($this->dbModel->late_interest, $this->dbModel->paid_late_interest);
        }
        return $this;
    }

    public function setLatePenalty(): self
    {
        $this->latePenalty = 0;
        if ($this->isOnTimeRepayment() && $this->dbModel->status === DbModel::INSTALLMENT_STATUS_LATE) {
            $this->latePenalty = Calculator::round($this->dbModel->getRestLatePenalty());
        }

        if ($this->isLateRepayment()) {
            $this->latePenalty = Calculator::sub($this->dbModel->late_penalty, $this->dbModel->paid_late_penalty);
        }
        return $this;
    }

    public function getTotalDebtSum(): float
    {
        $this->setCalculated();
        return Calculator::sum(
            $this->unpaidPrincipal,
            $this->interest,
            $this->penalty,
            $this->lateInterest,
            $this->latePenalty
        );
    }

    public function dbModel(): BaseModel|DbModel
    {
        return $this->dbModel;
    }
}
