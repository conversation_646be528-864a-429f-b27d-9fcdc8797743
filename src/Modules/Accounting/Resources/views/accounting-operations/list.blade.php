@extends('layouts.app')
@section('content')
    @php
        /**
        * @var \Modules\Accounting\Models\AccountingPayment $row
         */
    @endphp
    <x-card-filter-form :filter-form="$filterForm"/>

    <x-card>
        <x-table>
            <x-slot:head>
                <tr>
                    <th>{{__('table.CreatedAt')}}</th>
                    <th>{{__('table.AccountingDate')}}</th>
                    <th>{{__('table.PaymentId')}}</th>
                    <th>{{__('table.Office')}}</th>
                    <th>{{__('table.LoanId')}}</th>
                    <th>{{__('table.Pin')}}</th>
                    <th>{{__('table.direction')}}</th>
                    <th>{{__('table.RefinanceLoans')}}</th>
                    <th>{{__('table.Juridical')}}</th>
                    <th>{{__('table.New')}}</th>
                    <th>{{__('table.UnclaimedMoney')}}</th>
                    <th>{{__('table.CreditAmount')}}</th>
                    <th>{{__('table.DebitAmount')}}</th>
                    <th>{{__('table.DocumentType')}}</th>
                    <th>{{__('table.CreditAccount')}}</th>
                    <th>{{__('table.DebitAccount')}}</th>
                    <th>{{__('table.Term')}}</th>
                </tr>
            </x-slot:head>
            @foreach($rows as $row)
                <tr>
                    <td>{{$row->created_at}}</td>
                    <td>{{$row->date}}</td>
                    <td>{{$row->payment_id}}</td>
                    <td>{{$row->office->name}}</td>
                    <td>{{$row->loan_id}}</td>
                    <td>{{$row->client?->pin}}</td>
                    <td>{{$row->paymentDirection}}</td>
                    <td>{{$row->isRefinance ? __('table.Yes') : __('table.No')}}</td>
                    <td>{{$row->isJuridical ? __('table.Yes') : __('table.No')}}</td>
                    <td>{{($row->isNewClient === 'yes') ? __('table.Yes') : __('table.No')}}</td>
                    <td>{{$row->isUnclaimedMoney ? __('table.Yes') : __('table.No')}}</td>
                    <td>{{$row->creditAmount}}</td>
                    <td>{{$row->debitAmount}}</td>
                    <td>{{$row->doc_type}}</td>
                    <td>{{$row->credit_account}}</td>
                    <td>{{$row->debit_account}}</td>
                    <td>{{$row->term}}</td>
                </tr>
            @endforeach
        </x-table>

        <x-table-pagination :rows="$rows"/>
    </x-card>
@endsection