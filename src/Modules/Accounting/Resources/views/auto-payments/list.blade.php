@extends('layouts.app')
@section('content')
    @php
        /** @var \Modules\Common\Models\PayConfirm $payment **/
    @endphp
    <x-card-filter-form
        :filter-form="$filterForm"
        query-string="true"
    />

    <x-card>
        <x-table>
            <x-slot:head>
                <tr>
                    <th>{{__('table.PaymentId')}}</th>
                    <th>{{__('table.datePayment')}}</th>
                    <th>{{__('table.Amount')}}</th>
                    <th>{{__('table.Pin')}}</th>
                    <th>{{__('table.Transaction')}}</th>
                    <th>{{__('table.RawData')}}</th>
                </tr>
            </x-slot:head>
            @foreach($payments as $payment)
                <tr>
                    <td>
                        @if(!$payment->payment_id)
                            <span class="text-danger">
                                Плащане не е намерено.
                            </span>
                        @else
                            <a href="{{route('payment.payments.details', $payment->payment_id)}}">
                                {{$payment->payment_id}}
                            </a>
                        @endif
                    </td>
                    <td>{{formatDate($payment->created_at, 'd.m.Y H:i')}}</td>
                    <td>{{intToFloat($payment->total)}}</td>
                    <td>{{$payment->idn}}</td>
                    <td>{{$payment->tid}}</td>
                    <td>
                        {{$payment->input_data}}
                    </td>
                </tr>
            @endforeach
        </x-table>

        <x-table-pagination :rows="$payments"/>
    </x-card>
@endsection