@extends('layouts.app')
@section('content')
    @php
        /**
    * @var \Modules\Accounting\Models\DailyAccountingReport $dailyReport
     */
    @endphp
    <x-card title="{{__('table.DailyReports')}}">

        @if(getAdmin()->hasPermissionTo('acc.daily-reports.runManual'))
        <div class="form-group">
            <a href="{{route('acc.daily-reports.runManual')}}" class="btn btn-primary btn-sm">
                {{__('btn.RunМanual')}}
            </a>
        </div>
        @endif

        <x-table>
            <x-slot:head>
                <tr>
                    <td>{{__('table.Id')}}</td>
                    <td>{{__('table.CreatedAt')}}</td>
                    <td>{{__('table.Date')}}</td>
                    <td>{{__('table.Actions')}}</td>
                </tr>
            </x-slot:head>
            @foreach($dailyReports as $dailyReport)
                <tr>
                    <td>{{$dailyReport->getKey()}}</td>
                    <td>{{$dailyReport->created_at}}</td>
                    <td>{{$dailyReport->date}}</td>
                    <td>
                        <a href="{{route('acc.daily-reports.download-report', $dailyReport->getKey())}}"
                           class="btn btn-sm btn-primary"
                        >
                            <i class="fa fa-download"></i>&nbsp;
                            {{__('btn.DownloadReport')}}
                        </a>
                    </td>
                </tr>
            @endforeach
        </x-table>
        <x-table-pagination :rows="$dailyReports"/>
    </x-card>
@endsection
