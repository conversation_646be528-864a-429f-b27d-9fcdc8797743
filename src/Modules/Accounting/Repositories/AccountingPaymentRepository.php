<?php

namespace Modules\Accounting\Repositories;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Modules\Accounting\Models\AccountingPayment;
use Modules\Admin\Repositories\BaseRepository;

class AccountingPaymentRepository extends BaseRepository
{
    public function __construct(
        protected readonly AccountingPayment $accountingPayment
    ) {}

    public function getPaginatorByFilters(array $filters = [], int $perPage = 10): LengthAwarePaginator
    {
        return $this
            ->getBuilderByFilters($filters)
            ->paginate($perPage);
    }

    public function getByFilters(array $filters = []): EloquentCollection
    {
        return $this
            ->getBuilderByFilters($filters)
            ->get();
    }

    public function getBuilderByFilters(array $filters = []): Builder
    {
        return $this->accountingPayment->filterBy($filters)
            ->with(['client', 'payment', 'office', 'loan'])
            ->orderBy('id', 'DESC');
    }

    // used in: DailyAccountingModel
    public function getByConditions(array $conditions): Collection
    {
        return $this->accountingPayment
            ->with(['client', 'payment' => function ($query) {
                $query->withTrashed();
            }, 'office', 'loan'])
            ->where($conditions)
            ->orderBy('id', 'ASC')
            ->get();
    }
}
