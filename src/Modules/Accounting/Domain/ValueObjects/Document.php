<?php

namespace Modules\Accounting\Domain\ValueObjects;

use Modules\Accounting\Models\AccountingPayment;

// used in DailyAccountingModel - daily accounting export (see Kernel)
class Document
{
    public function __construct(
        private AccountingPayment $accountingPayment,
        private ?string           $documentType = null,
        private ?int              $number = null,
        private ?string           $date = null,
    ) {}

    public function build(AccountingPayment $accountingPayment): self
    {
        $this
            ->setAccountingPayment($accountingPayment)
            ->setDocumentType()
            ->setDocumentNumber()
            ->setDate();

        return $this;
    }

    private function setDate(): self
    {
        $this->date = $this->accountingPayment->date;

        return $this;
    }

    private function setDocumentNumber(): self
    {
        $this->number = $this->accountingPayment->accounting_number;

        return $this;
    }

    private function setDocumentType(): self
    {
        $this->documentType = $this->accountingPayment->doc_type;

        return $this;
    }

    private function setAccountingPayment(AccountingPayment $accountingPayment): self
    {
        $this->accountingPayment = $accountingPayment;

        return $this;
    }

    public function toArray(): array
    {
        return [
            'DocumentType' => $this->documentType,
            'Number' => $this->number,
            'Date' => $this->date,
        ];
    }
}
