<?php

namespace Modules\Accounting\Domain\Exceptions;

use Modules\Common\Domain\Exceptions\DomainException;

class NoAvailableAccountingRows extends DomainException
{
    public function __construct(string $dateFrom, string $dateTo)
    {
        $this->baseMessage = sprintf('No available accounting rows for dates: %s - %s', $dateFrom, $dateTo);
        parent::__construct(get_defined_vars());
    }
}
