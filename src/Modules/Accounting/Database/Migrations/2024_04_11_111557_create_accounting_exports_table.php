<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\File;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('accounting_exports', function (Blueprint $table) {
            $table->id();
            $table->timestamp('created_at')->nullable();
            $table->foreignId('created_by')->constrained(
                Administrator::getTableName(),
                'administrator_id'
            );

            $table->foreignId('file_id')->constrained(File::getTableName(), 'file_id');
            $table->json('filters')->nullable();
            $table->string('download_url')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('accounting_exports');
    }
};
