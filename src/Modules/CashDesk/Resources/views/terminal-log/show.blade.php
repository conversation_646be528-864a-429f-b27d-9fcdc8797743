@php
    use Modules\CashDesk\Models\TerminalLog;
    /**
    * @var TerminalLog $item
    */
function varexport($expression, $return=false) {
    $export = var_export($expression, true);
    $export = preg_replace("/^([ ]*)(.*)/m", '$1$1$2', $export);
    $array = preg_split("/\r\n|\n|\r/", $export);
    $array = preg_replace(["/\s*array\s\($/", "/\)(,)?$/", "/\s=>\s$/"], [null, ']$1', ' => ['], $array);
    $export = join(PHP_EOL, array_filter(["["] + $array));
    if ((bool)$return) return $export; else echo $export;
}
@endphp
@extends('layouts.app')
@section('content')
    <x-card>
        <form>
            <x-cashdesk::terminal-log.show-row :label="__('table.Id')" :value="$item->terminal_log_id"/>
            @if($item->client)
                <x-cashdesk::terminal-log.show-row-link
                    :label="__('table.Client')"
                    :value="[$item->client->first_name, $item->client->last_name, $item->client->middle_name]"
                    :link="route('common.client-card-boxes.index', $item->client_id)"
                />
            @endif
            @if($item->loan)
                <x-cashdesk::terminal-log.show-row-link
                    :label="__('client_id')"
                    :value="$item->loan->loan_id"
                    :link="route('head.loans.list', ['loan_id'=>$item->loan->loan_id])"
                />
            @endif
            <x-cashdesk::terminal-log.show-row :label="__('table.OfficeName')"
                                               :value="$item->fiscalDevice?->office?->name"/>
            <x-cashdesk::terminal-log.show-row :label="__('table.Amount')" :value="$item->amount"/>
            <x-cashdesk::terminal-log.show-row :label="__('table.Details')">
                <pre>{{varexport($item->details,true)}}</pre>
            </x-cashdesk::terminal-log.show-row>
            <x-cashdesk::terminal-log.show-row :label="__('table.Type')" :value="$item->printing_type"/>
            <x-cashdesk::terminal-log.show-row :label="__('table.Status')" :value="$item->status"/>
            <x-cashdesk::terminal-log.show-row :label="__('other.processing_stack')" :value="$item->processing_stack"/>
            <x-cashdesk::terminal-log.show-row :label="__('table.Comment')" :value="$item->comment"/>
            <x-cashdesk::terminal-log.show-row :label="__('other.error_trace')">
                <pre>{{varexport($item->error_trace,true)}}</pre>
            </x-cashdesk::terminal-log.show-row>
            <x-cashdesk::terminal-log.show-row :label="__('table.CreatedAt')" :value="$item->created_at"/>
            <x-cashdesk::terminal-log.show-row :label="__('table.CreatedBy')" :value="$item->created_by"/>
            <x-cashdesk::terminal-log.show-row :label="__('table.DeletedAt')" :value="$item->deleted_at"/>
            <x-cashdesk::terminal-log.show-row :label="__('table.DeletedBy')" :value="$item->deleted_by"/>
            <x-cashdesk::terminal-log.show-row :label="__('table.UpdatedAt')" :value="$item->updated_at"/>
            <x-cashdesk::terminal-log.show-row :label="__('table.UpdatedBy')" :value="$item->updated_by"/>
            <x-cashdesk::terminal-log.show-row :label="__('table.ConfirmAt')" :value="$item->confirm_at"/>
            <x-cashdesk::terminal-log.show-row :label="__('table.ConfirmBy')" :value="$item->confirm_by"/>
        </form>
    </x-card>
@endsection