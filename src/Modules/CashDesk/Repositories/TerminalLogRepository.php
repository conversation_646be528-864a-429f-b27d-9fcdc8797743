<?php

namespace Modules\CashDesk\Repositories;

use Illuminate\Support\Carbon;
use Modules\CashDesk\Enums\TeminalPrintingTypeEnum;
use Modules\CashDesk\Enums\TerminalLogStatusEnum;
use Modules\CashDesk\Enums\TremolProcessingStackEnum;
use Modules\CashDesk\Models\TerminalLog;
use Modules\Common\Models\Administrator;

class TerminalLogRepository
{
    public function getById(string $id): TerminalLog|null
    {
        return (new TerminalLog)->getByUuid($id);
    }

    public function create(
        float $amount,
        array $details,
        TremolProcessingStackEnum $processingStack,
        TeminalPrintingTypeEnum $printingType,
        array $otherFields = []
    ): TerminalLog {
        $model = new TerminalLog();

        $model->fill($otherFields);
        $model->amount = $amount;
        $model->details = $details;
        $model->printing_type = $printingType;
        $model->status = $model->status ?? TerminalLogStatusEnum::new;
        $model->processing_stack = $processingStack;
        $model->save();

        return $model;
    }

    public function createWithUuid(
        string $uuid,
        int $fiscalDeviceId,
        float $amount,
        array $details,
        TremolProcessingStackEnum $processingStack,
        TeminalPrintingTypeEnum $printingType,
        array $otherFields = []
    ): TerminalLog {
        $model = new TerminalLog();
        $model->fill($otherFields);
        $model->amount = $amount;
        $model->fiscal_device_id = $fiscalDeviceId;
        $model->details = $details;
        $model->printing_type = $printingType;
        $model->processing_stack = $processingStack;
        $model->status = $model->status ?? TerminalLogStatusEnum::new;
        $model->terminal_log_id = $uuid;

        $model->save();

        return $model;
    }

    public function update(string $id, array $data): TerminalLog
    {
        $model = (new TerminalLog)->getByUuidOrFail($id);
        $model->fill($data);
        $model->save();

        return $model;
    }

    public function addConfirmFields(array $data, ?Administrator $administrator = null): array
    {
        $data['confirm_at'] = new Carbon();
        $data['confirm_by'] = $administrator?->getKey();

        return $data;
    }
}