<?php

namespace Modules\CashDesk\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Models\BaseModel;
use Modules\Common\Models\Office;

/**
 * @mixin IdeHelperCashOperationalTransactionStatsMonthly
 */
class CashOperationalTransactionStatsMonthly extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'cash_operational_transaction_stats_monthly';

    /**
     * @var string
     */
    protected $primaryKey = 'stats_id';

    /**
     * @var string[]
     */
    protected $guarded = [
        'stats_id',
        'active',
        'deleted',
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
        'deleted_at',
        'deleted_by',
        'enabled_at',
        'enabled_by',
        'disabled_at',
        'disabled_by',
    ];

    /**
     * @return BelongsTo
     */
    public function office(): BelongsTo
    {
        return $this->belongsTo(Office::class);
    }
}

