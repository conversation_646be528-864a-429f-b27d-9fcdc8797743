<?php

namespace Modules\CashDesk\Enums;

use App;
use Modules\ThirdParty\Services\FiscalDevice\FiscalProcessingAbstract;
use Modules\ThirdParty\Services\FiscalDevice\PHPProcessingService;

enum TremolProcessingStackEnum: string
{
    case PHP = 'php';

    public function getProcessingClass(): FiscalProcessingAbstract
    {
        return match ($this) {
            self::PHP => app(PHPProcessingService::class)
        };
    }
}
