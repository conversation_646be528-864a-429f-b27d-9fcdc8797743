<?php

namespace Modules\CashDesk\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Interfaces\ListSearchInterface;

/**
 * Class CashDeskSearchRequest
 * @package Modules\CashDesk\Http\Requests
 */
class CashDeskSearchRequest extends BaseRequest implements ListSearchInterface
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $rules = [
            'amount_from' => 'nullable|integer',
            'amount_to' => 'nullable|integer',
            'transaction_type' => 'nullable|array',
            'from_to_whom' => 'nullable|string',
            'loan_id' => 'nullable|numeric',
            'name' => 'nullable|string',
            'active' => 'nullable',
        ];

        $this->fillRuleOffice($rules);
        $this->fillRuleCreatedAt($rules);
        $this->fillRuleLimitRule($rules);
        $this->fillRuleOrder($rules);

        return $rules;
    }
}
