<?php

namespace Modules\CashDesk\Http\Requests;

use Modules\CashDesk\Enums\CashOperationalTransactionModalEnum;
use Modules\Common\Http\Requests\BaseRequest;

class CashDeskInitBalanceModalRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'office_id' => 'required|numeric',
            'modalName' => 'in:' . implode(',', CashOperationalTransactionModalEnum::getInitBalanceModalNames()),
        ];
    }
}
