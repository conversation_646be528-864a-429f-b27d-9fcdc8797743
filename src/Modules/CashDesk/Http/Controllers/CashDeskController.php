<?php

namespace Modules\CashDesk\Http\Controllers;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Kris\LaravelFormBuilder\FormBuilder;
use Modules\Admin\Repositories\AdministratorRepository;
use Modules\Admin\Services\OfficeService;
use Modules\CashDesk\Application\ActionDTO\ModalDTO;
use Modules\CashDesk\Application\Actions\MakeTransAction;
use Modules\CashDesk\Application\Actions\PrepareCashDeskInitBalanceModalAction;
use Modules\CashDesk\Application\Actions\PrepareCashDeskListAction;
use Modules\CashDesk\Application\Actions\PrepareCashDeskTopPanel;
use Modules\CashDesk\Domain\Entities\IncomingCashTransaction;
use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\CashDesk\Exceptions\TransactionForbiddenException;
use Modules\CashDesk\Http\Requests\CashDeskCrudRequest;
use Modules\CashDesk\Http\Requests\CashDeskInitBalanceModalRequest;
use Modules\CashDesk\Http\Requests\CashDeskInitBalanceRequest;
use Modules\CashDesk\Http\Requests\CashDeskModalRequest;
use Modules\CashDesk\Http\Requests\CashDeskTaxFeeRequest;
use Modules\CashDesk\Http\Requests\DTO\CashDeskSearchRequest;
use Modules\CashDesk\Models\CashOperationalDocument;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\CashDesk\Services\CashBalanceExportFileService;
use Modules\CashDesk\Services\CashDeskService;
use Modules\CashDesk\Services\CashOperationalDocumentDownloadService;
use Modules\CashDesk\Services\CashOperationalDocumentService;
use Modules\CashDesk\Services\CashOperationalTransactionService;
use Modules\CashDesk\Services\SessionCacheService;
use Modules\CashDesk\Services\TremolSessionService;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\Payment\PaymentPurposeEnum;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Common\Models\PaymentMethod;
use Modules\Payments\Application\Dto\NewPaymentDto;
use Modules\Payments\Domain\Entities\IncomingPayment;
use Modules\Payments\Domain\Events\LoanPayInWasDelivered;
use Modules\Payments\Domain\Events\LoanPaymentWasReceivedNew;
use Modules\Payments\Domain\Events\LoanPayoutWasDelivered;
use Modules\Payments\Services\PaymentService;
use Throwable;

class CashDeskController extends BaseController
{
    public string $pageTitle = 'CashDesk list';
    public string $indexRoute = 'payment.cashDesk.list';

    public function __construct(
        protected OfficeService                                 $officeService,
        protected CashOperationalTransactionService             $cashOperationalTransactionService,
        protected CashOperationalDocumentService                $cashOperationalDocumentService,
        protected CashDeskService                               $cashDeskService,
        protected AdministratorRepository                       $administratorRepository,
        private readonly SessionCacheService                    $sessionCacheService,
        private readonly TremolSessionService                   $tremolSessionService,
        private readonly CashOperationalDocumentDownloadService $cashOperationalDocumentFilePathService,
    )
    {
        parent::__construct();
        $this->sessionCacheService->setCacheKey($this->cacheKey);
    }

    public function topPanel(
        Office                  $office,
        PrepareCashDeskTopPanel $prepareCashDeskTopPanel,
        CashDeskSearchRequest   $request,
    ): JsonResponse
    {
        $dto = $prepareCashDeskTopPanel->execute($office, $request);
        $dto->initialBalance = intToFloat($dto->initialBalance);
        $dto->earnings = intToFloat($dto->earnings);
        $dto->expense = intToFloat($dto->expense);
        $dto->difference = intToFloat($dto->difference);
        $dto->fpBalance = intToFloat($dto->fpBalance);
        return response()->json($dto);
    }

    public function selectOffice(): RedirectResponse
    {
        $offices = $this->administratorRepository->getOfficesOrderByName(
            $this->getAdministratorOrFail()
        );

        return redirect(route('payment.cashDesk.list', ['office' => $offices->first()]));
    }

    public function list(
        Office                    $office,
        PrepareCashDeskListAction $prepareCashDeskListAction,
        FormBuilder               $formBuilder,
        CashDeskSearchRequest     $request,
    ): View {

        $request->limit = $this->getPaginationLimit();
        $dto = $prepareCashDeskListAction->execute(
            $this->cacheKey,
            $request,
            $this->getAdministratorOrFail(),
            $office,
            $formBuilder,
        );

        return view('cashdesk::cash-desk.list', compact('dto'));
    }

    public function createServiceFeeTransaction(
        CashDeskTaxFeeRequest $request,
        MakeTransAction       $action
    ): RedirectResponse {

        try {
            $dto = $request->asDto();
            $loan = Loan::where('client_id', $dto->client_id)
                ->orderBy('created_at', 'DESC')
                ->first();

            $dto = NewPaymentDto::from([
                'source' => PaymentSourceEnum::MANUAL_CREATION,
                'method' => PaymentMethodEnum::fromId($dto->payment_method_id),
                'amount' => $dto->amount,
                'rest_amount' => 0,
                'purpose' => PaymentPurposeEnum::SERVICE_FEE,
                'bank_account_id' => $dto->bank_account_id,
                'client_id' => $dto->client_id,
                'loan_id' => $loan?->loan_id,
                'office_id' => $dto->office_id,
                'description' => 'Такса удостоверение',
                'document_number' => $data['document_number'] ?? null,
                'created_by' => getAdminId(),
            ]);

            // make payment
            $payment = app(IncomingPayment::class)
                ->new()
                ->buildFromDtoForManualPayment($dto)
                ->dbModel();

            // optional for physical offices
            app(IncomingCashTransaction::class)->printReceiptFromPayment($payment);

            return back()->with('success', __('cashdesk::cashDesk.CreatedSuccessfully'));

        } catch (\Throwable $e) {
            return redirect()->back()->with('fail', $e->getMessage());
        }
    }


    /**
     * @throws ProblemException
     * @throws Throwable
     * @throws TransactionForbiddenException
     */
    public function add(CashDeskCrudRequest $request, MakeTransAction $action): JsonResponse
    {
        try {
            $action->execute($request->asDto());
        } catch (Exception $exception) {
            return response()->json([
                'status' => false,
                'message' => $exception->getMessage()
            ]);
        }

        return response()->json(['status' => true]);
    }


    public function initBalance(
        CashDeskInitBalanceRequest $request,
        MakeTransAction            $action
    ): JsonResponse
    {
        try {
            $action->execute($request->asDto());

            return response()->json(['status' => true]);
        } catch (\Throwable $e) {
            return response()->json([
                'status' => false,
                'noClosingBalanceRecordYesterday' => true,
                'message' => $e->getMessage()
            ]);
        }
    }


    public function clearTremolSession()
    {
        $this->tremolSessionService->clear();
    }

    /**
     * @throws ProblemException
     */
    public function edit(int $id): View
    {
        $tr = CashOperationalTransaction::find($id);

        return view(
            'cashdesk::cash-desk.edit',
            ['cashDesk' => CashOperationalTransaction::find($id)]
        );
    }

    /**
     * @param $id
     * @param CashDeskCrudRequest $request
     *
     * @return RedirectResponse
     * @throws Exception
     */
    public function update($id, CashDeskCrudRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['direction'] = CashOperationalTransactionTypeEnum::from($data['transaction_type'])->direction();
        $data['amount'] = floatToInt($data['amount']);
        $this->cashOperationalTransactionService->update($id, $data);

        return redirect()
            ->route($this->indexRoute, $request->integer('office_id'))
            ->with('success', __('cashdesk::cashDesk.UpdateSuccessfully'));
    }

    /**
     * @param Office $office
     * @param CashDeskSearchRequest $request
     * @param PrepareCashDeskListAction $prepareCashDeskListAction
     * @param FormBuilder $formBuilder
     * @return View
     */
    public function refresh(
        Office                    $office,
        CashDeskSearchRequest     $request,
        PrepareCashDeskListAction $prepareCashDeskListAction,
        FormBuilder               $formBuilder,
    ): View
    {
        $request->limit = $this->getPaginationLimit();
        $dto = $prepareCashDeskListAction->execute(
            $this->cacheKey,
            $request,
            $this->getAdministratorOrFail(),
            $office,
            $formBuilder,
        );

        return view('cashdesk::cash-desk.list-table', compact('dto'));
    }

    /**
     * @throws NotFoundException
     */
    public function getModal(CashDeskModalRequest $request): View
    {
        $validated = $request->validated();
        $office = $this->officeService->getOfficeById($validated['office_id']);

        $dto = ModalDTO::from([
            'offices' => $this->getAdministratorOrFail()->offices,
            'selectedOffice' => $office,
        ]);

        return view('cashdesk::cash-desk.modals.' . $validated['modalName'], compact('dto'));
    }

    /**
     * @throws Throwable
     * @throws NotFoundException
     */
    public function getInitBalanceModal(
        CashDeskInitBalanceModalRequest       $request,
        PrepareCashDeskInitBalanceModalAction $prepareInitBalanceModalAction,
    ): View
    {
        $dto = $prepareInitBalanceModalAction->execute($request, $this->getAdministratorOrFail());
        $dto->balance = intToFloat($dto->balance);

        return view('cashdesk::cash-desk.modals.' . $request->get('modalName'), compact('dto'));
    }


    /**
     * @throws ProblemException
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function export(
        Office                       $office,
        CashDeskSearchRequest        $request,
        CashBalanceExportFileService $cashBalanceExportFileService,
    )
    {
        $cashBalanceExportFileService->execute(
            $request,
            $this->getAdministratorOrFail(),
            $office
        );

        return $this
            ->getStorageService()
            ->download(
                $cashBalanceExportFileService->getFileName(),
                ['collectionClass' => $cashBalanceExportFileService->getOfficeBalanceExport()]
            );
    }

    /**
     * @throws ProblemException
     */
    public function download(int $id)
    {
        return $this->cashOperationalDocumentFilePathService->downloadByIdTransaction($id);
    }

    public function getDocument(CashOperationalDocument $cashOperationalDocument)
    {
        return response()->file(
            $this->cashOperationalDocumentFilePathService
                ->openInNewTab($cashOperationalDocument)
        );
    }

    public function deleteTransaction(CashOperationalTransaction $cashTransaction)
    {
        $payment = $cashTransaction->payment;

        if (empty($payment->payment_id)) {
            return redirect()
                ->back()
                ->with('fail', __('cashdesk::cashDesk.noPaymentForTransaction'));
        }

        if (!$payment->canBeDeleted()) {
            return redirect()
                ->back()
                ->with('fail', __('cashdesk::cashDesk.forbidenDeletingTransaction'));
        }

        DB::beginTransaction();
        try {

            $paymentService = app(PaymentService::class);
            $paymentService->rollBack($payment);

            DB::commit();

            return back()->with('success', __('cashdesk::cashDesk.successDeletingTransaction'));

        } catch (\Throwable $e) {
            DB::rollBack();

            Log::debug($e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            return back()->with('fail', __('cashdesk::cashDesk.errorDeletingTransaction') . $e->getMessage() . ' ' . $e->getFile() . ':' . $e->getLine());
        }
    }


    public function pingFpDeviceServer(Office $office): RedirectResponse
    {
        if (!$office->exists) {
            return back()->with('fail', __('cashdesk::cashDesk.invalidOffice'));
        }

        if (!$office->fiscalDevice) {
            return back()->with('fail', __('cashdesk::cashDesk.invalidFpDevice'));
        }

        try {
            $resp = Http::timeout(2)->baseUrl("{$office->fiscalDevice->server_ip}:4444")->get('/');
            if ($resp->status() === 200) {
                return back()->with('success', __('cashdesk::cashDesk.pingFpServerSuccess'));
            }
        } catch (Exception $exception) {
            Log::channel('fiscalDevice')->error($exception->getMessage());

            return back()->with('fail', __('cashdesk::cashDesk.pingFpServerError'));
        }

        return back()->with('fail', __('cashdesk::cashDesk.pingFpServerError'));
    }
}
