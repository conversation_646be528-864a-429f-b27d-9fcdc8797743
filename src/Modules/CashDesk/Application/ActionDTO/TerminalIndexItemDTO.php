<?php

namespace Modules\CashDesk\Application\ActionDTO;

use Carbon\Carbon;
use Modules\CashDesk\Enums\TerminalLogStatusEnum;
use Modules\CashDesk\Models\TerminalLog;
use Spatie\LaravelData\Data;

class TerminalIndexItemDTO extends Data
{
    public function __construct(
        public Carbon $createdAt,
        public string $id,
        public float $amount,
        public array $details,
        public string $printingTypeLabel,
        public TerminalLogStatusEnum $status,
        public string $statusLabel,
        public ?string $statusTrClass,
        public ?string $comment = null,
        public ?int $officeId = null,
        public ?string $officeLabel = null,
        public bool $isReprint = false,
        public ?int $fiscalDeviceId = null
    ) {
    }

    public static function fromModel(TerminalLog $terminalLog): TerminalIndexItemDTO
    {
        $office = $terminalLog->fiscalDevice?->office;
        return self::from([
            'createdAt' => $terminalLog->created_at,
            'id' => $terminalLog->terminal_log_id,
            'details' => $terminalLog->details,
            'amount' => self::getAmount($terminalLog),
            'printingTypeLabel' => $terminalLog->printing_type->getLabel(),
            'status' => $terminalLog->status,
            'statusLabel' => $terminalLog->status->getLabel(),
            'comment' => $terminalLog->comment,
            'statusTrClass' => $terminalLog->status->getTrClass(),
            'officeId' => $office?->office_id,
            'officeLabel' => $office?->name,
            'fiscalDeviceId' => $office?->fiscalDevice?->fiscal_device_id,
            'isReprint' => $terminalLog->status->isReprint() && $terminalLog->printing_type->isReprint()
        ]);
    }

    /**
     * @param TerminalLog $terminalLog
     * @return string
     */
    private static function getAmount(TerminalLog $terminalLog): float
    {
        if ($terminalLog->printing_type->name !== 'fiscalReceipt') {
            return $terminalLog->amount;
        }

        if (!empty($terminalLog->details['products'])) {
            $prices = array_column($terminalLog->details['products'], 'Price');
            $sumPrice = array_sum($prices);

            return (float) $sumPrice;
        }

        return 0;
    }
}
