<?php

namespace Modules\CashDesk\Application\ActionDTO;

use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\CashDesk\FilterForms\CashDeskListFilterForm;
use Modules\Common\Models\FiscalDevice;
use Modules\Common\Models\Office;
use Spatie\LaravelData\Data;

class ListDTO extends Data
{
    public function __construct(
        public string $cacheKey,
        public CashDeskListFilterForm $form,
        public ?FiscalDevice $fiscalDevice,
        /**
         * @var array<Office>|EloquentCollection
         */
        public array|EloquentCollection $offices = new EloquentCollection(),
        /**
         * @var array<string>
         */
        public array $transactionTypes = [],
        /**
         * @var array<ListItemDTO>|LengthAwarePaginator
         */
        public ?LengthAwarePaginator $cashTransactions = null,
        public ?object $balance = null,
        public ?int $selectedOfficeId = null,
    ) {
    }
}