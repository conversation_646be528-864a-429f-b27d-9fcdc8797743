<?php

namespace Modules\CashDesk\Application\ActionDTO;

use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use <PERSON><PERSON>\LaravelData\Data;

class ListItemDTO extends Data
{
    public function __construct(
        public ?int $loanId,
        public string $directionClassName,
        public string $cashTransactionNonActiveClassName,
        public ?string $createdFormat,
        public bool $cashTransactionIsIn,
        public bool $cashTransactionIsOut,
        public ?string $routeCashDeskGetDocument,
        public ?string $routeHeadClientWithLoan,
        public ?string $routeClientWithoutLoan,
        public ?string $routeTransactionEdit,
        public string $tabTransactionEdit,
        public string $amount,
        public ?string $fromToWhom,
        public ?string $transactionTypeValue,
        public string $creatorFullName,
        public ?string $clientLoanFullName,
        public ?CashOperationalTransaction $object = null,
    ) {
    }

    public static function fromModel(CashOperationalTransaction $cashTransaction): ListItemDTO
    {
        $directionCssClass = $cashTransaction->direction ? 'cash-transaction-direction-' . $cashTransaction->direction->value : '';
        if ($cashTransaction->direction->value == 'in' && $cashTransaction->amount < 0) {
            $directionCssClass = 'cash-transaction-direction-in-negative';
        }

        $cashTransactionLoan = $cashTransaction->loan;
        $cashTransactionLoanClient = $cashTransactionLoan?->client;
        $directionClassName = $directionCssClass;
        $cashTransactionNonActiveClassName = $cashTransaction->active ?? 'not-active';
        $createdFormat = formatDate($cashTransaction->created_at, 'd/m/Y H:i:s');
        $cashTransactionIsIn = $cashTransaction->transaction_type->isIn();
        $cashTransactionIsOut = $cashTransaction->transaction_type->isOut();

        $routeHeadClientWithLoan = self::routeHeadClientWithLoan(
            $cashTransactionLoan,
            $cashTransactionLoanClient,
            $cashTransactionIsIn,
        );

        $routeCashDeskGetDocument = self::routeCashDeskGetDocument($cashTransaction);

        $routeClientWithoutLoan = self::routeClientWithoutLoan(
            $cashTransactionLoan,
            $cashTransactionLoanClient,
            $cashTransactionIsIn,
        );

        $routeTransactionEdit = route('payment.cashDesk.edit', $cashTransaction->cash_operational_transaction_id);
        $tabTransactionEdit = '_self';
        if ($cashTransaction->transaction_type->isInstallmentOrTax()) {
            $routeTransactionEdit = '';
            $tabTransactionEdit = '_blank';
        } elseif ($cashTransaction->client_id && $cashTransaction->transaction_type === CashOperationalTransactionTypeEnum::LOAN_PAYOUT) {
            $routeTransactionEdit = route(
                'head.client-without-loan.index',
                [$cashTransaction->client_id, '#payment_schedule']
            );
            $tabTransactionEdit = '_blank';
        }

        $creator = $cashTransaction->creator?->getFullNames();
        if (empty($creator)) {
            $creator = 'Undefinded admin';
        }

        $amount = $cashTransaction->amount;
        $fromToWhom = $cashTransaction->from_to_whom;
        $loanId = $cashTransactionLoan?->getKey();
        $transactionTypeValue = $cashTransaction->transaction_type->value;
        $creatorFullName = $creator;
        $clientFullName = $cashTransactionLoanClient?->getFullName();
        $object = $cashTransaction;

        return self::from(
            compact(
                'loanId',
                'directionClassName',
                'cashTransactionNonActiveClassName',
                'createdFormat',
                'cashTransactionIsIn',
                'cashTransactionIsOut',
                'routeCashDeskGetDocument',
                'routeHeadClientWithLoan',
                'routeClientWithoutLoan',
                'routeTransactionEdit',
                'tabTransactionEdit',
                'amount',
                'fromToWhom',
                'transactionTypeValue',
                'creatorFullName',
                'clientFullName',
                'object',
            )
        );
    }

    private static function routeHeadClientWithLoan(
        ?Loan $cashTransactionLoan,
        ?Client $cashTransactionLoanClient,
        bool $cashTransactionIsIn
    ): ?string {
        $routeHeadClientWithLoan = null;
        if ($cashTransactionLoan && $cashTransactionLoanClient && $cashTransactionIsIn) {
            $routeHeadClientWithLoan = route('head.client-with-loan.index', [
                $cashTransactionLoanClient->getKey(),
                $cashTransactionLoan->getKey(),
                '#payment_schedule'
            ]);
        }

        return $routeHeadClientWithLoan;
    }

    private static function routeCashDeskGetDocument(CashOperationalTransaction $cashTransaction): ?string
    {
        $routeCashDeskGetDocument = null;

        $doc = $cashTransaction->latestCashOperationalDocument();
        if (!empty($doc->cash_operational_documents_id)) {
            $routeCashDeskGetDocument = route(
                'payment.cashDesk.getDocument',
                $doc->cash_operational_documents_id
            );
        }

        return $routeCashDeskGetDocument;
    }

    private static function routeClientWithoutLoan(
        ?Loan $cashTransactionLoan,
        ?Client $cashTransactionLoanClient,
        bool $cashTransactionIsIn
    ): ?string {
        $routeClientWithoutLoan = null;
        if ($cashTransactionIsIn && $cashTransactionLoan && $cashTransactionLoanClient) {
            $routeClientWithoutLoan = route('head.client-without-loan.index', [$cashTransactionLoanClient->getKey()]);
        }

        return $routeClientWithoutLoan;
    }
}
