<?php

namespace Modules\CashDesk\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\CashDesk\Models\CashOperationalTransaction;

class RemoveLoanIdCashOperationalTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        DB::table(CashOperationalTransaction::getTableName())->whereNotIn('transaction_type', [
            CashOperationalTransactionTypeEnum::LOAN_PAYMENT->value,
            CashOperationalTransactionTypeEnum::LOAN_PAYOUT->value,
            CashOperationalTransactionTypeEnum::SERVICE_FEE->value,
        ])->update(['loan_id' => null]);
    }
}
