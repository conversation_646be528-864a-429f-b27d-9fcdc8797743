<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Database\Libraries\BaseMigration;

return new class extends BaseMigration {
    public function up()
    {
        Schema::create('terminal_logs', function (Blueprint $table) {
            $table->uuid('terminal_log_id')->unique();
            $this->refBigInt($table, 'client_id', 'client', 'client_id')->nullable();
            $this->refBigInt($table, 'loan_id', 'loan', 'loan_id')->nullable();
            $this->refBigInt($table, 'fiscal_device_id', 'fiscal_device', 'fiscal_device_id');

            $table->float('amount')->comment('summary amount');
            $table->json('details')->comment('options to generate receipt');
            $table->string('printing_type')->comment('receipt printing type. See: TeminalPrintingTypeEnum');
            $table->string('status')->comment('processing status. See: TerminalLogStatusEnum');
            $table->string('processing_stack')->comment('how to process driver TremolProcessingStackEnum');
            $table->text('comment')->nullable()->comment('here we can write an error if occurs');
            $table->json('error_trace')->nullable()->comment('error trace');

            $this->created($table);
            $this->deleted($table);
            $this->updated($table);
            $this->dateAuthorFields('confirm', $table, 'print receipt confirmation from terminal ');

            $table->integer('cash_operational_transaction_id')
                ->nullable()
                ->comment('Log asynchronous, but connection not available reference');

            $table->comment('Saving the log of data exchange with the terminal');
        });
    }

    public function down()
    {
        Schema::dropIfExists('terminal_logs');
        Schema::dropIfExists('terminals');
    }
};