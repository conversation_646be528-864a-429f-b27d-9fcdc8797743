<?php

namespace Modules\Collect\FilterForms;

use Carbon\Carbon;
use Kris\LaravelFormBuilder\Field;
use Modules\Common\FilterForms\BaseFilterForm;
use Modules\Common\Repositories\ConsultantRepository;

class CollectedInstallmentFilterForm extends BaseFilterForm
{
    protected static string $route = 'collect.collected-installments.list';

    protected static string $exportRoute = 'collect.collected-installments.export';

    public function buildForm(): void
    {
        $this->add('for_month', Field::TEXT, [
            'value' => $this->request->get('for_month', Carbon::now()->format('m-Y')),
            'attr' => [
                'data-year-month-picker' => 'true',
            ]
        ]);

        $this->addOfficeIdFilter('office_id');
        $this->modify('office_id', Field::SELECT, [
            'empty_value' => '',
            'attr' => [
                'data-live-search' => 'true',
                'data-actions-box' => 'true',
                'multiple' => 'true',
            ]
        ]);

        $consultantOptions = app(ConsultantRepository::class)->getBuilderByCurrentAdmin()
            ->get()->pluck('name', 'consultant_id')->toArray();

        $this->add('consultant_id', Field::SELECT, [
            'label' => __('table.Consultant'),
            'choices' => $consultantOptions,
            'empty_value' => '',
            'selected' => $this->request->get('consultant_id'),
            'attr' => [
                'data-live-search' => 'true',
//                'data-actions-box' => 'true',
                'multiple' => 'true',
            ]
        ]);
    }
}