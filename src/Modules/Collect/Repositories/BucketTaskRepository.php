<?php

namespace Modules\Collect\Repositories;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Modules\Admin\Traits\AdminTrait;
use Modules\Collect\Http\Dto\CloseBucketTaskDto;
use Modules\Common\Models\BucketTask;
use Modules\Common\Models\BucketTaskHistory;
use Modules\Common\Models\CollectorDecision;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanBucket;
use Modules\Common\Repositories\BaseRepository;

final class BucketTaskRepository extends BaseRepository
{
    use AdminTrait;

    public function getByFiltersPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getBuilderByFilters($filters)->paginate($this->getPaginationLimit());
    }

    public function getBuilderByFilters(array $filters = []): Builder
    {
        // we have overdue amount and days  and paid_installments_count in bucket_task
        // but still take it from stats, to have fresh info
        $builder = BucketTask::select(DB::raw("
            bucket_task.bucket_task_id,
            bucket_task.created_at,
            bucket_task.bucket_id,
            CASE
                WHEN bucket_task.bucket_id = 0 THEN 'Предстоящо плащане'
                ELSE 'Просрочие'
            END as task_type,
            bucket_task.loan_id,
            bucket_task.client_id,
            product.name as product_name,
            loan.amount_approved as loan_amount,
            loan.period_approved as loan_period,
            loan.product_type_id,
            client_actual_stats.repaid_loans_count as repaid_loans_count,
            bucket_task.client_full_name,
            bucket_task.pin,
            bucket_task.phone,
            CASE
                WHEN bucket_task.bucket_id = 0 THEN CAST(bucket_task.overdue_amount AS float) / 100
                ELSE loan_actual_stats.current_overdue_amount
            END as amount_overdue,
            CASE
                WHEN bucket_task.bucket_id = 0 THEN '0'
                ELSE COALESCE(loan_actual_stats.current_overdue_days, 0)
            END as days_overdue,
            loan_actual_stats.first_installment_date,
            bucket_task.status,
            bucket_task.processed_by,
            CONCAT_WS(' ', administrator.first_name, administrator.last_name) as admin_name,
            collector_decision.name as last_decision
        "));

        $builder->where('loan.outer_collector', '!=', 1);

        if (!empty($filters['show_after'])) {
            $builder->where('bucket_task.show_after', '<=', $filters['show_after']);
        }
        if (!empty($filters['bucketOfficeIds'])) {
            $builder->whereIn('loan.office_id', $filters['bucketOfficeIds']);
        }
        if (!empty($filters['bucketOfficeId'])) {
            $builder->where('loan.office_id', '=', $filters['bucketOfficeId']);
        }
        if (!empty($filters['bucket_task_status_not'])) {
            $builder->where('bucket_task.status', '!=', $filters['bucket_task_status_not']);
        } else if (!empty($filters['bucket_task_status'])) {
            $builder->where('bucket_task.status', $filters['bucket_task_status']);
        }
        if (!empty($filters['bucket_pin'])) {
            $builder->where('client.pin', '=', $filters['bucket_pin']);
        }
        if (!empty($filters['loan_id'])) {
            $builder->where('bucket_task.loan_id', $filters['loan_id']);
        }
        if (!empty($filters['bucketClientPhone'])) {
            $builder->where('bucket_task.phone', $filters['bucketClientPhone']);
        }
        if (!empty($filters['product_id'])) {
            $builder->where('product.product_id', $filters['product_id']);
        }
        if (isset($filters['bucket_id']) && is_array($filters['bucket_id'])) {
            $builder->whereIn('bucket_task.bucket_id', $filters['bucket_id']);
        }
        if (!empty($filters['repaid_credits_count'])) {
            $builder->where('client_actual_stats.repaid_loans_count', $filters['repaid_credits_count']);
        }
        if (!empty($filters['period'])) {
            $builder->where('bucket_task.period', $filters['period']);
        }
        if (!empty($filters['period'])) {
            $builder->where('bucket_task.period', $filters['period']);
        }
        if (!empty($filters['collector_decision_id'])) {
            $builder->where('bucket_task.prev_decision_id', $filters['collector_decision_id']);
        }
        if (!empty($filters['bucketClientNames'])) {
            $builder->whereRaw("bucket_task.client_full_name ILIKE '" . $filters['bucketClientNames'] . "%'");
        }
        if (!empty($filters['amount_from'])) {
            $builder->where('bucket_task.amount', '>=', floatToInt($filters['amount_from']));
        }
        if (!empty($filters['amount_to'])) {
            $builder->where('bucket_task.amount', '<=', floatToInt($filters['amount_to']));
        }
        if (!empty($filters['overdue_amount_from'])) {
            $builder->where('loan_actual_stats.current_overdue_amount', '>=', $filters['overdue_amount_from']);
        }
        if (!empty($filters['overdue_amount_to'])) {
            $builder->where('loan_actual_stats.current_overdue_amount', '<=', $filters['overdue_amount_to']);
        }
        if (!empty($filters['overdue_days_from'])) {
            $builder->where('loan_actual_stats.current_overdue_days', '>=', $filters['overdue_days_from']);
        }
        if (!empty($filters['overdue_days_to'])) {
            $builder->where('loan_actual_stats.current_overdue_days', '<=', $filters['overdue_days_to']);
        }

        $builder->whereRaw('loan.office_id IN (select ao.office_id from administrator_office ao where ao.administrator_id = ' . getAdminId() . ')');

        return $builder
            ->leftJoin('loan','loan.loan_id', '=', 'bucket_task.loan_id')
            ->leftJoin('client','client.client_id', '=', 'bucket_task.client_id')
            ->leftJoin('product','product.product_id', '=', 'bucket_task.product_id')
            ->leftJoin('loan_actual_stats','loan_actual_stats.loan_id', '=', 'bucket_task.loan_id')
            ->leftJoin('client_actual_stats','client_actual_stats.client_id', '=', 'bucket_task.client_id')
            ->leftJoin('administrator','administrator.administrator_id', '=', 'bucket_task.processed_by')
            ->leftJoin('collector_decision', 'collector_decision.collector_decision_id', '=', 'bucket_task.prev_decision_id')
            ->orderByRaw('bucket_task.prev_decision_id = ' . CollectorDecision::CALL_LATER . ' desc nulls last')
            ->orderBy('bucket_task.show_after', 'ASC')
            ->orderBy('bucket_task.overdue_days_count', 'ASC');
    }

    /**
     * @param null|int $limit
     * @param array $joins
     * @param array $where
     * @param array|string[] $order
     * @param bool $showDeleted
     *
     * @return mixed
     */
    public function getAll(
        int   $limit,
        array $where = [],
        array $order = [
            'bucket_task.active' => 'DESC',
            'bucket_task.bucket_task_id' => 'ASC',
            'bucket_task.show_after' => 'ASC'
        ]
    ) {
        // first we check if any office attached to logged-in administrator
        // by getting his office IDs
        $adminOfficeIds = $this->getAdminOfficeIds();
        if (empty($adminOfficeIds)) {
            return [];
        }

        $builder = DB::table('bucket_task');

        // select the fields we need
        $builder->select(
            DB::raw(
                "
                bucket_task.*,
                bucket.bucket_id,
                office.name AS office_name,
                EXTRACT(EPOCH FROM (NOW()::timestamp - bucket_task.created_at)) AS time_difference,
                product.name AS product_name,
                bucket_task.details AS collector_attempt_details,
                collector_decision.collector_decision_id AS collector_decision_id,
                collector_decision.name AS decision_name,
                bucket_task.created_at AS last_decision_date,
                concat_ws( ' ', administrator.first_name,administrator.last_name) as process_by,
                CASE
                    WHEN bucket_task.parent_bucket_task_history_id IS NOT NULL THEN bucket_task.show_after
                    ELSE bucket_task.created_at
                END as skip_till,
                CASE
                    WHEN collector_decision.collector_decision_id = 4 THEN 1
                    ELSE 0
                END as is_call_later
            "
            )
        );

        $builder->join('loan', 'loan.loan_id', '=', 'bucket_task.loan_id');
        $builder->join('office', 'office.office_id', '=', 'loan.office_id');
        $builder->join('product', 'product.product_id', '=', 'loan.product_id');
        $builder->leftJoin('bucket', 'bucket.bucket_id', '=', 'bucket_task.bucket_id');
        $builder->leftJoin('collector_decision', 'collector_decision.collector_decision_id', '=', 'bucket_task.collector_decision_id');
        $builder->leftJoin('administrator', 'administrator.administrator_id', '=', 'bucket_task.processed_by');

        // add where conditions
        if (!empty($where)) {
            $builder->where($where);
        }
        $builder->whereIn('loan.office_id', $adminOfficeIds);

        // add order if provided
        if (empty($order)) {
            $builder->orderBy('bucket_task.active', 'DESC');
            $builder->orderBy('bucket_task.bucket_task_id', 'ASC');
            $builder->orderBy('bucket_task.show_after', 'ASC');
        } else {
            foreach ($order as $key => $direction) {
                $builder->orderBy($key, $direction);
            }
        }


        if (empty($limit)) {
            $records = $builder->get();
            $result = (new BucketTask())->hydrateCustom($records->toArray());
        } else {
            $result = $builder->paginate($limit);
            $records = (new BucketTask())->hydrateCustom($result->items());
            $result->setCollection($records);
        }

        return $result;
    }

    public function getById(int $bucketTaskId): ?BucketTask
    {
        return BucketTask::where(['bucket_task_id' => $bucketTaskId])->first();
    }


    public function getLastBucketTasks(Loan $loan, $limit): Collection
    {
        return BucketTaskHistory::where('loan_id', $loan->getKey())
            ->whereHas('loan', function (Builder $query) {
                $query->where('outer_collector', '!=', 1);
            })
            ->orderBy('bucket_task_history_id', 'DESC')
            ->limit($limit)
            ->get();
    }

    public function getLoansBucketCount(): int
    {
        return LoanBucket::count();
    }

    public function getCreatedCount(Carbon $createdFrom): int
    {
        return BucketTask::where('created_at', '>=', $createdFrom)->count();
    }

    public function save(BucketTask $bucketTask): ?BucketTask
    {
        return $bucketTask->save() ? $bucketTask : null;
    }

    public function delete(BucketTask $task): BucketTaskHistory
    {
        $history = new BucketTaskHistory();
        $history->fill($task->getAttributes());
        $history->save();
        $task->delete();
        return $history;
    }

    public function getByLoanId(int $loanId): ?BucketTask
    {
        return BucketTask::where(['loan_id' => $loanId])->first();
    }

    public function getHistoryById(int $bucketTaskId, int $collctorDesicionId): array
    {
        return DB::select(DB::raw("
            WITH RECURSIVE TaskHierarchy AS (
                SELECT bucket_task_id, parent_id
                FROM bucket_task_history
                WHERE bucket_task_id = (SELECT parent_id FROM bucket_task WHERE bucket_task_id = " . $bucketTaskId . ")
                UNION ALL
                SELECT bt.bucket_task_id, bt.parent_id
                FROM TaskHierarchy th
                JOIN bucket_task_history bt ON th.parent_id = bt.bucket_task_id
            )
            SELECT bth.*
            FROM TaskHierarchy th
            JOIN bucket_task_history bth ON th.bucket_task_id = bth.bucket_task_id
            WHERE bth.collector_decision_id = " . $collctorDesicionId . ";
        "));
    }

    public function moveTaskToHistoryAndDeleteOriginal(int $bucketTaskId, ?CloseBucketTaskDto $closeBucketTaskDto = null)
    {
        $bucketTask = BucketTask::whereBucketTaskId($bucketTaskId)->first([
            'bucket_task_id',
            'bucket_id',
            'loan_id',
            'client_id',
            'product_id',
            'amount',
            'promised_amount',
            'promised_date',
            'details',
            'period',
            'repaid_credits_count',
            'client_full_name',
            'pin',
            'phone',
            'email',
            'overdue_amount',
            'overdue_days_count',
            'show_after',
            'started_at',
            'finished_at',
            'processed_by',
            'created_at',
            'status',
            'collector_decision_id',
            'prev_decision_id',
            'prev_promised_date',
            'prev_promised_amount',
            'prev_agent_id',
            'parent_id'
        ]);

        /// We're adding a date from DTO because the entire function is wrapped in a transaction,
        /// and the model fails to preserve the data before creating new ones.
        $bucketTask->setAttribute('promised_date', $closeBucketTaskDto?->show_after);

        BucketTaskHistory::create($bucketTask->toArray());


        DB::statement("DELETE FROM bucket_task WHERE bucket_task_id = " . $bucketTaskId);
    }
}
