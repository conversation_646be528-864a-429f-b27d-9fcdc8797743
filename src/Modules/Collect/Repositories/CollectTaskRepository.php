<?php

namespace Modules\Collect\Repositories;

use Illuminate\Support\Facades\DB;
use Modules\Common\Enums\ClientCardRouteParamEnum;
use Modules\Common\Repositories\BaseRepository;

class CollectTaskRepository extends BaseRepository
{
    /**
     * @param int $clientId
     *
     * @return array
     */
    public function getOpenTasks(int $clientId, int $limit = 5, bool $showAll = false): array
    {
        $comeFrom = ClientCardRouteParamEnum::COLLECT->value;
        $nowTimestamp = now()->toDateTimeString();

        return DB::select(
            DB::raw(
                "
                    SELECT
                        bt.*,
                        bsa.overdue_days_from AS from_days,
                        bsa.overdue_days_to AS to_days,
                        cd.name AS last_decision,
                        '$comeFrom' AS come_from
                    FROM bucket_task AS bt
                    INNER JOIN bucket AS bsa ON bsa.bucket_id = bt.bucket_id
                    INNER JOIN loan AS l ON bt.loan_id = l.loan_id
                    LEFT JOIN collector_decision as cd ON cd.collector_decision_id = bt.collector_decision_id
                    WHERE l.outer_collector != 1 AND bt.client_id = $clientId
                        " . (
                            $showAll == true
                            ? ''
                            : "
                                AND bt.show_after <= TIMESTAMP '$nowTimestamp'
                                AND (bt.show_after IS NULL OR bt.show_after <= TIMESTAMP '$nowTimestamp')
                            "
                            ) . "
                    ORDER BY created_at
                    LIMIT '$limit'
                "
            )
        );
    }
}
