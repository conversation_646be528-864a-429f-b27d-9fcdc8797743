<?php

namespace Modules\Collect\Domain\Entities;

use Illuminate\Support\Collection;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Models\Currency;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Payment;
use Modules\Common\Models\Tax;
use Modules\Common\Models\Tax as DbModel;
use Modules\Head\Repositories\InstallmentRepository;
use StikCredit\Calculators\Fees\DefaultFee;

class Fee extends DomainModel
{
    private DbModel $dbModel;
    private ?DefaultFee $calcFee = null;

    //
    public function buildFromExisting(
        DbModel $dbModel,
        Collection $fees
    ): self
    {
        return $this
            ->setDbModel($dbModel)
            ->setCalcFee($fees);
    }

    public function createForExtension(
        int $amount,
        Loan $loan,
        int $daysToExtend,
        ?Payment $payment = null
    ): self {

        $installment = $loan->getFirstInstallmentForExtend();

        $tax = new Tax();
        $tax->client_id = $loan->client_id;
        $tax->loan_id = $loan->getKey();
        $tax->installment_id = $installment->getKey();
        $tax->currency_id = Currency::BGN_CURRENCY_ID;
        $tax->date_from = date('Y-m-d'); // always today, so the payment can handle it
        $tax->due_date = date('Y-m-d'); // always today, so the payment can handle it
        $tax->type = Tax::TAX_TYPE_LOAN_EXTENSION_FEE;
        $tax->comment = __('head::clientCard.extend');
        $tax->status = Tax::TAX_STATUS_SCHEDULED;
        $tax->extended_days = $daysToExtend;
        $tax->amount = $amount;
        $tax->rest_amount = $amount;
        $tax->paid_amount = 0;
        $tax->created_by = getAdminId();
        if ($payment) {
            $tax->created_from_payment_id = $payment->getKey();
        }
        $tax->save();

        return $this->setDbModel($tax);
    }

    private function setDbModel(DbModel $dbModel): self
    {
        $this->dbModel = $dbModel;
        return $this;
    }

    private function setCalcFee(Collection $calcFees): self
    {
        /** @var DefaultFee $calcFee */
        foreach ($calcFees as $calcFee) {
            if ($calcFee->feeId == $this->dbModel->getKey()) {
                $this->calcFee = $calcFee;
                return $this;
            }
        }
        return $this;
    }

    public function new()
    {
        return app(self::class);
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
