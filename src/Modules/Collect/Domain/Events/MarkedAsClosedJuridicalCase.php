<?php

namespace Modules\Collect\Domain\Events;

use App\Providers\EventServiceProvider;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\Accounting\Listeners\ClosedJuridicalCase;
use Modules\Common\Models\Loan;
use Modules\Head\Repositories\LoanRepository;

/**
 * @see LoanRepository::markAsClosedJuridicalCase()
 * @see EventServiceProvider
 * @see ClosedJuridicalCase::handle()
 */
class MarkedAsClosedJuridicalCase
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public Loan $loan
    )
    {
    }
}
