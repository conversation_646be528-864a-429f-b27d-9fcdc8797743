<?php

namespace Modules\Collect\Tests\Integration\Repositories;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Collect\Database\Seeders\Test\BucketTaskRepositorySeeder;
use Modules\Collect\Repositories\BucketTaskRepository;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Http\Dto\DateRangeDto;
use Modules\Common\Models\Bucket;
use Modules\Common\Models\BucketTask;
use Modules\Common\Models\BucketTaskHistory;
use Modules\Common\Models\Installment;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanActualStats;
use Tests\TestCase;

class BucketTaskRepositoryTest extends TestCase
{
    use DatabaseTransactions;
    private BucketTaskRepository $sut;
    private CurrentDate $currentDate;

    public function setUp(): void
    {
        parent::setUp();
        $this->seed(BucketTaskRepositorySeeder::class);
        $this->sut = new BucketTaskRepository();
        $this->currentDate = new CurrentDate(BucketTaskRepositorySeeder::CURRENT_DATETIME);

    }

    public function testDelete()
    {
        $bt = BucketTask::where(['loan_id'=>6])->first();
        $this->sut->delete($bt);
        $btn = BucketTask::where(['loan_id'=>6])->first();
        $bth = BucketTaskHistory::where(['loan_id'=>6])->first();
        $this->assertNull($btn);
        $this->assertNotNull($bth);
        $this->assertEquals(6, $bth->loan_id);
        $this->assertEquals('six Патлеев Илиев', $bth->client_full_name);
    }
}
