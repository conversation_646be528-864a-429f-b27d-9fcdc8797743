<?php

namespace Modules\Collect\Tests\Integration\Application\Action;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Modules\Collect\Application\Action\BucketTaskIndexAction;
use Modules\Collect\Database\Seeders\Test\BucketTaskSeeder;
use Modules\Collect\Database\Seeders\Test\DoneBucketTaskHistorySeeder;
use Modules\Collect\Database\Seeders\Test\LoanSeeder;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\BucketTask;
use Modules\Common\Models\CollectorDecision;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Product;
use Modules\Common\Statistics\Dashboard\BucketTasks;
use Tests\TestCase;

class GeneralViewActionTest extends TestCase
{
    use DatabaseTransactions;
    public function filterProvider()
    {
        return [
            'PIN_ONLY'=>[
                'filters'=>[
                    'pin' => '100000'
                ],
                'expectedIds'=>[1],
            ],
            'CLIENT_ONLY'=>[
                'filters'=>[
                    'client_id' => '15'
                ],
                'expectedIds'=>[15],
            ],
            'LOAN_ID_ONLY'=>[
                'filters'=>[
                    'loan_id' => 14
                ],
                'expectedIds'=>[14],
            ],
            'CLIENT_NAME_ONLY'=>[
                'filters'=>[
                    'bucket_client_names' => 'four'
                ],
                'expectedIds'=>[4, 14],
            ],
            'PHONE_ONLY'=>[
                'filers'=>['phone'=>'2222211'],
                'expectedIds'=>[11],
            ],
            'REPAID_LOANS_COUNT_ONLY'=>[
                'filers'=>['repaid_credits_count'=>5],
                'expectedIds'=>[6],
            ],
            'PRODUCT_ONLY'=>[
                'filers'=>['product_id'=>2],
                'expectedIds'=>[2,6,10,14],
            ],
            'BUCKET_ONLY'=>[
                'filers'=>['bucket_id'=>2],
                'expectedIds'=>[2,7,12],
            ],
            'STATUS_ONLY'=>[
                'filers'=>['status'=>'new'],
                'expectedIds'=>[1,3,5,7,9,11,13,15],
            ],
            'PERIOD_ONLY'=>[
                'filers'=>['period'=>14],
                'expectedIds'=>[3,7,11,15],
            ],
            'SUM_FROM_ONLY'=>[
                'filers'=>['amount_from'=>10],
                'expectedIds'=>[10,11,12,13,14,15],
            ],
            'SUM_TO_ONLY'=>[
                'filers'=>['amount_to'=>10],
                'expectedIds'=>[1,2,3,4,5,6,7,8,9,10],
            ],
            'OVERDUE_FROM_ONLY'=>[
                'filers'=>['overdue_amount_from'=>500],
                'expectedIds'=>[10,11,12,13,14,15],
            ],
            'OVERDUE_TO_ONLY'=>[
                'filers'=>['overdue_amount_to'=>500],
                'expectedIds'=>[1,2,3,4,5,6,7,8,9,10],
            ],
            'OFFICE_ONLY'=>[
                'filers'=>['bucket_office_id'=>1],
                'expectedIds'=>[1,3,5,7,9,11,13,15],
            ],
            'COLLECTOR_DECISION'=>[
                'filters'=>[
                    'collector_decision_id' => CollectorDecision::BUSY
                ],
                'expectedIds'=>[11,12,13,14,15],
            ],
            'LIMIT_ONLY'=>[
                'filters'=>['limit'=>10],
                'expectedIds'=>[1,2,3,4,5,6,7,8,9,10],
            ],
            'ALL_AT_ONCE'=>[
                'filters'=>[
                    'status'=>BucketTask::STATUS_PROCESSING,
                    'product_id'=>Product::WEB_INSTALLMENTS_ID,
                    'pin' => '10000',
                    'amount_from'=>6,
                    'amount_to'=>10,
                    'overdue_amount_from'=>300,
                    'overdue_amount_to'=>500,
                    'collector_decision_id' => CollectorDecision::CALL_LATER,
                    'limit'=>4
                ],
                'expectedIds'=>[6,10],
            ]
        ];
    }

    /**
     * @dataProvider filterProvider
     */
    public function testFilters(array $filters, array $expectedIds)
    {
        $this->seed(BucketTaskSeeder::class);
        $this->actingAs(Administrator::where(['administrator_id'=>2])->first());
        $sut = app()->make(BucketTaskIndexAction::class);
        $response = $sut->execute($filters);
        /** @var CustomEloquentCollection $tasks */
        $tasks = $response['bucketTasks']->getCollection();
        $this->assertEqualsCanonicalizing($expectedIds, $tasks->modelKeys());
    }

    public function testStatistics()
    {
        $date = Carbon::parse(DoneBucketTaskHistorySeeder::CURRENT_DATETIME);
        $this->seed(DoneBucketTaskHistorySeeder::class);
        $this->actingAs(Administrator::where(['administrator_id' => 2])->first());
        foreach (DoneBucketTaskHistorySeeder::$processedDates as $value) {
            Artisan::call('script:refresh-module-stats --day="' . $value . '"');
        }
        $currentDate = new CurrentDate($date);

        $result = (new BucketTasks())->getStatsByUser(getAdmin(), $date)->toArray();
        $this->assertEquals(2, $result["processedTasksToday"]);
        $this->assertEquals(1, $result["processedTasksLastWeek"]);
        //10 done tasks, 23 days
        $this->assertEquals(0.4, $result["processedTasksAvgMonth"]);
        $this->assertEquals(2900, $result["paymentsSumToday"]);
        $this->assertEquals(900, $result["paymentsSumLastWeek"]);
        //total sum of payments last month was 10500. Days in current month 23
        $this->assertEquals(456.5, $result["paymentsSumAvgMonth"]);
        $this->assertEquals(2, $result["promisesToday"]);
        $this->assertEquals(1, $result["promisesLastWeek"]);
        $this->assertEquals(0.4 , $result["promisesAvgMonth"]);
        $this->assertEquals(2, $result["keptPromisesToday"]);
        $this->assertEquals(1, $result["keptPromisesLastWeek"]);
        $this->assertEquals(0.4, $result["keptPromisesAvgMonth"]);
    }

    public function testOrdering()
    {
        $this->seed([
            LoanSeeder::class
        ]);
        $loans = Loan::all();
        $bucketTasks = [];
        $cd = new CurrentDate(DoneBucketTaskHistorySeeder::CURRENT_DATETIME);
        /** @var Loan $loan */
        foreach ($loans as $i=>$loan){
            $bucketTasks[] = [
                'bucket_task_id' => $i+1,
                'parent_bucket_task_history_id'=>$loan->client_id,
                'bucket_id' => $i%5+1,
                'loan_id' => $loan->loan_id,
                'client_id' => $loan->client_id,
                'product_id' => $i%4+1,
                'amount' => $loan->amount_approved,
                'period' => $loan->period_approved,
                'repaid_credits_count' => $i,
                'client_full_name' => $loan->client->getFullName(),
                'pin' => $loan->client->pin,
                'phone' => $loan->client->phone,
                'email' => $loan->client->email,
                'overdue_amount' => 5000*($i+1),//in cents
                'overdue_days_count' => $i+1,
                'collector_decision_id'=>$i%2 ? CollectorDecision::CALL_LATER : CollectorDecision::BUSY,
                'status' => [BucketTask::STATUS_NEW, BucketTask::STATUS_PROCESSING][$i%2],
                'created_at' => '2022-10-27 16:39:17',
                'show_after' => $cd->now()->addMinutes(random_int(0, 10)),
            ];
        }
        DB::table('bucket_task')->insert($bucketTasks);
        $this->actingAs(Administrator::where(['administrator_id'=>2])->first());
        $sut = app()->make(BucketTaskIndexAction::class);
        $response = $sut->execute();
        /** @var CustomEloquentCollection $tasks */
        $tasks = $response['bucketTasks']->getCollection();
        $prevDate = new CurrentDate(DoneBucketTaskHistorySeeder::CURRENT_DATETIME);
        /** @var BucketTask $task */
        foreach ($tasks as $i=>$task){
            //CALL_LATER have bigger priority
            $expectedDecision = $i < 7 ? CollectorDecision::CALL_LATER : CollectorDecision::BUSY;
            if($i== 7){
                $prevDate = new CurrentDate(DoneBucketTaskHistorySeeder::CURRENT_DATETIME);
            }
            $expectedBiggerDate = new CurrentDate($task->show_after);
            $this->assertEquals($expectedDecision, $task->collector_decision_id);
            //EACH NEXT SHOW_DATE SHOULD BE BIGGER THAN PREVIOUS (if same priority)
            $this->assertGreaterThanOrEqual($prevDate, $expectedBiggerDate);
            $prevDate = $expectedBiggerDate;
        }
    }
}
