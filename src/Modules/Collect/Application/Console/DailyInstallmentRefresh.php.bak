<?php

namespace Modules\Collect\Application\Console;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Installment;
use Symfony\Component\Console\Command\Command;
use Modules\Product\Services\ProductSettingsService;
use Throwable;

class DailyInstallmentRefresh extends CommonCommand
{
    protected $name = 'script:daily-installment-refresh';
    protected $signature = 'script:daily-installment-refresh {loan_id?}';
    protected $description = 'Refresh accrued & late amounts + installment overdue stats';

    protected int $totalEntries;
    protected int $processedEntries;
    protected array $settings = [];
    protected string $logChannel = 'daily_installment_refresh';


    public function __construct() {
        parent::__construct();
    }

    public function handle(): int
    {
        $this->startLog();

        $loanId = null;
        if (!empty($this->argument('loan_id'))) {
            $loanId = (int)$this->argument('loan_id');
        }

        try {

            $this->settings = app(ProductSettingsService::class)->getAllProductSettingsForDailyRecalc();

            $chunk = 10000;
            $done  = 0;
            $total = $this->getTotalCountForHandling($loanId);
            $this->log("Total rows to be updated: " . $total);

            if ($total > 0) {
                $pages = ceil($total / $chunk);
                $this->log("Total chunks: " . $pages);

                for ($i = 1; $i < ($pages + 1); $i++) {
                    $offset = (($i - 1)  * $chunk);
                    $start  = microtime(true);

                    $installmentsChunk = DB::select(DB::raw("
                        select
                            firstRes.*,
                            case
                                when firstRes.type = 'accrued' then EXTRACT(DAY FROM (current_date - firstRes.installment_start_date))
                                else null
                            end AS days_used,
                            case
                                when firstRes.type = 'accrued' then EXTRACT(DAY FROM (firstRes.due_date - firstRes.installment_start_date))
                                else null
                            end AS days_total,
                            case
                                when firstRes.type = 'late' then EXTRACT(DAY FROM (current_date - firstRes.due_date::timestamp))
                                else null
                            end AS days_overdue,
                            (select ROUND((sum(t.amount) - sum(t.paid_amount)), 2) from tax t where t.installment_id = firstRes.installment_id and t.paid = 0) as collector_tax_amount
                        from (
                            select
                                l.product_id,
                                i.installment_id,
                                i.loan_id,
                                i.seq_num,
                                i.due_date,
                                i.accrued_total_amount,
                                ROUND(((i.principal + i.interest + i.penalty) - (i.paid_principal + i.paid_interest + i.paid_penalty)), 2) as primary_total_amount,
                                i.principal,
                                i.paid_principal,
                                i.rest_principal,
                                i.accrued_interest,
                                i.interest,
                                i.late_interest,
                                i.paid_accrued_interest,
                                i.paid_interest,
                                i.rest_interest,
                                i.paid_late_interest,
                                i.accrued_penalty,
                                i.penalty,
                                i.late_penalty,
                                i.paid_accrued_penalty,
                                i.paid_penalty,
                                i.rest_penalty,
                                i.paid_late_penalty,
                                i.max_overdue_days,
                                i.max_overdue_amount,
                                i.max_overdue_date,
                                i.rest_late_penalty,
                                i.rest_late_interest,
                                i.accrued_updated_at,
                                i.accrued_updated_log,
                                i.late_updated_at,
                                i.late_updated_log,
                                i.overdue_days,
                                i.overdue_amount,
                                i.max_overdue_days,
                                i.max_overdue_amount,
                                i.max_overdue_date,
                                case
                                    when i.due_date >= current_date then 'accrued'
                                    else 'late'
                                end as type,
                                case
                                    when i.seq_num = 1 then coalesce(l.activated_at, l.created_at)
                                    else (select i2.due_date from installment i2 where i2.loan_id = i.loan_id and i2.seq_num < i.seq_num order by i2.seq_num desc limit 1)
                                end as installment_start_date
                            from installment i
                            join loan l on l.loan_id = i.loan_id and l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                            where
                                i.paid = 0
                                and (i.due_date <= (current_date + interval '1 month')::date or i.seq_num = 1)
                                " . (!empty($loanId) ? " and i.loan_id = " . $loanId : "" ) . "
                        ) as firstRes
                        order by firstRes.installment_id
                        offset " . $offset . "
                        limit " . $chunk . ";"
                    ));

                    $done += $this->updateInstallments($installmentsChunk);

                    $this->log("--- Handled: " . $offset . ' = ' . round((microtime(true) - $start), 2) . ' sec');
                }
            }


        } catch (Throwable $t) {
            $this->log(
                'Installment state refresh failed. Error' . $t->getMessage()
                . ', file: ' . $t->getFile() . ', line: ' . $t->getLine()
            );
        }

        $msg = 'Finished installment state refresh. Refreshed: ' . $done;
        $logMessages = [
            $msg,
            $this->executionTimeString()
        ];
        $this->finishLog($logMessages, $total, $done, $msg);


        return Command::SUCCESS;
    }

    private function getTotalCountForHandling(?int $loanId = null)
    {
        $todoCount = DB::selectOne("
            select count(i.installment_id) as count
            from installment i
            join loan l on l.loan_id = i.loan_id and l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
            where
                i.paid = 0
                and (i.due_date <= (current_date + interval '1 month')::date or i.seq_num = 1)
                " . (!empty($loanId) ? " and i.loan_id = " . $loanId : "" ) . "
        ");

        return $todoCount->count ?? 0;
    }

    private function updateInstallments(array $installments): int
    {
        $changesCommon = [];
        foreach ($installments as $installment) {

            $loanSetting = [];
            if (isset($this->settings[$installment->product_id])) {
                $loanSetting = $this->settings[$installment->product_id];
            }

            $changes = $this->prepareInstallmentChanges(
                $installment,
                $loanSetting
            );
            if (!empty($changes)) {
                $changesCommon[] = $changes;
            }
        }

        if (!empty($changesCommon)) {
            try {
                DB::statement("
                    update installment as i
                    set
                        accrued_interest = newdata.accrued_interest,
                        accrued_penalty = newdata.accrued_penalty,
                        accrued_total_amount = newdata.accrued_total_amount,
                        accrued_updated_at = newdata.accrued_updated_at::timestamp,
                        accrued_updated_log = newdata.accrued_updated_log::json,
                        late_interest = newdata.late_interest,
                        late_penalty = newdata.late_penalty,
                        rest_late_interest = newdata.rest_late_interest,
                        rest_late_penalty = newdata.rest_late_penalty,
                        overdue_days = newdata.overdue_days,
                        overdue_amount = newdata.overdue_amount,
                        max_overdue_days = newdata.max_overdue_days,
                        max_overdue_amount = newdata.max_overdue_amount,
                        max_overdue_date = newdata.max_overdue_date::date,
                        status = newdata.status,
                        late_updated_at = newdata.late_updated_at::timestamp,
                        late_updated_log = newdata.late_updated_log::json
                    from
                        ( values
                            " . implode(',' , $changesCommon ) . "
                        ) as newdata (
                            installment_id,
                            accrued_interest,
                            accrued_penalty,
                            accrued_total_amount,
                            accrued_updated_at,
                            accrued_updated_log,
                            late_interest,
                            late_penalty,
                            rest_late_interest,
                            rest_late_penalty,
                            overdue_days,
                            overdue_amount,
                            max_overdue_days,
                            max_overdue_amount,
                            max_overdue_date,
                            status,
                            late_updated_at,
                            late_updated_log
                        )
                    where i.installment_id = newdata.installment_id ;
                ");
            } catch (Throwable $e) {
                $this->log(
                    "Failed to update installments"
                    . ', Msg:' . $e->getMessage()
                    . ', file: ' . $e->getFile()
                    . ', line: ' . $e->getLine()
                );

                return 0;

            }
        }


        $countDone = count($changesCommon);
        $this->log("installment(s) updated: " . $countDone);


        unset($installments);
        unset($changesCommon);


        return $countDone;
    }

    private function prepareInstallmentChanges($instDb, array $loanSettings): string {

        if ($instDb->type == 'accrued') {

            $totalInstallmentDays = $instDb->days_total;
            $totalUsageDays = $instDb->days_used;
            $newInterest = ($totalInstallmentDays == 0 ? $instDb->interest : $instDb->interest / $totalInstallmentDays * $totalUsageDays);
            $newPenalty = ($totalInstallmentDays == 0 ? $instDb->penalty :$instDb->penalty / $totalInstallmentDays * $totalUsageDays);

            $problems = [];
            if ($newInterest < 0) {
                $problems[] = 'set acc.interest to 0, = ' . $newInterest;
                $newInterest = 0;
            }
            if ($newPenalty < 0) {
                $problems[] = 'set acc.penalty to 0, = ' . $newPenalty;
                $newPenalty = 0;
            }


            $newInterestRounded     = round($newInterest, 2);
            $newPenaltyRounded      = round($newPenalty, 2);
            $newTotalAccruedRounded = round(($newInterest + $newPenalty), 2);

            $log = [
                'total_days'    => $totalInstallmentDays,
                'usage_days'    => $totalUsageDays,
                'old_interest'  => $instDb->interest,
                'new_interest'  => $newInterestRounded,
                'old_penalty'   => $instDb->penalty,
                'new_penalty'   => $newPenaltyRounded,
                'total_accrued' => $newTotalAccruedRounded,
                'failed_instructions' => $problems,
            ];

            $instDb->accrued_interest     = $newInterestRounded;
            $instDb->accrued_penalty      = $newPenaltyRounded;
            $instDb->accrued_total_amount = $newTotalAccruedRounded;

            $instDb->status               = Installment::INSTALLMENT_STATUS_SCHEDULED;
            $instDb->accrued_updated_log  = json_encode($log);
            $instDb->accrued_updated_at   = 'NOW()';

        } else {

            // settings validation
            if (
                empty($loanSettings['late_interest'])
                && empty($loanSettings['late_penalty'])
                && empty($loanSettings['late_penalty_days'])
            ) {
                return ''; // if no product settings for late -> we skip late accrual
            }

            // we have 2 tactics: global & daily
            // - global we use only once to have global sync
            // - daily we used when we have executed global once

            $oldLateInterest = $instDb->late_interest;
            $oldLatePenalty  = $instDb->late_penalty;
            $amountBase      = $instDb->primary_total_amount;
            $overdueDaysReal = $instDb->days_overdue;

            // by default we start from this
            $newLateInterest = $oldLateInterest;
            $newLatePenalty  = $oldLatePenalty;

            // here we save if failed default behavior
            $failInstructions = [];


            // Important 1: Formulas description:
            //              formula(global) = rest(always = 0) + (principal + interest + penalty) * %setting * broi dni v prosrochie / 360
            //              formula(daily)  = rest + (principal + interest + penalty) * %setting * broi dni v prosrochie / 360
            // Important 2: we have product setting for late accrual based on days
            //              means we add late only first X days, if days == 0 -> skip late adding
            // Important 3: if new values less then old, keep old, never go down (possible cases from migration)

            if (empty($instDb->late_updated_at)) { // global
                $tactic = 'global';
                $overdueDaysInFormula = $overdueDaysReal;
                $restLateInterest = 0;
                $restLatePenalty  = 0;
            } else { // daily
                $tactic = 'daily';
                $overdueDaysInFormula = 1;
                $restLateInterest = (float) $instDb->rest_late_interest;
                $restLatePenalty  = (float) $instDb->rest_late_penalty;
            }


            $lateInterestPercent = !empty($loanSettings['late_interest']) ? floatval($loanSettings['late_interest']) : 0;
            if (!empty($lateInterestPercent)) {
                $newLateInterest = $restLateInterest + round(($amountBase * $lateInterestPercent/100 * $overdueDaysInFormula / 360), 2);
            } else {
                $failInstructions[] = 'no late_interest percent configured for product';
            }

            $latePenaltyDaysBorder = !empty($loanSettings['late_penalty_days']) ? intval($loanSettings['late_penalty_days']) : 0;
            $latePenaltyPercent = !empty($loanSettings['late_penalty']) ? floatval($loanSettings['late_penalty']) : 0;

            if ($tactic == 'daily') {
                if (
                    $latePenaltyDaysBorder > 0
                    && $overdueDaysReal <= $latePenaltyDaysBorder
                    && !empty($latePenaltyPercent)
                ) {
                    $newLatePenalty = $restLatePenalty + round(($amountBase * $latePenaltyPercent/100 * $overdueDaysInFormula / 360), 2);
                } else {
                    $failInstructions[] = 'no late_penalty percent configured for product OR days passed';
                }
            } else {
                if (
                    $latePenaltyDaysBorder > 0
                    && !empty($latePenaltyPercent)
                ) {
                    if ($overdueDaysReal > $latePenaltyDaysBorder) {
                        $overdueDaysInFormula = $latePenaltyDaysBorder;
                    }
                    $newLatePenalty = $restLatePenalty + round(($amountBase * $latePenaltyPercent/100 * $overdueDaysInFormula / 360), 2);
                } else {
                    $failInstructions[] = 'no late_penalty percent configured for product OR days passed';
                }
            }


            // never go down
            if ($newLateInterest < $oldLateInterest) {
                $newLateInterest = $oldLateInterest;
                $failInstructions[] = 'old late_interest higher then new late_interest';
            }
            if ($newLatePenalty < $oldLatePenalty) {
                $newLatePenalty = $oldLatePenalty;
                $failInstructions[] = 'old late_penalty higher then new late_penalty';
            }

            $log = [
                'tactic'               => $tactic,
                'amount_base'          => $amountBase,
                'overdue_days_real'    => $overdueDaysReal,
                'overdue_days_formula' => $overdueDaysInFormula,
                'rest_late_interest'   => $restLateInterest,
                'rest_late_penalty'    => $restLatePenalty,
                'product_setting'      => $loanSettings,
                'old_late_interest'    => $oldLateInterest,
                'new_late_interest'    => $newLateInterest,
                'old_late_penalty'     => $oldLatePenalty,
                'new_late_penalty'     => $newLatePenalty,
                'failed_instructions'  => $failInstructions,
            ];

            $instDb->late_interest      = $newLateInterest;
            $instDb->late_penalty       = $newLatePenalty;
            $instDb->rest_late_interest = round(($newLateInterest - $instDb->paid_late_interest), 2);
            $instDb->rest_late_penalty  = round(($newLatePenalty  - $instDb->paid_late_penalty), 2);

            // stats realted columns
            $instDb->overdue_days       = $overdueDaysReal;
            $instDb->overdue_amount     = (
                $instDb->primary_total_amount
                + $instDb->rest_late_interest
                + $instDb->rest_late_penalty
                + $instDb->collector_tax_amount
            );

            if ($instDb->overdue_days > $instDb->max_overdue_days) {
                $instDb->max_overdue_days = $instDb->overdue_days;
                $instDb->max_overdue_date = Carbon::now();
            }
            if ($instDb->overdue_amount > $instDb->max_overdue_amount) {
                $instDb->max_overdue_amount = $instDb->overdue_amount;
            }

            $instDb->status            = Installment::INSTALLMENT_STATUS_LATE;
            $instDb->late_updated_log  = json_encode($log);
            $instDb->late_updated_at   = 'NOW()';
        }

        $result = ''
            . '('
            . $instDb->installment_id . ','
            . $instDb->accrued_interest . ','
            . $instDb->accrued_penalty . ','
            . $instDb->accrued_total_amount . ','
            . (!empty($instDb->accrued_updated_at) ? "'" . $instDb->accrued_updated_at . "'," : "NULL,")
            . (!empty($instDb->accrued_updated_log) ? "'" . $instDb->accrued_updated_log . "'," : "NULL,")
            . $instDb->late_interest . ','
            . $instDb->late_penalty . ','
            . $instDb->rest_late_interest . ','
            . $instDb->rest_late_penalty . ','
            . $instDb->overdue_days . ','
            . $instDb->overdue_amount . ','
            . $instDb->max_overdue_days . ','
            . $instDb->max_overdue_amount . ','
            . (!empty($instDb->max_overdue_date) ? "'" . $instDb->max_overdue_date . "', " : "NULL,")
            . "'" . $instDb->status . "',"
            . (!empty($instDb->late_updated_at) ? "'" . $instDb->late_updated_at . "',"  : "NULL,")
            . (!empty($instDb->late_updated_log) ? "'" . $instDb->late_updated_log . "'" : "NULL")
            . ')';

        return $result;
    }
}
