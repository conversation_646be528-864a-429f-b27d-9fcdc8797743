<?php

namespace Modules\Collect\Application\Console;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Bucket;
use Modules\Common\Models\BucketTask;
use Modules\Common\Models\LoanBucket;
use Modules\Collect\Repositories\BucketRepository;

class LoanToBucketPlacer extends CommonCommand
{
    const MIN_DAYS_OVERDUE_FOR_TASK = 2;

    protected $name = 'script:assign-loans-to-buckets';
    protected $signature = 'script:assign-loans-to-buckets {loanId?}';
    protected $description = 'Assign loans to buckets.';

    private $bucketRepo = null;

    public function __construct() {
        parent::__construct();

        $this->bucketRepo = app(BucketRepository::class);
    }

    public function handle()
    {
        $this->startLog();


        $loanId = (int) $this->argument('loanId');

        $now = now();
        $todo = 0;
        $done = 0;

        try {
            $this->bucketRepo->clearActiveLoansOutOfOverdue($loanId);
            $this->log('1a. cleared out of overdue');

            $this->bucketRepo->clearNotActiveLoans($loanId);
            $this->log('1b. cleared not active');

            $this->bucketRepo->clearOuterCollector($loanId);
            $this->log('1c. cleared outer collector');
        } catch (\Throwable $e) {
            $msg = 'LoanToBucketPlacer Error: ' . $e->getMessage()
                . ', file: ' . $e->getFile()
                . ', line: ' . $e->getLine();
            \Log::debug($msg);
        }


        // Important, temporary stopped!
        if (false) {
            // first comming due date after 1 day!
            $commingUpdateLb = [];
            $commingInsertLb = [];
            $commingInsertBt = [];

            $commingBuilder = $this->bucketRepo->getBuilderLoansForBucketBasedOnCommingOverdue();
            if ($loanId) {
                $commingBuilder->where('loan.loan_id', $loanId);
            }
            $commingBuilder->getQuery()->chunkById(
                100,
                function ($loans) use($now, &$commingUpdateLb, &$commingInsertLb, &$commingInsertBt) {

                    foreach ($loans as $loan) {

                        if (!empty($loan->loan_bucket_id)) {
                            $commingUpdateLb[$loan->loan_id] = $loan->loan_id;
                        } else {
                            $commingInsertLb[] = [
                                'loan_id'    => $loan->loan_id,
                                'bucket_id'  => 0,
                                'created_at' => $now,
                                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                            ];
                        }

                        $commingInsertBt[] = [
                            'bucket_id' => 0,
                            'loan_id' => $loan->loan_id,
                            'client_id' => $loan->client_id,
                            'product_id' => $loan->product_id,
                            'amount' => $loan->amount,
                            'period' => $loan->period,
                            'repaid_credits_count' => $loan->repaid_credits_count,
                            'client_full_name' => $loan->client_full_name,
                            'pin' => $loan->pin,
                            'phone' => $loan->phone,
                            'email' => $loan->email,
                            'overdue_amount' => floatToInt($loan->overdue_amount), // inst.total_amount
                            'overdue_days_count' => 0,
                            'status' => 'new',
                            'show_after' => $now,
                            'created_at' => $now,
                            'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                        ];
                    }
                },
                'loan.loan_id',
                'loan_id'
            );

            if (!empty($commingUpdateLb)) {
                LoanBucket::whereIn('loan_bucket_id', $commingUpdateLb)->update(['bucket_id' => 0]);
            }
            if (!empty($commingInsertLb)) {
                LoanBucket::insert($commingInsertLb);
            }
            if (!empty($commingInsertBt)) {
                BucketTask::insert($commingInsertBt);

                // set first count based of added due date comming task
                $todo = $done = count($commingInsertBt);
            }
            $this->log('2. created comming due date task(s): ' . $done);
        }


        $dynamicBuckets = $this->bucketRepo->getDynamicBuckets();
        if ($dynamicBuckets->count() < 1) {

            $this->log('No dynamic buckets, nothing todo');
            $this->finishLog([], 0, 0,'No dynamic buckets');

            return ;
        }

        foreach ($dynamicBuckets as $bucket) {

            // skip bucket=0, since we already did it here: getBuilderLoansForBucketBasedOnCommingOverdue
            if ($bucket->bucket_id == 0) {
                continue;
            }

            $bucketRes = $this->actualizeBucket($bucket, $loanId);
            $todo += $bucketRes['todo'];
            $done += $bucketRes['done'];
        }


        $this->bucketRepo->clearNotActualTasks();
        $this->log('3. cleared not actual tasks');


        $msg = '4. Total: ' . $todo . ', Updated: ' . $done;
        $this->log($msg);


        $this->finishLog([], $todo, $done, $msg);
    }

    private function actualizeBucket(Bucket $bucket, ?int $loanId = null): array
    {
        $this->log('-- started Bucket #' . $bucket->bucket_id);

        $todo = 0;
        $done = 0;

        $now = now();
        $bucketBuilder = $this->bucketRepo->getBuilderLoansForBucketBasedOnStats($bucket);
        if ($loanId) {
            $bucketBuilder->where('loan.loan_id', $loanId);
        }

        $bucketBuilder->getQuery()->chunkById(
            100,
            function ($loans) use($bucket, $now, &$todo, &$done) {

                // variants:
                // I. first time go to bucket(no $loan->loan_bucket_id): add to loan_bucket + add bucket_task or update bucket_task
                // II. has bucket, but it != to current one: update loan_bucket, update bucket_task or create bucket_task if not exists
                // III. has bucket, but it's the same: loan_bucket - do nothing, if has task update bucket_task

                $toUpdateLb = [];
                $toUpdateBt = [];
                $toInsertLb = [];
                $toInsertBt = [];

                $todo += $loans->count();

                foreach ($loans as $loan) {

                    // var I.
                    if (empty($loan->loan_bucket_id)) {

                        $toInsertLb[] = [
                            'loan_id'    => $loan->loan_id,
                            'bucket_id'  => $bucket->bucket_id,
                            'created_at' => $now,
                            'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                        ];


                        // do not create tasks for outer_collector loans
                        // we need to put loan in bucket, and create tasks only if > 3d
                        if (
                            empty($loan->outer_collector)
                            && $loan->overdue_days_count > self::MIN_DAYS_OVERDUE_FOR_TASK
                        ) {
                            if (!empty($loan->bucket_task_id)) {

                                $rowUpdate = (array) $loan;
                                unset($rowUpdate['loan_bucket_id']);
                                unset($rowUpdate['bucket_task_id']);
                                unset($rowUpdate['current_bucket_id']);
                                unset($rowUpdate['outer_collector']);
                                $rowUpdate['bucket_id'] = $bucket->bucket_id;
                                $rowUpdate['overdue_amount'] = floatToInt($rowUpdate['overdue_amount']);

                                $toUpdateBt[$loan->bucket_task_id] = $rowUpdate;
                            } else {

                                $rowInsert = (array) $loan;
                                unset($rowInsert['loan_bucket_id']);
                                unset($rowInsert['bucket_task_id']);
                                unset($rowInsert['current_bucket_id']);
                                unset($rowInsert['outer_collector']);
                                $rowInsert['bucket_id'] = $bucket->bucket_id;
                                $rowInsert['overdue_amount'] = floatToInt($rowInsert['overdue_amount']);

                                $toInsertBt[] = $rowInsert + [
                                    'show_after' => $now,
                                    'created_at' => $now,
                                    'status'     => 'new',
                                ];
                            }
                        }

                        continue;
                    }

                    // var II.
                    if (!empty($loan->loan_bucket_id) && $loan->current_bucket_id != $bucket->bucket_id) {
                        $toUpdateLb[$loan->loan_bucket_id] = $loan->loan_bucket_id;


                        // we need to put loan in bucket, and create tasks only if > 3d
                        if (
                            empty($loan->outer_collector)
                            && $loan->overdue_days_count > self::MIN_DAYS_OVERDUE_FOR_TASK
                        ) {
                            if (!empty($loan->bucket_task_id)) {

                                $rowUpdate = (array) $loan;
                                unset($rowUpdate['loan_bucket_id']);
                                unset($rowUpdate['bucket_task_id']);
                                unset($rowUpdate['current_bucket_id']);
                                unset($rowUpdate['outer_collector']);
                                $rowUpdate['bucket_id'] = $bucket->bucket_id;
                                $rowUpdate['overdue_amount'] = floatToInt($rowUpdate['overdue_amount']);

                                $toUpdateBt[$loan->bucket_task_id] = $rowUpdate;
                            } else {

                                $rowInsert = (array) $loan;
                                unset($rowInsert['loan_bucket_id']);
                                unset($rowInsert['bucket_task_id']);
                                unset($rowInsert['current_bucket_id']);
                                unset($rowInsert['outer_collector']);
                                $rowInsert['bucket_id'] = $bucket->bucket_id;
                                $rowInsert['overdue_amount'] = floatToInt($rowInsert['overdue_amount']);

                                $toInsertBt[] = $rowInsert + [
                                    'show_after' => $now,
                                    'created_at' => $now,
                                    'status'     => 'new',
                                ];
                            }
                        }

                        continue;
                    }

                    // var III.
                    if (!empty($loan->loan_bucket_id) && $loan->current_bucket_id == $bucket->bucket_id) {

                        // we need to put loan in bucket, and create tasks only if > 3d
                        if (
                            empty($loan->outer_collector)
                            && $loan->overdue_days_count > self::MIN_DAYS_OVERDUE_FOR_TASK
                        ) {
                            if (!empty($loan->bucket_task_id)) {
                                $rowUpdate = (array) $loan;
                                unset($rowUpdate['loan_bucket_id']);
                                unset($rowUpdate['bucket_task_id']);
                                unset($rowUpdate['current_bucket_id']);
                                unset($rowUpdate['outer_collector']);
                                $rowUpdate['bucket_id'] = $bucket->bucket_id;
                                $rowUpdate['overdue_amount'] = floatToInt($rowUpdate['overdue_amount']);

                                $toUpdateBt[$loan->bucket_task_id] = $rowUpdate;
                            } else {
                                $rowInsert = (array) $loan;
                                unset($rowInsert['loan_bucket_id']);
                                unset($rowInsert['bucket_task_id']);
                                unset($rowInsert['current_bucket_id']);
                                unset($rowInsert['outer_collector']);
                                $rowInsert['bucket_id'] = $bucket->bucket_id;
                                $rowInsert['overdue_amount'] = floatToInt($rowInsert['overdue_amount']);

                                $toInsertBt[] = $rowInsert + [
                                    'show_after' => $now,
                                    'created_at' => $now,
                                    'status'     => 'new',
                                ];
                            }
                        }

                        continue;
                    }
                }

                // proceed updates

                if (!empty($toUpdateLb)) {
                    LoanBucket::whereIn('loan_bucket_id', $toUpdateLb)
                        ->update(['bucket_id' => $bucket->bucket_id]);
                }

                if (!empty($toUpdateBt)) {

                    foreach ($toUpdateBt as $btId => $btData) {
                        $bt = BucketTask::where('bucket_task_id', $btId)->first();
                        if ($bt) {
                            $bt->fill($btData);
                            $bt->save();
                        }
                    }

                    // BucketTask::whereIn('bucket_task_id', $toUpdateBt)
                    //     ->update(['bucket_id' => $bucket->bucket_id]);
                }

                // proceed inserts

                if (!empty($toInsertLb)) {
                    LoanBucket::insert($toInsertLb);
                }

                if (!empty($toInsertBt)) {
                    BucketTask::insert($toInsertBt);
                }

                $done += count($toUpdateLb) + count($toUpdateBt) + count($toInsertLb) + count($toInsertBt);
            },
            'loan.loan_id',
            'loan_id'
        );

        return [
            'todo' => $todo,
            'done' => $done,
        ];
    }
}
