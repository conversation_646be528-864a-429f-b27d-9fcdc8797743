<?php

namespace Modules\Collect\Application\Console;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Installment;
use Symfony\Component\Console\Command\Command;
use Modules\Product\Services\ProductSettingsService;
use Throwable;

class DailyInstallmentRefresh extends CommonCommand
{
    protected $name = 'script:daily-installment-refresh';
    protected $signature = 'script:daily-installment-refresh {loan_id?}';
    protected $description = 'Refresh accrued & late amounts + installment overdue stats';

    protected int $totalEntries;
    protected int $processedEntries;
    protected array $settings = [];
    protected string $logChannel = 'daily_installment_refresh';

    public function __construct() {
        parent::__construct();
    }

    public function handle(): int
    {
        $this->startLog();

        $loanId = null;
        if (!empty($this->argument('loan_id'))) {
            $loanId = (int)$this->argument('loan_id');
        }

        $today = Carbon::now()->format('Y-m-d');
        $beforeDate = getLoanStartDate(
            $today,
            $today,
            '+1 month'
        );

        try {

            $this->settings = app(ProductSettingsService::class)->getAllProductSettingsForDailyRecalc();

            $chunk = 10000;
            $done  = 0;
            $total = self::getTotalCountForHandling($beforeDate, $loanId);
            $skippedToday = self::getSkippedTodayCount($beforeDate, $loanId);

            $this->log("Total rows to be updated: " . $total);
            $this->log("Records already processed today (will be skipped): " . $skippedToday);

            if ($total > 0) {
                $pages = ceil($total / $chunk);
                $this->log("Total chunks: " . $pages);

                $lastInstallmentId = 0; // Cursor for pagination
                $chunkNumber = 0;

                do {
                    $chunkNumber++;
                    $start = microtime(true);

                    $installmentsChunk = DB::select(DB::raw("
                        select
                            firstRes.*,
                            case
                                when firstRes.type = 'accrued' then (current_date - firstRes.installment_start_date::DATE)
                                else null
                            end AS days_used,
                            case
                                when firstRes.type = 'accrued' then (firstRes.due_date - firstRes.installment_start_date::DATE)
                                else null
                            end AS days_total,
                            case
                                when firstRes.type = 'late' then (current_date - firstRes.due_date::DATE)
                                else null
                            end AS days_overdue,
                            (select ROUND((sum(t.amount) - sum(t.paid_amount)), 2) from tax t where t.installment_id = firstRes.installment_id and t.paid = 0) as collector_tax_amount
                        from (
                            select
                                l.product_id,
                                i.installment_id,
                                i.loan_id,
                                i.seq_num,
                                i.due_date,
                                i.accrued_total_amount,
                                ROUND(((i.principal + i.interest + i.penalty) - (i.paid_principal + i.paid_interest + i.paid_penalty)), 2) as primary_total_amount,
                                i.principal,
                                i.paid_principal,
                                i.rest_principal,
                                i.accrued_interest,
                                i.interest,
                                i.late_interest,
                                i.paid_accrued_interest,
                                i.paid_interest,
                                i.rest_interest,
                                i.paid_late_interest,
                                i.accrued_penalty,
                                i.penalty,
                                i.late_penalty,
                                i.paid_accrued_penalty,
                                i.paid_penalty,
                                i.rest_penalty,
                                i.paid_late_penalty,
                                i.max_overdue_days,
                                i.max_overdue_amount,
                                i.max_overdue_date,
                                i.rest_late_penalty,
                                i.rest_late_interest,
                                i.accrued_updated_at,
                                i.accrued_updated_log,
                                i.late_updated_at,
                                i.late_updated_log,
                                i.overdue_days,
                                i.overdue_amount,
                                i.max_overdue_days,
                                i.max_overdue_amount,
                                i.max_overdue_date,
                                l.juridical,
                                case
                                    when i.due_date >= current_date then 'accrued'
                                    else 'late'
                                end as type,
                                case
                                    when i.seq_num = 1 then coalesce(l.activated_at, l.created_at)
                                    else (select i2.due_date from installment i2 where i2.loan_id = i.loan_id and i2.seq_num < i.seq_num order by i2.seq_num desc limit 1)
                                end as installment_start_date
                            from installment i
                            join loan l on l.loan_id = i.loan_id and l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
                            where
                                i.paid = 0
                                and (i.due_date <= '" . $beforeDate . "' or i.seq_num = 1)
                                and i.installment_id > " . $lastInstallmentId . "
                                " . (!empty($loanId) ? " and i.loan_id = " . $loanId : "" ) . "
                        ) as firstRes
                        order by firstRes.installment_id
                        limit " . $chunk . ";"
                    ));

                    if (empty($installmentsChunk)) {
                        break; // No more records to process
                    }

                    $chunkProcessed = $this->updateInstallments($installmentsChunk);
                    $done += $chunkProcessed;

                    // Update cursor to the last processed installment_id
                    $lastInstallmentId = end($installmentsChunk)->installment_id;

                    $this->log("--- Chunk {$chunkNumber}: Processed {$chunkProcessed} records, Last ID: {$lastInstallmentId} = " . round((microtime(true) - $start), 2) . ' sec');

                } while (count($installmentsChunk) == $chunk); // Continue while we get full chunks
            }


        } catch (Throwable $t) {
            $this->log(
                'Installment state refresh failed. Error' . $t->getMessage()
                . ', file: ' . $t->getFile() . ', line: ' . $t->getLine()
            );
        }

        // Verify if all records were processed
        $remainingCount = self::getTotalCountForHandling($beforeDate, $loanId);
        if ($remainingCount > 0) {
            $this->log("WARNING: {$remainingCount} records still need processing. This might indicate concurrent updates or other issues.");
        }

        $msg = 'Finished installment state refresh. Refreshed: ' . $done . ', Remaining: ' . $remainingCount;
        $logMessages = [
            $msg,
            $this->executionTimeString()
        ];
        $this->finishLog($logMessages, $total, $done, $msg);


        return Command::SUCCESS;
    }

    public static function getTotalCountForHandling(string $beforeDate, ?int $loanId = null)
    {
        $todoCount = DB::selectOne("
            select count(i.installment_id) as count
            from installment i
            join loan l on l.loan_id = i.loan_id and l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
            where
                i.paid = 0
                and (i.due_date <= '" . $beforeDate . "' or i.seq_num = 1)
                " . (!empty($loanId) ? " and i.loan_id = " . $loanId : "" ) . "
        ");

        return $todoCount->count ?? 0;
    }

    public static function getSkippedTodayCount(string $beforeDate, ?int $loanId = null)
    {
        $skippedCount = DB::selectOne("
            select count(i.installment_id) as count
            from installment i
            join loan l on l.loan_id = i.loan_id and l.loan_status_id = " . LoanStatus::ACTIVE_STATUS_ID . "
            where
                i.paid = 0
                and (i.due_date <= '" . $beforeDate . "' or i.seq_num = 1)
                and (
                    (i.due_date >= current_date and i.accrued_updated_at::date = current_date)
                    or (i.due_date < current_date and i.late_updated_at::date = current_date)
                )
                " . (!empty($loanId) ? " and i.loan_id = " . $loanId : "" ) . "
        ");

        return $skippedCount->count ?? 0;
    }

    private function updateInstallments(array $installments): int
    {
        $changesCommon = [];
        foreach ($installments as $installment) {

            $loanSetting = [];
            if (isset($this->settings[$installment->product_id])) {
                $loanSetting = $this->settings[$installment->product_id];
            }

            $changes = $this->prepareInstallmentChanges(
                $installment,
                $loanSetting
            );
            if (!empty($changes)) {
                $changesCommon[] = $changes;
            }
        }

        if (!empty($changesCommon)) {
            try {
                DB::statement("
                    update installment as i
                    set
                        accrued_interest = newdata.accrued_interest,
                        accrued_penalty = newdata.accrued_penalty,
                        accrued_total_amount = newdata.accrued_total_amount,
                        accrued_updated_at = newdata.accrued_updated_at::timestamp,
                        accrued_updated_log = newdata.accrued_updated_log::json,
                        late_interest = newdata.late_interest,
                        late_penalty = newdata.late_penalty,
                        rest_late_interest = newdata.rest_late_interest,
                        rest_late_penalty = newdata.rest_late_penalty,
                        overdue_days = newdata.overdue_days,
                        overdue_amount = newdata.overdue_amount,
                        max_overdue_days = newdata.max_overdue_days,
                        max_overdue_amount = newdata.max_overdue_amount,
                        max_overdue_date = newdata.max_overdue_date::date,
                        status = newdata.status,
                        late_updated_at = newdata.late_updated_at::timestamp,
                        late_updated_log = newdata.late_updated_log::json
                    from
                        ( values
                            " . implode(',' , $changesCommon ) . "
                        ) as newdata (
                            installment_id,
                            accrued_interest,
                            accrued_penalty,
                            accrued_total_amount,
                            accrued_updated_at,
                            accrued_updated_log,
                            late_interest,
                            late_penalty,
                            rest_late_interest,
                            rest_late_penalty,
                            overdue_days,
                            overdue_amount,
                            max_overdue_days,
                            max_overdue_amount,
                            max_overdue_date,
                            status,
                            late_updated_at,
                            late_updated_log
                        )
                    where i.installment_id = newdata.installment_id ;
                ");
            } catch (Throwable $e) {
                $this->log(
                    "Failed to update installments"
                    . ', Msg:' . $e->getMessage()
                    . ', file: ' . $e->getFile()
                    . ', line: ' . $e->getLine()
                );

                return 0;

            }
        }


        $countDone = count($changesCommon);
        $totalReceived = count($installments);
        $skippedCount = $totalReceived - $countDone;

        $this->log("Chunk summary - Received: {$totalReceived}, Updated: {$countDone}, Skipped: {$skippedCount}");

        unset($installments);
        unset($changesCommon);

        return $countDone;
    }

    private function prepareInstallmentChanges(
        $instDb,
        array $loanSettings
    ): string {

        if ($instDb->type == 'accrued') {

            // if already calculated for today -> skip
            if (!empty($instDb->accrued_updated_at)) {
                $carbonDate = Carbon::parse($instDb->accrued_updated_at);
                if ( $carbonDate->isToday()) {
                    return '';
                }
            }

            $totalInstallmentDays = $instDb->days_total;
            $totalUsageDays = $instDb->days_used;
            $newInterest = ($totalInstallmentDays == 0 ? $instDb->interest : $instDb->interest / $totalInstallmentDays * $totalUsageDays);
            $newPenalty = ($totalInstallmentDays == 0 ? $instDb->penalty :$instDb->penalty / $totalInstallmentDays * $totalUsageDays);

            $problems = [];
            if ($newInterest < 0) {
                $problems[] = 'set acc.interest to 0, = ' . $newInterest;
                $newInterest = 0;
            }
            if ($newPenalty < 0) {
                $problems[] = 'set acc.penalty to 0, = ' . $newPenalty;
                $newPenalty = 0;
            }


            if ($newInterest < $instDb->accrued_interest) {
                $newInterest = $instDb->accrued_interest;
                $problems[] = 'old accr_interest higher then new accr_interest';
            }
            if ($newPenalty < $instDb->accrued_penalty) {
                $newPenalty = $instDb->accrued_penalty;
                $problems[] = 'old accr_penalty higher then new accr_penalty';
            }

            $newInterestRounded     = round($newInterest, 2);
            $newPenaltyRounded      = round($newPenalty, 2);
            $newTotalAccruedRounded = round(($instDb->rest_principal + $newInterest + $newPenalty), 2);

            $log = [
                'total_days'    => $totalInstallmentDays,
                'usage_days'    => $totalUsageDays,
                'old_interest'  => $instDb->interest,
                'new_interest'  => $newInterestRounded,
                'old_penalty'   => $instDb->penalty,
                'new_penalty'   => $newPenaltyRounded,
                'total_accrued' => $newTotalAccruedRounded,
                'failed_instructions' => $problems,
            ];

            $instDb->accrued_interest     = $newInterestRounded;
            $instDb->accrued_penalty      = $newPenaltyRounded;
            $instDb->accrued_total_amount = $newTotalAccruedRounded;

            $instDb->status               = Installment::INSTALLMENT_STATUS_SCHEDULED;
            $instDb->accrued_updated_log  = json_encode($log);
            $instDb->accrued_updated_at   = 'NOW()';

            $instDb->overdue_days = 0;

        } else {

            // if already calculated for today -> skip
            if (!empty($instDb->late_updated_at)) {
                $carbonDate = Carbon::parse($instDb->late_updated_at);
                if ( $carbonDate->isToday()) {
                    return '';
                }
            }



            // settings validation
            // there are producs without late percents etc, but we still need to calc overdue days, for cucr etc
            if (
                empty($loanSettings['late_interest'])
                && empty($loanSettings['late_penalty'])
                && empty($loanSettings['late_penalty_days'])
            ) {

                $overdueDaysReal = $instDb->days_overdue;

                // stats realted columns
                $instDb->overdue_days = $overdueDaysReal;
                $instDb->overdue_amount = (
                    $instDb->primary_total_amount
                    + $instDb->rest_late_interest
                    + $instDb->rest_late_penalty
                    + $instDb->collector_tax_amount
                );

                if ($instDb->overdue_days > $instDb->max_overdue_days) {
                    $instDb->max_overdue_days = $instDb->overdue_days;
                    $instDb->max_overdue_date = Carbon::now();
                }
                if ($instDb->overdue_amount > $instDb->max_overdue_amount) {
                    $instDb->max_overdue_amount = $instDb->overdue_amount;
                }

                $instDb->status            = Installment::INSTALLMENT_STATUS_LATE;
                $instDb->late_updated_log  = json_encode([]);
                $instDb->late_updated_at   = 'NOW()';

                $result = ''
                    . '('
                    . $instDb->installment_id . ','
                    . $instDb->accrued_interest . ','
                    . $instDb->accrued_penalty . ','
                    . $instDb->accrued_total_amount . ','
                    . (!empty($instDb->accrued_updated_at) ? "'" . $instDb->accrued_updated_at . "'," : "NULL,")
                    . (!empty($instDb->accrued_updated_log) ? "'" . $instDb->accrued_updated_log . "'," : "NULL,")
                    . $instDb->late_interest . ','
                    . $instDb->late_penalty . ','
                    . $instDb->rest_late_interest . ','
                    . $instDb->rest_late_penalty . ','
                    . $instDb->overdue_days . ','
                    . $instDb->overdue_amount . ','
                    . $instDb->max_overdue_days . ','
                    . $instDb->max_overdue_amount . ','
                    . (!empty($instDb->max_overdue_date) ? "'" . $instDb->max_overdue_date . "', " : "NULL,")
                    . "'" . $instDb->status . "',"
                    . (!empty($instDb->late_updated_at) ? "'" . $instDb->late_updated_at . "',"  : "NULL,")
                    . (!empty($instDb->late_updated_log) ? "'" . $instDb->late_updated_log . "'" : "NULL")
                    . ')';

                return $result;
            }


            // we have 2 tactics: global & daily
            // - global we use only once to have global sync
            // - daily we used when we have executed global once

            $oldRestLateInterest = $instDb->rest_late_interest;
            $oldRestLatePenalty  = $instDb->rest_late_penalty;
            $oldLateInterest = $instDb->late_interest;
            $oldLatePenalty  = $instDb->late_penalty;
            $amountBase      = $instDb->primary_total_amount;
            $overdueDaysReal = $instDb->days_overdue;

            // by default we start from this
            $newLateInterest = $oldLateInterest;
            $newLatePenalty  = $oldLatePenalty;

            // here we save if failed default behavior
            $failInstructions = [];





            // Define CALCULCATION LOGIC (global VS daily)
            // Important 1: Formulas description:
            //              formula(global) = rest(always = 0) + (principal + interest + penalty) * %setting * broi dni v prosrochie / 360
            //              formula(daily)  = rest + (principal + interest + penalty) * %setting * broi dni v prosrochie / 360
            // Important 2: we have product setting for late accrual based on days
            //              means we add late only first X days, if days == 0 -> skip late adding
            // Important 3: if new values less then old, keep old, never go down (possible cases from migration)

            if (empty($instDb->late_updated_at)) { // global
                $tactic = 'global';
                $overdueDaysInFormula = $overdueDaysReal;
                $prevLateInterest = 0;
                $prevLatePenalty  = 0;
            } else { // daily
                $tactic = 'daily';
                $overdueDaysInFormula = 1;
                $prevLateInterest = (float) $instDb->late_interest;
                $prevLatePenalty  = (float) $instDb->late_penalty;
            }




            // Calculcate new LATE INTEREST
            $lateInterestPercent = !empty($loanSettings['late_interest']) ? floatval($loanSettings['late_interest']) : 0;
            if (!empty($lateInterestPercent)) {
                $newLateInterest = $prevLateInterest + round(($amountBase * $lateInterestPercent/100 * $overdueDaysInFormula / 360), 2);
            } else {
                $failInstructions[] = 'no late_interest percent configured for product';
            }



            // Calculcate new LATE PENALTY
            $latePenaltyDaysBorder = !empty($loanSettings['late_penalty_days']) ? intval($loanSettings['late_penalty_days']) : 0;
            $latePenaltyPercent = !empty($loanSettings['late_penalty']) ? floatval($loanSettings['late_penalty']) : 0;

            if ($tactic == 'daily') {
                if (
                    $latePenaltyDaysBorder > 0
                    && $overdueDaysReal <= $latePenaltyDaysBorder
                    && !empty($latePenaltyPercent)
                ) {
                    $newLatePenalty = $prevLatePenalty + round(($amountBase * $latePenaltyPercent/100 * $overdueDaysInFormula / 360), 2);
                } else {
                    $failInstructions[] = 'no late_penalty percent configured for product OR days passed';
                }
            } else {
                if (
                    $latePenaltyDaysBorder > 0
                    && !empty($latePenaltyPercent)
                ) {
                    if ($overdueDaysReal > $latePenaltyDaysBorder) {
                        $overdueDaysInFormula = $latePenaltyDaysBorder;
                    }
                    $newLatePenalty = $prevLatePenalty + round(($amountBase * $latePenaltyPercent/100 * $overdueDaysInFormula / 360), 2);
                } else {
                    $failInstructions[] = 'no late_penalty percent configured for product OR days passed';
                }
            }



            // never go down
            if ($newLateInterest < $oldLateInterest) {
                $newLateInterest = $oldLateInterest;
                $failInstructions[] = 'old late_interest higher then new late_interest';
            }
            if ($newLatePenalty < $oldLatePenalty) {
                $newLatePenalty = $oldLatePenalty;
                $failInstructions[] = 'old late_penalty higher then new late_penalty';
            }
            if ($newLateInterest < 0) {
                $failInstructions[] = 'set late.interest to 0, = ' . $newLateInterest;
                $newLateInterest = 0;
            }
            if ($newLatePenalty < 0) {
                $failInstructions[] = 'set late.penalty to 0, = ' . $newLatePenalty;
                $newLatePenalty = 0;
            }


            $newRestLateInterest = round(($newLateInterest - $instDb->paid_late_interest), 2);
            $newRestLatePenalty  = round(($newLatePenalty  - $instDb->paid_late_penalty), 2);


            $log = [
                'tactic'               => $tactic,
                'amount_base'          => $amountBase,
                'overdue_days_real'    => $overdueDaysReal,
                'overdue_days_formula' => $overdueDaysInFormula,
                'product_setting'      => $loanSettings,

                'old_late_interest'    => $oldLateInterest,
                'new_late_interest'    => $newLateInterest,

                'old_late_penalty'     => $oldLatePenalty,
                'new_late_penalty'     => $newLatePenalty,

                'old_rest_late_interest'   => $oldRestLateInterest,
                'new_rest_late_interest'   => $newRestLateInterest,

                'old_rest_late_penalty'    => $oldRestLatePenalty,
                'new_rest_late_penalty'    => $newRestLatePenalty,

                'failed_instructions'  => $failInstructions,
            ];



            $instDb->late_interest      = $newLateInterest;
            $instDb->rest_late_interest = $newRestLateInterest;

            if ($instDb->juridical == 0) {
                $instDb->late_penalty       = $newLatePenalty;
                $instDb->rest_late_penalty  = $newRestLatePenalty;
            } else {
                // for juridical loans we should keep old value
                // since it's not changed
                $log['new_late_penalty'] = $oldLatePenalty;
                $log['new_rest_late_penalty'] = $oldRestLatePenalty;
            }


            // stats realted columns
            $instDb->overdue_days       = $overdueDaysReal;
            $instDb->overdue_amount     = (
                $instDb->primary_total_amount
                + $instDb->rest_late_interest
                + $instDb->rest_late_penalty
                + $instDb->collector_tax_amount
            );

            if ($instDb->overdue_days > $instDb->max_overdue_days) {
                $instDb->max_overdue_days = $instDb->overdue_days;
                $instDb->max_overdue_date = Carbon::now();
            }
            if ($instDb->overdue_amount > $instDb->max_overdue_amount) {
                $instDb->max_overdue_amount = $instDb->overdue_amount;
            }

            $instDb->status            = Installment::INSTALLMENT_STATUS_LATE;
            $instDb->late_updated_log  = json_encode($log);
            $instDb->late_updated_at   = 'NOW()';
        }




        $result = ''
            . '('
            . $instDb->installment_id . ','
            . $instDb->accrued_interest . ','
            . $instDb->accrued_penalty . ','
            . $instDb->accrued_total_amount . ','
            . (!empty($instDb->accrued_updated_at) ? "'" . $instDb->accrued_updated_at . "'," : "NULL,")
            . (!empty($instDb->accrued_updated_log) ? "'" . $instDb->accrued_updated_log . "'," : "NULL,")
            . $instDb->late_interest . ','
            . $instDb->late_penalty . ','
            . $instDb->rest_late_interest . ','
            . $instDb->rest_late_penalty . ','
            . $instDb->overdue_days . ','
            . $instDb->overdue_amount . ','
            . $instDb->max_overdue_days . ','
            . $instDb->max_overdue_amount . ','
            . (!empty($instDb->max_overdue_date) ? "'" . $instDb->max_overdue_date . "', " : "NULL,")
            . "'" . $instDb->status . "',"
            . (!empty($instDb->late_updated_at) ? "'" . $instDb->late_updated_at . "',"  : "NULL,")
            . (!empty($instDb->late_updated_log) ? "'" . $instDb->late_updated_log . "'" : "NULL")
            . ')';

        return $result;
    }
}
