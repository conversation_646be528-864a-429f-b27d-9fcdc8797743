<?php

namespace Modules\Collect\Application\Console;

use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Symfony\Component\Console\Command\Command;
use Throwable;

class DispatchNewDayForActiveLoans extends CommonCommand
{
    protected $name = 'script:new-day-active-loan';
    protected $signature = 'script:new-day-active-loan {loan_id?}';
    protected $description = 'Day has passed for loan. used for stats and recalculations';

    private $currentDate = null;

    public function __construct() {
        parent::__construct();
    }

    /**
     * @return bool
     * @throws Exception
     */
    public function handle(): bool
    {
        $this->startLog();

        $this->currentDate = Carbon::today()->format('Y-m-d');

        $loanId = null;
        if (!empty($this->argument('loan_id'))) {
            $loanId = (int)$this->argument('loan_id');
        }

        try {

            $chunk = 1000;
            $done  = 0;
            $total = self::getTotalCountForHandling($loanId);
            $this->log("Total rows to be updated: " . $total);
            if ($total > 0) {
                $pages = ceil($total / $chunk);
                $this->log("Total chunks: " . $pages);

                /// this case need to update single loan when is processing
                $loanStatuses = [LoanStatus::ACTIVE_STATUS_ID];

                for ($i = 1; $i < ($pages + 1); $i++) {
                    $offset = (($i - 1)  * $chunk);
                    $start  = microtime(true);

                    $statsChunk = DB::select(DB::raw("
                        select
                            l.loan_id,
                            (current_date - l.activated_at::DATE) as used_days
                        from loan l
                        where
                            l.loan_status_id IN (" . implode(',', $loanStatuses) . ")
                            " . (!empty($loanId) ? " and l.loan_id = " . $loanId : "" ) . "
                        order by l.loan_id
                        offset " . $offset . "
                        limit " . $chunk . ";"
                    ));

                    $done += $this->updateStatsChunk($statsChunk);

                    $this->log("--- Handled: " . $offset . ' = ' . round((microtime(true) - $start), 2) . ' sec');
                }
            }


        } catch (Throwable $t) {
            $this->log(
                'Stats state refresh failed. Error' . $t->getMessage()
                . ', file: ' . $t->getFile() . ', line: ' . $t->getLine()
            );
        }

        $msg = 'Finished daily stats refresh. Refreshed: ' . $done;
        $logMessages = [
            $msg,
            $this->executionTimeString()
        ];
        $this->finishLog($logMessages, $total, $done, $msg);


        return Command::SUCCESS;
    }

    public static function getTotalCountForHandling(?int $loanId = null)
    {
        $loanStatuses = [LoanStatus::ACTIVE_STATUS_ID];

        $todoCount = DB::selectOne("
            select count(l.loan_id) as count
            from loan l
            where l.loan_status_id IN (" . implode(',', $loanStatuses) . ")
            " . (!empty($loanId) ? " and l.loan_id = " . $loanId : "" ) . "
        ");

        return $todoCount->count ?? 0;
    }

    private function updateStatsChunk(array $statsChunk): int
    {
        $loanIds = [];
        $newData = [];

        foreach ($statsChunk as $stat) {
            $loanIds[] = $stat->loan_id;

            $newData[] = ''
            . '('
            . $stat->loan_id . ','
            . ($stat->used_days ?? 0) . '' /// set used days as 0 when is null
            . ')';

        }

        if (!empty($newData)) {
            try {
                DB::statement("
                    with
                        installment_totals AS (
                            select
                                i.loan_id,
                                coalesce(sum(i.principal), 0) due_amount_total_principal,
                                coalesce(sum(i.interest), 0) as due_amount_total_interest,
                                coalesce(sum(i.penalty), 0) as due_amount_total_penalty,
                                coalesce(sum(i.late_interest), 0) as due_amount_total_late_interest,
                                coalesce(sum(i.late_penalty), 0) as due_amount_total_late_penalty,
                                coalesce(sum(i.paid_principal), 0) as repaid_amount_principal,
                                coalesce(sum(i.paid_interest), 0) as repaid_amount_interest,
                                coalesce(sum(i.paid_penalty), 0) as repaid_amount_penalty,
                                coalesce(sum(i.paid_late_interest), 0) as repaid_amount_late_interest,
                                coalesce(sum(i.paid_late_penalty), 0) as repaid_amount_late_penalty,
                                coalesce(sum(i.rest_principal), 0) as outstanding_amount_principal,
                                coalesce(sum(i.rest_interest), 0) as outstanding_amount_interest,
                                coalesce(sum(i.rest_penalty), 0) as outstanding_amount_penalty,
                                coalesce(sum(i.rest_late_interest), 0) as outstanding_amount_late_interest,
                                coalesce(sum(i.rest_late_penalty), 0) as outstanding_amount_late_penalty
                            from installment i
                            where i.loan_id in (" . implode(',', $loanIds) . ")
                            group by i.loan_id
                        ), installment_accrued AS (
                            select
                                i.loan_id,
                                coalesce(sum(i.rest_principal), 0) as accrued_amount_principal,
                                coalesce(sum(i.rest_interest), 0) as accrued_amount_interest,
                                coalesce(sum(i.rest_penalty), 0) as accrued_amount_penalty,
                                coalesce(sum(i.rest_late_interest), 0) as accrued_amount_late_interest,
                                coalesce(sum(i.rest_late_penalty), 0) as accrued_amount_late_penalty
                            from installment i
                            where
                                i.loan_id in (" . implode(',', $loanIds) . ")
                                and i.due_date::DATE <= current_date
                            group by i.loan_id
                        ), installment_overdue AS (
                            select
                                i.loan_id,
                                (
                                    coalesce(sum(i.rest_principal), 0)
                                    + coalesce(sum(i.rest_interest), 0)
                                    + coalesce(sum(i.rest_penalty), 0)
                                    + coalesce(sum(i.rest_late_interest), 0)
                                    +coalesce(sum(i.rest_late_penalty), 0)
                                ) as overdue_amount
                            from installment i
                            where
                                i.loan_id in (" . implode(',', $loanIds) . ")
                                and i.due_date::DATE < current_date
                            group by i.loan_id
                        ), installment_overdue_days AS (
                            select
                                i.loan_id,
                                max(i.overdue_days) as overdue_days
                            from installment i
                            where
                                i.loan_id in (" . implode(',', $loanIds) . ")
                            group by i.loan_id
                        ), taxes_totals AS (
                            select
                                t.loan_id,
                                ROUND(coalesce(sum(t.amount), 0)::numeric / 100, 2) as due_amount_total_taxes,
                                ROUND(coalesce(sum(t.paid_amount), 0)::numeric / 100, 2) as repaid_amount_taxes,
                                ROUND(coalesce(sum(t.rest_amount), 0)::numeric / 100, 2) as outstanding_amount_taxes
                            from tax t
                            where t.loan_id in (" . implode(',', $loanIds) . ")  and t.active = 1
                            group by t.loan_id
                        ), paid_inst_counts AS (
                            select
                                i.loan_id,
                                count(i.installment_id) as count
                            from installment i
                            where i.loan_id in (" . implode(',', $loanIds) . ") and i.paid = 1
                            group by i.loan_id
                        ), unpaid_inst_counts AS (
                            select
                                i.loan_id,
                                count(i.installment_id) as count
                            from installment i
                            where i.loan_id in (" . implode(',', $loanIds) . ") and i.paid = 0
                            group by i.loan_id
                        ), overdue_inst_counts AS (
                            select
                                i.loan_id,
                                count(i.installment_id) as count
                            from installment i
                            where i.loan_id in (" . implode(',', $loanIds) . ") and i.paid = 0 and i.overdue_days > 0
                            group by i.loan_id
                        )
                    update loan_actual_stats as las
                    set
                        date = current_date,
                        daily_update_at = NOW(),
                        paid_installments_count = pic.count,
                        unpaid_installments_count = uic.count,
                        overdue_installments = oic.count,
                        days_in_use = COALESCE(newdata.days_in_use, 0),
                        current_overdue_days = COALESCE(iod.overdue_days, 0),
                        current_overdue_amount = (COALESCE(io.overdue_amount, 0) + COALESCE(tt.outstanding_amount_taxes, 0)),
                        max_overdue_days = (
                            CASE
                                WHEN iod.overdue_days > las.max_overdue_days THEN iod.overdue_days
                                ELSE las.max_overdue_days
                            END
                        ),
                        max_overdue_amount = (
                            CASE
                                WHEN (COALESCE(io.overdue_amount, 0) + COALESCE(tt.outstanding_amount_taxes, 0)) > las.max_overdue_amount THEN (COALESCE(io.overdue_amount, 0) + COALESCE(tt.outstanding_amount_taxes, 0))
                                ELSE las.max_overdue_amount
                            END
                        ),
                        due_amount_total_principal = it.due_amount_total_principal,
                        due_amount_total_interest = it.due_amount_total_interest,
                        due_amount_total_penalty = it.due_amount_total_penalty,
                        due_amount_total_late_interest = it.due_amount_total_late_interest,
                        due_amount_total_late_penalty = it.due_amount_total_late_penalty,
                        due_amount_total_taxes = COALESCE(tt.due_amount_total_taxes, 0),
                        due_amount_total = (
                            it.due_amount_total_principal
                            + it.due_amount_total_interest
                            + it.due_amount_total_penalty
                            + it.due_amount_total_late_interest
                            + it.due_amount_total_late_penalty
                            + COALESCE(tt.due_amount_total_taxes, 0)
                        ),
                        repaid_amount_principal = it.repaid_amount_principal,
                        repaid_amount_interest = it.repaid_amount_interest,
                        repaid_amount_penalty = it.repaid_amount_penalty,
                        repaid_amount_late_interest = it.repaid_amount_late_interest,
                        repaid_amount_late_penalty = it.repaid_amount_late_penalty,
                        repaid_amount_taxes = COALESCE(tt.repaid_amount_taxes, 0),
                        repaid_amount_total = (
                            it.repaid_amount_principal
                            + it.repaid_amount_interest
                            + it.repaid_amount_penalty
                            + it.repaid_amount_late_interest
                            + it.repaid_amount_late_penalty
                            + COALESCE(tt.repaid_amount_taxes, 0)
                        ),
                        outstanding_amount_principal = it.outstanding_amount_principal,
                        outstanding_amount_interest = it.outstanding_amount_interest,
                        outstanding_amount_penalty = it.outstanding_amount_penalty,
                        outstanding_amount_late_interest = it.outstanding_amount_late_interest,
                        outstanding_amount_late_penalty = it.outstanding_amount_late_penalty,
                        outstanding_amount_taxes = COALESCE(tt.outstanding_amount_taxes, 0),
                        outstanding_amount_total = (
                            it.outstanding_amount_principal
                            + it.outstanding_amount_interest
                            + it.outstanding_amount_penalty
                            + it.outstanding_amount_late_interest
                            + it.outstanding_amount_late_penalty
                            + COALESCE(tt.outstanding_amount_taxes, 0)
                        ),
                        outstanding_amount_total_no_taxes = (
                            it.outstanding_amount_principal
                            + it.outstanding_amount_interest
                            + it.outstanding_amount_penalty
                            + it.outstanding_amount_late_interest
                            + it.outstanding_amount_late_penalty
                        ),
                        accrued_amount_principal = COALESCE(ia.accrued_amount_principal, 0),
                        accrued_amount_interest = COALESCE(ia.accrued_amount_interest, 0),
                        accrued_amount_penalty = COALESCE(ia.accrued_amount_penalty, 0),
                        accrued_amount_late_interest = COALESCE(ia.accrued_amount_late_interest, 0),
                        accrued_amount_late_penalty = COALESCE(ia.accrued_amount_late_penalty, 0),
                        accrued_amount_taxes = COALESCE(tt.outstanding_amount_taxes, 0),
                        accrued_amount_total = (
                            COALESCE(ia.accrued_amount_principal, 0)
                            + COALESCE(ia.accrued_amount_interest, 0)
                            + COALESCE(ia.accrued_amount_penalty, 0)
                            + COALESCE(ia.accrued_amount_late_interest, 0)
                            + COALESCE(ia.accrued_amount_late_penalty, 0)
                            + COALESCE(tt.outstanding_amount_taxes, 0)
                        )
                    from
                        ( values
                            " . implode(',' , $newData ) . "
                        ) as newdata (
                            loan_id,
                            days_in_use
                        )
                    left join installment_totals it on it.loan_id = newdata.loan_id
                    left join installment_accrued ia on ia.loan_id = newdata.loan_id
                    left join installment_overdue io on io.loan_id = newdata.loan_id
                    left join installment_overdue_days iod on iod.loan_id = newdata.loan_id
                    left join taxes_totals tt on tt.loan_id = newdata.loan_id
                    left join paid_inst_counts pic on pic.loan_id = newdata.loan_id
                    left join unpaid_inst_counts uic on uic.loan_id = newdata.loan_id
                    left join overdue_inst_counts oic on oic.loan_id = newdata.loan_id
                    where
                        las.loan_id = newdata.loan_id
                    ;
                ");

            } catch (Throwable $e) {
                $this->log(
                    "Failed to update installments"
                    . ', Msg:' . $e->getMessage()
                    . ', file: ' . $e->getFile()
                    . ', line: ' . $e->getLine()
                );

                return 0;
            }
        }


        $countDone = count($newData);
        $this->log("stats row(s) updated: " . $countDone);

        unset($statsChunk);
        unset($newData);
        unset($loanIds);


        return $countDone;
    }
}
