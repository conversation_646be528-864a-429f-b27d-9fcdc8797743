<?php

namespace Modules\Collect\Application\Listeners;

use Modules\Approve\Domain\Events\LoanWasCanceled;
use Modules\Collect\Domain\Entities\LoanForBucket;
use Modules\Payments\Domain\Events\LoanWasRepaid;

readonly class RemoveLoanFromBucketListener
{
    public function __construct(private LoanForBucket $loanForBucket) {}

    public function handle(LoanWasCanceled|LoanWasRepaid $event): void
    {
        $this->loanForBucket
            ->buildFromExisting($event->loan)
            ->removeFromBucket();
    }
}
