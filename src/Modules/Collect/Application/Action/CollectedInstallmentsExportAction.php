<?php

namespace Modules\Collect\Application\Action;

use Illuminate\Support\Facades\File;
use Modules\Collect\Repositories\ConsultantStatsRepository;
use Modules\Common\Models\ConsultantStats;
use Modules\Head\Services\SpreadsheetHelper;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class CollectedInstallmentsExportAction
{
    public function __construct(
        public ConsultantStatsRepository $consultantStatsRepository
    ) {
    }

    public function execute(array $filters, bool $saveToFile = false): ?string
    {
        // Create a new Spreadsheet object
        $spreadsheet = new Spreadsheet();

        // Get the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        // Add column headers as the first row
        $headers = [
            __('table.Month'),
            __('table.Office'),
            __('table.Consultant'),
            __('table.CollectedInstallments'),
            __('table.Ratio'),
            __('table.NewClients'),
            __('table.InstallmentsLessThan400'),
            __('table.InstallmentsMoreThan400'),
            __('table.LoansRepaidBefore3DaysOverdue'),
            __('table.LoansRepaidBetween_4_30_DaysOverdue'),
            __('table.LoansRepaidBetween_31_60_DaysOverdue'),
        ];
        $sheet->fromArray([$headers], null, 'A1');
        $rowIndex = 1;


        $builder = $this->consultantStatsRepository
            ->loadRelations(['office', 'consultant'])
            ->getBuilder($filters)
            ->orderBy('id', 'DESC');

        $builder->chunkById(100, function ($rows) use (&$sheet, &$rowIndex) {
            /** @var ConsultantStats $row ** */
            foreach ($rows as $row) {
                $r = [
                    formatDate($row->for_month, 'm-Y'),
                    $row->office->name,
                    $row->consultant->name,
                    intToFloat($row->collected_amount),
                    number_format($row->ratio, 2),
                    $row->new_clients,
                    $row->installments_less_than_400,
                    $row->installments_more_than_400,
                    $row->repaid_loans_less_than_3_overdue_days,
                    $row->repaid_loans_between_4_30_overdue_days,
                    $row->repaid_loans_between_31_60_overdue_days
                ];

                // Populate data from the array
                $rowIndex++;
                foreach ($r as $columnIndex => $value) {
                    $sheet->setCellValueByColumnAndRow($columnIndex + 1, $rowIndex, $value);
                }
            }
        });

        /// set autosize columns
        app(SpreadsheetHelper::class)->setAutoSize($sheet);

        // Create a writer object
        $writer = new Xlsx($spreadsheet);
        // Set the headers to force download the file
        $fileName = 'collected_installments_' . time() . '.xlsx';

        if (!$saveToFile) {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $fileName . '"');
            header('Cache-Control: max-age=0');

            // Write the spreadsheet to the output
            $writer->save('php://output');
            exit;
        }

        $directoryPath = storage_path("exports/consultant_stats");
        if (!File::exists($directoryPath)) {
            File::makeDirectory($directoryPath, 0755, true, true);
        }
        $filePath = storage_path("exports/consultant_stats/{$fileName}");
        $writer->save($filePath);

        return $filePath;
    }
}