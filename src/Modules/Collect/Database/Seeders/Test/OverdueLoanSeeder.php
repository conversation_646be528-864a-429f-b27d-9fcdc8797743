<?php

namespace Modules\Collect\Database\Seeders\Test;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Channel;
use Modules\Common\Models\Currency;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\LoanType;
use Modules\Common\Models\Office;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\Product;

class OverdueLoanSeeder extends Seeder
{
    use WithoutModelEvents;
    public const CURRENT_DATETIME = '2022-12-12 12:12:12';

    public function run(): void
    {
        $this->call([
            ClientSeeder::class,
        ]);
        $data = [];
        for ($i=0; $i<15; $i++){
            $data[] = [
                'loan_id' => $i+1,
                'client_id' => $i+1,
                'product_id' => $i%4+1,
                'product_type_id' => Product::where(['product_id'=>$i%4+1])->first()->product_type_id,
                'loan_type_id' => LoanType::LOAN_TYPE_ID_NORMAL,
                'discount_percent' => 10,
                'amount_requested' => 100*($i+1),
                'amount_approved' => 100*($i+1),
                'installments_requested' => 30,
                'installments_approved' => 30,
                'currency_id' => Currency::BGN_CURRENCY_ID,
                'period_requested' => [1,7,14,30][$i%4],
                'period_approved' => [1,7,14,30][$i%4],
                'period_grace' => null,
                'loan_status_id' => LoanStatus::ACTIVE_STATUS_ID,
                'client_discount_actual_id'=>null,
                'last_status_update_administrator_id' => Administrator::DEFAULT_ADMINISTRATOR_ID,
                'administrator_id' => Administrator::DEFAULT_ADMINISTRATOR_ID,
                'last_status_update_date' => '2022-10-10 11:11:11',
                'payment_method_id' => PaymentMethod::PAYMENT_METHOD_BANK,
                'source' => LoanSourceEnum::CRM,
                'channel_id' => Channel::PHONE_ID,
                'office_id' => Office::OFFICE_ID_NOVI_PAZAR_1,
                'active'=>1,
                'amount_rest'=>1000,
                'comment' => 'first default loan',
                'hash'=>md5(time() + rand(0, 9999)),
                'created_at'=>'2022-10-10 11:11:11',
                'interest_percent'=>10,
                'penalty_percent'=>10,
                'installment_modifier'=>'+1 month',
            ];
            $overdueDays = array_keys(Loan::LOAN_OVERDUE_DAYS_SEND_SMS);
            $stats[] = [
                'loan_id'=>$i+1,
                'total_installments_count'=>1,
                'first_installment_date'=>now()->subMonth()->addDay(),
                'last_installment_date'=>now()->subMonth()->addDay(),
                'current_overdue_days' => $i == 14 ? 14 : $overdueDays[$i%5],
                'current_overdue_amount' => 50*($i+1)
            ];
        }
        DB::table('loan')->insert($data);
        DB::table('loan_actual_stats')->insert($stats);
    }
}
