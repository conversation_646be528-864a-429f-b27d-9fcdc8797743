<?php

namespace Modules\Collect\Database\Seeders\Test;

use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Collect\Database\Seeders\BucketSeeder;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Models\BucketTask;
use Modules\Common\Models\BucketTaskHistory;
use Modules\Common\Models\CollectorDecision;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentMethod;
use NumberFormatter;

class DoneBucketTaskHistorySeeder extends Seeder
{
    public const CURRENT_DATETIME = '2022-12-23 22:22:22';//Friday

    public static array $processedDates = [
        14 => '2022-12-23 17:22:22',//today
        13 => '2022-12-23 12:22:22',//today
        12 => '2022-12-22 12:22:22',
        11 => '2022-12-21 12:22:22',
        10 => '2022-12-20 12:22:22',
        9 => '2022-12-19 12:22:22',//monday
        8 => '2022-12-16 12:22:22',//last week friday
        7 => '2022-12-15 12:22:22',//last week thursday
        6 => '2022-12-14 12:22:22',//last week wednesday
        5 => '2022-12-13 12:22:22',//last week tuesday
        4 => '2022-11-25 17:22:22',//last month friday
        3 => '2022-11-25 15:22:22',//last month friday
        2 => '2022-11-25 12:22:22',//last month friday
        1 => '2022-11-13 12:22:22',//last week friday
        0 => '2022-11-11 12:22:22',//last week friday
    ];

    public function run()
    {
        $this->call([
            LoanSeeder::class
        ]);
        $currentDate = new CurrentDate(self::CURRENT_DATETIME);
        $loans = Loan::all();
        $loansCount = 15;
        $bucketTasks = [];
        $processedDates = static::$processedDates;
        /** @var Loan $loan */
        foreach ($loans as $i => $loan) {
            $processedAt = Carbon::parse($processedDates[$i]);
            $bucketTasks[] = [
                'bucket_task_id' => $loan->client_id,
                'parent_bucket_task_history_id' => $loan->client_id,
                'bucket_id' => $i % 5 + 1,
                'loan_id' => $loan->loan_id,
                'client_id' => $loan->client_id,
                'product_id' => $i % 4 + 1,
                'amount' => $loan->amount_approved,
                'period' => $loan->period_approved,
                'repaid_credits_count' => $i,
                'client_full_name' => (new NumberFormatter("en", NumberFormatter::SPELLOUT))->format($i),
                'pin' => $loan->client->pin,
                'phone' => $loan->client->phone,
                'email' => $loan->client->email,
                'overdue_amount' => 50 * ($i + 1),
                'overdue_days_count' => $i + 1,
                'status' => BucketTask::STATUS_DONE,
                'collector_decision_id' => CollectorDecision::PROMISE,
                'created_at' => $processedAt->startOfDay(),
                'started_at' => $processedAt,
                'processed_by' => 2,
                'finished_at' => $processedAt->addHour(),
                'show_after' => $processedAt->startOfDay(),
            ];
        }
        DB::table('bucket_task_history')->insert($bucketTasks);
        $allBucketTasks = BucketTaskHistory::all();

        $payData = [];
        /** @var BucketTaskHistory $bt */
        foreach ($allBucketTasks as $bt) {
            $payData[] = [
                'payment_id' => $bt->loan_id,
                'client_id' => $bt->client_id,
                'loan_id' => $bt->loan_id,
                'installment_id' => $bt->client_id,
                'office_id' => 1,
                'currency_id' => 1,
                'status' => PaymentStatusEnum::DELIVERED,
                'payment_method_id' => PaymentMethod::PAYMENT_METHOD_BANK,
                'direction' => 'in',
                'source' => Payment::PAYMENT_SOURCE_BANK,
                'amount_received' => $bt->loan_id * 100,
                'amount_resto' => 0,
                'amount' => $bt->loan_id * 100,
                'manual' => 1,
                'correction' => 1,
                'created_at' => Carbon::parse($bt->finished_at)->addHour(),
                'created_by' => 2,
            ];
        }
        DB::table('payment')->insert($payData);
    }
}
