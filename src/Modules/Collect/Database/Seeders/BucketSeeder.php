<?php

namespace Modules\Collect\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Bucket;

class BucketSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $bucketsArr = [
            [
                'bucket_id' => Bucket::BUCKET_0_ID,
                'name' => 'collect::buckets.bucket_0',
                'description' => 'upcoming payments',
                'type' => 'dynamic',
                'overdue_days_from'=>0,
                'overdue_days_to'=>2,
            ],
            [
                'bucket_id' => Bucket::BUCKET_1_ID,
                'name' => 'collect::buckets.bucket_1',
                'description' => 'collect::buckets_description.bucket_1',
                'type' => 'dynamic',
                'overdue_days_from'=>3,
                'overdue_days_to'=>10,
            ],
            [
                'bucket_id' => Bucket::BUCKET_2_ID,
                'name' => 'collect::buckets.bucket_2',
                'description' => 'collect::buckets_description.bucket_2',
                'type' => 'dynamic',
                'overdue_days_from'=>11,
                'overdue_days_to'=>30,
            ],
            [
                'bucket_id' => Bucket::BUCKET_3_ID,
                'name' => 'collect::buckets.bucket_3',
                'description' => 'collect::buckets_description.bucket_3',
                'type' => 'dynamic',
                'overdue_days_from'=>31,
                'overdue_days_to'=>60,
            ],
            [
                'bucket_id' => Bucket::BUCKET_4_ID,
                'name' => 'collect::buckets.bucket_4',
                'description' => 'collect::buckets_description.bucket_4',
                'type' => 'dynamic',
                'overdue_days_from'=>61,
                'overdue_days_to'=>90,
            ],
            [
                'bucket_id' => Bucket::BUCKET_5_ID,
                'name' => 'collect::buckets.bucket_5',
                'description' => 'collect::buckets_description.bucket_5',
                'type' => 'dynamic',
                'overdue_days_from'=>91,
                'overdue_days_to'=>9999,
            ],
            [
                'bucket_id' => Bucket::BUCKET_LEGAL_ACTION_ID,
                'name' => 'collect::buckets.bucket_6',
                'description' => 'legal action',
                'type' => 'static',
            ],
            [
                'bucket_id' => Bucket::BUCKET_SELLING_ID,
                'name' => 'collect::buckets.bucket_7',
                'description' => 'selling',
                'type' => 'static',
            ],
            [
                'bucket_id' => Bucket::BUCKET_WRITTEN_OFF_ID,
                'name' => 'collect::buckets.bucket_8',
                'description' => 'written off',
                'type' => 'static',
            ],
        ];

        foreach ($bucketsArr as $bucketItem) {
            $bucket = new Bucket();
            $bucket->fill($bucketItem);
            $bucket->save();
        }

        // Fix auto increment
        DB::statement(
            "SELECT setval('bucket_bucket_id_seq', (SELECT MAX(bucket_id) FROM bucket));"
        );
    }
}
