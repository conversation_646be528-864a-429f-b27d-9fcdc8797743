<?php

namespace Modules\Collect\Console;

use Carbon\Carbon;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Installment;
use Modules\Common\Models\Loan;

// used only in tests!
class MakeLoanOverdue extends CommonCommand
{
    protected $name = 'script:make-loan-overdue {loanId?} {days?} {direction?}';
    protected $signature = 'script:make-loan-overdue {loanId?} {days?} {direction?}';

    protected $description = 'Make loan as overdue';

    public function handle()
    {
        $this->startLog();

        $loanId = $this->argument('loanId');
        $days = $this->argument('days');
        $direction = $this->argument('direction');

        $loan = Loan::whereLoanId($loanId)->first();

        if (!$loan || !$loan->isActive()) {
            throw new \Exception('Error cant make IN-ACTIVE loan as overdue');
        }

        if ($direction === 'in') {
            $toDate = Carbon::parse($loan->activated_at)->addDays($days);
        } else {
            $toDate = Carbon::parse($loan->activated_at)->subDays($days);
        }

        $loan->setAttribute('activated_at', $toDate);
        $loan->saveQuietly();

        /// refresh installment due dates
        $loan->orderedInstallments->each(function (Installment $installment) use ($loan, $days, $toDate, $direction) {
            $installment->setAttribute('accrued_updated_at', null);
            $installment->setAttribute('late_updated_at', null);
            $installment->setAttribute('created_at', $toDate);
            $installment->setAttribute('updated_at', $toDate);

            if ($direction === 'in') {
                $dueDate = Carbon::parse($installment->due_date)->addDays($days);
            } else {
                $dueDate = Carbon::parse($installment->due_date)->subDays($days);
            }
            $installment->setAttribute('due_date', $dueDate);
            $installment->saveQuietly();

            if ($installment->seq_num == 1) {
                $loan->loanActualStats->setAttribute('first_installment_date', $installment->due_date);
                $loan->loanActualStats->saveQuietly();
            }

            if ($installment->seq_num == $loan->installments_approved) {
                $loan->loanActualStats->setAttribute('last_installment_date', $installment->due_date);
                $loan->loanActualStats->saveQuietly();
            }
        });


        \Artisan::call("script:daily-installment-refresh {$loan->getKey()}");

        \Artisan::call("script:new-day-active-loan {$loan->getKey()}");

        \Artisan::call("script:new-day-active-client {$loan->client_id}");

        \Artisan::call("script:assign-loans-to-buckets {$loan->getKey()}");
    }
}
