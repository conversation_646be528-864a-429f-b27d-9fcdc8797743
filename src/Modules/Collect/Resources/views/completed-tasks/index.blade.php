@extends('layouts.app')

@php
    /** @var \Modules\Common\Models\BucketTaskHistory[] $tasks */
    /** @var \Modules\Collect\FilterForms\CompletedCollectTasksFilterForm $filterForm */
@endphp

@section('content')
    <x-card-filter-form
        :filter-form="$filterForm"
        col-lg="2"
        query-string="true"
    />
    <x-card>
        <x-slot:title>{{ __('menu.CompletedCollectTasks') }}</x-slot:title>

        <x-table>
            <x-slot:head>
                <tr>
                    <th>{{ __('table.TaskId') }}</th>
                    <th>{{ __('table.CreatedAt') }}</th>
                    <th>{{ __('table.TaskType') }}</th>
                    <th>{{ __('table.LoanId') }}</th>
                    <th>{{ __('table.ClientId') }}</th>
                    <th>{{ __('table.NoRepaidLoans') }}</th>
                    <th>{{ __('table.OverdueAmount') }}</th>
                    <th>{{ __('table.OverdueDays') }}</th>
                    <th>{{ __('table.ButtonExit') }}</th>
                    <th>{{ __('table.Comment') }}</th>
                    <th>{{ __('table.ButtonExitPressedAt') }}</th>
                    <th>{{ __('table.ButtonExitPressedBy') }}</th>
                    <th>{{ __('table.PromisedDate') }}</th>
                    <th>{{ __('table.PromisedAmount') }}</th>
                </tr>
            </x-slot:head>
            @foreach($tasks as $task)
                <tr>
                    <td>{{ $task->bucket_task_id }}</td>
                    <td>{{ $task->created_at }}</td>
                    <td>{{ $task->bucket_id === 0 ? 'Предстояща вноска' : 'Просрочие' }}</td>
                    <td>{{ $task->loan_id }}</td>
                    <td>{{ $task->client_id }}</td>
                    <td>{{ $task->repaid_credits_count }}</td>
                    <td>{{ intToFloat($task->overdue_amount) }}</td>
                    <td>{{ $task->overdue_days_count }}</td>
                    <td>{{ __($task->collector_decision?->name) }}</td>
                    <td>{{ $task->details }}</td>
                    <td>{{ $task->finished_at }}</td>
                    <td>{{ $task->processedBy?->getName() }}</td>
                    <td>{{ $task->promised_date }}</td>
                    <td>{{ $task->promised_amount }}</td>
                </tr>
            @endforeach
        </x-table>
        <x-table-pagination :rows="$tasks" show-all-pages="false" max-per-page="250"/>
    </x-card>
@endSection
