<?php

namespace Modules\Collect\Exports;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Modules\Head\Repositories\LoanRepository;

class OuterCollectorLmfLoansFullExportXlsx implements FromQuery, WithHeadings, WithMapping, WithChunkReading
{
    use Exportable;

    protected $repository;

    public function __construct(LoanRepository $repository)
    {
        $this->repository = $repository;
    }

    public function query()
    {
        return $this->repository->getLmfExportRows();
    }

    public function headings(): array
    {
        return [
            "Account Number",
            "EGN_EIK",
            "Product's name",
            "Gender",
            "Customer Name",
            "Due Date",
            "Total_Debt_Amount_at_Cession",
            "Principal",
            "Contractual_Interest",
            "Insurance",
            "Penalty_Charges_and_Expenditures",
            "Contractual_Penalty_Interest",
            "e-mail",
            "Phone_MOBILE_1",
            "Phone_MOBILE_2",
            "Phone_MOBILE_3",
            "Permanent Address City",
            "Permanent Address Street 1",
            "Current_Address_Postcode",
            "Current_Address_City",
            "Current_Address_Street_1",
            "Contract_Date",
            "Име на Лице за Контакт 1",
            "Телефонен Номер на Лице за Контакт 1",
            "Име на Лице за Контакт 2",
            "Телефонен Номер на Лице за Контакт 2",
            "Original Currency",
            "DPD at Cession",
            "Initial Loan Amount",
            "N Instalments by Contract",
            "Host_Last_Payment_Date",
            "Host_Last_Payment_Amount",
            "Брой неплатени вноски",
            "Сума Плащания за Последите 6M",
            "Сума Плащания за Последите 12M",
            "Сума Плащания за Последите 24M",
            "Сума на Всички Плащания",
        ];
    }

    public function map($row): array
    {
        $loanStats = $row->loanActualStats;
        $overdueAmounts = $row->getOverdueAmounts();
        $lastPayment = $row->getLastPayment();
        $periodPayments = $row->getPaymentsByPeriods();
        $client = $row->client;
        $clientPhones = $client->allClientPhonesExceptMain(2);
        $contacts = $client->getLastContactsCollection(2);

        $phone2 = $clientPhones->get(0)?->number ?? '';
        $phone3 = $clientPhones->get(1)?->number ?? '';

        $addrCurrent = $client->clientLastAddressCurrent();
        $addrIdCard = $client->clientLastAddressIdcard();

        $contact1 = $contacts->get(0);
        $contact2 = $contacts->get(1);

        $gender = $client->gender == 'female' ? 'жена' : 'мъж';

        return [
            $row->loan_id,
            $client->pin,
            $row->product_type_id == 1 ? 'до заплата' : 'на вноски',
            $gender,
            $client->getFullName(),
            $loanStats->last_installment_date,
            (
                $overdueAmounts->overdue_principal_amount +
                $overdueAmounts->overdue_interest_amount +
                $overdueAmounts->overdue_penalty_amount +
                $overdueAmounts->overdue_late_interest_amount +
                $overdueAmounts->overdue_late_penalty_amount +
                $overdueAmounts->overdue_tax_amount
            ),
            $overdueAmounts->overdue_principal_amount,
            $overdueAmounts->overdue_interest_amount,
            '0.00',
            (
                $overdueAmounts->overdue_late_penalty_amount +
                $overdueAmounts->overdue_late_interest_amount +
                $overdueAmounts->overdue_tax_amount
            ),
            $overdueAmounts->overdue_penalty_amount,
            $client->email,
            $client->phone,
            $phone2,
            $phone3,
            $addrIdCard?->city?->name,
            $addrIdCard?->getAddress(),
            $addrCurrent?->post_code,
            $addrCurrent?->city?->name,
            $addrCurrent?->getAddress(),
            Carbon::parse($row->created_at)->format('d.m.Y'),
            $contact1?->name ?? '',
            $contact1?->phone ?? '',
            $contact2?->name ?? '',
            $contact2?->phone ?? '',
            'BGN',
            $loanStats->current_overdue_days,
            intToFloat($row->amount_approved),
            $loanStats->total_installments_count,
            !empty($lastPayment->created_at) ? Carbon::parse($lastPayment->created_at)->format('d.m.Y') : '',
            !empty($lastPayment->amount) ? intToFloat($lastPayment->amount) : '',
            $loanStats->unpaid_installments_count,
            $periodPayments?->paid_180 ?? 0,
            $periodPayments?->paid_365 ?? 0,
            $periodPayments?->paid_730 ?? 0,
            $periodPayments?->total_paid ?? 0,
        ];
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
