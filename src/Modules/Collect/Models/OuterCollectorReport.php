<?php

namespace Modules\Collect\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Collect\Application\Enums\CollectorReportTypeEnum;
use Modules\Collect\Events\OuterCollectorReport\OuterCollectorReportWasCreated;
use Modules\Common\Models\BaseModel;
use Modules\Common\Models\File;

/**
 * @mixin IdeHelperOuterCollectorReport
 */
class OuterCollectorReport extends BaseModel
{
    // STIK DB
    const AKPZ_CONSULTANT_ID = 807;
    const APS_CONSULTANT_ID = 800;
    const LMF_CONSULTANT_ID = 802;

    // LENDIVO DB
    const LND_MELON_CONSULTANT_ID = 2;
    const LND_LMF_CONSULTANT_ID = 3;
    const LND_APS_CONSULTANT_ID = 4;

    protected $fillable = [
        'file_id',
        'report_type',
        'from_date',
        'to_date',
        'send_to',
        'firm',
        'export_type',
    ];

    protected $casts = [
        'send_to' => 'array',
        'report_type' => CollectorReportTypeEnum::class,
    ];

    protected $dispatchesEvents = [
        'created' => OuterCollectorReportWasCreated::class // SendOuterCollectorDailyReport
    ];

    public function file(): BelongsTo
    {
        return $this->belongsTo(File::class, 'file_id');
    }
}
