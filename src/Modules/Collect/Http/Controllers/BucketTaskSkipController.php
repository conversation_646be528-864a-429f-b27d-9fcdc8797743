<?php

namespace Modules\Collect\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Modules\Collect\Http\Requests\BucketTaskSkipRequest;
use Modules\Collect\Repositories\BucketTaskSkipRepository;
use Modules\Collect\Repositories\CollectTaskRepository;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Head\Repositories\LoanRepository;

class BucketTaskSkipController extends BaseController
{
    public function __construct(
        private readonly BucketTaskSkipRepository $bucketTaskSkipRepository,
    ) {}

    public function storeSkipBucketTask(BucketTaskSkipRequest $request): RedirectResponse
    {
        $data = $request->validated();

        $loan = app(LoanRepository::class)->getById($data['loan_id']);
        /// if is at first time skipping get date from installment
        if (!$loan->bucketTaskSkip->count()) {
            $firstUnPaidInstallment = $loan->getFirstUnpaidInstallment(true);
            if(!$firstUnPaidInstallment){
                return back()->with('fail', __('messages.bucket_task_skip.clientNoHaveIncomingInstallment'));
            }
            $data['till_date'] = Carbon::parse($firstUnPaidInstallment->due_date)->addDays($data['skip_days']);
        } else {
            $data['till_date'] = $loan->bucketTaskSkip->last()->till_date->addDays($data['skip_days']);
        }

        $collectorTasks = app(CollectTaskRepository::class)->getOpenTasks($loan->client_id);
        if (!empty($collectorTasks)) {
            return back()->with('fail', __('messages.bucket_task_skip.haveOpenBucketTasks'));
        }

        $bucketTaskSkip = $this->bucketTaskSkipRepository->create($data);
        if ($bucketTaskSkip) {
            return back()->with('success', __('messages.bucket_task_skip.success'));
        }

        return back()->with('success', __('messages.bucket_task_skip.error'));
    }
}
