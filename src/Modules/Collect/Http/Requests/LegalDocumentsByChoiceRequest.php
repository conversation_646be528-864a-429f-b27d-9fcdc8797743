<?php

namespace Modules\Collect\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Interfaces\ListSearchInterface;

class LegalDocumentsByChoiceRequest extends BaseRequest implements ListSearchInterface
{
    public function rules(): array
    {
        return [
            'loans' => 'required|array',
            'loans.*' => 'integer', // Ensure each element in the array is an integer
        ];
    }
}
