<?php

namespace Modules\Collect\Http\Dto;

use Illuminate\Support\Carbon;
use Modules\Common\Http\Dto\SpatieDto;

class CloseBucketTaskDto extends SpatieDto
{
    public function __construct(
        public int $loan_id,
        public int $collector_decision_id,
        public ?string $details,
        public ?Carbon $show_after,
        public ?float $promise_amount,
        public ?int $administrator_id
    ){}
}
