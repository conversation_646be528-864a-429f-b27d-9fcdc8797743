<?php

namespace Modules\Approve\Listeners\Loan;

use Modules\Approve\Domain\Events\LoanWasApproved;
use Modules\Approve\Domain\Events\LoanWasCanceled;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Models\Loan;
use Modules\Common\Services\AffiliateReportsService;


class ResetVeriffAction
{
    public function handle(LoanWasCanceled $event): void
    {
        $client = $event->loan->client;
        if (empty($client->client_id)) {
            \Log::debug('Error reseting not client found.');

            return;
        }

        if (1 === $client->new) {
            $client->verif_skipped = null;
            $client->verif_processed_at = null;
            $client->verif_processed_loan_id = null;

            $client->saveQuietly();
        }
    }
}
