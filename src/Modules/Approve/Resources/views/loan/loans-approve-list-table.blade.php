@inject('clientClass', '\Modules\Common\Models\Client')
@inject('loanClass', '\Modules\Common\Models\Loan')

@foreach($loans as $loan)
    @php
        $loanIsProcessing = $loan->loan_status_id === \Modules\Common\Models\LoanStatus::PROCESSING_STATUS_ID;
        $showTimer = !$loanIsProcessing;
        $isCurrentAdminProcessing = $loan->last_status_update_administrator_id === Auth::user()->getKey();
        if($loanIsProcessing && $isCurrentAdminProcessing) {
            $loanIsProcessing = false;
        }
        $hasPreviousExit = in_array(
            $loan->last_exit_processed_by,
            \Modules\Common\Models\ApproveDecision::getWaitingApproveDecisions('values')
        );
        if ($hasPreviousExit) {
            $loan->last_exit_processed_by =  __('approve::approveLoanList.previousExit')
            . __('approve::approveDecision.' . $loan->last_exit_processed_by);
        }
    @endphp
    <tr>
        <td>{{ $loan->client_id }}</td>
        <td>{{ $loan->loan_id }}</td>
        <td>{{ __('head::loanProductType.' . $loan->product_type_name) ?? '' }}</td>
        <td>{{ $loan->amount_requested }}</td>
        <td>{{ $loan->period_requested . ' ' . __('approve::approveLoanList.' . $loan->loan_period_type) }}</td>
        <td>{{ $loan->client_actions }}</td>
        <td>{{ $loan->client_new ? __('table.New') : __('table.Old') }}</td>
        <td>{{ $loan->client_first_name . ' ' . $loan->client_last_name }}</td>
        <td>{{ $loan->client_pin }}</td>
        <td>{{ $loan->client_phone }}</td>
        <td>{{ formatDate($loan->created_at, 'd.m.Y H:i:s') }}</td>
        <td class="secondsTd">{{$showTimer ? $loan->loanForApproveTimer() : '' }}</td>
        <td>{{ $loan->last_exit_processed_by }}</td>
        <td>
            @if(Auth::user()->canApproveLoan($loan))
                <x-btn-process
                        url="{{ route('approve.loan-decision.process', $loan->loan_id) }}"
                        name="btnProcessName"
                        title="{{ __('btn.Process') }}"
                        disabled={{$loanIsProcessing}}
                />
            @endif
        </td>
    </tr>
@endforeach
<tr id="pagination-nav">
    <td colspan="14">
        <span>{{__('table.Shows')}}
            @if($loans instanceof \Illuminate\Pagination\LengthAwarePaginator)
                {{($loans->currentPage() - 1)*($loans->perPage()) + 1 . ' ' .
                __('table.To') . ' ' . $loans->currentPage()*$loans->perPage() . ' ' .
                __('table.From') . ' ' . $loans->total() . ' ' . __('table.Results') }}
                </span>
        {{ $loans->links() }}
        @else
            </span>
        {{ $loans->count() . ' ' . __('table.Results')}}
        @endif
        <span>
            @php $rows = [10, 25, 50, 100, 0]; @endphp
            <x-max-rows
                    url="{{ route('head.approveLoans.refreshApproved') }}"
                    :rowsShow="$rows"
                    formId="#loansForm"
                    tableId="#loansTable"
                    selected="{{ session($cacheKey . '.limit') }}"
            />
            <span style="padding-top: 6px;">на стр.</span>
        </span>
    </td>
</tr>

