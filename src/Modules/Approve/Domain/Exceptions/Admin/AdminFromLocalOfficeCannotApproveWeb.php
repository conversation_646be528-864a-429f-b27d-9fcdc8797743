<?php

namespace Modules\Approve\Domain\Exceptions\Admin;

use Modules\Common\Domain\Exceptions\DomainException;

class AdminFromLocalOfficeCannotApproveWeb extends DomainException
{
    public function __construct(int $adminId)
    {
        $this->baseMessage = sprintf('Administrator with id %d from local office cannot approve online loans', $adminId);
        parent::__construct(get_defined_vars());
    }
}
