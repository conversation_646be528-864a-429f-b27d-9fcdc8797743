<?php

namespace Modules\Approve\Domain\Entities\Loan;

use App\Exceptions\FrontEndExceptions;
use Modules\Approve\Domain\Entities\Admin;
use Modules\Approve\Domain\Entities\AdminOffice;
use Modules\Approve\Domain\Entities\Office;
use Modules\Approve\Domain\Exceptions\LoanNotSaved;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Models\Loan as DbModel;
use Modules\Common\Models\LoanStatus;
use Modules\Head\Repositories\LoanRepository as Repo;

class LoanForProcessing extends DecisionLoan
{
    public function __construct(
        protected DbModel $dbModel,
        protected Repo $repo,
        protected CurrentDate $currentDate,
        protected Office $office,
        protected Admin $admin,
        protected AdminOffice $adminOffice,
        protected DecisionAttempt $decisionAttempt,
        protected Installments $installments
    ) {
        parent::__construct(
            $dbModel,
            $repo,
            $currentDate,
            $office,
            $admin,
            $adminOffice,
            $decisionAttempt,
            $installments
        );
    }

    public function process(DecisionDto $dto): static
    {
        return $this
            ->setOffice()
            ->setAdmin($dto->admin_id)
            ->setAdminOffice($dto->admin_id, $this->dbModel->office_id)
            ->setStatus()
            ->save();
    }

    protected function setStatus(): static
    {
        if ($this->dbModel->getStatus() !== LoanStatus::STATUS_SIGNED) {
            throw new FrontEndExceptions('Задача вече се обработва от друг администратор');
        }
        $this->dbModel->last_status_update_date = $this->currentDate->now();
        $this->dbModel->loan_status_id = LoanStatus::PROCESSING_STATUS_ID;
        return $this;
    }

    protected function setAdmin(int $adminId): static
    {
        $this->admin->loadById($adminId);
        $this->dbModel->last_status_update_administrator_id = $adminId;
        $this->dbModel->administrator_id = getAdminId();
        return $this;
    }

    protected function save(): self
    {
        if ($this->repo->save($this->dbModel)) {
            return $this;
        }
        throw new LoanNotSaved();
    }
}
