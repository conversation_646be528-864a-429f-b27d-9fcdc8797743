<?php

namespace Modules\Approve\Database\Seeders;

use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;

class ApproveDecisionSeeder extends Seeder
{
    public function run(): void
    {
        $approveDecisions = [
            [
                'approve_decision_id' => ApproveDecision::APPROVE_DECISION_ID_CALL_LATER,
                'name' => ApproveDecision::APPROVE_DECISION_CALL_LATER,
                'type' => ApproveDecision::APPROVE_DECISION_TYPE_WAITING,
                'active' => 1,
                'deleted' => 0,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'approve_decision_id' => ApproveDecision::APPROVE_DECISION_ID_NO_ANSWER,
                'name' => ApproveDecision::APPROVE_DECISION_NO_ANSWER,
                'type' => ApproveDecision::APPROVE_DECISION_TYPE_WAITING,
                'active' => 1,
                'deleted' => 0,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'approve_decision_id' => ApproveDecision::APPROVE_DECISION_ID_BUSY,
                'name' => ApproveDecision::APPROVE_DECISION_BUSY,
                'type' => ApproveDecision::APPROVE_DECISION_TYPE_WAITING,
                'active' => 1,
                'deleted' => 0,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'approve_decision_id' => ApproveDecision::APPROVE_DECISION_ID_APPROVED,
                'name' => ApproveDecision::APPROVE_DECISION_APPROVED,
                'type' => ApproveDecision::APPROVE_DECISION_TYPE_FINAL,
                'active' => 1,
                'deleted' => 0,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'approve_decision_id' => ApproveDecision::APPROVE_DECISION_ID_CANCELED,
                'name' => ApproveDecision::APPROVE_DECISION_CANCELED,
                'type' => ApproveDecision::APPROVE_DECISION_TYPE_FINAL,
                'active' => 1,
                'deleted' => 0,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'approve_decision_id' => ApproveDecision::APPROVE_DECISION_ID_WRONG_PHONE,
                'name' => ApproveDecision::APPROVE_DECISION_WRONG_PHONE,
                'type' => ApproveDecision::APPROVE_DECISION_TYPE_FINAL,
                'active' => 1,
                'deleted' => 0,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
        ];

        DB::table('approve_decision')->insert($approveDecisions);

        DB::statement(
            "SELECT setval('approve_decision_approve_decision_id_seq', (SELECT MAX(approve_decision_id) FROM approve_decision) + 1);"
        );
    }
}
