<?php

namespace Modules\Approve\Services;

use Exception;
use Illuminate\Support\Collection;
use Modules\Approve\Exceptions\ApproveException;
use Modules\Approve\Repositories\ApproveDecisionReasonRepository;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Services\BaseService;
use Throwable;

class ApproveDecisionReasonService extends BaseService
{
    private const CACHE_TTL = 60 * 60 * 24;

    /**
     * @var ApproveDecisionReasonRepository
     */
    private ApproveDecisionReasonRepository $approveDecisionReasonRepository;

    /**
     * ApproveDecisionReasonService constructor.
     *
     * @param ApproveDecisionReasonRepository $approveDecisionReasonRepository
     */
    public function __construct(
        ApproveDecisionReasonRepository $approveDecisionReasonRepository
    ) {
        $this->approveDecisionReasonRepository = $approveDecisionReasonRepository;

        parent::__construct();
    }

    /**
     * @param ApproveDecisionReason $approveDecisionReason
     * @param array $data
     *
     * @return ApproveDecisionReason
     *
     * @throws ProblemException
     */
    public function update(ApproveDecisionReason $approveDecisionReason, array $data)
    {
        try {
            $updatedAppDecReason = $this->approveDecisionReasonRepository->update($approveDecisionReason, $data);
        } catch (Exception $exception) {
            throw new ApproveException(
                __('approve::approveDecisionReason.approveDecisionReasonUpdateFailed'),
                $exception
            );
        }

        return $updatedAppDecReason;
    }

    /**
     * @param array $data
     *
     * @return ApproveDecisionReason|null
     * @throws ProblemException
     */
    public function store(array $data): ?ApproveDecisionReason
    {
        $approveDecisionReason = null;

        try {
            $approveDecisionReason = $this->approveDecisionReasonRepository->create($data);

            if (empty($approveDecisionReason->approve_decision_reason_id)) {
                throw new ProblemException(
                    __('approve::approveDecisionReason.emptyApproveDecisionReasonId')
                );
            }
        } catch (Exception $exception) {
            $this->handleProblemExceptions($exception);
            throw new ApproveException(
                __('approve::approveDecisionReason.approveDecisionReasonCreationFailed'),
                $exception
            );
        }

        return $approveDecisionReason;
    }

    /**
     * @return Collection
     * @throws ProblemException
     * @throws Throwable
     */
    public function getApproveDecisions(): Collection
    {
        $cacheKey = 'all_approve_decisions';
        $approveDecisions = \Cache::remember($cacheKey, self::CACHE_TTL, function () {
            return $this->approveDecisionReasonRepository->getApproveDecisions();
        });

        $approveDecisions = $this->convertArrayToCollection($approveDecisions);

        if ($approveDecisions->isEmpty()) {
            throw new ProblemException(
                __('approve::approveDecisionReason.approveDecisionReasonDecisionsNotFound')
            );
        }
        // TODO: handle tremol later

        return $approveDecisions;
    }

    /**
     * @param int|null $limit
     * @param array $data
     *
     * @return mixed
     */
    public function getByFilters(?int $limit, array $data)
    {
        $joins = $this->getJoins($data);
        $whereConditions = $this->getWhereConditions($data);

        return $this->approveDecisionReasonRepository->getAll(
            $limit,
            $joins,
            $whereConditions
        );
    }

    /**
     * @param array $data
     *
     * @return array
     */
    public function getJoins(array $data): array
    {
        return [];
    }

    /**
     * @param ApproveDecisionReason $approveDecisionReason
     *
     * @return bool
     *
     * @throws ProblemException
     */
    public function delete(ApproveDecisionReason $approveDecisionReason)
    {
        if ($approveDecisionReason->isDeleted()) {
            throw new ProblemException('approve::approveDecisionReason.approveDecisionReasonDeletionFailed');
        }

        try {
            $this->approveDecisionReasonRepository->delete($approveDecisionReason);
        } catch (Exception $exception) {
            throw new ApproveException(
                __('approve::approveDecision.approveDecisionReasonDeletionFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @param ApproveDecisionReason $approveDecisionReason
     *
     * @return bool
     *
     * @throws ProblemException
     */
    public function disable(ApproveDecisionReason $approveDecisionReason)
    {
        if (!$approveDecisionReason->isActive()) {
            throw new ProblemException(
                __('approve::approveDecisionReason.approveDecisionReasonDisabledForbidden')
            );
        }
        try {
            $this->approveDecisionReasonRepository->disable($approveDecisionReason);
        } catch (Exception $exception) {
            throw new ApproveException(
                __('approve::approveDecisionReason.approveDecisionReasonDisabledFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @param ApproveDecisionReason $approveDecisionReason
     *
     * @return bool
     *
     * @throws ProblemException
     */
    public function enable(ApproveDecisionReason $approveDecisionReason)
    {
        if ($approveDecisionReason->isActive()) {
            throw new ProblemException(
                __('approve::approveDecisionReason.approveDecisionReasonEnabledForbidden')
            );
        }
        try {
            $this->approveDecisionReasonRepository->enable($approveDecisionReason);
        } catch (Exception $exception) {
            throw new ApproveException(
                __('approve::approveDecisionReason.approveDecisionReasonEnabledFailed'),
                $exception
            );
        }

        return true;
    }


    /**
     * @return Collection
     * @throws NotFoundException
     */
    public function getAll(): Collection
    {
        $approveDecisionReasons = \Cache::remember('all_approve_decision_reasons', self::CACHE_TTL, function () {
            return $this->approveDecisionReasonRepository->getAllApproveDecisionReasons();
        });

        $approveDecisionReasons = $this->convertArrayToCollection($approveDecisionReasons);

        if ($approveDecisionReasons->isEmpty()) {
            throw new ProblemException(
                __('approve::approveDecisionReason.approveDecisionReasonNotFound')
            );
        }

        return $approveDecisionReasons;
    }
}
