<?php

namespace Modules\Approve\Console;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Mail;
use Modules\Approve\Mail\ReportProblemMail;
use Modules\Common\Console\CommonCommand;
use Modules\Head\Repositories\LoanRepository;
use Modules\ThirdParty\Repositories\A4EReportRepository;
use Modules\ThirdParty\Repositories\CcrReportRepository;
use Modules\ThirdParty\Repositories\MvrReportRepository;
use Modules\ThirdParty\Repositories\NoiReportRepository;

final class CheckThirdPartyReportsHealth extends CommonCommand
{
    protected $name = 'script:third-party-report-health';
    protected $signature = 'script:third-party-report-health';
    protected $description = 'Counts bad/unparsed reports for the last day for: MVR, NOI, CCR, A4E';
    protected string $logChannel = 'single';

    public function __construct(
        private readonly MvrReportRepository $mvrRepo,
        private readonly NoiReportRepository $noiRepo,
        private readonly CcrReportRepository $ccrRepo,
        private readonly A4EReportRepository $a4eRepo,
        private readonly LoanRepository $loanRepo,
    ) {
        parent::__construct();
    }

    public function handle(): void
    {
        $this->startLog();

        $from = Carbon::yesterday()->startOfDay();
        $to = Carbon::yesterday()->endOfDay();

        $mvrUnparsed = $this->mvrRepo->getLatestUnparsed($from, $to);
        $ccrUnparsed = $this->ccrRepo->getLatestUnparsed($from, $to);
        $a4eUnparsed = $this->a4eRepo->getLatestUnparsed($from, $to);
        $loansAllCount = $this->loanRepo->getFromToForOnline($from, $to)->count();

        $loansWithoutNoiReport = $this->getLoansWithoutNoiReport($from, $to);
        $noiUnparsed = $this->getNoiUnparsedReports($from, $to);
        $noiCallStatusFour = $this->getNoiCallStatusFourReports($from, $to);
        $noiCallStatusOne = $this->getNoiCallStatusOneReports($from, $to);
        $noiAllCount = $this->noiRepo->getFromTo($from, $to)->count();
        $noiUnparsedWeekCount = $this->noiRepo->getUnparsed(Carbon::now()->subDays(7)->startOfDay(), $to)->count();

        $total = $mvrUnparsed->count() + $noiUnparsed->count() + $ccrUnparsed->count() + $a4eUnparsed->count()
            + $loansWithoutNoiReport->count() + $noiCallStatusFour->count();

        if ($total) {
            Mail::to(config('mail.reports_monitor')['receivers'])->send(
                new ReportProblemMail(
                    $mvrUnparsed,
                    $ccrUnparsed,
                    $a4eUnparsed,
                    $noiUnparsed,
                    $noiCallStatusFour,
                    $noiCallStatusOne,
                    $noiAllCount,
                    $loansAllCount,
                    $loansWithoutNoiReport,
                    $noiUnparsedWeekCount,
                )
            );
        }

        $message = sprintf(
            'MVR: %d, NOI: %d, CCR: %d, A4E: %d',
            $mvrUnparsed->count(),
            $noiUnparsed->count(),
            $ccrUnparsed->count(),
            $a4eUnparsed->count()
        );

        $this->finishLog([$this->executionTimeString()], $total, $total, $message);
    }

    private function getLoansWithoutNoiReport(Carbon $from, Carbon $to): Collection
    {
        return $this->loanRepo->withoutNoiReportForOnline($from, $to)->with(['client:client_id,pin'])->get([
            'loan_id',
            'client_id'
        ]);
    }

    private function getNoiUnparsedReports(Carbon $from, Carbon $to): Collection
    {
        return $this->noiRepo->getUnparsed($from, $to)->with(['loans:loan_id'])->get([
            'noi_report_id', 'pin', 'created_at'
        ]);
    }

    private function getNoiCallStatusFourReports(Carbon $from, Carbon $to): Collection
    {
        return $this->noiRepo->getCallStatusFour($from, $to)->with(['loans:loan_id'])->get([
            'noi_report_id', 'pin', 'created_at'
        ]);
    }

    private function getNoiCallStatusOneReports(Carbon $from, Carbon $to): Collection
    {
        return $this->noiRepo->getCallStatusOne($from, $to)->with(['loans:loan_id'])->get([
            'noi_report_id', 'pin', 'created_at'
        ]);
    }
}
