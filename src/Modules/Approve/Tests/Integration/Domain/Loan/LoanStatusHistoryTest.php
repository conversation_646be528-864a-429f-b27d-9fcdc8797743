<?php

namespace Modules\Approve\Tests\Integration\Domain\Loan;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Approve\Application\Action\ApproveLoanAction as Sut;
use Modules\Approve\Application\Action\ProcessLoanAction;
use Modules\Approve\Domain\Entities\Loan\LoanForCancellation;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Database\Seeders\Test\ProcessedLoanSeeder;
use Modules\Common\Database\Seeders\Test\SignedLoanSeeder;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\LoanStatusHistory;
use Modules\Common\Models\Payment;
use Tests\TestCase;

class LoanStatusHistoryTest extends TestCase
{
    use DatabaseTransactions;

    public function setUp(): void
    {
        parent::setUp();
    }

    public function testCancel()
    {
        $this->seed(ProcessedLoanSeeder::class);
        app()->make(LoanForCancellation::class)->loadById(ProcessedLoanSeeder::LOAN_ID)->process($dto);
        $lsh = LoanStatusHistory::where(['loan_id' => ProcessedLoanSeeder::LOAN_ID])->get();
        $this->assertCount(2, $lsh);
        $this->assertEquals(LoanStatus::SIGNED_STATUS_ID, $lsh[0]->loan_status_id);
        $this->assertEquals(LoanStatus::CANCELLED_STATUS_ID, $lsh[1]->loan_status_id);
    }

    public function testApprove()
    {
        $this->seed(ProcessedLoanSeeder::class);
        $sut = app()->make(Sut::class);
        $sut->execute(new DecisionDto(
            ProcessedLoanSeeder::LOAN_ID,
            1,
            ApproveDecision::APPROVE_DECISION_APPROVED,
            ApproveDecisionReason::APPROVE_DECISION_REASON_OTHER,
            '',
            null
        ));
        $lsh = LoanStatusHistory::where(['loan_id' => ProcessedLoanSeeder::LOAN_ID])->get();
        $this->assertCount(2, $lsh);
        $this->assertEquals(LoanStatus::APPROVED_STATUS_ID, $lsh[1]->loan_status_id);
    }

    public function testProcess()
    {
        $this->seed(SignedLoanSeeder::class);
        $sut = app()->make(ProcessLoanAction::class);
        $sut->execute(Loan::get(SignedLoanSeeder::LOAN_ID));
        $lsh = LoanStatusHistory::where(['loan_id' => SignedLoanSeeder::LOAN_ID])->get();
        $this->assertCount(1, $lsh);
        $this->assertEquals(LoanStatus::PROCESSING_STATUS_ID, $lsh[0]->loan_status_id);
    }
}
