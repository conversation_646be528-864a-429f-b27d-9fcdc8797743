<?php

namespace Modules\Approve\Tests\Integration\Domain\Loan;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Approve\Domain\Entities\Admin as Sut;
use Modules\Approve\Domain\Exceptions\Admin\AdminNotFound;
use Modules\Approve\Domain\Exceptions\Admin\AdminPermissionNotFound;
use Modules\Approve\Domain\Exceptions\Admin\ApproveAmountIsTooBig;
use Modules\Approve\Domain\Exceptions\Admin\DecisionPermissionNotFound;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\AdministratorPermission;
use Modules\Common\Models\Permission;
use Tests\TestCase;

class AdminTest extends TestCase
{
    use DatabaseTransactions;

    public function testHappyPath()
    {
        $adminId = Administrator::DEFAULT_ADMINISTRATOR_ID;
        $approvedAmount = 1000;
        $sut = Sut::loadSelf($adminId);
        $this->assertEquals($adminId, $sut->dbModel()->getKey());
        $this->assertInstanceOf(
            Sut::class,
            $sut->checkPermissionsForLoanDecision(
                $adminId,
                $approvedAmount
            )
        );
    }

    public function testSumBelowApprovalLimit()
    {
        $approvedAmount = 1000;
        $admin = Administrator::factory()->create();
        $adminId = $admin->getKey();
        $sut = Sut::loadSelf($adminId);
        $this->assertEquals($adminId, $sut->dbModel()->getKey());
        $permission = Permission::where(['name' => 'approve.loan-decision.approve'])->first();
        AdministratorPermission::factory()->create([
            'administrator_id' => $adminId,
            'permission_id' => $permission->getKey(),
            'additional_info' => ['approve_amount' => $approvedAmount],
        ]);
        $this->expectException(ApproveAmountIsTooBig::class);
        $sut->checkPermissionsForLoanDecision($adminId, $approvedAmount + 1);
    }

    public function testMissingPermission()
    {
        $adminId = Administrator::DEFAULT_ADMINISTRATOR_ID;
        $approvedAmount = 1000;
        $sut = Sut::loadSelf($adminId);
        Permission::where(['name' => 'approve.loan-decision.approve'])->delete();
        $this->assertEquals($adminId, $sut->dbModel()->getKey());
        $this->expectException(DecisionPermissionNotFound::class);
        $sut->checkPermissionsForLoanDecision($adminId, $approvedAmount);
    }

    public function testMissingAdminPermission()
    {
        $adminId = Administrator::DEFAULT_ADMINISTRATOR_ID;
        $approvedAmount = 1000;
        $sut = Sut::loadSelf($adminId);
        $this->assertEquals($adminId, $sut->dbModel()->getKey());
        $permission = Permission::where(['name' => 'approve.loan-decision.approve'])->first();
        $permission->update(
            ['additional_info' => ['approve_amount' => $approvedAmount]]
        );
        AdministratorPermission::where([
            'permission_id' => $permission->getKey(),
            'administrator_id' => $adminId
        ])->delete();
        $this->expectException(AdminPermissionNotFound::class);
        $sut->checkPermissionsForLoanDecision($adminId, $approvedAmount);
    }

    public function testMissingAdmin()
    {
        Administrator::find(Administrator::DEFAULT_ADMINISTRATOR_ID)->delete();
        $sut = app()->make(Sut::class);
        $this->expectException(AdminNotFound::class);
        $sut->loadById(Administrator::DEFAULT_ADMINISTRATOR_ID);
    }
}