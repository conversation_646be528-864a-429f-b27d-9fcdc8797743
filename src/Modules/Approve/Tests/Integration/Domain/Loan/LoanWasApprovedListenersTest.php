<?php

namespace Modules\Approve\Tests\Integration\Domain\Loan;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Modules\Approve\Application\Listeners\ApproveDockPackSender;
use Modules\Approve\Domain\Entities\Loan\LoanForApproval;
use Modules\Approve\Domain\Events\LoanWasApproved;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Database\Seeders\Test\Payday30LoanExistingInstallmentSeeder;
use Modules\Common\Database\Seeders\Test\Payday30LoanSeeder;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Sales\Application\Listeners\LoanMetaCreateListener;
use Modules\Common\Models\LoanMeta;
use Tests\TestCase;

class LoanWasApprovedListenersTest extends TestCase
{
    use DatabaseTransactions;
    private LoanWasApproved $event;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed(Payday30LoanExistingInstallmentSeeder::class);
        DB::table('loan_status_history')->insert([[
            'loan_id' => Payday30LoanSeeder::LOAN_ID,
            'loan_status_id' => 2,
            'date'=>'2022-10-10 11:11:11',
            'administrator_id'=>Administrator::DEFAULT_ADMINISTRATOR_ID],
        ]);
        $loan = app()->make(LoanForApproval::class);
        $dbLoan = DbLoan::get(Payday30LoanSeeder::LOAN_ID);
        $dto = DecisionDto::from([
            'loan_id'=>Payday30LoanSeeder::LOAN_ID,
            'admin_id'=>1,
            'decision'=>ApproveDecision::APPROVE_DECISION_APPROVED,
            'description'=>'Loan is approved.'
        ]);
        $this->expectsEvents(LoanWasApproved::class);
        $loan->buildFromExisting($dbLoan)->process($dto);
        $this->event = new LoanWasApproved($loan);
    }

    public function testLoanMetaCreateListener()
    {
        $sut = app()->make(LoanMetaCreateListener::class);
        $sut->handle($this->event);
        $meta = LoanMeta::where(['loan_id'=>Payday30LoanSeeder::LOAN_ID])->first();
        $this->assertEquals(
            '{"loan_discount":"10.00","admin_discount":0,"client_discount":null,"sale_task_discount":null,"client_discount_actual_id":null,"administrator_id":null,"product_ids":null}',
            $meta->value
        );
    }

    public function testApproveDocPackSender()
    {
        $sut = app()->make(ApproveDockPackSender::class);
        $msg = $sut->handle($this->event);
        $this->assertEquals('success', $msg);

    }
}
