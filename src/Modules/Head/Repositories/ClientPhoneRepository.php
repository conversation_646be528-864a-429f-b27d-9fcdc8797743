<?php

namespace Modules\Head\Repositories;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientPhone;
use Modules\Common\Repositories\BaseRepository;

class ClientPhoneRepository extends BaseRepository
{
    protected ClientPhone $clientPhone;

    /**
     * ClientPhoneRepository constructor.
     *
     * @param ClientPhone $clientPhone
     */
    public function __construct(ClientPhone $clientPhone = new ClientPhone())
    {
        $this->clientPhone = $clientPhone;
    }

    /**
     * @param int $clientId
     *
     * @return mixed
     */
    public function getByClientId(int $clientId)
    {
        return ClientPhone::where(
            [
                'client_id' => $clientId,
                'last' => 1
            ]
        )->first();
    }

    /**
     * @param Client $client
     */
    public function updateClientPhone(Client $client)
    {
        /// if already exists return
        $isExists = ClientPhone::where(['number' => $client->phone, 'client_id' => $client->getKey()])->first();
        if ($isExists?->exists) {
            if (intval($isExists->last) !== 1) {
                /// update all client phones last to 0
                ClientPhone::where('client_id', $client->getKey())->update(['last' => 0]);

                $isExists->update(['last' => 1]);
            }

            return;
        }

        ClientPhone::where(
            [
                'client_id' => $client->client_id,
                'last' => 1,
            ]
        )->update(['last' => 0]);

        $clientPhone = new ClientPhone();
        $clientPhone->fill(
            [
                'client_id' => $client->client_id,
                'number' => $client->phone,
                'last' => 1,
                'seq_num' => ($this->countCurrentClientPhones($client->getKey()) + 1),
            ]
        );

        $clientPhone->save();
    }

    public function countCurrentClientPhones(int $clientId): int
    {
        return ClientPhone::whereClientId($clientId)->count();
    }

    /**
     * @param Client $client
     * @param array $data
     */
    public function updateClientRemovePhones(Client $client, array $data)
    {
        $this->clientPhone::where(
            [
                'client_id' => $client->client_id,
                'seq_num' => $data['seq_num'],
                'last' => 1,
            ]
        )->update(['last' => 0, 'active' => 0, 'deleted' => 1]);
    }

    /**
     * @param Client $client
     * @param array $data
     */
    public function updateClientPhones(Client $client, array $data)
    {
        $this->updateClientPhoneWithData($client, $data);
    }

    /**
     * @param Client $client
     * @param array $data
     */
    public function updateClientPhoneByAjax(Client $client, array $data)
    {
        $data['seq_num'] = $data['client']['seq_num'];
        $data['number'] = $data['client']['phone'];

        $this->updateClientPhoneWithData($client, $data);
    }

    /**
     * @param Client $client
     * @param array $data
     */
    public function updateClientPhoneWithData(Client $client, array $data)
    {
        $this->clientPhone::where(
            [
                'client_id' => $client->client_id,
                'seq_num' => $data['seq_num'],
                'last' => 1,
            ]
        )->update(['last' => 0]);

        $clientPhone = new ClientPhone();
        $clientPhone->fill(
            [
                'client_id' => $client->client_id,
                'seq_num' => $data['seq_num'],
                'number' => $data['number'],
                'last' => 1,
            ]
        );

        $clientPhone->save();
    }

    /**
     * @param Client $client
     * @param array $data
     */
    public function updateClientRestorePhone(Client $client, array $data)
    {
        $this->clientPhone::where(
            [
                'client_id' => $client->client_id,
                'number' => $data['number'],
            ]
        )->update(['active' => 1, 'deleted' => 0]);
    }

    /**
     * @param array $params
     *
     * @return bool
     */
    public function isExists(array $params)
    {
        return ($this->clientPhone::where(
                [
                    'client_id' => $params['client_id'],
                    'seq_num' => $params['seq_num'],
                    'last' => 1,
                ]
            )->count()) > 0;
    }

    /**
     * @param array $params
     *
     * @return bool
     */
    public function isExistsDiffPhone(array $params)
    {
        return ($this->clientPhone::query()->where(
                [
                    'client_id' => $params['client_id'],
                    'seq_num' => $params['seq_num'],
                    'last' => 1,
                ]
            )->where('number', '!=', $params['number'])->count()) > 0;
    }


    /**
     * @param array $data
     * @return ClientPhone
     */
    public function create(array $data): ClientPhone
    {
        $clientPhone = new ClientPhone();
        $clientPhone->fill($data);
        $clientPhone->save();

        return $clientPhone;
    }

    public function delete(ClientPhone $clientPhone): bool
    {
        $clientPhone->delete();

        return true;
    }

    public function getLastPackByClientId(int $clientId): ?Collection
    {
        return ClientPhone::where([
            'client_id' => $clientId,
            'last'=>1,
            'active'=>1,
            'deleted'=>0
        ])->get();
    }

    public function setLastToZero(ClientPhone $clientPhone)
    {
        $clientPhone->update(['last'=>0]);
    }

    public function save(ClientPhone $clientPhone)
    {
        $clientPhone->last = 1;
        $clientPhone->save();

        return $clientPhone;
    }
}
