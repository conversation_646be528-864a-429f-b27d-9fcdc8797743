<?php

namespace Modules\Head\Repositories\Loan;

use Modules\Common\Models\LoanEmployer;

class LoanEmployerRepository
{
    public function setLastToZeroForOthers(LoanEmployer $loanEmployer)
    {
        $loanEmployer
            ->where('client_employer_id', '!=' ,
                $loanEmployer->exists ? $loanEmployer->client_employer_id : 0
            )
            ->where('loan_id', '=' , $loanEmployer->loan_id)
            ->where('last', '=', 1)
            ->update(['last' => 0]);
    }

    public function save(LoanEmployer $loanEmployer): ?LoanEmployer
    {
        $loanEmployer->last = 1;
        return $loanEmployer->save() ? $loanEmployer : null;
    }
}