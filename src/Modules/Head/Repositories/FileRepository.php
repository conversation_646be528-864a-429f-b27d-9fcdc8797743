<?php

namespace Modules\Head\Repositories;

use Modules\Common\Models\Document;
use Modules\Common\Models\File;
use Modules\Common\Repositories\BaseRepository;

class FileRepository extends BaseRepository
{
    protected File $file;

    public function __construct(File $file)
    {
        $this->file = $file;
    }

    public function create(array $data): ?File
    {
        return File::create($data);
    }

    public function getClientFile(int $fileId, int $clientId): ?File
    {
        if (!Document::where(['client_id' => $clientId, 'file_id' => $fileId])->count()) {
            return null;
        }

        return File::where(['file_id' => $fileId])->first();
    }

    public function getById(int $id)
    {
        return File::where(['file_id' => $id])->first();
    }
}
