<?php

namespace Modules\Head\Console;

use Mo<PERSON>les\Common\Console\CommonCommand;
use Mo<PERSON>les\Head\Services\AutoProcessService;
use Mo<PERSON>les\Head\Services\LoanService;
use Symfony\Component\Console\Command\Command;

class AutoProcessCheck extends CommonCommand
{
    protected $name = 'script:auto-process:check';
    protected $signature = 'script:auto-process:check {loanId}';
    protected $description = 'Check auto-process for the loan';

    protected string $logChannel = 'autoProcessError';

    public function handle()
    {
        $this->startLog();
        $loanId = (int) $this->argument('loanId');
        if (empty($loanId)) {
            $this->line('Error: no loan ID provided');
            return Command::FAILURE;
        }

        $loanService = app(LoanService::class);
        $loan = $loanService->getLoanById($loanId);
        if (empty($loan->loan_id)) {
            $this->line('Error: no loan found for ID #' . $loanId);
            return Command::FAILURE;
        }

        $client = $loan->client;
        $a4EReport = $client->getLastA4EReport($loanId);
        if (empty($a4EReport->a4e_report_id)) {
            $this->line('Error: no A4Ereport for loan #' . $loanId);
            return Command::FAILURE;
        }

        $apService = app(AutoProcessService::class);
        $res = $apService->autoProcess($loan, $a4EReport, true);

        $this->finishLog([$this->executionTimeString()], 1, 1, 'Send export to DirectService');

        return $res ? Command::SUCCESS : Command::FAILURE;
    }
}
