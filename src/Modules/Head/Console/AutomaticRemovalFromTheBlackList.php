<?php

namespace Modules\Head\Console;

use Carbon\Carbon;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Client;
use Modules\Head\Services\ClientService;

class AutomaticRemovalFromTheBlackList extends CommonCommand
{
    protected $name = 'script:automatic-removal-from-the-black-list';
    protected $signature = 'script:automatic-removal-from-the-black-list';
    protected $description = 'Automatic removal from the black list';

    public function handle()
    {
        $this->startLog();

        Client::whereNotNull('blocked_to_date')
            ->where('blocked_to_date', '<=', Carbon::today()->toDateTimeString())
            ->where('blocked', 1)
            ->chunkById(100, function ($clients) {
                foreach ($clients as $client) {
                    $this->log('Unblock: ' . $client->getKey());
                    app(ClientService::class)->unblock($client, [
                        'comment' => 'Автоматично изваждане от черен списък'
                    ]);
                }
            });


        $this->finishLog();
    }
}