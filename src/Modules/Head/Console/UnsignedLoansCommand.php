<?php

namespace Modules\Head\Console;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Admin\Services\SettingService;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Loan;
use Modules\Common\Models\SaleTask;
use Modules\Common\Models\SaleTaskHistory;
use Modules\Common\Models\SaleTaskType;
use Modules\Head\Repositories\LoanRepository;
use Modules\Sales\Repositories\SaleTaskRepository;
use Symfony\Component\Console\Command\Command;

class UnsignedLoansCommand extends CommonCommand
{
    protected $name = 'script:unsigned-loans:check';
    protected $signature = 'script:unsigned-loans:check';
    protected $description = 'Check for unsigned loans and add sale task';

    protected int $processedEntries;
    protected int $totalEntries;


    public function __construct(
        protected LoanRepository $loanRepository,
        protected SaleTaskRepository $saleTaskRepository,
        protected SettingService $settingService,
    ) {
        parent::__construct();

        $this->processedEntries = 0;
        $this->totalEntries = 0;
    }

    public function handle()
    {
        $this->startLog();


        $minutes = $this->settingService->getSetting(
            SettingsEnum::task_sales_wait_incomplete_product_minutes_3rd_party
        )->default_value;

        $unsignedLoans = $this->loanRepository->getUnsignedLoans($minutes);
        $this->totalEntries = $unsignedLoans->count();

        foreach ($unsignedLoans as $unsignedLoan) {

            // prepare array with data for sale task
            $saleTaskData = $this->getSaleTaskData($unsignedLoan);

            // remove prev.tasks for same phone/amount/period
            $this->removePrevSaleTasks($unsignedLoan);

            // create sale task - unsigned contract
            $this->saleTaskRepository->create($saleTaskData);

            $this->processedEntries++;
        }

        $msg = 'Added sale tasks number: ' . $this->processedEntries;

        $this->finishLog(
            [$msg, $this->executionTimeString()],
            $this->totalEntries,
            $this->processedEntries,
            $msg
        );

        return Command::SUCCESS;
    }

    private function getSaleTaskData(Loan $loan): array {

        $client = $loan->client;

        return [
            'sale_task_type_id' => SaleTaskType::SALE_TASK_TYPE_ID_INCOMPLETE_APPLICATION,
            'client_id' => $loan->client_id,
            'office_id' => $loan->office_id,
            'loan_id' => $loan->loan_id,
            'product_ids' => [$loan->product_id],
            'pin' => $client->pin,
            'client_full_name' => $client->getFullName(),
            'phone' => $client->getLastClientPhone()->number,
            'email' => $client->email ?? '',
            'details' => __('sales::saleTaskType.' . SaleTaskType::SALE_TASK_TYPE_INCOMPLETE_APPLICATION),
            'status' => SaleTask::SALE_TASK_STATUS_NEW,
            'show_after' => Carbon::now(),
            'valid_from' => Carbon::now(),
        ];
    }

    private function removePrevSaleTasks(Loan $loan)
    {
        $client = $loan->client;
        $phone = $client->getLastClientPhone()->number;

        $rows = DB::select("
            SELECT *
            FROM sale_task
            WHERE
                phone = '" . $phone . "'
                AND amount = '" . $loan->amount_requested . "'
                AND period = '" . $loan->period_requested . "'
        ");

        if (!count($rows)) {
            return ;
        }

        $toInsert = [];
        $toDelete = [];
        foreach ($rows as $row) {
            $toDelete[] = $row->sale_task_id;

            $data = (array) $row;

            $this->castDate($data, 'created_at');
            $this->castDate($data, 'updated_at');
            $this->castDate($data, 'deleted_at');
            $this->castDate($data, 'enabled_at');
            $this->castDate($data, 'disabled_at');

            $data['archived_at'] = (Carbon::now())->format('Y-m-d H:i:s');
            $data['archived_by'] = Administrator::SYSTEM_ADMINISTRATOR_ID;
            $data['product_ids'] = json_encode($data['product_ids']);

            $toInsert[] = $data;
        }

        SaleTaskHistory::insert($toInsert);

        DB::statement("
            DELETE
            FROM sale_task
            WHERE sale_task_id IN (" . implode(',', $toDelete) . ")
        ");

        dump('deleted task: ' . count($toDelete));
    }

    public function castDate(&$data, $key)
    {
        if (!empty($data[$key])) {
            $data[$key] = (Carbon::parse($data[$key]))->format('Y-m-d H:i:s');
        }
    }
}
