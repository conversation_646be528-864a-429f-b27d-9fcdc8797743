<?php

namespace Modules\Head\FilterForms;

use Modules\Common\FilterForms\BaseFilterForm;

final class DiscountedLoansFilterForm extends BaseFilterForm
{
    protected static string $route = 'head.loans.discounted';

    public function buildForm(): void
    {
        if (getAdmin()->hasPermissionTo('head.loans.discounted.export')) {
            $this->setFormOptions(['exportRoute' => 'head.loans.discounted.export']);
        }

        $this->addDateFilterFromTo('loan_created_at', __('table.FilterByDateCreated'));
    }
}
