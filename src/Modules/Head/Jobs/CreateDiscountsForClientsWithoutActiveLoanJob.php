<?php

declare(strict_types=1);

namespace Modules\Head\Jobs;

use Carbon\CarbonInterface;
use Illuminate\Support\Facades\DB;
use Modules\Common\Jobs\CommonJob;
use Modules\Common\Services\ClientDiscountActualService;

final class CreateDiscountsForClientsWithoutActiveLoanJob extends CommonJob
{
    protected $logChannel = 'manualSendingsErrors';

    public function __construct(
        private readonly array $clientsIds,
        private readonly array $productIds,
        private readonly int $discount,
        private readonly CarbonInterface $validFrom,
        private readonly CarbonInterface $validTo,
    ) {
    }

    public function handle(ClientDiscountActualService $discountService): void
    {
        DB::transaction(function () use ($discountService) {
            foreach (array_chunk($this->clientsIds, 500) as $chunk) {
                $discountService->createForClientsByProducts(
                    $chunk,
                    $this->productIds,
                    $this->discount,
                    $this->validFrom,
                    $this->validTo,
                    $this->discount === 100,
                );
            }
        });
    }
}
