<?php

namespace Modules\Head\Policies;

use Illuminate\Auth\Access\HandlesAuthorization;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;

class LoanStatusPolicy
{
    use HandlesAuthorization;

    protected Administrator $currentAdmin;

    public function __construct(Administrator $administrator)
    {
        $this->currentAdmin = $administrator;
    }

    public function processLoan(Administrator $administrator, Loan $loan, $task = null): bool
    {
        //// todo уточнить точнъе правила так как после аппров
        /// договор меняет статус и его никто откръть не может
        if (!$loan->administrator_id) {
            return true;
        }

        if (
            $task === 'approve'
            && (
                $loan->getStatus() !== LoanStatus::STATUS_PROCESSING
                || $loan->administrator_id !== $administrator->getKey()
            )
        ) {
            return false;
        }

        return true;
    }
}
