<?php

namespace Modules\Head\Domain\Entities;

use Modules\Common\Models\Installment;
use Modules\Common\Models\InstallmentSnapshot;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Tax;
use Modules\Common\Models\TaxesSnapshot;
use Modules\Head\Application\Dto\UpdatePaymentScheduleDto;
use Modules\Head\Repositories\InstallmentRepository;

class UpdatePaymentScheduleModel
{
    public function __construct(
        private ?Loan $loan,
        private ?InstallmentRepository $installmentRepository
    ) {
    }

    public function build(UpdatePaymentScheduleDto $updatePaymentScheduleDto): void
    {
        $this
            ->updateInstallments($updatePaymentScheduleDto->installments)
            ->setLoanHasCustomPaymentSchedule()
            ->updateLoanFees($updatePaymentScheduleDto->taxes)
            ->recalculateStatistics($updatePaymentScheduleDto->loan_id);
    }

    private function recalculateStatistics($loanId): self
    {
        \Artisan::call("script:new-day-active-loan {$loanId}");

        return $this;
    }

    private function updateLoanFees(?array $taxes = null): self
    {
        if (!$taxes) {
            return $this;
        }

        collect($taxes)->map(function ($taxData) {
            /**
             * @var Tax $tax
             */
            $tax = Tax::where('tax_id', $taxData['tax_id'])->first();

            $tax->setAttribute('amount', floatToInt($taxData['amount']));

            /// при опит за изтриване на сума по голяма от платена
            if ($tax->amount < $tax->paid_amount) {
                throw new \Exception(__('messages.ErrorDeletingAmountLessThanPaid'));
            }

            $tax->setAttribute('rest_amount', $tax->amount - $tax->paid_amount);

            /// create snapshot for tax
            $originalAttributes = $tax->getOriginal();
            if ($tax->save()) {
                $changedAttributes = $tax->getChanges();

                // Get a comparison of original and changed attributes
                $changes = [];
                foreach ($changedAttributes as $key => $value) {
                    if (isset($originalAttributes[$key])) {
                        $changes[$key] = [
                            'key' => $key,
                            'from' => $originalAttributes[$key],
                            'to' => $value,
                        ];
                    }
                }

                TaxesSnapshot::create([
                    'created_by' => getAdminId(),
                    'loan_id' => $tax->loan_id,
                    'tax_id' => $tax->getKey(),
                    'tax_data' => $originalAttributes,
                    'tax_changes' => $changes
                ]);
            }
        });

        return $this;
    }

    private function setLoanHasCustomPaymentSchedule(): self
    {
        $this->loan->setAttribute('has_custom_payment_schedule', 'yes');

        $this->loan->save();

        return $this;
    }

    private function updateInstallments(array $installments): self
    {
        collect($installments)->map(function ($installmentData) {
            $installment = Installment::where('installment_id', $installmentData['installment_id'])->first();

            $installment->setAttribute('late_updated_at', null);
            $installment->setAttribute('accrued_updated_at', null);
            $installment->setAttribute('installment_id', $installmentData['installment_id']);
            $installment->setAttribute('interest', $installmentData['interest']);
            $installment->setAttribute('penalty', $installmentData['penalty']);
            $installment->setAttribute('late_interest', $installmentData['late_interest']);
            $installment->setAttribute('late_penalty', $installmentData['late_penalty']);

            /// проверяваме дали редактираната сума е по малка от платената
            /// ако е хвърляме грешка, агент не може да изтрие сума по голяма от платената
            if ($installment->interest < $installment->paid_interest) {
                throw new \Exception(__('messages.ErrorDeletingAmountLessThanPaid'));
            }

            if ($installment->penalty < $installment->paid_penalty) {
                throw new \Exception(__('messages.ErrorDeletingAmountLessThanPaid'));
            }

            if ($installment->late_interest < $installment->paid_late_interest) {
                throw new \Exception(__('messages.ErrorDeletingAmountLessThanPaid'));
            }

            if ($installment->late_penalty < $installment->paid_late_penalty) {
                throw new \Exception(__('messages.ErrorDeletingAmountLessThanPaid'));
            }

            if ($installment->accrued_interest > $installment->interest) {
                $installment->setAttribute('accrued_interest', $installment->interest);
            }
            $installment->setAttribute('rest_interest', ($installment->interest - $installment->paid_interest));


            if ($installment->accrued_penalty > $installment->penalty) {
                $installment->setAttribute('accrued_penalty', $installment->penalty);
            }
            $installment->setAttribute('rest_penalty', ($installment->penalty - $installment->paid_penalty));

            /// update lates
            $installment->setAttribute('rest_late_interest', $installment->late_interest - $installment->paid_late_interest);
            $installment->setAttribute('rest_late_penalty', $installment->late_penalty - $installment->paid_late_penalty);

            /// create snapshot for installment
            $originalAttributes = $installment->getOriginal();
            if ($this->installmentRepository->save($installment)) {
                $changedAttributes = $installment->getChanges();

                // Get a comparison of original and changed attributes
                $changes = [];
                foreach ($changedAttributes as $key => $value) {
                    if (isset($originalAttributes[$key])) {
                        $changes[$key] = [
                            'key' => $key,
                            'from' => $originalAttributes[$key],
                            'to' => $value,
                        ];
                    }
                }

                InstallmentSnapshot::create([
                    'created_by' => getAdminId(),
                    'loan_id' => $installment->loan_id,
                    'installment_id' => $installment->getKey(),
                    'installment_data' => $originalAttributes,
                    'installment_changes' => $changes
                ]);
            }
        });


        return $this;
    }

    public function setLoan(Loan $loan): self
    {
        $this->loan = $loan;

        return $this;
    }
}
