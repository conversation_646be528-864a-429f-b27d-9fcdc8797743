<?php

namespace Modules\Head\Exceptions;

use Exception;
use Illuminate\Support\Facades\Log;
use Modules\Common\Interfaces\BaseExceptionInterface;

class HeadException extends Exception implements BaseExceptionInterface
{
    private const LOG_CHANNEL = 'headModule';
    private const DEFAULT_ERROR_CODE = 400;

    /**
     * @var array
     */
    private array $params;
    private string $additionalMsg;

    /**
     * @throws Exception
     */
    public function __construct(string $message, \Throwable $t, array $params = [])
    {
        $this->message = $message;
        $this->additionalMsg = $t->getMessage();
        $this->code = $t->getCode();
        $this->file = $t->getFile();
        $this->line = $t->getLine();
        $this->params = $params;

        parent::__construct();
    }

    /**
     * @param string $file
     * @param string $line
     * @param string $msg
     * @param array $params
     */
    public function saveToLog(string $file, string $line, string $msg, $params)
    {
        Log::channel(self::LOG_CHANNEL)->error(
            'Error!' . 'msg: ' . $msg . ', file: ' . $file . ', line: ' . $line . 'params:' . implode(',', $params)
        );
    }

    /**
     * @return array
     */
    public function getParams(): array
    {
        return $this->params;
    }

    public function getAdditionalMessage()
    {
        return $this->additionalMsg;
    }
}
