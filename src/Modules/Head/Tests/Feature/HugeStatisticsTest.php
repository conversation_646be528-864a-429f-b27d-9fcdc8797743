<?php

namespace Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\PaymentTaskNameEnum;
use Modules\Common\Helpers\TestHelpers\LoanSteps;
use Modules\Common\Helpers\TestHelpers\TimeMachine;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ClientActualStats;
use Modules\Common\Models\Installment;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanActualStats;
use Modules\Common\Models\LoanRefinance;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Office;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentTask;
use Modules\Head\Console\RecalculateStatsCommand;
use Tests\TestCase;

/**
 * vendor/bin/phpunit --filter testHappyPathStats Modules/Head/Tests/Feature/HugeStatisticsTest.php
 */
class HugeStatisticsTest extends TestCase
{
    use DatabaseTransactions;

    private TimeMachine $timeMachine;
    const OFFICE_ID = Office::OFFICE_ID_WEB;
    const ADMIN_ID = Administrator::DEFAULT_ADMINISTRATOR_ID;
    const BANK_LOAN_PRINCIPAL = 40000;
    const CASH_LOAN_PRINCIPAL = 60000;
    const EASY_PAY_LOAN_PRINCIPAL = 80000;
    const SECOND_BANK_PAYMENT_AMOUNT = 10000;

    const CASH_LOAN_EXTENSION_AMOUNT = 15000;

    private array $firstBankPaymentExpected = [];
    private array $secondBankPaymentExpected = [];

    private array $refinanceCashPaymentExpected = [];
    private array $extendCashPaymentExpected = [];

    private array $bankLoanStatsExpected = [];
    private array $cashLoanStatsExpected = [];
    private array $easyPayLoanStatsExpected = [];

    private array $refundEasyPayExpected = [];

    private array $bankLoanExpected = [];
    private array $cashLoanExpected = [];
    private array $easyPayLoanExpected = [];

    private array $bankLoanInstallmentExpected = [];
    private array $cashLoanInstallmentExpected = [];
    private array $easyPayLoanInstallmentExpected = [];

    private array $clientStatsExpected = [];

    public function testHappyPathStats()
    {
        /********************* INITIATING TEST *************************/
        ini_set('memory_limit', '4096M');
        set_time_limit(60);
        Auth::loginUsingId(self::ADMIN_ID, true);
        $this->timeMachine = new TimeMachine();
        $currentDateString = now()->format('Y-m-d');
        $weekAgoDateString =  now()->subDays(7)->format('Y-m-d');
        $days40AgoDateString =  now()->subDays(40)->format('Y-m-d');
        $days44AgoDateString =  now()->subDays(44)->format('Y-m-d');
        $days4AgoDateString =  now()->subDays(4)->format('Y-m-d');

        $this->clientStatsExpected['applications_count'] = 0;
        $this->clientStatsExpected['activated_loans_count'] = 0;
        $this->clientStatsExpected['repaid_loans_count'] = 0;
        $this->clientStatsExpected['cancel_loans_count'] = 0;
        $this->clientStatsExpected['approved_loans_count'] = 0;
        $this->clientStatsExpected['disapproved_loans_count'] = 0;
        $this->clientStatsExpected['lifetime_principal_withdrawn'] = 0;
        $this->clientStatsExpected['lifetime_principal_repaid'] = 0;
        $this->clientStatsExpected['lifetime_income_paid'] = 0;
        $this->clientStatsExpected['lifetime_value_total'] = 0;
        $this->clientStatsExpected['max_overdue_days'] = 0;
        $this->clientStatsExpected['max_overdue_amount'] = 0;
        $this->clientStatsExpected['max_overdue_date'] = null;
        $this->clientStatsExpected['days_without_loan'] = 0;
        $this->clientStatsExpected['first_loan_created_at'] = null;
        $this->clientStatsExpected['first_loan_activated_at'] = null;
        $this->clientStatsExpected['first_loan_repaid_at'] = null;

        $this->bankLoanExpected['loan_status_id'] = LoanStatus::NEW_STATUS_ID;
        $this->bankLoanExpected['discount_percent'] = 0;
        $this->bankLoanExpected['days_amended'] = null;//missing column, always null
        $this->cashLoanExpected = $this->bankLoanExpected;
        $this->easyPayLoanExpected = $this->bankLoanExpected;

        $this->bankLoanInstallmentExpected['max_overdue_amount'] = 0;
        $this->bankLoanInstallmentExpected['max_overdue_days'] = 0;
        $this->bankLoanInstallmentExpected['max_overdue_date'] = 0;
        $this->bankLoanInstallmentExpected['has_payment'] = 0;
        $this->bankLoanInstallmentExpected['first_payment_received_at'] = null;
        $this->bankLoanInstallmentExpected['days_passed_before_first_payment'] = 0;//negative for early, 0 if on time, positive if overdue
        $this->cashLoanInstallmentExpected = $this->bankLoanInstallmentExpected;
        $this->easyPayLoanInstallmentExpected = $this->bankLoanInstallmentExpected;


        $this->bankLoanStatsExpected['previous_applications_count'] = 0;
        $this->bankLoanStatsExpected['previous_loans_count'] = 0;
        $this->bankLoanStatsExpected['lifetime_principal_withdrawn'] = 0;
        $this->bankLoanStatsExpected['lifetime_principal_repaid'] = 0;
        $this->bankLoanStatsExpected['lifetime_income_repaid'] = 0;
        $this->bankLoanStatsExpected['max_overdue_days'] = 0;
        $this->bankLoanStatsExpected['max_overdue_amount'] = 0;
        $this->bankLoanStatsExpected['max_overdue_date'] = 0;
        $this->bankLoanStatsExpected['refinanced_loan_id'] = null;//missing column will be always null
        $this->bankLoanStatsExpected['days_after_previous_loan'] = null;
        $this->bankLoanStatsExpected['days_in_use'] = 0;
        $this->bankLoanStatsExpected['days_by_contract'] = 0;
        $this->bankLoanStatsExpected['days_amended'] = 0;
        $this->bankLoanStatsExpected['loan_extension_count'] = 0;
        $this->bankLoanStatsExpected['has_payment'] = 0;
        $this->bankLoanStatsExpected['first_payment_received_at'] = null;
        $this->bankLoanStatsExpected['repayment_days_value'] = null;
        $this->bankLoanStatsExpected['creation_browser'] = null;
        $this->bankLoanStatsExpected['sign_ip'] = null;
        $this->bankLoanStatsExpected['sign_location'] = null;
        $this->bankLoanStatsExpected['rate_annual_percentage'] = 0;
        $this->bankLoanStatsExpected['overdue_installments'] = 0;
        $this->cashLoanStatsExpected = $this->bankLoanStatsExpected;
        $this->easyPayLoanStatsExpected = $this->bankLoanStatsExpected;

        $this->firstBankPaymentExpected['days_on_books'] = 0;//Days passed since loan activation till payment
        $this->firstBankPaymentExpected['months_on_books'] = 0;//days divided 30 rounded UP
        $this->firstBankPaymentExpected['payment_passed_days'] = 0;//difference in days between FIRST UNPAID installment.due_date and current date
        $this->firstBankPaymentExpected['payment_overdue_days'] = 0;//days loan was overdue on the moment of payment
        $this->firstBankPaymentExpected['payment_overdue_amount'] = 0;//amount loan was overdue when payment was made
        $this->secondBankPaymentExpected = $this->firstBankPaymentExpected;
        $this->extendCashPaymentExpected = $this->firstBankPaymentExpected;
        $this->refinanceCashPaymentExpected = $this->firstBankPaymentExpected;
        $this->refundEasyPayExpected = $this->firstBankPaymentExpected;

        /*****************CREATING BANK LOAN*********************/
        $bankLoan = LoanSteps::createNewClientAndBankLoan(self::BANK_LOAN_PRINCIPAL);
        $client = $bankLoan->client;

        $this->testObject($this->bankLoanInstallmentExpected, $bankLoan->installments[0]);

        $this->testObject($this->bankLoanExpected, $bankLoan);

        $this->bankLoanStatsExpected['days_after_previous_loan'] = 0;
        $this->bankLoanStatsExpected['days_by_contract'] = 30;
        $this->bankLoanStatsExpected['days_amended'] = 30;
        $this->bankLoanStatsExpected['creation_browser'] = 'test_browser';
        $this->bankLoanStatsExpected['rate_annual_percentage'] = $bankLoan->getCredit()->gpr;
        $this->testObject($this->bankLoanStatsExpected, $bankLoan->loanActualStats);

        $this->clientStatsExpected['applications_count'] = 1;
        $this->clientStatsExpected['first_loan_created_at'] = $currentDateString;
        $this->clientStatsExpected['days_without_loan'] = -1;
        $this->testObject($this->clientStatsExpected, $client->clientActualStats);


        /*****************APPROVING BANK LOAN*********************/
        $bankLoan = LoanSteps::signLoan($bankLoan);

        $this->bankLoanStatsExpected['sign_ip'] = '**************';
        //$this->expectedBankLoanStats['sign_location'] = 'sign_test_browser';//TODO: Location script doesn't work
        $this->testObject($this->bankLoanStatsExpected, $bankLoan->loanActualStats);

        $bankLoan = LoanSteps::approveLoan($bankLoan);
        $this->assertEquals(LoanStatus::APPROVED_STATUS_ID, $bankLoan->loan_status_id);

        $this->clientStatsExpected['approved_loans_count'] = 1;
        $this->testObject($this->clientStatsExpected, $bankLoan->client->clientActualStats);


        /*****************ACTIVATING BANK LOAN *********************/
        sleep(1);
        $bankLoan =  LoanSteps::activateBankLoan($bankLoan);

        $this->bankLoanExpected['loan_status_id'] = LoanStatus::ACTIVE_STATUS_ID;
        $this->testObject($this->bankLoanExpected, $bankLoan);

        $this->bankLoanStatsExpected['lifetime_principal_withdrawn'] = 0.00;//loan doesn't count itself
        $this->testObject($this->bankLoanStatsExpected, $bankLoan->loanActualStats);

        $this->clientStatsExpected['activated_loans_count'] = 1;
        $this->clientStatsExpected['lifetime_principal_withdrawn'] = intToFloat(self::BANK_LOAN_PRINCIPAL);
        $this->clientStatsExpected['lifetime_value_total'] = intToFloat(0 - self::BANK_LOAN_PRINCIPAL);
        $this->clientStatsExpected['first_loan_activated_at'] = $currentDateString;
        $this->testObject($this->clientStatsExpected, $bankLoan->client->clientActualStats);

        $this->testObject($this->firstBankPaymentExpected, $bankLoan->payments->first());


        /*****************REPAYING BANK LOAN PARTIALLY*********************/
        sleep(1);
        $fullBankLoanDebt = $bankLoan->getCredit()->outstandingAmountCollection()->getTotalOutstandingAmount();
        $firstBankPaymentAmount = $fullBankLoanDebt - self::SECOND_BANK_PAYMENT_AMOUNT;

        $bankLoan =  LoanSteps::repayLoan($bankLoan, $firstBankPaymentAmount);
        $firstBankPayment = $bankLoan->payments[1];
        $paidBankPrincipal =  $bankLoan->getCredit()->loanCarton()->paidPrinciple;
        $paidBankInterest = $firstBankPaymentAmount  - $paidBankPrincipal;

        $this->bankLoanInstallmentExpected['has_payment'] = 1;
        $this->bankLoanInstallmentExpected['first_payment_received_at'] = $currentDateString;
        $this->bankLoanInstallmentExpected['days_passed_before_first_payment'] = -30;
        $this->testObject($this->bankLoanInstallmentExpected, $bankLoan->installments[0]);

        $this->testObject($this->bankLoanExpected, $bankLoan);

        $this->firstBankPaymentExpected['payment_passed_days'] = -30;
        $this->testObject($this->firstBankPaymentExpected, $firstBankPayment);

        $this->bankLoanStatsExpected['has_payment'] = 1;
        $this->bankLoanStatsExpected['first_payment_received_at'] = $currentDateString;
        $this->testObject($this->bankLoanStatsExpected, $bankLoan->loanActualStats);

        $this->clientStatsExpected['lifetime_principal_repaid'] = intToFloat($paidBankPrincipal);
        $this->clientStatsExpected['lifetime_income_repaid'] = intToFloat($paidBankInterest);
        $this->clientStatsExpected['lifetime_value_total'] = intToFloat($firstBankPaymentAmount  - self::BANK_LOAN_PRINCIPAL);
        $this->testObject($this->clientStatsExpected, $bankLoan->client->clientActualStats);


        /*****************REPAYING REST OF BANK LOAN*********************/
        sleep(1);
        $bankLoan =  LoanSteps::repayLoan($bankLoan, self::SECOND_BANK_PAYMENT_AMOUNT);
        $secondBankPayment = $bankLoan->payments[2];
        $bankLoanProfit = $fullBankLoanDebt - self::BANK_LOAN_PRINCIPAL;

        $this->bankLoanExpected['loan_status_id'] = LoanStatus::REPAID_STATUS_ID;
        $this->testObject($this->bankLoanExpected, $bankLoan);

        $this->secondBankPaymentExpected['payment_passed_days'] = -30;
        $this->testObject($this->secondBankPaymentExpected, $secondBankPayment);

        $this->bankLoanStatsExpected['has_payment'] = 1;
        $this->bankLoanStatsExpected['repayment_days_value'] = -30;
        $this->testObject($this->bankLoanStatsExpected, $bankLoan->loanActualStats);

        $this->clientStatsExpected['repaid_loans_count'] = 1;
        $this->clientStatsExpected['lifetime_principal_repaid'] = intToFloat(self::BANK_LOAN_PRINCIPAL);
        $this->clientStatsExpected['lifetime_income_repaid'] = intToFloat($bankLoanProfit);
        $this->clientStatsExpected['lifetime_value_total'] = intToFloat($bankLoanProfit);//because both principal and income are repaid fully
        $this->clientStatsExpected['first_loan_repaid_at'] = $currentDateString;
        $this->testObject($this->clientStatsExpected, $bankLoan->client->clientActualStats);

        /******************* WEEK HAS PASSED ***************************/
        $client = $this->timeMachine->sendBackInTime($client, 7);

        $this->bankLoanStatsExpected['first_payment_received_at'] = $weekAgoDateString;
        $this->testObject($this->bankLoanStatsExpected, $bankLoan->loanActualStats->refresh());

        $this->clientStatsExpected['days_without_loan'] = 7;
        $this->clientStatsExpected['first_loan_created_at'] = $weekAgoDateString;
        $this->clientStatsExpected['first_loan_activated_at'] = $weekAgoDateString;
        $this->clientStatsExpected['first_loan_repaid_at'] = $weekAgoDateString;
        $this->testObject($this->clientStatsExpected, $client->clientActualStats);


        /******************* ADDITIONAL CASH LOAN ***************************/
        $cashLoan = LoanSteps::createCashLoan($bankLoan->client, self::CASH_LOAN_PRINCIPAL);

        $this->cashLoanExpected['loan_status_id'] = LoanStatus::SIGNED_STATUS_ID;

        $this->testObject($this->cashLoanInstallmentExpected, $cashLoan->installments[0]);

        $this->testObject($this->cashLoanExpected, $cashLoan);

        $this->cashLoanStatsExpected['lifetime_principal_withdrawn'] = $this->clientStatsExpected['lifetime_principal_withdrawn'];
        $this->cashLoanStatsExpected['lifetime_principal_repaid'] = $this->clientStatsExpected['lifetime_principal_repaid'];
        $this->cashLoanStatsExpected['lifetime_income_repaid'] = $this->clientStatsExpected['lifetime_income_repaid'];//TODO: get rid of those BS columns
        $this->cashLoanStatsExpected['previous_applications_count'] = 1;
        $this->cashLoanStatsExpected['previous_loans_count'] = 1;
        $this->cashLoanStatsExpected['days_after_previous_loan'] = 7;
        $this->cashLoanStatsExpected['days_by_contract'] = 30;
        $this->cashLoanStatsExpected['days_amended'] = 30;
        $this->cashLoanStatsExpected['creation_ip'] = '************';
        $this->cashLoanStatsExpected['creation_browser'] = 'create_test_browser';
        //$this->cashLoanStatsExpected['creation_location'] = 'xxx';//TODO: find out location
        $this->cashLoanStatsExpected['sign_ip'] = '************';
        //$this->cashLoanStatsExpected['sign_location'] = 'create_test_browser';//TODO: run 'script:loan-stats-location-update';
        $this->cashLoanStatsExpected['rate_annual_percentage'] = $cashLoan->getCredit()->gpr;
        $this->testObject($this->cashLoanStatsExpected, $cashLoan->loanActualStats);

        $this->clientStatsExpected['applications_count'] = 2;
        $this->clientStatsExpected['days_without_loan'] = -1;
        $this->testObject($this->clientStatsExpected, $cashLoan->client->clientActualStats);

        /*****************APPROVING CASH*********************/
        $cashLoan = LoanSteps::approveLoan($cashLoan);

        $this->cashLoanExpected['loan_status_id'] = LoanStatus::APPROVED_STATUS_ID;
        $this->testObject($this->cashLoanExpected, $cashLoan);

        $this->clientStatsExpected['approved_loans_count'] = 2;
        $this->testObject($this->clientStatsExpected, $cashLoan->client->clientActualStats);

        /*****************ACTIVATING CASH*********************/
        sleep(1);
        $cashLoan = LoanSteps::activateCashLoan($cashLoan);

        $this->cashLoanExpected['loan_status_id'] = LoanStatus::ACTIVE_STATUS_ID;
        $this->testObject($this->cashLoanExpected, $cashLoan);

        $this->cashLoanStatsExpected['lifetime_principal_withdrawn'] = $this->clientStatsExpected['lifetime_principal_withdrawn'];
        $this->cashLoanStatsExpected['lifetime_principal_repaid'] = $this->clientStatsExpected['lifetime_principal_repaid'];
        $this->cashLoanStatsExpected['lifetime_income_repaid'] = $this->clientStatsExpected['lifetime_income_repaid'];
        $this->testObject($this->cashLoanStatsExpected, $cashLoan->loanActualStats);

        $this->clientStatsExpected['activated_loans_count'] = 2;
        $this->clientStatsExpected['lifetime_principal_withdrawn'] = intToFloat(self::BANK_LOAN_PRINCIPAL + self::CASH_LOAN_PRINCIPAL);
        $this->clientStatsExpected['lifetime_value_total'] = intToFloat($bankLoanProfit - self::CASH_LOAN_PRINCIPAL);
        $this->testObject($this->clientStatsExpected, $cashLoan->client->clientActualStats);

        /******************* ANOTHER 33 DAYS HAS PASSED ***************************/
        $client = $this->timeMachine->sendBackInTime($client, 33);
        $bankLoan = $bankLoan->fresh();
        $cashLoan = $cashLoan->fresh();
        $cashOverdueAmount = $cashLoan->getCredit()->loanCarton()->currentOverdueAmount;

        $this->cashLoanInstallmentExpected['max_overdue_amount'] = 698.04;
        $this->cashLoanInstallmentExpected['max_overdue_days'] = 3;
        $this->cashLoanInstallmentExpected['max_overdue_date'] = now()->format('Y-m-d');
        $this->testObject($this->cashLoanInstallmentExpected, $cashLoan->installments[0]);

        $this->cashLoanStatsExpected['max_overdue_days'] = 3;
        $this->cashLoanStatsExpected['max_overdue_amount'] = intToFloat($cashOverdueAmount);
        $this->cashLoanStatsExpected['max_overdue_date'] = $currentDateString;
        $this->cashLoanStatsExpected['days_in_use'] = 33;
        $this->cashLoanStatsExpected['overdue_installments'] = 1;
        $this->testObject($this->cashLoanStatsExpected, $cashLoan->loanActualStats);

        $this->clientStatsExpected['first_loan_created_at'] = $days40AgoDateString;
        $this->clientStatsExpected['first_loan_activated_at'] = $days40AgoDateString;
        $this->clientStatsExpected['first_loan_repaid_at'] = $days40AgoDateString;
        $this->clientStatsExpected['max_overdue_days'] = 3;
        $this->clientStatsExpected['max_overdue_amount'] = intToFloat($cashOverdueAmount);
        $this->clientStatsExpected['max_overdue_date'] = $currentDateString;
        $this->clientStatsExpected['days_without_loan'] = -1;
        $this->testObject($this->clientStatsExpected, $client->clientActualStats);

        /******************* ADDITIONAL EASY PAY REFINANCING LOAN ***************************/
        $easyPayLoan = LoanSteps::createEasyPayRefinancingLoan($cashLoan, self::EASY_PAY_LOAN_PRINCIPAL);
        $easyPayLoan = LoanSteps::signLoan($easyPayLoan);
        $refinances = LoanRefinance::all();
        $this->assertEquals(1, $refinances->count());
        /** @var LoanRefinance $loanRefinance */
        $loanRefinance = $refinances->first();

        $this->testObject($this->easyPayLoanInstallmentExpected, $easyPayLoan->installments[0]);

        $this->easyPayLoanExpected['loan_status_id'] = LoanStatus::SIGNED_STATUS_ID;
        $this->testObject($this->easyPayLoanExpected, $easyPayLoan);

        $this->easyPayLoanStatsExpected['lifetime_principal_withdrawn'] = $this->clientStatsExpected['lifetime_principal_withdrawn'];
        $this->easyPayLoanStatsExpected['lifetime_principal_repaid'] = $this->clientStatsExpected['lifetime_principal_repaid'];
        $this->easyPayLoanStatsExpected['lifetime_income_repaid'] = $this->clientStatsExpected['lifetime_income_repaid'];//TODO: get rid of those BS columns
        $this->easyPayLoanStatsExpected['previous_applications_count'] = 2;
        $this->easyPayLoanStatsExpected['previous_loans_count'] = 2;
        $this->easyPayLoanStatsExpected['days_after_previous_loan'] = -1;//has active loan
        $this->easyPayLoanStatsExpected['days_by_contract'] = 30;
        $this->easyPayLoanStatsExpected['days_amended'] = 30;
        $this->easyPayLoanStatsExpected['creation_browser'] = 'create_test_browser';
        $this->easyPayLoanStatsExpected['sign_ip'] = '**************';
        $this->easyPayLoanStatsExpected['rate_annual_percentage'] = $easyPayLoan->getCredit()->gpr;
        $this->testObject($this->easyPayLoanStatsExpected, $easyPayLoan->loanActualStats);

        $this->clientStatsExpected['applications_count'] = 3;
        $this->testObject($this->clientStatsExpected, $easyPayLoan->client->clientActualStats);

        /*****************APPROVING EASYPAY*********************/
        $easyPayLoan = LoanSteps::approveLoan($easyPayLoan);

        $this->easyPayLoanExpected['loan_status_id'] = LoanStatus::APPROVED_STATUS_ID;
        $this->testObject($this->easyPayLoanExpected, $easyPayLoan);

        $this->clientStatsExpected['approved_loans_count'] = 3;

        $this->testObject($this->clientStatsExpected, $easyPayLoan->client->clientActualStats);

        /*****************ACTIVATING EASYPAY*********************/
        sleep(1);
        $fullCashLoanDebt = $cashLoan->getCredit()->outstandingAmountCollection()->getTotalOutstandingAmount();//58266
        $cashLoanOverdueAmount = $cashLoan->getCredit()->loanCarton()->currentOverdueAmount;//TODO: why it doesnt equal previous?
        $easyPayLoan = LoanSteps::activateEasyPayLoan($easyPayLoan);
        $cashLoan->refresh();
        $cashLoanProfit = $fullCashLoanDebt - self::CASH_LOAN_PRINCIPAL;
        $easyPayLoanProfit = 0 - self::EASY_PAY_LOAN_PRINCIPAL;

        $this->cashLoanInstallmentExpected['has_payment'] = 1;
        $this->cashLoanInstallmentExpected['first_payment_received_at'] = now()->format('Y-m-d');
        $this->cashLoanInstallmentExpected['days_passed_before_first_payment'] = 3;
        $this->testObject($this->cashLoanInstallmentExpected, $cashLoan->installments[0]->refresh());

        $this->easyPayLoanExpected['loan_status_id'] = LoanStatus::ACTIVE_STATUS_ID;
        $this->testObject($this->easyPayLoanExpected, $easyPayLoan);

        $this->cashLoanExpected['loan_status_id'] = LoanStatus::REPAID_STATUS_ID;
        $this->testObject($this->cashLoanExpected, $cashLoan);

        $this->refinanceCashPaymentExpected['days_on_books'] = 33;
        $this->refinanceCashPaymentExpected['months_on_books'] = 2;
        $this->refinanceCashPaymentExpected['payment_passed_days'] = 3;
        $this->refinanceCashPaymentExpected['payment_overdue_days'] = 3;//TODO: why do we even have this column? Previous has the same data
        $this->refinanceCashPaymentExpected['payment_overdue_amount'] = intToFloat($cashLoanOverdueAmount);
        $this->testObject($this->refinanceCashPaymentExpected, $cashLoan->payments[1]);

        $this->cashLoanStatsExpected['has_payment'] = 1;
        $this->cashLoanStatsExpected['repayment_days_value'] = 3;
        $this->cashLoanStatsExpected['first_payment_received_at'] = $currentDateString;
        $this->cashLoanStatsExpected['overdue_installments'] = 0;
        $this->testObject($this->cashLoanStatsExpected, $cashLoan->loanActualStats->refresh());

        $this->testObject($this->easyPayLoanStatsExpected, $easyPayLoan->loanActualStats);

        $this->clientStatsExpected['activated_loans_count'] = 3;
        $this->clientStatsExpected['repaid_loans_count'] = 2;
        $this->clientStatsExpected['lifetime_principal_withdrawn'] = intToFloat(self::BANK_LOAN_PRINCIPAL + self::CASH_LOAN_PRINCIPAL + self::EASY_PAY_LOAN_PRINCIPAL);
        $this->clientStatsExpected['lifetime_principal_repaid'] = intToFloat(self::BANK_LOAN_PRINCIPAL + self::CASH_LOAN_PRINCIPAL);
        $this->clientStatsExpected['lifetime_income_repaid'] = intToFloat($bankLoanProfit + $cashLoanProfit); //(148.02)
        $this->clientStatsExpected['lifetime_value_total'] = intToFloat($bankLoanProfit + $cashLoanProfit + $easyPayLoanProfit);//(-451.98)
        $this->testObject($this->clientStatsExpected, $easyPayLoan->client->clientActualStats);

        //testing recalculation
        Artisan::call(RecalculateStatsCommand::name(), ['clientId'=>$client->getKey()]);
        $this->cashLoanStatsExpected['days_after_previous_loan'] = 40;//client was sent back in time for 7 and 33 days along with previous bank loan
        $this->testObject($this->clientStatsExpected, $easyPayLoan->client->clientActualStats);
        $this->testObject($this->cashLoanStatsExpected, $cashLoan->loanActualStats->refresh());
        $this->testObject($this->cashLoanInstallmentExpected, $cashLoan->installments[0]->refresh());

        /******************* ANOTHER 4 DAYS HAS PASSED AND TASK FOR REFUND ***************************/
        $client = $this->timeMachine->sendBackInTime($client, 4);
        $bankLoan = $bankLoan->fresh();
        $cashLoan = $cashLoan->fresh();
        $easyPayLoan = $easyPayLoan->fresh();

        $this->cashLoanStatsExpected['first_payment_received_at'] = $days4AgoDateString;
        $this->testObject($this->cashLoanStatsExpected, $cashLoan->loanActualStats->refresh());

        $this->easyPayLoanStatsExpected['days_in_use'] = 4;
        $this->easyPayLoanStatsExpected['days_after_previous_loan'] = 0; //0 days since closing cash loan
        $this->testObject($this->easyPayLoanStatsExpected, $easyPayLoan->loanActualStats->refresh());

        $this->clientStatsExpected['first_loan_created_at'] = $days44AgoDateString;
        $this->clientStatsExpected['first_loan_activated_at'] = $days44AgoDateString;
        $this->clientStatsExpected['first_loan_repaid_at'] = $days44AgoDateString;
        $this->clientStatsExpected['max_overdue_date'] = $currentDateString;
        $this->clientStatsExpected['repaid_loans_count'] = 2;//it was wrong on previous step
        $this->testObject($this->clientStatsExpected, $client->clientActualStats);

        $this->assertCount(2, $cashLoan->payments);//1 on creation, 1 by refinancing
        $this->assertCount(2, $easyPayLoan->payments);//2 on creation
        $this->assertCount(3, $bankLoan->payments);
        $this->artisan('script:create-easy-pay-refund-tasks');
        /** @var Payment $unreceivedPayment */
        $unreceivedPayment = Payment::where([
            'loan_id'=>$easyPayLoan->getKey(),
            'direction'=>'out',
            'payment_method_id' => PaymentMethodEnum::EASY_PAY->id()
        ])->first();
        /** @var PaymentTask $currentPaymentTask */
        $currentPaymentTask = PaymentTask::all()->first();
        $this->assertEquals(
            PaymentTaskNameEnum::REQUEST_EASY_PAY_REFUND,
            $currentPaymentTask->name
        );
        $this->assertEquals($unreceivedPayment->getKey(), $currentPaymentTask->payment_id);

        /******************* REQUESTING REFUND ***************************/
        $easyPayLoan = $easyPayLoan->fresh();
        $cashLoan->refresh();
        $cashOverdueAmount = $cashLoan->getCredit()->loanCarton()->currentOverdueAmount;

        $this->cashLoanInstallmentExpected['has_payment'] = 0;
        $this->cashLoanInstallmentExpected['first_payment_received_at'] = null;
        $this->cashLoanInstallmentExpected['days_passed_before_first_payment'] = null;
        $this->cashLoanInstallmentExpected['max_overdue_date'] = $currentDateString;
        $this->cashLoanInstallmentExpected['max_overdue_days'] = 7;
        $this->testObject($this->cashLoanInstallmentExpected, $cashLoan->installments[0]->refresh());

        $this->easyPayLoanExpected['loan_status_id'] = LoanStatus::CANCELLED_STATUS_ID;
        $this->testObject($this->easyPayLoanExpected, $easyPayLoan);

        $this->cashLoanExpected['loan_status_id'] = LoanStatus::ACTIVE_STATUS_ID;
        $this->testObject($this->cashLoanExpected, $cashLoan);

        $this->refundEasyPayExpected['days_on_books'] = 4;
        $this->refundEasyPayExpected['months_on_books'] = 1;
        $this->refundEasyPayExpected['payment_passed_days'] = -26;
        $this->refundEasyPayExpected['payment_overdue_days'] = 0;//refund payment comes max 4 days after loan, ant go to overdue
        $this->refundEasyPayExpected['payment_overdue_amount'] = 0;
        $this->testObject($this->refundEasyPayExpected, $easyPayLoan->payments[1]);

        $this->cashLoanStatsExpected['max_overdue_days'] = 7;
        $this->cashLoanStatsExpected['max_overdue_amount'] = intToFloat($cashOverdueAmount);
        $this->cashLoanStatsExpected['max_overdue_date'] = $currentDateString;
        $this->cashLoanStatsExpected['days_in_use'] = 37;
        $this->cashLoanStatsExpected['has_payment'] = 0;
        $this->cashLoanStatsExpected['first_payment_received_at'] = null;
        $this->cashLoanStatsExpected['overdue_installments'] = 1;
        $this->cashLoanStatsExpected['repayment_days_value'] = null;
        $this->testObject($this->cashLoanStatsExpected, $cashLoan->loanActualStats->refresh());

        $this->easyPayLoanStatsExpected['has_payment'] = 1;//refund payment
        $this->easyPayLoanStatsExpected['first_payment_received_at'] = $currentDateString;
        $this->testObject($this->easyPayLoanStatsExpected, $easyPayLoan->loanActualStats->refresh());

        $this->clientStatsExpected['activated_loans_count'] = 3;
        $this->clientStatsExpected['repaid_loans_count'] = 1;
        $this->clientStatsExpected['cancel_loans_count'] = 1;
        $this->clientStatsExpected['lifetime_principal_withdrawn'] = 1800;
        $this->clientStatsExpected['lifetime_principal_repaid'] = intToFloat(self::BANK_LOAN_PRINCIPAL + self::EASY_PAY_LOAN_PRINCIPAL);
        $this->clientStatsExpected['lifetime_income_repaid'] = intToFloat($bankLoanProfit);
        $this->clientStatsExpected['lifetime_value_total'] = intToFloat($bankLoanProfit - self::CASH_LOAN_PRINCIPAL);//-434.64
        $this->clientStatsExpected['max_overdue_days'] = 7;
        $this->clientStatsExpected['max_overdue_amount'] = intToFloat($cashOverdueAmount);
        $this->clientStatsExpected['max_overdue_date'] = $currentDateString;
        $this->testObject($this->clientStatsExpected, $easyPayLoan->client->clientActualStats);

        /************************** EXTEND CASH LOAN *******************************/
        $realExtensionAmount = $cashLoan->getExtendLoanFeeAmountDb();
        $cashLoan = LoanSteps::postponeLoan($cashLoan, self::CASH_LOAN_EXTENSION_AMOUNT);


        $this->testObject($this->easyPayLoanStatsExpected, $easyPayLoan->loanActualStats->refresh());

        $this->extendCashPaymentExpected['days_on_books'] = 37;
        $this->extendCashPaymentExpected['months_on_books'] = 2;
        $this->extendCashPaymentExpected['payment_passed_days'] = 7;
        $this->extendCashPaymentExpected['payment_overdue_days'] = 7;
        $this->extendCashPaymentExpected['payment_overdue_amount'] = intToFloat($cashOverdueAmount);

        $this->testObject($this->extendCashPaymentExpected, $cashLoan->payments[1]);

        $this->cashLoanStatsExpected['days_amended'] = 45;
        $this->cashLoanStatsExpected['loan_extension_count'] = 1;
        $this->cashLoanStatsExpected['has_payment'] = 1;
        $this->cashLoanStatsExpected['first_payment_received_at'] = $currentDateString;
        $this->cashLoanStatsExpected['overdue_installments'] = 1;//TODO: fix to 0
        $this->testObject($this->cashLoanStatsExpected, $cashLoan->loanActualStats->refresh());

        $this->clientStatsExpected['lifetime_value_total'] = intToFloat($bankLoanProfit - self::CASH_LOAN_PRINCIPAL + $realExtensionAmount);
        $this->clientStatsExpected['lifetime_income_repaid'] = intToFloat($bankLoanProfit + $realExtensionAmount);
        $this->testObject($this->clientStatsExpected, $cashLoan->client->clientActualStats->refresh());
    }

    private function testObject(array $expected, ClientActualStats|LoanActualStats|Payment|Loan|Installment $object): void
    {
        foreach ($expected as $key=>$val){
            if(is_null($val)){
                $this->assertNull($object->getAttribute($key), $key);
            } else {
                $this->assertEquals($val, $object->getAttribute($key), $key);
            }
        }
    }

}
