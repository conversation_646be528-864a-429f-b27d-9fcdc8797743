<?php

namespace Modules\Head\Tests\Unit;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Collect\Domain\Entities\GraceLoanReset;
use Modules\Common\Enums\PaymentDeliveryEnum;
use Modules\Common\Models\Installment;
use Modules\Common\Models\InstallmentSnapshot;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\Tax;
use Modules\Common\Providers\TestDataProvider;
use Modules\Head\Application\Actions\UpdatePaymentScheduleAction;
use Modules\Head\Application\Dto\UpdatePaymentScheduleDto;
use Modules\Payments\Application\Actions\ManualPaymentSaveAction;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\Sales\Http\Dto\ClientDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;
use Tests\TestCase;

/**
 * ./vendor/bin/phpunit Modules/Head/Tests/Unit/DelInstallmentInterestTest.php
 */
class DelInstallmentInterestTest extends TestCase
{
    use DatabaseTransactions;

    private ClientDto $dto;
    private NewAppAction $newAppAction;
    private UpdatePaymentScheduleAction $updatePaymentScheduleAction;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dto = TestDataProvider::get(
            'applicationRequest1',
            DtoSerializerNewClientLoan::class,
            'createClientDto'
        );
        $this->newAppAction = app(NewAppAction::class);
        $this->updatePaymentScheduleAction = app(UpdatePaymentScheduleAction::class);
    }

    /**
     * 1) Нов кредит преди due_date, без плащания - пълно изтриване на лихва
     * @return void
     */
    public function testInstallmentFullDeleteInterest()
    {
        $loan = $this->prepareLoan();

        /** @var Installment $installment ** */
        $installment = $loan->installments->first();

        //// check before update ///////////////////////////////////////////////////////////////////////////////////////
        $this->assertEquals("4.20", $installment->interest);
        $this->assertEquals("3.00", $installment->accrued_interest);
        $this->assertEquals("4.20", $installment->rest_interest);
        $this->assertEquals("0.00", $installment->paid_interest);

        $this->assertEquals("18.66", $installment->penalty);
        $this->assertEquals("13.33", $installment->accrued_penalty);
        $this->assertEquals("18.66", $installment->rest_penalty);
        $this->assertEquals("0.00", $installment->paid_penalty);

        $this->assertEquals("0.00", $installment->late_interest);
        $this->assertEquals("0.00", $installment->paid_late_interest);
        $this->assertEquals("0.00", $installment->rest_late_interest);

        $this->assertEquals("0.00", $installment->late_penalty);
        $this->assertEquals("0.00", $installment->paid_late_penalty);
        $this->assertEquals("0.00", $installment->rest_late_penalty);

        //// update interest to 0.00
        $this->updatePaymentSchedule($loan, 'interest', 0);
        $installment->refresh();

        $installmentSnapshot = $installment->installmentSnapshot;
        $this->assertEquals("4.20", $installmentSnapshot->installment_changes['interest']['from']);
        $this->assertEquals("0.00", $installmentSnapshot->installment_changes['interest']['to']);

        $this->assertEquals("0.00", $installment->interest);
        $this->assertEquals("0.00", $installment->accrued_interest);
        $this->assertEquals("0.00", $installment->rest_interest);
        $this->assertEquals("0.00", $installment->paid_interest);
    }

    /**
     * @return void
     * 2) Нов кредит преди due_date, с частично платена неустойка -  пълно изтриване на неустойка
     */
    public function testInstallmentPartialPaidPenaltyFullDeletePenalty()
    {
        $loan = $this->prepareLoan();

        /** @var Installment $installment ** */
        $installment = $loan->installments->first();

        $this->assertEquals("18.66", $installment->penalty);
        $this->assertEquals("13.33", $installment->accrued_penalty);
        $this->assertEquals("18.66", $installment->rest_penalty);
        $this->assertEquals("0.00", $installment->paid_penalty);

        //// add penalty payment 10.00
        $this->addPaymentAmount($loan, 1000);
        $installment->refresh();

        $this->assertEquals("18.66", $installment->penalty);
        $this->assertEquals("13.33", $installment->accrued_penalty);
        $this->assertEquals("8.66", $installment->rest_penalty);
        $this->assertEquals("10.00", $installment->paid_penalty);

        //// update payment schedule remove all penalty 1000 === 10.00
        $this->updatePaymentSchedule($loan, 'penalty', 1000);
        $installment->refresh();

        $this->assertEquals("10.00", $installment->penalty);
        $this->assertEquals("10.00", $installment->accrued_penalty);
        $this->assertEquals("0.00", $installment->rest_penalty);
        $this->assertEquals("10.00", $installment->paid_penalty);
    }

    /**
     * 3) Нов кредит преди due_date, с частично платена лихва и неустойка - пълно изтриване на лихва и неустойка
     * @return void
     */
    public function testInstallmentPartialPaidPenaltyAndInterestFullDeleteBoth()
    {
        $loan = $this->prepareLoan();

        /** @var Installment $installment ** */
        $installment = $loan->installments->first();

        $this->assertEquals("4.20", $installment->interest);
        $this->assertEquals("3.00", $installment->accrued_interest);
        $this->assertEquals("4.20", $installment->rest_interest);
        $this->assertEquals("0.00", $installment->paid_interest);

        $this->assertEquals("18.66", $installment->penalty);
        $this->assertEquals("13.33", $installment->accrued_penalty);
        $this->assertEquals("18.66", $installment->rest_penalty);
        $this->assertEquals("0.00", $installment->paid_penalty);

        //// add penalty payment 10.00
        $this->addPaymentAmount($loan, 1000);
        $installment->refresh();

        //// check after payment
        $this->assertEquals("18.66", $installment->penalty);
        $this->assertEquals("13.33", $installment->accrued_penalty);
        $this->assertEquals("8.66", $installment->rest_penalty);
        $this->assertEquals("10.00", $installment->paid_penalty);

        //// update payment schedule remove all penalty 1000 === 10.00
        $this->updatePaymentSchedule($loan, 'penalty', 1000);
        $installment->refresh();

        /// check after full delete penalty
        $this->assertEquals("10.00", $installment->penalty);
        $this->assertEquals("10.00", $installment->accrued_penalty);
        $this->assertEquals("0.00", $installment->rest_penalty);
        $this->assertEquals("10.00", $installment->paid_penalty);

        /// add partial payment for interest
        $this->addPaymentAmount($loan, 200);
        $installment->refresh();

        /// check partial paid interest
        $this->assertEquals("4.20", $installment->interest);
        $this->assertEquals("3.00", $installment->accrued_interest);
        $this->assertEquals("2.20", $installment->rest_interest);
        $this->assertEquals("2.00", $installment->paid_interest);

        //// update payment schedule remove all interest 200 === 2.00
        $this->updatePaymentSchedule($loan, 'interest', 200);
        $installment->refresh();

        /// check after delete all interest
        $this->assertEquals("2.00", $installment->interest);
        $this->assertEquals("2.00", $installment->accrued_interest);
        $this->assertEquals("0.00", $installment->rest_interest);
        $this->assertEquals("2.00", $installment->paid_interest);
    }

    /**
     * 4) Нов кредит преди due_date, с частично платена лихва и неустойка - частично изтриване на лихва и неустойка
     * @return void
     */
    public function testPartialPaidPenaltyAndInterestAndPartialDelete()
    {
        $loan = $this->prepareLoan();

        /** @var Installment $installment ** */
        $installment = $loan->installments->first();

        //// add penalty payment 10.00
        $this->addPaymentAmount($loan, 1000);
        $installment->refresh();

        //// check after payment
        $this->assertEquals("18.66", $installment->penalty);
        $this->assertEquals("13.33", $installment->accrued_penalty);
        $this->assertEquals("8.66", $installment->rest_penalty);
        $this->assertEquals("10.00", $installment->paid_penalty);

        //// update payment schedule remove partial penalty 1100 === 11.00
        $this->updatePaymentSchedule($loan, 'penalty', 1100);
        $installment->refresh();

        /// check after full delete penalty
        $this->assertEquals("11.00", $installment->penalty);
        $this->assertEquals("11.00", $installment->accrued_penalty);
        $this->assertEquals("1.00", $installment->rest_penalty);
        $this->assertEquals("10.00", $installment->paid_penalty);

        /// add partial payment for interest
        $this->addPaymentAmount($loan, 200);
        $installment->refresh();

        /// check partial paid interest
        $this->assertEquals("4.20", $installment->interest);
        $this->assertEquals("3.00", $installment->accrued_interest);
        $this->assertEquals("3.20", $installment->rest_interest);
        $this->assertEquals("1.00", $installment->paid_interest);

        //// update payment schedule remove partial interest 200 === 2.00
        $this->updatePaymentSchedule($loan, 'interest', 200);
        $installment->refresh();

        /// check after partial all interest
        $this->assertEquals("2.00", $installment->interest);
        $this->assertEquals("2.00", $installment->accrued_interest);
        $this->assertEquals("1.00", $installment->rest_interest);
        $this->assertEquals("1.00", $installment->paid_interest);
    }

    /**
     * 5) Нов кредит преди due_date, с частично платена лихва и неустойка - частично изтриване на лихва и неустойка = II пример
     * @return void
     */
    public function testPartialPaidPenaltyAndInterestPartialDelete()
    {
        $loan = $this->prepareLoan();

        /** @var Installment $installment ** */
        $installment = $loan->installments->first();

        //// check before modifications
        $this->assertEquals("18.66", $installment->penalty);
        $this->assertEquals("13.33", $installment->accrued_penalty);
        $this->assertEquals("18.66", $installment->rest_penalty);
        $this->assertEquals("0.00", $installment->paid_penalty);

        $this->assertEquals("4.20", $installment->interest);
        $this->assertEquals("3.00", $installment->accrued_interest);
        $this->assertEquals("4.20", $installment->rest_interest);
        $this->assertEquals("0.00", $installment->paid_interest);

        /// add partial payment for penalty 933 = 9.33
        $this->addPaymentAmount($loan, 933);
        $installment->refresh();

        //// check before modifications
        $this->assertEquals("18.66", $installment->penalty);
        $this->assertEquals("13.33", $installment->accrued_penalty);
        $this->assertEquals("9.33", $installment->rest_penalty);
        $this->assertEquals("9.33", $installment->paid_penalty);

        /// remove 50% from rest_penalty 9.33/2 + paid penalty 9.33 === 13.99
        $this->updatePaymentSchedule($loan, 'penalty', 1399);
        $installment->refresh();

        /// check after remove 50% from penalty
        $this->assertEquals("13.99", $installment->penalty);
        $this->assertEquals("13.33", $installment->accrued_penalty);
        $this->assertEquals("4.66", $installment->rest_penalty);
        $this->assertEquals("9.33", $installment->paid_penalty);
        ////////////////////////////////////////////////////////////////////////
        /// End for penalty cases


        /// add partial payment for interest (4.66 for penalty) + 2.10 for interest total: 6.76
        $this->addPaymentAmount($loan, 676);
        $installment->refresh();

        /// check amounts after partial paid interest
        $this->assertEquals("4.20", $installment->interest);
        $this->assertEquals("3.00", $installment->accrued_interest);
        $this->assertEquals("2.10", $installment->rest_interest);
        $this->assertEquals("2.10", $installment->paid_interest);

        /// remove 50% from rest_interest 2.10/2 + paid interest 2.10 === 3.15
        $this->updatePaymentSchedule($loan, 'interest', 315);
        $installment->refresh();

        /// check after partial remove interest
        $this->assertEquals("3.15", $installment->interest);
        $this->assertEquals("3.00", $installment->accrued_interest);
        $this->assertEquals("1.05", $installment->rest_interest);
        $this->assertEquals("2.10", $installment->paid_interest);
    }

    /**
     * 6) Нов кредит след due_date, с частично платена лихва и неустойка - частично изтриване на лихва и неустойка И частично триено на late*
     * @return void
     */
    public function testPartialPaidAllAndPartialDeleteAll()
    {
        $loan = $this->prepareLoan();

        /** @var Installment $installment ** */
        $installment = $loan->installments->first();

        $this->assertEquals("0.00", $installment->late_interest);
        $this->assertEquals("0.00", $installment->paid_late_interest);
        $this->assertEquals("0.00", $installment->rest_late_interest);

        $this->assertEquals("0.00", $installment->late_penalty);
        $this->assertEquals("0.00", $installment->paid_late_penalty);
        $this->assertEquals("0.00", $installment->rest_late_penalty);

        /// 10 days overdue
        \Artisan::call("script:make-loan-overdue {$loan->getKey()} 12 out");
        $loan->refresh();
        $installment->refresh();

        $this->assertEquals("1.73", $installment->late_interest);
        $this->assertEquals("0.00", $installment->paid_late_interest);
        $this->assertEquals("1.73", $installment->rest_late_interest);

        $this->assertEquals("1.73", $installment->late_penalty);
        $this->assertEquals("0.00", $installment->paid_late_penalty);
        $this->assertEquals("1.73", $installment->rest_late_penalty);

        /// add partial payment for late penalty 0.70
        $this->addPaymentAmount($loan, 70);
        $installment->refresh();

        $this->assertEquals("1.73", $installment->late_penalty);
        $this->assertEquals("0.70", $installment->paid_late_penalty);
        $this->assertEquals("1.03", $installment->rest_late_penalty);

        /// partially remove late penalty (0.70 + paid penalty 0.70) = 1.40
        $this->updatePaymentSchedule($loan, 'late_penalty', 140);
        $installment->refresh();

        $this->assertEquals("1.40", $installment->late_penalty);
        $this->assertEquals("0.70", $installment->paid_late_penalty);
        $this->assertEquals("0.70", $installment->rest_late_penalty);

        //// remove all late penalty
        $this->updatePaymentSchedule($loan, 'late_penalty', 70);
        $installment->refresh();

        $this->assertEquals("0.70", $installment->late_penalty);
        $this->assertEquals("0.70", $installment->paid_late_penalty);
        $this->assertEquals("0.00", $installment->rest_late_penalty);


        /// add partial payment for late interest 0.70
        $this->addPaymentAmount($loan, 73);
        $installment->refresh();

        $this->assertEquals("1.73", $installment->late_interest);
        $this->assertEquals("0.73", $installment->paid_late_interest);
        $this->assertEquals("1.00", $installment->rest_late_interest);

        //// partial remove late interest (0.73 paid + 0.50 remove) 1.23
        $this->updatePaymentSchedule($loan, 'late_interest', 123);
        $installment->refresh();

        $this->assertEquals("1.23", $installment->late_interest);
        $this->assertEquals("0.73", $installment->paid_late_interest);
        $this->assertEquals("0.50", $installment->rest_late_interest);

        //// remove all late interest (0.73 paid + 0.50 remove) 1.23
        $this->updatePaymentSchedule($loan, 'late_interest', 73);
        $installment->refresh();

        $this->assertEquals("0.73", $installment->late_interest);
        $this->assertEquals("0.73", $installment->paid_late_interest);
        $this->assertEquals("0.00", $installment->rest_late_interest);
    }

    public function testLoanTaxes()
    {
        $loan = $this->prepareLoan();

        $loan->taxes()->create([
            'client_id' => $loan->client_id,
            'loan_id' => $loan->getKey(),
            'type' => Tax::TAX_TYPE_MANUALLY_ADDED_EXPENSE,
            'amount' => 10000,
            'rest_amount' => 10000,
            'currency_id' => 1,
            'comment' => 'TEST TAX'
        ]);

        /** @var Tax $loanTax * */
        $loanTax = $loan->taxes()->first();

        $this->assertEquals("10000", $loanTax->amount);
        $this->assertEquals("0", $loanTax->paid_amount);
        $this->assertEquals("10000", $loanTax->rest_amount);

        $this->addPaymentAmount($loan, 2000);
        $loanTax->refresh();

        $this->assertEquals("10000", $loanTax->amount);
        $this->assertEquals("2000", $loanTax->paid_amount);
        $this->assertEquals("8000", $loanTax->rest_amount);

        $this->updateLoanTaxes($loan, 'amount', 5000);
        $loanTax->refresh();

        $this->assertEquals("5000", $loanTax->amount);
        $this->assertEquals("2000", $loanTax->paid_amount);
        $this->assertEquals("3000", $loanTax->rest_amount);

        /// full remove
        $this->updateLoanTaxes($loan, 'amount', 2000);
        $loanTax->refresh();

        $this->assertEquals("2000", $loanTax->amount);
        $this->assertEquals("2000", $loanTax->paid_amount);
        $this->assertEquals("0000", $loanTax->rest_amount);
    }

    public function ddVars(Installment $installment)
    {
        dd([
            'interest' => $installment->interest,
            'accrued_interest' => $installment->accrued_interest,
            'rest_interest' => $installment->rest_interest,
            'paid_interest' => $installment->paid_interest,

            'PENALTY',
            'penalty' => $installment->penalty,
            'accrued_penalty' => $installment->accrued_penalty,
            'rest_penalty' => $installment->rest_penalty,
            'paid_penalty' => $installment->paid_penalty,

            "LATE INTEREST",
            'late_interest' => $installment->late_interest,
            'paid_late_interest' => $installment->paid_late_interest,
            'rest_late_interest' => $installment->rest_late_interest,

            "LATE PENALTY",
            'late_penalty' => $installment->late_penalty,
            'paid_late_penalty' => $installment->paid_late_penalty,
            'rest_late_penalty' => $installment->rest_late_penalty,
        ]);
    }

    private function updateLoanTaxes(
        Loan $loan,
        string $key,
        int $value,
    ) {
        $updateData['loan_id'] = $loan->getKey();
        $updateData['installments'] = $loan->installments->map(function (Installment $installment) {
            return [
                'installment_id' => $installment->getKey(),
                'interest' => $installment->interest,
                'penalty' => $installment->penalty,
                'late_interest' => $installment->late_interest,
                'late_penalty' => $installment->late_penalty,
            ];
        })->toArray();
        $updateData['taxes'] = $loan->taxes->map(function (Tax $tax) {
            return [
                'tax_id' => $tax->getKey(),
                'amount' => $tax->amount,
                'paid_amount' => $tax->paid_amount,
                'rest_amount' => $tax->rest_amount,
            ];
        })->toArray();
        $updateData['taxes'][0][$key] = intToFloat($value);

        //// update payment schedule
        $this->updatePaymentScheduleAction->execute(
            UpdatePaymentScheduleDto::from($updateData),
            $loan
        );
        $loan->refresh();
    }

    private function updatePaymentSchedule(
        Loan $loan,
        string $key,
        int $value,
    ) {
        $updateData['loan_id'] = $loan->getKey();
        $updateData['installments'] = $loan->installments->map(function (Installment $installment) {
            return [
                'installment_id' => $installment->getKey(),
                'interest' => $installment->interest,
                'penalty' => $installment->penalty,
                'late_interest' => $installment->late_interest,
                'late_penalty' => $installment->late_penalty,
            ];
        })->toArray();
        $updateData['installments'][0][$key] = intToFloat($value);

        //// update payment schedule
        $this->updatePaymentScheduleAction->execute(
            UpdatePaymentScheduleDto::from($updateData),
            $loan
        );
        $loan->refresh();
    }

    private function addPaymentAmount(
        Loan $loan,
        int $paymentAmount
    ) {
        $this->app->make(ManualPaymentSaveAction::class)->execute([
            'loans' => [$loan->getKey() => $loan->getKey()],
            'loanPaymentAmount' => [$loan->getKey() => $paymentAmount],
            'loanAction' => [$loan->getKey() => PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT->value],
            'payment_method_id' => PaymentMethod::PAYMENT_METHOD_EASYPAY,
            'payment_amount' => $paymentAmount,
            'description' => 'TEST',
            'office_id' => 1,
            'bank_account_id' => 1
        ]);
        $loan->refresh();
    }

    private function prepareLoan(
        ?int $outDays = 5
    ): Loan {
        $dbClient = $this->newAppAction->execute($this->dto)->dbModel()->refresh();
        $this->assertCount(1, $dbClient->loans);

        /** @var Loan $loan *** */
        $loan = $dbClient->loans->first();
        $loan->setAttribute('loan_status_id', LoanStatus::ACTIVE_STATUS_ID);
        $loan->setAttribute('period_grace', 0);
        $loan->setAttribute('grace_until', null);
        $loan->saveQuietly();

        /// reset grace period
        app(GraceLoanReset::class)->build($loan, 'test');
        $loan->refresh();

        /// make loan overdue 60 days
        \Artisan::call("script:make-loan-overdue {$loan->getKey()} {$outDays} out");
        $loan->refresh();

        return $loan;
    }
}
