<?php

namespace Modules\Head\Exports;

use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Modules\Common\Models\Loan;

final class OverdueStatsPhysicalOfficesExport implements FromQuery, ShouldAutoSize, WithHeadings, WithMapping, WithCustomChunkSize
{
    use Exportable;

    public function __construct(private readonly Builder $query)
    {
    }

    public function query(): Builder
    {
        return $this->query;
    }

    public function chunkSize(): int
    {
        return 100;
    }

    public function headings(): array
    {
        return [
            __('table.LoanId'),
            __('table.ClientFullName'),
            __('table.Pin'),
            __('table.Phone'),
            __('table.Address'),
            __('table.OverdueAmount'),
            __('table.OverdueDays'),
            __('table.LastPayment'),
            "1." . __('table.Guarant'),
            "1." . __('table.Phone'),
            "1." . __('table.Address'),
            "2." . __('table.Guarant'),
            "2." . __('table.Phone'),
            "2." . __('table.Address'),
            __('table.LastPromiseDetails'),
            __('table.PromisedAmount'),
        ];
    }

    /**
     * @param Loan $row
     */
    public function map($row): array
    {
        return [
            $row->loan_id,
            $row->client_name,
            $row->pin,
            $row->phone,
            $row->address,
            $row->current_overdue_amount,
            $row->current_overdue_days,
            intToFloat($row->last_payment_amount),
            $row->first_guarantor_name,
            $row->first_guarantor_phone,
            $row->first_guarantor_address,
            $row->second_guarantor_name,
            $row->second_guarantor_phone,
            $row->second_guarantor_address,
            $row->bucket_task_details,
            $row->bucket_task_promised_amount,
        ];
    }
}
