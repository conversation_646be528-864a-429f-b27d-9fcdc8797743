<?php

namespace Modules\Head\Services;

class SpreadsheetHelper
{
    public function setAutoSize($sheet)
    {
        // Calculate the maximum width for each column
        foreach (range('A', 'Z') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }

        // Adjust row height based on content
        foreach ($sheet->getRowIterator() as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(true);

            $maxHeight = 15; // Default row height, adjust as needed

            foreach ($cellIterator as $cell) {
                $cellValue = $cell->getValue();
                if (is_string($cellValue)) {
                    $lineCount = substr_count($cellValue, "\n") + 1;
                    $estimatedHeight = $lineCount * 15; // Estimate 15 points per line, adjust as needed
                    if ($estimatedHeight > $maxHeight) {
                        $maxHeight = $estimatedHeight;
                    }
                }
            }
            $sheet->getRowDimension($row->getRowIndex())->setRowHeight($maxHeight);
        }

        return $sheet;
    }
}