<?php

namespace Modules\Head\Services;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\Common\Models\Loan;
use Modules\Product\Services\ProductSettingsService;

class CalendarService
{
    public static function getRepaymentStatsForDate(
        Loan $loan,
        string $dateString // Format: Y-m-d
    ): array {

        $date = Carbon::parse($dateString)->startOfDay();

        if ($date->isToday() || $date->isPast()) {
            return self::calculateStatsForPastOrToday($loan);
        }

        $result = self::calculateFutureStats($loan, $date);

        // fix based on: MIN_REPAY_AMOUNT_KEY & MIN_REPAY_INST_COUNT_KEY
        // so if we set at the begin min due amount for future it will be a mininum possible accrued!
        // means:
        // we calculated at the begin minimal due, that client should pay
        // and here at calendar we're calculating due to some date,
        // so if our new calculation is lower then minimal due,
        // we should use minimal due instead!
        $minAmountSettings = app(ProductSettingsService::class)
            ->getProductSettingsForMinRepayAmount($loan->product_id);

        // if no spec.settings - use calculated result
        if (empty($minAmountSettings)) {
            return $result;
        }

        // if calculated result less than default, use default
        $resultBasic = self::calculateStatsForPastOrToday($loan);
        if ($resultBasic['early'] > $result['early']) {
            return $resultBasic;
        }

        return $result;
    }

    protected static function calculateStatsForPastOrToday(Loan $loan): array
    {
        $amounts = $loan->getEarlyRepaymentDebtDbSeparated();
        return self::buildResultArray($loan, $amounts);
    }

    protected static function calculateFutureStats(
        Loan $loan,
        Carbon $date
    ): array {

        $installments = $loan->getUnpaidInstallments();
        $lastInstallment = $installments->last();
        $lastInstallmentDate = Carbon::parse($lastInstallment->due_date)->startOfDay();

        if ($date->gte($lastInstallmentDate)) {
            $amounts = $loan->getRegularRepaymentDebtDbSeparated();
        } else {
            $amounts = self::simulateUnpaidInstallments($loan, $installments, $date);
        }

        return self::buildResultArray($loan, $amounts);
    }

    protected static function simulateUnpaidInstallments(
        Loan $loan,
        Collection $installments,
        Carbon $date
    ): array {

        $amounts = [
            'rest_principal' => 0,
            'rest_interest' => 0,
            'rest_penalty' => 0,
            'rest_accrued_interest' => 0,
            'rest_accrued_penalty' => 0,
            'rest_late_interest' => 0,
            'rest_late_penalty' => 0,
            'rest_taxes' => $loan->getTotalUnpaidTaxAmount(),
        ];

        // Whether to skip calculating for other installments after the first accrual calculation
        $skipOthers = false;

        foreach ($installments as $installment) {
            $installmentDate = Carbon::parse($installment->due_date)->startOfDay();

            // Always add these amounts
            $amounts['rest_principal'] += floatToInt($installment->rest_principal);
            $amounts['rest_late_interest'] += floatToInt($installment->rest_late_interest);
            $amounts['rest_late_penalty'] += floatToInt($installment->rest_late_penalty);

            // If due-date has passed, add rest of the amounts
            if ($installmentDate->lte($date)) {

                $amounts['rest_interest'] += floatToInt($installment->rest_interest);
                $amounts['rest_penalty'] += floatToInt($installment->rest_penalty);

            } else {

                $accrInstInterest = 0;
                $accrInstPenalty = 0;

                if ($skipOthers) {
                    continue;
                }

                $startFrom = Carbon::parse($loan->activated_at)->startOfDay();
                if ($installment->seq_num > 1) {
                    $prevInstallment = $installment->getPreviousInstallment();
                    if (!empty($prevInstallment->due_date)) {
                        $startFrom = Carbon::parse($prevInstallment->due_date);
                    }
                }

                // ако не сме влезнали в периода на вноска, т.е. тя още не е започнала да расте
                // направо я пропускаме
                if ($date->lt($startFrom)) {
                    $skipOthers = true;
                    continue;
                }

                $totalUsageDays = $startFrom->diffInDays($date);
                $totalInstallmentDays = $startFrom->diffInDays($installmentDate);

                $accrInstInterest = ($totalInstallmentDays == 0 ? $installment->interest : $installment->interest / $totalInstallmentDays * $totalUsageDays);
                $accrInstPenalty = ($totalInstallmentDays == 0 ? $installment->penalty :$installment->penalty / $totalInstallmentDays * $totalUsageDays);
                if ($accrInstInterest < 0) {
                    $accrInstInterest = 0;
                }
                if ($accrInstPenalty < 0) {
                    $accrInstPenalty = 0;
                }
                $accrInstInterest = round($accrInstInterest, 2);
                $accrInstPenalty = round($accrInstPenalty, 2);

                // ако смятаме акруд, а там има нещо предплатено, което покрива акруд стойности -> зануляваме
                $restInterest = $accrInstInterest - $installment->paid_interest;
                if ($restInterest < 0) {
                    $restInterest = 0;
                }
                $restPenalty = $accrInstPenalty - $installment->paid_penalty;
                if ($restPenalty < 0) {
                    $restPenalty = 0;
                }

                $amounts['rest_accrued_interest'] += floatToInt($restInterest);
                $amounts['rest_accrued_penalty'] += floatToInt($restPenalty);

                // щом сме в този IF означава че текуща падежна дата не е настъпила,
                // т.е. текуща вноска все още расте, затова,
                // слагаме този флаг за следващите вноски, да се пропускат,
                // щото щом тази не минала, следващите не са минали със сигурност
                $skipOthers = true;
            }
        }

        return $amounts;
    }

    protected static function buildResultArray(
        Loan $loan,
        array $amounts
    ): array {

        return [
            'regular' => $loan->getRegularRepaymentDebtDb(),
            'early' => array_sum([
                $amounts['rest_principal'],
                $amounts['rest_interest'],
                $amounts['rest_penalty'],
                $amounts['rest_accrued_interest'],
                $amounts['rest_accrued_penalty'],
                $amounts['rest_late_interest'],
                $amounts['rest_late_penalty'],
                $amounts['rest_taxes'],
            ]),
            'outstanding_amount_principal' => $amounts['rest_principal'],
            'accrued_amount_interest' => $amounts['rest_interest'] + $amounts['rest_accrued_interest'],
            'accrued_amount_penalty' => $amounts['rest_penalty'] + $amounts['rest_accrued_penalty'],
            'outstanding_amount_taxes' => $amounts['rest_taxes'],
            'accrued_amount_late_interest' => $amounts['rest_late_interest'],
            'accrued_amount_late_penalty' => $amounts['rest_late_penalty'],

            // client profile:
            'interests' => (
                $amounts['rest_interest']
                + $amounts['rest_penalty']
                + $amounts['rest_accrued_interest']
                + $amounts['rest_accrued_penalty']
                + $amounts['rest_late_interest']
            ),
            'taxes' => (
                $amounts['rest_taxes']
                + $amounts['rest_late_penalty']
            ),
        ];
    }
}
