@php
    /**
     * @var \Modules\Common\Models\Payment $payment
     */
@endphp
<tr id="collapse-{{$payment->getKey()}}" class="collapse bg-light p-0">
    <td colspan="8" class="p-0">
        <div class="row justify-content-md-center">
            <div class="col-4 bg-white">
                <h4 class="pt-4 card-title">{{ __('head::clientCard.paymentDetails') }}</h4>
                <table class="table table-bordered table-sm">
                    @if($payment->creator)
                        <tr>
                            <th>{{ __('head::clientCard.createdBy') }}</th>
                            <td>{{ $payment->creator->getFullNames() }}</td>
                        </tr>
                        <tr>
                            <th>{{ __('head::clientCard.lastChange') }}</th>
                            <td>
                                {{ $payment->creator->getFullNames() }}
                                {{--                                {{ !empty($payment->handled_at) ? $payment->handled_at?->format('d-m-Y h:i:s') : '' }}--}}
                                {{$payment->handled_at}}
                            </td>
                        </tr>
                    @endif
                    @if($payment->paymentMethod)
                        <tr>
                            <th>{{ __('head::clientCard.paymentMethod') }}</th>
                            <td>{{ __('payments::paymentMethods.' . $payment->paymentMethod->getKey()) }}</td>
                        </tr>
                    @endif

                    <tr>
                        <th>{{ __('head::clientCard.office') }}</th>
                        <td>{{ $payment->office->name }}</td>
                    </tr>
                    <tr>
                        <th>{{ __('head::clientCard.documentNum') }}</th>
                        <td>{{ $payment->document_number }}</td>
                    </tr>
                </table>
            </div>
        </div>
        <!-- End ./row -->
    </td>
</tr>
