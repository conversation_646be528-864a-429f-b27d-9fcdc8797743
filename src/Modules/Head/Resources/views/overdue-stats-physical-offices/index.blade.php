@extends('layouts.app')

@php
    /** @var \Modules\Common\Models\Loan[] $loans */
    /** @var $filterForm */
    /** @var int $countOfFilteredClients */
@endphp

@section('content')
    <x-card-filter-form
        :filter-form="$filterForm"
        col-lg="2"
        query-string="true"
    />
    <x-card>
        <x-slot:title>{{ __('menu.OverdueStatsPhysicalOffices') }}</x-slot:title>

        <x-table>
            <x-slot:head>
                <tr>
                    <th>{{ __('table.LoanId') }}</th>
                    <th>{{ __('table.ClientFullName') }}</th>
                    <th>{{ __('table.Pin') }}</th>
                    <th>{{ __('table.Phone') }}</th>
                    <th>{{ __('table.Address') }}</th>
                    <th>{{ __('table.OverdueAmount') }}</th>
                    <th>{{ __('table.OverdueDays') }}</th>
                    <th>{{ __('table.LastPayment') }}</th>
                    <th>{!! "1.&nbsp;" . __('table.Guarant') !!}</th>
                    <th>{!! "1.&nbsp;" . __('table.Phone') !!}</th>
                    <th>{!! "1.&nbsp;" . __('table.Address') !!}</th>
                    <th>{!! "2.&nbsp;" . __('table.Guarant') !!}</th>
                    <th>{!! "2.&nbsp;" . __('table.Phone') !!}</th>
                    <th>{!! "2.&nbsp;" . __('table.Address') !!}</th>
                    <th>{{ __('table.LastPromiseDetails') }}</th>
                    <th>{{ __('table.PromisedAmount') }}</th>
                </tr>
            </x-slot:head>
            @foreach($loans as $loan)
                <tr>
                    <td>{{ $loan->loan_id }}</td>
                    <td>{{ $loan->client_name }}</td>
                    <td>{{ $loan->pin }}</td>
                    <td>{{ $loan->phone }}</td>
                    <td>{{ $loan->address }}</td>
                    <td>{{ $loan->current_overdue_amount }}</td>
                    <td>{{ $loan->current_overdue_days }}</td>
                    <td>{{ intToFloat($loan->last_payment_amount) }}</td>
                    <td>{{ $loan->first_guarantor_name }}</td>
                    <td>{{ $loan->first_guarantor_phone }}</td>
                    <td>{{ $loan->first_guarantor_address }}</td>
                    <td>{{ $loan->second_guarantor_name }}</td>
                    <td>{{ $loan->second_guarantor_phone }}</td>
                    <td>{{ $loan->second_guarantor_address }}</td>
                    <td>{{ $loan->bucket_task_details }}</td>
                    <td>{{ $loan->bucket_task_promised_amount }}</td>
                </tr>
            @endforeach
        </x-table>
        <x-table-pagination :rows="$loans" show-all-pages="false" max-per-page="250"/>
    </x-card>
@endSection
