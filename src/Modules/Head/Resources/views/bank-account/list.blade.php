@extends('layouts.app')
@php
    $sortingArray = [
       'bank_account' =>[
           'name' => __('table.Name'),
           'type' => __('table.Type'),
           'account_iban' => __('table.Iban'),
           'active' => __('table.Active'),
           'created_at' => __('table.CreatedAt'),
           'created_by' => __('table.CreatedBy'),
           'updated_at' => __('table.UpdatedAt'),
           'updated_by' => __('table.UpdatedBy')
   ]
   ];
@endphp
@section('style')
    <style>
        .class-no-label > label {
            display: none;
        }

        .class-no-label .form-control.live-search-city {
            width: 100% !important;
        }
    </style>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <form id="bankAccountForm" class="form-inline card-body"
                      action="{{ route('head.bankAccount.list') }}"
                      method="PUT">
                    @csrf
                    <div class="form-row w-100">
                        <div class="col-lg-2 mb-3">
                            <input name="name" class="form-control w-100 mb-3" type="text"
                                   placeholder="{{__('table.FilterByName')}}"
                                   value="{{ session($cacheKey . '.name') }}">
                        </div>
                        <div class="col-lg-2 mb-3">
                            <select class="form-control" required="" id="payment_method_id" name="payment_method_id"
                                    style="width: 100%;">
                                <option value="">Всички</option>
                                @foreach($paymentMethods as $paymentMethod)
                                    <option value="{{$paymentMethod->payment_method_id}}">{{$paymentMethod->name}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-lg-2 mb-3">
                            <x-select-active active="{{ session($cacheKey . '.active') }}"/>
                        </div>
                        <div class="col-lg-2 mb-3">
                            <input type="text" autocomplete="off" name="createdAt" class="form-control"
                                   id="createdAt"
                                   value="{{ session($cacheKey . '.createdAt') }}"
                                   placeholder="{{__('table.FilterByCreatedAt')}}">
                        </div>
                        <div class="col-lg-2 mb-3">
                            <input type="text" autocomplete="off" name="updatedAt"
                                   class="form-control" id="updatedAt"
                                   value="{{ session($cacheKey . '.updatedAt') }}"
                                   placeholder="{{__('table.FilterByUpdatedAt')}}">
                        </div>
                        <div class="col-lg-12">
                            <x-btn-filter/>
                            <div id="btns-panel">
                                <x-btn-create url="{{ route('head.bankAccount.create') }}"
                                              name="{{ __('btn.Create') }}"/>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="row" id="container-row">
        <div class="col-lg-12">
            <div id="main-table" class="card">
                <div id="bank-table" class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                            @include('head::bank-account.sorting-head')
                            </thead>
                            <tbody id="bankAccountTable">
                            @include('head::bank-account.list-table')
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection
@push('scripts')
    <script type="text/javascript" src="{{ asset('js/jsGrid.js') }}"></script>
    <script>
        loadSimpleDataGrid('{{ route('head.bankAccount.refresh') }}', $("#bankAccountForm"), $("#bankAccountTable"));

        $('.live-search-city').selectpicker();
    </script>

@endpush()
