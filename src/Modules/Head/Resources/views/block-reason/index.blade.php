@extends('layouts.app')

@php
    /**
* @var \Modules\Common\Models\BlockReason $blockReason
 */
@endphp
@section('content')
    <x-card title="">
        <div class="form-group">
            <x-btn-create url="{{ route('head.blockReason.create') }}" name="{{ __('btn.Create') }}"/>
        </div>
        <!-- End ./form-group -->

        <x-table>
            <x-slot:head>
                <tr>
                    <th>{{__('table.Name')}}</th>
                    <th>{{__('table.CreatedAt')}}</th>
                    <th>{{__('table.Active')}}</th>
                    <th>{{__('table.Actions')}}</th>
                </tr>
            </x-slot:head>

            @foreach($blockReasons as $blockReason)
                <tr>
                    <td>{{$blockReason->name}}</td>
                    <td>{{$blockReason->created_at}}</td>
                    <td>{{$blockReason->isActiveLabel()}}</td>
                    <td>
                        <x-btn-edit
                                url="{{ route('head.blockReason.edit', $blockReason->getKey()) }}"/>
                        <x-btn-delete
                                url="{{ route('head.blockReason.delete', $blockReason->getKey()) }}"/>
                        @if($blockReason->isActive())
                            <x-btn-disable
                                    url="{{ route('head.blockReason.disable', $blockReason->getKey()) }}"/>
                        @else
                            <x-btn-enable
                                    url="{{ route('head.blockReason.enable', $blockReason->getKey()) }}"/>
                        @endif
                    </td>
                </tr>
            @endforeach

        </x-table>

        <x-table-pagination :rows="$blockReasons"/>
    </x-card>
@endsection
