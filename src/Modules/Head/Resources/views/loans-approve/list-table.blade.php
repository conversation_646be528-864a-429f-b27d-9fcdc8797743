@php
    /**
* @var \Modules\Common\Models\Loan $loan
 */
@endphp
<x-table>
    <x-slot:head>
        <tr>
            <th>{{ __('table.ClientId') }}</th>
            <th>{{ __('table.ApproveLoanSerialNumber') }}</th>
            <th>{{ __('table.Office') }}</th>
            <th>{{ __('table.PaymentMethod') }}</th>
            <th>{{ __('table.Product') }}</th>
            <th>{{ __('table.AmountRequested') }}</th>
            <th>{{ __('table.PeriodRequested') }}</th>
            <th>{{ __('table.Task') }}</th>
            <th>{{ __('table.Client') }}</th>
            <th>{{ __('table.ClientFullName') }}</th>
            <th>{{ __('table.Pin') }}</th>
            <th>{{ __('table.Phone') }}</th>
            <th>{{ __('table.ApproveCreatedAt') }}</th>
            <th>{{ __('table.Timer') }}</th>
            <th>{{ __('table.Status') }}</th>
            <th>{{ __('table.StartReview') }}</th>
        </tr>
    </x-slot:head>

    @foreach($loans as $loan)
        <tr>
            @php
                $client = $loan->client;
            @endphp
            <td class="text-center">{{$loan->client_id}}</td>
            <td class="text-center">{{$loan->getKey()}}</td>
            <td>{{getOfficeName($loan->office_id)}}</td>
            <td>{{getPaymentMethodName($loan->payment_method_id)}}</td>
            <td>{{getProductName($loan->product_id)}}</td>
            <td>{{amount($loan->amount_requested)}}</td>
            <td>
                {{ $loan->period_requested }}
                {{ trans_choice('product::product.keys.' . $loan->loanProductSetting->period, $loan->period_approved) }}
            </td>
            <td>
                @if(!empty($loan->approve_tasks))
                    @foreach($loan->approve_tasks as $agentApproveTask)
                        <div class="text-nowrap"
                             title="{{$agentApproveTask}}">
                            {{ __('approve::approveLoanList.' . $agentApproveTask) }}
                            <br/>
                        </div>
                    @endforeach
                @endif
            </td>
            <td>{{$client?->isNewLabel()}}</td>
            <td>{{$client?->getFullName()}}</td>
            <td>{{$client?->pin}}</td>
            <td class="copyButton" data-toggle="tooltip" title="Phone Copied!">{{$client?->phone}}</td>
            <td>{{ formatDate($loan->created_at, 'd.m.Y H:i:s') }}</td>
            <td>{{$loan->loanForApproveTimer()}}</td>
            <td>{!! $loan->getLastApproveDecisionLabel() !!}</td>
            <td>
                @php
                    $statusRow = Auth::user()->approveListingStatus($loan);
                @endphp

                @if($statusRow == 'showProcessBtn')
                    <x-btn-process
                            url="{{ route('approve.loan-decision.process', $loan->loan_id) }}"
                            name="btnProcessName"
                            title="{{ __('btn.Process') }}"
                    />
                @else
                    {{ $statusRow }}
                @endif
            </td>
        </tr>
    @endforeach
</x-table>
<x-table-pagination :rows="$loans"/>
