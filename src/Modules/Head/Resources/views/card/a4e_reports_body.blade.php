@php
// @deprecated - view
    $maxAmount = (
        isset($creditLimit) && !empty($creditLimit->amount)
        ? amount($creditLimit->amount)
        : 0
    );
@endphp

@forelse($a4eReports  as $a4eReport)
    @php
        $show = $loop->first == 1 ? 'show' : '';
        $sign = $show ? 'minus-square' : 'plus-square';
        $last = $a4eReport->last === 1 ? ' / Last' : '';

        if (empty($step)) {
            $step = 0;
        }
        $step++;
    @endphp
    <div class="card">
        <div class="card-header" id="headingA4EReport{{$loop->iteration}}">
            <h2 class="mb-0">
                <button class="d-flex align-items-center justify-content-between btn btn-link"
                        data-toggle="collapse"
                        data-target="#collapse{{$loop->iteration}}"
                        aria-controls="collapse{{$loop->iteration}}">
                    {{ $a4eReport->created_at . $last}}
                    <span class="fa-stack fa-sm">
                            <i class="fa fa-{{$sign}}"></i>
                        </span>
                </button>
            </h2>
        </div>
        <div id="collapse{{$loop->iteration}}"
             class="collapse {{$show}}"
             aria-labelledby="headingA4EReport{{$loop->iteration}}"
             data-parent="#a4eReportsContainer">
            <div class="card-body noi-reports">
                @if(!empty($a4eReport->gbr) && !empty($a4eReport->gb))
                    <span style="color: #ff4f70">GB: {{ ucfirst($a4eReport->gb) }}, GBR: {{ ucfirst($a4eReport->gbr) }}</span>
                    - <b>Max Credit Limit = {{ $maxAmount }}</b>
                    <br/>
                @endif
                <button class="btn btn-primary" type="button" data-toggle="collapse" data-target="#collapseRawA4e{{$step}}" aria-expanded="false" aria-controls="collapseRawA4e{{$step}}" style="width:200px">
                    Покажи RAW data
                </button>
                <div class="collapse" id="collapseRawA4e{{$step}}">
                    {{ $a4eReport->response }}
                </div>
            </div>
        </div>
    </div>
@empty
    <p style="color: #ff4f70">No A4E reports for this client</p>
@endforelse
<div id="a4ePermissionHiddenDiv" style="display: none">{{ $hasA4EReportPermission === false ? 'disabled' : '' }}</div>
