@extends('layouts.app')
@section('content')

    <div class="row">
        <div class="col-lg-9">
            <x-card title="{{__('table.BlockingAndUnblockingHistory')}}">
                <x-table>
                    <x-slot:head>
                        <tr>
                            <th>{{__('table.Client')}}</th>
                            <th>{{__('table.Pin')}}</th>
                            <th>{{__('table.Type')}}</th>
                            <th>{{__('table.Date')}}</th>
                            <th>{{__('table.Reason')}}</th>
                            <th>{{__('table.BlackListТо')}}</th>
                            <th>{{__('table.BlackListBy')}}</th>
                            <th>{{__('table.Comment')}}</th>
                        </tr>
                    </x-slot:head>

                    @php
                        /**
        * @var \Modules\Head\Repositories\ClientBlockHistoryRepository|\Modules\Common\Models\ClientUnBlockHistory $clientBlockHistory
         */
                    @endphp
                    @foreach($clientBlockHistories as $clientBlockHistory)
                        @if($clientBlockHistory instanceof \Modules\Common\Models\ClientBlockHistory)
                            <tr>
                                <td>{{ $client->getFullName() }}</td>
                                <td>{{ $client->pin }}</td>
                                <td class="text-danger">Блокиран</td>
                                <td>{{$clientBlockHistory->created_at}}</td>
                                <td>{{$clientBlockHistory->reason->name}}</td>
                                <td>{{$client->blocked_to_date ?? __('table.ToManualExit')}}</td>
                                <td>{{$clientBlockHistory->creator->getFullNames()}}</td>
                                <td>{{$clientBlockHistory->comment}}</td>
                            </tr>
                        @endif

                        @if($clientBlockHistory instanceof \Modules\Common\Models\ClientUnBlockHistory)
                            <tr>
                                <td>{{ $client->getFullName() }}</td>
                                <td>{{ $client->pin }}</td>
                                <td class="text-success">Отблокиран</td>
                                <td>{{$clientBlockHistory->created_at}}</td>
                                <td></td>
                                <td>{{$client->blocked_to_date}}</td>
                                <td>{{$clientBlockHistory->creator->getFullNames()}}</td>
                                <td>{{$clientBlockHistory->comment}}</td>
                            </tr>
                        @endif
                    @endforeach
                </x-table>
            </x-card>
        </div>
    </div>

@endsection