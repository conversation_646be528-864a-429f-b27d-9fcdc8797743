@php
    /**
* @var \Modules\Common\Entities\CommunicationPivot $communication
 */
@endphp
<x-card>
    <x-slot:title>
        {{__('head::clientCard.communicationTable')}}
    </x-slot:title>
    <x-slot:cardOptions>
        <span class="cursor-move" title="{{ __('click and move me') }}">
            <i class="fa fa-arrows-alt"></i>
        </span>
    </x-slot:cardOptions>

    @isset($last5Communications)
        <x-table>
            @foreach($last5Communications as $communication)
                <tr>
                    <td>{{$communication->created_at}}</td>
                    <td>{{$communication->communication_type}}</td>
                    <td>
                        @if($communication->communication_type == 'comment')
                            {{ $communication->communication_key }}
                        @else
                            {{ __('communication::templates.' . $communication->communication_key) }}
                        @endif
                    </td>
                    <td>{{$communication->sender}}</td>
                </tr>
            @endforeach
        </x-table>
    @endisset
</x-card>
