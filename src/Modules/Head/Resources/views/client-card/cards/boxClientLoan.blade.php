{{--@if(!empty($loan) && !$loan->isOnlineLoan() && $loan->isApproved())--}}

{{--    <div id="getOrNotMoneyFormDiv" class="justify-content-center">--}}
{{--        <h4 class="card-title mb-3">{{ __('head::clientCard.ExitTask') }}</h4>--}}
{{--        <div class="form-group">--}}
{{--            <form id="acquireMoney"--}}
{{--                  class="d-inline-block"--}}
{{--                  method="POST"--}}
{{--                  action="{{route('head.clientCard.acquireMoney', $loan->loan_id ?? 0)}}"--}}
{{--            >--}}
{{--                @csrf--}}
{{--                <input type="hidden" id="loanId" name="loan[loan_id]" value="{{$loan->loan_id ?? ''}}">--}}
{{--                <button--}}
{{--                    id="acquireMoneyBtn"--}}
{{--                    type="button"--}}
{{--                    class="btn btn-success button-style disableOnClick"--}}
{{--                    style="width: 12rem;">--}}
{{--                    {{__('btn.AcquireMoney')}}--}}
{{--                </button>--}}
{{--            </form>--}}
{{--        </div>--}}

{{--        <div class="form-group">--}}
{{--            <button type="button" class="btn btn-danger button-style" style="width: 12rem;" data-toggle="modal"--}}
{{--                    data-target="#cancelLoanModal">--}}
{{--                {{__('btn.CancelLoan')}}--}}
{{--            </button>--}}
{{--        </div>--}}
{{--    </div>--}}

{{--    @include('head::client-card.modals.overview.cancel-loan-modal')--}}
{{--    @include('head::client-card.modals.overview.documents-notifier-modal')--}}

{{--    @if(!empty($refinanceParams['refinanceCheck']) ?? false)--}}
{{--        @include('head::client-card.modals.overview.refinance-notifier-modal')--}}
{{--    @endif--}}

{{--    @push('scripts')--}}
{{--        <script>--}}
{{--            var refinanceCheck = {{ !empty($refinanceParams['refinanceCheck']) === true ? 1 : 0 }};--}}

{{--            $(document).ready(function () {--}}
{{--                $('#acquireMoneyBtn').click(function () {--}}
{{--                    const dbLoanChangedAt = '{{ $loan->loan_changed_at }}';--}}

{{--                    // loanChangedAt is global variable declared in boxLoanUpdate--}}
{{--                    if (loanChangedAt ?? dbLoanChangedAt) {--}}
{{--                        $('#documentsNotifierModal').modal('show');--}}
{{--                    } else if (refinanceCheck) {--}}
{{--                        $('#refinanceNotifierModal').modal('show');--}}
{{--                    } else {--}}
{{--                        $('#acquireMoney').submit();--}}
{{--                    }--}}
{{--                });--}}

{{--                $('#documentsNotifierYesButton').click(function () {--}}
{{--                    $('#documentsNotifierModal').fadeOut(1000, function () {--}}
{{--                        $(this).modal('hide');--}}
{{--                    });--}}

{{--                    if (refinanceCheck) {--}}
{{--                        $('#refinanceNotifierModal').modal('show');--}}
{{--                    } else {--}}
{{--                        $('#acquireMoney').submit();--}}
{{--                    }--}}
{{--                });--}}
{{--            });--}}
{{--        </script>--}}
{{--    @endpush--}}

{{--@endif--}}
