@php
    use \Modules\Common\Models\PaymentMethod;
    use Modules\Common\Models\LoanStatus;
/**
* @var \Modules\Common\Models\Loan $loan
 */
@endphp

@if($loan?->showUpdateLoanParams())

    <x-card>
        <x-slot:title>
            <i class="fa fa-chart-line"></i>&nbsp;
            {{ __('Loan calculator') }}
        </x-slot:title>
        <x-slot:cardOptions>
        <span class="cursor-move" title="{{ __('click and move me') }}">
            <i class="fa fa-arrows-alt"></i>
        </span>
        </x-slot:cardOptions>

        <div id="vuejs-slider" style="min-height: 535px;">
            <x-common::calculator-slider
                :products="$products"
                :office-id="$officeId"
                :loan-id="$loanId"
                :client-pin="$client?->pin"
                :refinancing-loans="$refinancingLoans"
                :refinancing-loan-ids="$refinancingLoanIds"
                :office="$office"
            />
        </div>
        <hr>

        @if(!empty($paymentAccounts))
            <div style="margin-bottom: 10px;">
                <div class="form-group">
                    <label for="loan_bank_account_id" class="control-label">{{ __('other.PaymentAccount') }}</label>
                    <select class="form-control" required="required" id="loan_bank_account_id">
                        @foreach($paymentAccounts as $pa)
                            <option
                                value="{{$pa->bank_account_id}}"
                                show-iban-attr="{{$pa->isForBank() ? '1' : '0'}}"
                                pay-method-attr="{{$pa->payment_method_id}}"
                                {{($pa->bank_account_id == $loan->bank_account_id ? 'selected' : '')}}
                            >{{$pa->name}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group" id="loanIbanDiv" style="{{(empty($showIban) ? 'display:none;' : '')}}">
                    <label for="loanIban" class="control-label">{{ __('table.Iban') }}</label>
                    <input class="form-control" data-parsley-minlength="10" data-parsley-maxlength="22" id="loanIban"
                           value="{{ ($showIban && !empty($mainIban) ? $mainIban : '') }}">
                </div>
                <input type="hidden" id="bufferIban" value="{{ !empty($mainIban) ? $mainIban : '' }}"/>
            </div>
        @endif

        <div class="col-md-12">
            <div class="row">
                <div class="col-md-6 pl-0 p-1">
                    <input type="hidden" name="productId" value="{{$loan?->product_id}}"/>
                    <input type="hidden" name="sum" value="{{$loan?->amount_approved}}"/>
                    <input type="hidden" name="period" value="{{$loan?->period_approved}}"/>
                    <input type="hidden" name="discount" value="{{$loan?->discount_percent}}"/>
                    <input type="hidden" name="interest" value="{{$loan?->interest_percent}}"/>
                    <input type="hidden" name="penalty" value="{{$loan?->penalty_percent}}"/>

                    <button id="previewPaymentScheduleCC" class="btn btn-secondary btn-sm btn-block">
                        <i class="fa fa-list-alt"></i>&nbsp;
                        {{__('head::clientCard.repaymentPlanInfo')}}
                    </button>
                </div>
                <!-- End ./col -->

                <div class="col-md-6 p-1 pr-0">
                    <form action="{{route('sales.sale-loan.update-loan')}}"
                          method="POST"
                          id="updateLoanParamsForm"
                    >
                        @csrf
                        <input type="hidden" name="task" value="{{request('task')}}"/>
                        <input type="hidden" name="loan[client_id]" value="{{ $client?->getKey() }}"/>
                        <input type="hidden" name="loan[pin]" value="{{ $client?->pin }}"/>
                        <input type="hidden" name="loan[loan_id]" value="{{ $loan?->getKey() }}"/>
                        <input type="hidden" name="loan[loan_status_id]" value="{{ $loan?->loan_status_id }}"/>
                        <input type="hidden"
                               name="loan[product_id]"
                               value="{{session($loan::getTempLoanDataKey($loanId).'.loan.product_id', $loan?->product_id)}}"
                        />
                        <input type="hidden" name="loan[payment_method]"
                               value="{{session($loan::getTempLoanDataKey($loanId).'.loan.payment_method', $loan?->payment_method_id)}}"
                               id="hidden_payment_method_id"
                        />
                        <input type="hidden" name="loan[bank_account_id]"
                               value="{{session($loan::getTempLoanDataKey($loanId).'.loan.bank_account_id', $loan?->bank_account_id)}}"
                               id="hidden_bank_account_id"
                        />
                        <input type="hidden" name="loan[iban]"
                               value="{{session($loan::getTempLoanDataKey($loanId).'.loan.iban', (isset($mainIban) ? $mainIban : ''))}}"
                               id="hidden_iban"
                        />
                        <input type="hidden"
                               name="loan[loan_sum]"
                               value="{{session($loan::getTempLoanDataKey($loanId).'.loan.loan_sum', $loan?->amount_approved)}}"
                        />
                        <input type="hidden"
                               name="loan[loan_period]"
                               value="{{session($loan::getTempLoanDataKey($loanId).'.loan.loan_period', $loan?->period_approved)}}"
                        />
                        <input type="hidden"
                               name="loan[discount_percent]"
                               value="{{session($loan::getTempLoanDataKey($loanId).'.loan.discount_percent', $loan?->discount_percent)}}"
                        />
                        <input type="hidden" name="loan[office_id]" value="{{ $officeId }}"/>


                        @foreach(array_keys($refinancingLoanIds) as $refinancedId)
                            <input type="hidden"
                                   class="form-check"
                                   name="refinanced_loans[]"
                                   value="{{$refinancedId}}"
                            />
                        @endforeach

                        <div>
                            @if(isset($canCreateNewLoan) && !$canCreateNewLoan || !$loan)
                                <a href="{{route('sales.newApplication', ['pin' => $client->pin])}}"
                                   class="btn btn-primary btn-sm btn-block"
                                >
                                    <i class="fa fa-plus"></i>&nbsp;
                                    {{__('menu.NewRequest')}}
                                </a>
                            @else
                                @if(session($loan::getTempLoanDataKey($loanId)))
                                    <button id="updateTempLoanParamsButton"
                                            type="button"
                                            class="btn btn-primary btn-sm btn-block"
                                        @disabled(!$loan?->getIsChangeable())
                                    >
                                <span class="spinner-border spinner-border-sm d-none" role="status"
                                      aria-hidden="true"></span>
                                        <i class="fa fa-save"></i>&nbsp;
                                        {{__('menu.ChangeRequest')}}
                                    </button>
                                    <button id="updateLoanParamsButton"
                                            type="button"
                                            class="btn btn-success btn-sm btn-block"
                                        @disabled(!$loan?->getIsChangeable())
                                    >
                                <span class="spinner-border spinner-border-sm d-none" role="status"
                                      aria-hidden="true"></span>
                                        <i class="fa fa-circle-back"></i>&nbsp;
                                        {{__('menu.ReturnToSign')}}
                                    </button>
                                @else
                                    <button id="updateLoanParamsButton"
                                            type="button"
                                            class="btn btn-primary btn-sm btn-block"
                                        @disabled(!$loan?->getIsChangeable())
                                    >
                                <span class="spinner-border spinner-border-sm d-none" role="status"
                                      aria-hidden="true"></span>
                                        <i class="fa fa-save"></i>&nbsp;
                                        {{__('menu.ChangeRequest')}}
                                    </button>
                                @endif
                            @endif
                        </div>
                    </form>
                </div>
            </div>
            <!-- End ./row -->
        </div>
        <!-- End ./loan-form -->

    </x-card>

    @include('head::client-card.modals.overview.documents-notifier-modal')

    @push('scripts')
        <script type="text/javascript">

            window.showPreliminaryPaymentPlan = '{{route('head.loans.showPreliminaryPaymentPlan')}}';

            $(document).on('click', 'button#previewPaymentScheduleCC', function () {
                let activeProductDiv = $('.nav-link.active[aria-selected="true"]').data('target');
                let activeProductId = activeProductDiv.replace('#slider-', '');
                let activeAmountId = '#selectedAmount-' + activeProductId;
                let activePeriodId = '#selectedPeriod-' + activeProductId;
                let activeDiscountId = '#selectedDiscount-' + activeProductId;

                let $paymentSchedulePreview = window.showPreliminaryPaymentPlan + '?';
                $paymentSchedulePreview += 'productId=' + activeProductId;
                $paymentSchedulePreview += '&sum=' + currencyToInt(extractNumericValue($(activeAmountId).text()));
                $paymentSchedulePreview += '&period=' + extractNumericValue($(activePeriodId).text());
                $paymentSchedulePreview += '&discount=' + extractNumericValue($(activeDiscountId).text());
                $paymentSchedulePreview += '&interest=' + $('input[name="interest"]').val();
                $paymentSchedulePreview += '&penalty=' + $('input[name="penalty"]').val();

                window.open($paymentSchedulePreview, '_blank');
            });

            function extractNumericValue(str) {
                return str.match(/[0-9.]+/g)?.join('') || '';
            }

            function currencyToInt(str) {
                var numericValue = parseFloat(str.replace(/[^0-9.]/g, ''));
                return Math.round(numericValue * 100); // Convert to integer representation
            }


            var paymentMethodBank = parseInt('{{ PaymentMethod::PAYMENT_METHOD_BANK }}');
            var paymentMethodCash = parseInt('{{ PaymentMethod::PAYMENT_METHOD_CASH }}');
            var paymentMethodEasypay = parseInt('{{ PaymentMethod::PAYMENT_METHOD_EASYPAY }}');

            // We use this global variable to track whether the loan has been changed through the sliders
            var loanChangedAt = null;


            @if($loan)

            /// when document ready set refresh payment method
            $(document).ready(function () {
                $('select#loan_bank_account_id').val($('#hidden_bank_account_id').val());
            });

            // ------ PAYMENT ACCOUNT SECTION ---------
            var $loanIbanDiv = $('#loanIbanDiv'); // whole div for hide/show
            var $loanIban = $('#loanIban'); // visible input to offer enter the IBAN
            var $bufferIbanVal = $('#bufferIban').val(); // when remove our iban, this input always keep it
            var $hiddenIban = $('#hidden_iban'); // form field we need for submit

            $loanIban.on('change', function () {
                var ibanValue = $loanIban.val();
                $hiddenIban.val(ibanValue);
            });
            $('#loan_bank_account_id').on('change', function () {
                $('#hidden_bank_account_id').val($(this).val());
                toggleLoanIban($(this));
            });
            $('#updateLoanParamsButton,#updateTempLoanParamsButton').on('click', function (event) {

                var paymentMethodId = $('#hidden_payment_method_id').val();
                var ibanValue = $('#hidden_iban').val();

                if (paymentMethodId === '1' && ibanValue === '') {
                    $loanIban.addClass('is-invalid');

                    alert('Липсва клиентска сметка(IBAN) за избран пеймент акаунт.');

                    return false;
                }

                let $btn = $(this);
                $btn.prop('disabled', true);
                $('#updateLoanParamsButton .spinner-border').removeClass('d-none');
                $('#updateTempLoanParamsButton .spinner-border').removeClass('d-none');

                let $route = $('#updateLoanParamsForm').attr('action');
                let $formData = $('#updateLoanParamsForm').serialize();
                if ($(event.target).attr('id') === 'updateTempLoanParamsButton') {
                    $formData += '&updateTempData=1';
                }

                axios
                    .post($route, $formData)
                    .then(resp => {
                        /// when form submit for first time only reload page
                        if (resp.data.status === true && resp.data?.reloadPage === true) {
                            return window.location.reload();
                        }

                        if (resp.data.status === true) {
                            if (resp.data?.redirectToTasks && resp.data?.redirectTo) {
                                return window.location.replace(resp.data?.redirectTo);
                            } else {
                                return window.location.reload();
                            }
                        }

                        if (resp.data.status === false) {
                            $btn.prop('disabled', false);
                            $('#updateLoanParamsButton .spinner-border').addClass('d-none');
                            $('#updateTempLoanParamsButton .spinner-border').addClass('d-none');
                            alert(resp.data.message);
                        }
                    })
                    .catch(error => {
                        $btn.prop('disabled', false);
                        $('#updateLoanParamsButton .spinner-border').addClass('d-none');
                        $('#updateTempLoanParamsButton .spinner-border').addClass('d-none');

                        console.error(error);
                        if (error.response.data !== undefined) {
                            let msg = "";
                            Object.values(JSON.parse(error.response.data.message)).forEach(function (row, key) {
                                msg += row[0] + "\n";
                            });
                            alert(msg);
                        } else {
                            alert(error);
                        }
                    });
            });

            function toggleLoanIban($select) {
                var selectedOption = $select.find('option:selected');
                var showIbanAttr = selectedOption.attr('show-iban-attr');
                var selectedPaymentMethodId = selectedOption.attr('pay-method-attr');

                if (showIbanAttr === '1') {
                    $loanIbanDiv.show();
                    $loanIban.val($bufferIbanVal);
                    $hiddenIban.val($bufferIbanVal);
                } else {
                    $loanIbanDiv.hide();
                    $loanIban.val('');
                    $hiddenIban.val('');
                }

                $('#hidden_payment_method_id').val(selectedPaymentMethodId);
            }
            @endif
        </script>
    @endpush

@endif
