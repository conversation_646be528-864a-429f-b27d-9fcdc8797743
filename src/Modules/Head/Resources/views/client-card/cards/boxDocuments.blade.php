@inject('documentTemplateClass', '\Modules\Common\Models\DocumentTemplate')
@inject('productTypeClass', '\Modules\Common\Models\ProductType')

@php
    $loanId = !empty($loan->loan_id) ? $loan->getKey() : 0;
@endphp

<x-card>
    <x-slot:title>{{__('head::clientCard.Documents')}}</x-slot:title>
    <x-slot:cardOptions>
        <span class="cursor-move" title="{{ __('click and move me') }}">
            <i class="fa fa-arrows-alt"></i>
        </span>
    </x-slot:cardOptions>

    <x-table>
        <tr>
            <td colspan="2" class="p-0">
                <a class="btn btn-primary btn-sm"
                   href="{{ route('head.clientCard.printDocuments', [$documentTemplateClass::DOC_PACK_NEW_APP, $loanId]) }}"
                   target="_blank"
                >
                    <i class="fa fa-print"></i>&nbsp;
                    {{ __('head::clientCard.printDocumentsOnRequest') }}
                </a>
            </td>
        </tr>
        <tr>
            <td class="text-center">1</td>
            <td>
                <a href="{{ route('head.clientCard.printDocuments', [$documentTemplateClass::TPL_APPLICATION, $loanId]) }}"
                   target="_blank"
                >
                    {{ __('product::product.' . $documentTemplateClass::TPL_APPLICATION) }}
                </a>
            </td>
        </tr>
        <tr>
            <td class="text-center">2</td>
            <td>
                <a href="{{ route('head.clientCard.printDocuments', [$documentTemplateClass::TPL_DEC_PERSONAL_DATA, $loanId]) }}"
                   target="_blank"
                >
                    {{ __('product::product.' . $documentTemplateClass::TPL_DEC_PERSONAL_DATA) }}
                </a>
            </td>
        </tr>
        <tr>
            <td class="text-center">3</td>
            <td>
                <a href="{{ route('head.clientCard.printDocuments', [$documentTemplateClass::TPL_SEF, $loanId]) }}"
                   target="_blank"
                >
                    {{ __('product::product.' . $documentTemplateClass::TPL_SEF) }}
                </a>
            </td>
        </tr>

        <!--  -->
        <tr>
            <td colspan="2" class="p-0">
                <a class="btn btn-primary btn-sm"
                   href="{{ route('head.clientCard.printDocuments', [$documentTemplateClass::DOC_PACK_APPROVAL, $loanId]) }}"
                   target="_blank"
                >
                    <i class="fa fa-print"></i>&nbsp;
                    {{ __('head::clientCard.printDocumentsOnApprove') }}
                </a>
            </td>
        </tr>
        <tr>
            <td class="text-center">1</td>
            <td>
                <a href="{{ route('head.clientCard.printDocuments', [$documentTemplateClass::TPL_CONTRACT, $loanId]) }}"
                   target="_blank"
                >
                    {{ __('product::product.' . $documentTemplateClass::TPL_CONTRACT) }}
                </a>
            </td>
        </tr>
        <tr>
            <td class="text-center">2</td>
            <td>
                <a href="{{ route('head.clientCard.printDocuments', [$documentTemplateClass::TPL_PLAN, $loanId]) }}"
                   target="_blank"
                >
                    {{ __('product::product.' . $documentTemplateClass::TPL_PLAN) }}
                </a>
            </td>
        </tr>
        <tr>
            <td class="text-center">3</td>
            <td>
                <a href="{{ route('head.clientCard.printDocuments', [$documentTemplateClass::TPL_GEN_TERMS, $loanId]) }}"
                   target="_blank"
                >
                    {{ __('product::product.' . $documentTemplateClass::TPL_GEN_TERMS) }}
                </a>
            </td>
        </tr>
        <tr>
            <td class="text-center">4</td>
            <td>
                <a href="{{ route('head.clientCard.printDocuments', [$documentTemplateClass::TPL_ORDER_RECORD, $loanId]) }}"
                   target="_blank"
                >
                    {{ __('product::product.' . $documentTemplateClass::TPL_ORDER_RECORD) }}
                </a>
            </td>
        </tr>
        <tr>
            <td class="text-center">5</td>
            <td>
                <a href="{{ route('head.clientCard.printDocuments', [$documentTemplateClass::TPL_QUESTIONNAIRE, $loanId]) }}"
                   target="_blank"
                >
                    {{ __('product::product.' . $documentTemplateClass::TPL_QUESTIONNAIRE) }}
                </a>
            </td>
        </tr>
        <tr>
            <td class="text-center">6</td>
            <td>
                <a href="{{ route('head.clientCard.printDocuments', [$documentTemplateClass::TPL_DECLARATION_66, $loanId]) }}"
                   target="_blank"
                >
                    {{ __('product::product.' . $documentTemplateClass::TPL_DECLARATION_66) }}
                </a>
            </td>
        </tr>
    </x-table>

</x-card>
