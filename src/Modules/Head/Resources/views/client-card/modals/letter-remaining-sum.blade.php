<x-common::modal
        modal-id="letterRemainingSumModal"
        modal-title="{{ __('head::clientCard.LetterRemainingSum') }}"
>

    <div class="modal-body">
        <p class="text-left">{{ __('table.toDate')}}: {{\Carbon\Carbon::now()->format('d.m.Y')}}</p>
        <br>
        <a href="{{route('head.letter-notification.remainingEmail',$loan?->getKey() ?? 0)}}"
           class="btn btn-primary btn-circle-blue primary-ch-btn p-2 disable-on-click">
            {{ __('table.sendEmail') }}
        </a>
        <a href="{{route('head.letter-notification.remainingPdf', $loan?->getKey() ?? 0)}}"
           target="_blank"
           class="btn btn-primary btn-circle-blue primary-ch-btn p-2 disable-on-click"
        >
            {{ __('table.downloadPdf') }}
        </a>
    </div>
    <!-- End ./modal-body -->

</x-common::modal>

