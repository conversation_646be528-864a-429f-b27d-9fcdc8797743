<form id="clientCurrentAddressForm">
    @csrf
    <div class="modal fade" id="addressModal" tabindex="-1" role="dialog"
         aria-labelledby="addressModalLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <input type="hidden" id="clientId" name="client[client_id]"
                           value="{{$client->client_id}}">
                    <div id="currentAddress">
                        <div class="card-body">
                            <div
                                class="font-weight-bold mb-3">{{ __('table.CurrentAddress') }}</div>
                            @php
                                $address = !empty($client) ? $client->clientLastAddressCurrent()  : null;
                            @endphp
                            <x-current-address
                                addressName="client_address[address]"
                                address="{{old('client_address.address') ?? ($address->address ?? '') }}"
                                postCodeName="client_address[post_code]"
                                postCode="{{old('client_address.post_code') ?? ($address->post_code ?? '') }}"

                                :cities="$cities"
                                citiesName="client_address[city_id]"
                                citiesId="client_address_city_id"
                                selectedId="{{ old('client_address.city_id') ?? ($address->city_id ?? 0) }}"
                            />

                            <div class="w-100 text-center">
                                <button type="button" class="btn btn-danger col-3 m-2 p-2 btn-round-8px"
                                        data-dismiss="modal">{{ __('btn.Close') }}</button>
                                <x-btn-update-simple classes="btn btn-success col-3 m-2 p-2 btn-round-8px"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<form method="POST" action="{{ route('head.client-address.create', [$client->getKey()]) }}">
    @csrf
    <div class="modal fade" id="clientNewAddressModal" tabindex="-1" role="dialog"
         aria-labelledby="addressNewModalLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="clientId" name="client[client_id]"
                           value="{{$client->getKey()}}">
                    <div id="newAddress">
                        <div class="card">
                            <div class="card-body">
                                <div
                                    class="font-weight-bold mb-3">{{ __('table.CurrentAddress') }}</div>
                                <div class="form-group">
                                    <x-select-city
                                        :cities="$cities"
                                        id="selectCityNewCurrentAddress"
                                        name="new_client_address[city_id]"
                                        selectedId=" "
                                    />

                                    <textarea class="form-control mb-4 noFill"
                                              placeholder="{{ __('table.Address') }}"
                                              name="new_client_address[address]"></textarea>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default"
                            data-dismiss="modal">{{__('head::clientCard.Back')}}</button>
                    <x-btn-create-simple/>
                </div>
            </div>
        </div>
    </div>
</form>
