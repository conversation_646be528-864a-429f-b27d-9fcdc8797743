<x-common::modal
        modal-id="createCommunicationCommentModal"
        modal-title="{{ __('head::clientCard.addComment') }}"
>
    {!! Form::open(['route'=> ['communication.communicationComment.createCommunicationComment',['client' => $client?->getKey() ?? null, 'loan' => $loan?->getKey()]], 'onsubmit'=>'disableSubmitButtonWithClass(this)']) !!}

    <div class="modal-body">
        <div class="form-group">
                        <textarea
                                id="communicationCommentText"
                                name="communicationCommentText"
                                placeholder="{{ __('head::clientCard.Comment') }}"
                                class="form-control no-read-only"
                                required="required"
                                cols="50"
                                rows="8"
                        ></textarea>
        </div>
    </div>
    <!-- End ./modal-body -->

    <x-common::modal-footer/>

    {!! Form::close() !!}

</x-common::modal>
