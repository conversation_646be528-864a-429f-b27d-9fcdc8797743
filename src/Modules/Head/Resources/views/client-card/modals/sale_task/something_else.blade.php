@php
    use Modules\Common\Models\SaleDecision;
    $route = route('head.clientCard.completeSaleTask', ['saleTask' => !empty($saleTask) ? $saleTask->getKey() : 0]);
    $loanId = (!empty($saleTask) ? $saleTask->loan_id : 0);
@endphp

<x-common::modal
        modal-id="addCommentModal"
        modal-title="{{__('other.Reason_for_close_task')}}"
>

    {!! Form::open(['url' => $route]) !!}
    <div class="modal-body">

        <input type="hidden" name="sale_decision_id"
               value="{{ SaleDecision::SALE_DECISION_ID_OTHER }}">
        <input type="hidden" name="loan_id" value="{{$loanId}}">
        <input type="hidden" name="sale_task_id" value="{{ !empty($saleTask) ? $saleTask->getKey() : 0 }}">
        <div class="form-group">
            <label for="decisionComment">{{__('table.Comment')}}</label>
            <textarea id="decisionComment" name="comment" class="form-control"
                      rows="5"></textarea>
        </div>
    </div>

    <x-common::modal-footer/>

    {!! Form::close() !!}

</x-common::modal>
