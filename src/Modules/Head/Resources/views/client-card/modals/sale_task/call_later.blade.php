<div id="callLaterSaleDecisionModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-titlе font-weight-bold"
                    id="cancelLoanModalLabel">{{__('head::clientCard.ClientWantsLaterCall')}}</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>

            <form id="callLaterForm" method="POST" class="pl-4 pr-4"
                  action="{{route('head.clientCard.completeSaleTask', ['saleTask' => !empty($saleTask) ? $saleTask->getKey() : 0])}}">
                @csrf

                <input type="hidden" name="sale_task_id" value="{{!empty($saleTask) ? $saleTask->sale_task_id : 0}}">
                <input type="hidden" name="sale_decision_id"
                       value="{{ \Modules\Common\Models\SaleDecision::SALE_DECISION_ID_RECALL }}">
                <div class="form-row mt-4">
                    <div class="form-group col-md-5">
                        <div id="callLaterDiv" class="input-group">
                            <div class="input-group-prepend">
                                <div class="input-group-text"><i class="fa fa-calendar" aria-hidden="true"></i></div>
                            </div>
                            <input type="text" id="callLaterDate" class="form-control clear" name="date"
                                   autocomplete="off">
                        </div>
                    </div>
                    <div class="form-group col-md-2 mt-2 text-center">{{__('head::clientCard.In')}}</div>
                    <div class="form-group col-md-2">
                        <input type="number" class="form-control clear" name="call_later[hours]" min="0" max="23"
                               autocomplete="off" required>
                    </div>
                    <div class="form-group col-md-1 mt-2 text-center">
                        {{__('head::clientCard.HoursMinutesSeparator')}}
                    </div>
                    <div class="form-group col-md-2">
                        <input type="number" name="call_later[minutes]" class="form-control clear" min="0" max="59"
                               autocomplete="off" required>
                    </div>
                </div>

                <div class="form-row m-3">
                    <div class="form-group col-md-5 text-center mt-2">{{__('head::clientCard.OrAfter')}}</div>
                    <div class="form-group col-md-3">
                        <input type="number" id="callLaterMinutesOnly" name="call_later[minutes_only]"
                               class="form-control" autocomplete="off" required min="1">
                    </div>
                    <div
                        class="form-group col-md-4 text-center text-center mt-2">{{__('head::clientCard.Minutes')}}</div>
                </div>
                <div class="modal-footer justify-content-center">
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-danger col-3 m-3"
                            data-dismiss="modal">{{ __('btn.Close') }}</button>
                    <button type="submit" name="action"
                            class="btn btn-success col-3 m-3">{{ __('btn.Update') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

