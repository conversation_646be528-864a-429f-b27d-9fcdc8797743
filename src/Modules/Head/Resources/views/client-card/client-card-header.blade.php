<div class="bg-white p-1 mb-2">
    <h4 class="page-title text-dark font-weight-bold mb-1 text-left mb-3 mt-3">
        @php
            $headerString = '';
            $headerString .= $headerData['client_name'] ?? '';
            $headerString .= !empty($headerData['client_type']) ? ' - ' . $headerData['client_type'] : '';
            $headerString .= !empty($headerData['task_type']) ? ' - ' . $headerData['task_type'] : '';
            $headerString .= !empty($headerData['actions']) ? ': ' . implode(', ', $headerData['actions']) : '';
        @endphp
        {{ $headerString }}
        @if(!empty($headerData['block_reason']))
            <div class="btn-rounded btn-danger d-flex w-15-hard p-3 align-items-center justify-content-between">
                    <span class="font-weight-bold text-black-50">
                        {{ __('table.Reason') }}: {{ __('table.' . $headerData['block_reason']) ?? '' }}
                    </span>
            </div>
        @endif

        @php /** @var \Modules\Common\Models\Loan $loan ***/ @endphp
        @if(isset($loan) && $loan->outer_collector->isTrue())
            ,<strong class="text-danger">&nbsp;За външно събиране</strong>
        @endif

        @if(isset($loan) && $loan->skip_ref_amount_check->isTrue())
            ,<strong class="text-danger">&nbsp;Предоговорен кредит в просрочие</strong>
        @endif

        @if(isset($loan) && $loan->isJuridical())
            ,<strong class="text-danger">&nbsp;Съдебен</strong>
        @endif
    </h4>
</div>

@push('scripts')
    <script>
        $(document).ready(function () {
            let url = window.location.href;
            let hashIndex = url.indexOf("#");

            if (hashIndex !== -1) {
                let tabName = url.substring(hashIndex + 1);
                let selector = 'button[data-target="#' + tabName + '"]';

                if ($(selector).length > 0) {
                    $(selector).click();
                }
            }
        });
    </script>
@endpush
