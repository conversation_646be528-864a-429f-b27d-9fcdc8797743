@extends('layouts.app')

@section('content')
    <h2>Import CCR Reports from Nefin</h2>
    <!-- File Upload Form -->
    <form action="{{ route('head.ccrReports.storeImport') }}" method="POST" enctype="multipart/form-data" class="d-flex align-items-center gap-2">
        @csrf

        <!-- Dropdown: Select Report Type -->
        <div class="flex-grow-2">
            <label for="type" class="form-label">Report Type</label>
            <select name="type" id="type" class="form-control" required>
                @foreach($types as $type)
                    <option value="{{ $type }}">{{ ucfirst($type) }}</option>
                @endforeach
            </select>
        </div>

        <!-- File Input -->
        <div class="flex-grow-2">
            <label for="file" class="form-label">Upload File</label>
            <input type="file" name="file" id="file" class="form-control" required>
        </div>

        <!-- Submit Button -->
        <div class="d-flex align-items-end" style="padding-top: 12px;">
            <button type="submit" class="btn btn-primary mt-4">Import</button>
        </div>
    </form>

    <hr>

    <!-- Table of Uploaded Files -->
    <h3>Imported Files</h3>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Report Type</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($files as $file)
                <tr>
                    <td>{{ ucfirst($file['type']) }}</td>
                    <td>
                        <a href="{{ route('head.ccrReports.downloadImport', $file['type']) }}" class="btn btn-success btn-sm">Download</a>
                        <form action="{{ route('head.ccrReports.deleteImport', $file['type']) }}" method="POST" style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                        </form>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="2" class="text-center">No files uploaded.</td>
                </tr>
            @endforelse
        </tbody>
    </table>
@endsection
