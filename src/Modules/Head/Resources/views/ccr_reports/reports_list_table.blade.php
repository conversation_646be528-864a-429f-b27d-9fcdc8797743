<table class="table">
    <thead>
    <tr>
        <th scope="col">{{__('table.ReportId')}}</th>
        <th scope="col">{{__('table.Type')}}</th>
        <th scope="col">{{__('table.FileZip')}}</th>
        <th scope="col">{{__('table.FileCsv')}}</th>
        <th scope="col">{{__('table.CreatedBy')}}</th>
        <th scope="col">{{__('table.CreatedAt')}}</th>
        <th scope="col" style="max-width: 120px;">{{__('table.Details')}}</th>
        <th scope="col" style="max-width: 120px;">{{__('table.Actions')}}</th>
    </tr>
    </thead>
    <tbody>
    @php
        // Track the latest report for each type to show action buttons only for them
        $latestReportsByType = [];
        foreach($reports as $report) {
            if (!isset($latestReportsByType[$report->type])) {
                $latestReportsByType[$report->type] = $report->report_id;
            }
        }
    @endphp
    @foreach($reports as $report)
        <tr>
            <td>{{ $report->report_id }}</td>
            <td>{{ $report->type }}</td>
            @if(!empty($report->zip_file_id))
            <td><a target="_blank" href="{{ route('head.ccrReports.download', $report->zip_file_id) }}">{{ $report->zip_name }}</a></td>
            @else
                <td></td>
            @endif
            @if(!empty($report->csv_file_id))
            <td><a target="_blank" href="{{ route('head.ccrReports.download', $report->csv_file_id) }}">{{ $report->csv_name }}</a></td>
            @else
                <td></td>
            @endif
            <td>{{ $report->admin_name }}</td>
            <td>{{ $report->created_at }}</td>
            <td style="max-width: 120px;">{{ $report->details }}</td>
            <td style="max-width: 120px;">
                @if(isset($latestReportsByType[$report->type]) && $latestReportsByType[$report->type] == $report->report_id)
                    <a href="{{ route('head.ccrReports.cancel', $report->report_id) }}"
                       class="btn btn-sm btn-warning me-1"
                       title="Връща записи в състоянието преди генериране на отчет"
                       onclick="return confirm('Сигурни ли сте, че искате да отмените този отчет?')">
                        Отмени
                    </a>
                    <a href="{{ route('head.ccrReports.regenerate', $report->report_id) }}"
                       class="btn btn-sm btn-info"
                       title="Връща записи в състояние преди генерация на отчет и генерира нов с актуални данни към момента, само за записи, които бяха в предишен отчет"
                       onclick="return confirm('Сигурни ли сте, че искате да регенерирате този отчет?')">
                        Регененрирай
                    </a>
                @endif
            </td>
        </tr>
    @endforeach
    </tbody>
    <tfoot>
        <tr id="pagination-nav">
            <td colspan="8">
                {{ $reports->links() }}
            </td>
        </tr>
    </tfoot>
</table>
