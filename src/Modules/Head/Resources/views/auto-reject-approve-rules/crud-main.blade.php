@extends('layouts.app')
@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

@if(session('fail'))
    <div class="alert alert-danger">
        {{ session('fail') }}
    </div>
@endif

@section('content')
    <div class="card">
        <div class="card-body">
            <ul class=" nav nav-tabs mt-1" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="settings-tab" data-toggle="tab" href="#settings" role="tab"
                       aria-controls="settings" aria-selected="true">{{__('menu.Settings')}}</a>
                </li>
                @if(!empty($rule->rule_id))
                    <li class="nav-item">
                        <a class="nav-link" id="history-tab" data-toggle="tab" href="#history" role="tab"
                           aria-controls="history" aria-selected="false">{{__('menu.History')}}</a>
                    </li>
                @endif
            </ul>
            <br>
            <div class="tab-content mb-1 mt-1" id="myTabContent">
                <div class="tab-pane fade show active" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                    @include('head::auto-reject-approve-rules.crud')
                </div>
                @if(!empty($rule->rule_id))
                    <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
                        @include('head::auto-reject-approve-rules.history')
                    </div>
                @endif
            </div>
            <div class="clearfix"></div>
        </div>
    </div>
@endsection

@push('scripts')
    <script src="{{ asset('dist/js/role-permission-checkbox.js') }}"></script>
    <script>
        let urlMenu = document.location.toString();
        if (urlMenu.match('#')) {
            $('.nav-tabs a[href="#' + urlMenu.split('#')[1] + '"]').tab('show');
        }

        // Change hash for page-reload
        $('.nav-tabs a').on('show.bs.tab', function (e) {

            window.location.hash = e.target.hash;

            if (e.target.hash === '#communication') {
                e.preventDefault();
                location.reload();
            }

            $("html, body").scrollTop(0);
        });

        $('.multiSel').selectpicker();

    </script>
@endpush
