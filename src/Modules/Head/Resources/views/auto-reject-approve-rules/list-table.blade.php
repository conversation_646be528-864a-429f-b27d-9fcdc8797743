<table class="table">
    <thead>
    <tr>
        <th scope="col">{{__('head::autoProcess.name')}}</th>
        <th scope="col">{{__('head::autoProcess.rule')}}</th>
        <th scope="col">{{__('head::autoProcess.office')}}</th>
        <th scope="col">{{__('head::autoProcess.status')}}</th>
        <th scope="col">{{__('head::autoProcess.client')}}</th>
        <th scope="col">{{__('head::autoProcess.created_at')}}</th>
        <th scope="col">{{__('head::autoProcess.created_by')}}</th>
        <th scope="col">{{__('head::autoProcess.updated_at')}}</th>
        <th scope="col">{{__('head::autoProcess.updated_by')}}</th>
        <th scope="col">{{__('table.Actions')}}</th>
    </tr>
    </thead>
    <tbody>
        @foreach($rules as $rule)
            <tr
                @if(!$rule->active)
                class="not-active"
                @endif
            >
                <td>{{ $rule->name }}</td>
                <td>{{ __('head::autoProcess.auto.' . $rule->type) }}</td>
                <td>{{ implode(', ', getOfficesFromJson($rule->office_ids)) }}</td>
                <td>{{ $rule->active == 1 ? __('head::autoProcess.active') : __('head::autoProcess.not_active') }}</td>
                <td>{{ $rule->new_client === 1 ? __('head::autoProcess.client.new') : ($rule->new_client === 0 ? __('head::autoProcess.client.old') : __('head::autoProcess.all')) }}</td>
                <td>{{ \Carbon\Carbon::parse($rule->created_at)->format('d.m.Y H:i:s') }}</td>
                <td>{{ $rule->created_by }}</td>
                <td>{{ \Carbon\Carbon::parse($rule->updated_at)->format('d.m.Y H:i:s') }}</td>
                <td>{{ $rule->updated_by }}</td>
                <td class="button-div">
                    <div class="button-actions">
                        <a href="{{ route('head.autoProcessRules.edit', $rule->rule_id) }}" class="" role="button">{{ __('btn.Edit') }}</a>
                    </div>
                </td>
            </tr>
        @endforeach
    </tbody>
    <tfoot>
    <tr id="pagination-nav">
        <td colspan="14">
            {{ $rules->links() }}
        </td>
    </tr>
    </tfoot>
</table>
