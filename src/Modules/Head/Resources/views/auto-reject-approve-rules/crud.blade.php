<style>
    /* <PERSON><PERSON>, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }

    table tr {
        height: 14px;
    }
</style>
<div class="row" style="padding-left: 15px;">
    <form
        id="autoProcessForm"
        method="POST"
        class="col-lg-12 row"
        action="{{ !empty($rule->rule_id) ? route('head.autoProcessRules.update', $rule->getKey()) : route('head.autoProcessRules.store') }}"
        accept-charset="UTF-8">
        @csrf
        <div class="col-lg-12">
            <div class="row">
                <table>
                    <thead>
                    <td style="width: 100px;">{{ __('head::autoProcess.settins.apply') }}</td>
                    <td style="width: 250px;">{{ __('head::autoProcess.settins.condition') }}</td>
                    <td style="width: 300px;">{{ __('head::autoProcess.settins.value') }}</td>
                    </thead>
                    <tbody>
                    <tr>
                        <td colspan="3">
                            <hr style="margin-top: 0rem; margin-bottom: 5px;"/>
                        </td>
                    </tr>

                    <tr>
                        <td></td>
                        <td>{{ __('head::autoProcess.name') }}:</td>
                        <td>
                            <input class="form-control"
                                   minlength="2"
                                   maxlength="50"
                                   name="name"
                                   type="text"
                                   value="{{ old('name') ?? (!empty($rule) ? $rule->name : '') }}"
                                   id="name"
                                   style="text-align: left;"
                            >
                        </td>
                    <tr/>

                    <tr>
                        <td></td>
                        <td>{{ __('head::autoProcess.rule') }}:</td>
                        <td>
                            <select name="type" id="type" class="form-control w-100 mb-3">
                                @foreach($ruleTypes as $ruleType)
                                    <option value="{{ $ruleType }}"
                                            @if (!empty($rule->rule_id) && $ruleType == $rule->type)
                                                selected
                                        @endif
                                    >{{__('head::autoProcess.auto.' . $ruleType)}}</option>
                                @endforeach
                            </select>
                        </td>
                    <tr/>

                    <tr>
                        <td></td>
                        <td>{{ __('head::autoProcess.office') }}:</td>
                        <td>
                            <select id="office_ids" title="{{__('table.ChooseOffice')}}" name="office_ids[]"
                                    class="form-control w-100 multiSel"
                                    data-actions-box="true" data-selected-text-format="count > 3" multiple>
                                @php
                                    $officeIdsArray = !empty($rule->office_ids) ? json_decode($rule->office_ids, true) : [];
                                @endphp
                                @foreach($offices as $office)
                                    <option value="{{$office->office_id}}"
                                            @if (!empty($officeIdsArray) && in_array($office->office_id, $officeIdsArray))
                                                selected
                                        @endif
                                    >{{$office->name}}</option>
                                @endforeach
                            </select>
                        </td>
                    <tr/>

                    <tr>
                        <td></td>
                        <td>{{ __('head::autoProcess.status') }}:</td>
                        <td>
                            <select name="active" id="active" class="form-control w-100 mb-3">
                                <option value="1"
                                        @if (!empty($rule->rule_id) && $rule->active == true)
                                            selected
                                    @endif
                                >{{__('head::autoProcess.active')}}</option>
                                <option value="0"
                                        @if (!empty($rule->rule_id) && $rule->active == false)
                                            selected
                                    @endif
                                >{{__('head::autoProcess.not_active')}}</option>
                            </select>
                        </td>
                    <tr/>

                    <tr>
                        <td></td>
                        <td>{{ __('head::autoProcess.cards1') }}:</td>
                        <td>
                            <select id="cards1" title="{{__('table.ChooseCard')}}" name="cards1[]"
                                    class="form-control w-100 multiSel"
                                    data-actions-box="true" data-selected-text-format="count > 3" multiple>
                                @php
                                    $cards1Array = !empty($rule->cards1) ? json_decode($rule->cards1, true) : [];
                                @endphp
                                @foreach($cards as $card)
                                    <option value="{{$card}}"
                                            @if (!empty($cards1Array) && in_array($card, $cards1Array))
                                                selected
                                        @endif
                                    >{{underScoreToText($card)}}</option>
                                @endforeach
                            </select>
                        </td>
                    <tr/>

                    <tr>
                        <td></td>
                        <td>{{ __('head::autoProcess.cards2') }}:</td>
                        <td>
                            <select id="cards2" title="{{__('table.ChooseCard')}}" name="cards2[]"
                                    class="form-control w-100 multiSel"
                                    data-actions-box="true" data-selected-text-format="count > 3" multiple>
                                @php
                                    $cards2Array = !empty($rule->cards2) ? json_decode($rule->cards2, true) : [];
                                @endphp
                                @foreach($cards as $card)
                                    <option value="{{$card}}"
                                            @if (!empty($cards2Array) && in_array($card, $cards2Array))
                                                selected
                                        @endif
                                    >{{underScoreToText($card)}}</option>
                                @endforeach
                            </select>
                        </td>
                    <tr/>

                    <tr>
                        <td></td>
                        <td>{{ __('head::autoProcess.client') }}:</td>
                        <td>
                            <select name="new_client" id="new_client" class="form-control w-100 mb-3">
                                <option value="9999999"
                                        @if (!empty($rule->rule_id) && $rule->new_client === null)
                                            selected
                                    @endif
                                >{{__('head::autoProcess.all')}}</option>
                                <option value="0"
                                        @if (!empty($rule->rule_id) && $rule->new_client === 0)
                                            selected
                                    @endif
                                >{{__('head::autoProcess.client.old')}}</option>
                                <option value="1"
                                        @if (!empty($rule->rule_id) && $rule->new_client === 1)
                                            selected
                                    @endif
                                >{{__('head::autoProcess.client.new')}}</option>
                            </select>
                        </td>
                    <tr/>

                    <tr>
                        <td><input type="checkbox" id="repaid_loans_count" style="margin-left: 40px;"></td>
                        <td>{{ __('head::autoProcess.repaid_loans_count') }}:</td>
                        <td>
                            <div class="form-row">
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">min.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="repaid_loans_count_from"
                                       value="{{ old('repaid_loans_count_from') ?? (!empty($rule->repaid_loans_count_from) ? $rule->repaid_loans_count_from : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                                <span style="margin-left: 20px; margin-right: 10px; padding-top: 7px;"> - </span>
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">max.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="repaid_loans_count_to"
                                       value="{{ old('repaid_loans_count_to') ?? (!empty($rule->repaid_loans_count_to) ? $rule->repaid_loans_count_to : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                            </div>
                        </td>
                    <tr/>

                    <tr>
                        <td><input type="checkbox" id="max_overdue_days" style="margin-left: 40px;"></td>
                        <td>{{ __('head::autoProcess.max_overdue_days') }}:</td>
                        <td>
                            <div class="form-row">
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">min.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="max_overdue_days_from"
                                       value="{{ old('max_overdue_days_from') ?? (!empty($rule->max_overdue_days_from) ? $rule->max_overdue_days_from : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                                <span style="margin-left: 20px; margin-right: 10px; padding-top: 7px;"> - </span>
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">max.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="max_overdue_days_to"
                                       value="{{ old('max_overdue_days_to') ?? (!empty($rule->max_overdue_days_to) ? $rule->max_overdue_days_to : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                            </div>
                        </td>
                    <tr/>

                    <tr>
                        <td><input type="checkbox" id="amount_requested" style="margin-left: 40px;"></td>
                        <td>{{ __('head::autoProcess.amount_requested') }}:</td>
                        <td>
                            <div class="form-row">
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">min.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="amount_requested_from"
                                       value="{{ old('amount_requested_from') ?? (!empty($rule->amount_requested_from) ? $rule->amount_requested_from : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                                <span style="margin-left: 20px; margin-right: 10px; padding-top: 7px;"> - </span>
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">max.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="amount_requested_to"
                                       value="{{ old('amount_requested_to') ?? (!empty($rule->amount_requested_to) ? $rule->amount_requested_to : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                            </div>
                        </td>
                    <tr/>

                    <tr>
                        <td><input type="checkbox" id="ccr_overdue_days" style="margin-left: 40px;"></td>
                        <td>{{ __('head::autoProcess.ccr_overdue_days') }}:</td>
                        <td>
                            <div class="form-row">
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">min.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="ccr_overdue_days_from"
                                       value="{{ old('ccr_overdue_days_from') ?? (!empty($rule->ccr_overdue_days_from) ? $rule->ccr_overdue_days_from : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                                <span style="margin-left: 20px; margin-right: 10px; padding-top: 7px;"> - </span>
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">max.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="ccr_overdue_days_to"
                                       value="{{ old('ccr_overdue_days_to') ?? (!empty($rule->ccr_overdue_days_to) ? $rule->ccr_overdue_days_to : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                            </div>
                        </td>
                    <tr/>

                    <tr>
                        <td><input type="checkbox" id="ccr_and_overdue_principal" style="margin-left: 40px;"></td>
                        <td>{{ __('head::autoProcess.ccr_and_overdue_principal') }}:</td>
                        <td>
                            <div class="form-row">
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">min.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="ccr_and_overdue_principal_from"
                                       value="{{ old('ccr_and_overdue_principal_from') ?? (!empty($rule->ccr_and_overdue_principal_from) ? $rule->ccr_and_overdue_principal_from : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                                <span style="margin-left: 20px; margin-right: 10px; padding-top: 7px;"> - </span>
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">max.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="ccr_and_overdue_principal_to"
                                       value="{{ old('ccr_and_overdue_principal_to') ?? (!empty($rule->ccr_and_overdue_principal_to) ? $rule->ccr_and_overdue_principal_to : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                            </div>
                        </td>
                    <tr/>

                    <tr>
                        <td><input type="checkbox" id="ccr_or_overdue_principal" style="margin-left: 40px;"></td>
                        <td>{{ __('head::autoProcess.ccr_or_overdue_principal') }}:</td>
                        <td>
                            <div class="form-row">
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">min.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="ccr_or_overdue_principal_from"
                                       value="{{ old('ccr_or_overdue_principal_from') ?? (!empty($rule->ccr_or_overdue_principal_from) ? $rule->ccr_or_overdue_principal_from : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                                <span style="margin-left: 20px; margin-right: 10px; padding-top: 7px;"> - </span>
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">max.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="ccr_or_overdue_principal_to"
                                       value="{{ old('ccr_or_overdue_principal_to') ?? (!empty($rule->ccr_or_overdue_principal_to) ? $rule->ccr_or_overdue_principal_to : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                            </div>
                        </td>
                    <tr/>

                    <tr>
                        <td><input type="checkbox" id="credit_in_bank" style="margin-left: 40px;"></td>
                        <td>{{ __('head::autoProcess.credit_in_bank') }}:</td>
                        <td>
                            <select name="credit_in_bank" class="form-control w-100 mb-3 syncCheckBox">
                                <option value="9999999"
                                        @if (!empty($rule->rule_id) && $rule->income === null)
                                            selected
                                    @endif
                                ></option>
                                <option value="0"
                                        @if (!empty($rule->rule_id) && $rule->credit_in_bank === 0)
                                            selected
                                    @endif
                                >{{__('table.No')}}</option>
                                <option value="1"
                                        @if (!empty($rule->rule_id) && $rule->credit_in_bank === 1)
                                            selected
                                    @endif
                                >{{__('table.Yes')}}</option>
                            </select>
                        </td>
                    <tr/>

                    <tr>
                        <td><input type="checkbox" id="credit_in_nobank" style="margin-left: 40px;"></td>
                        <td>{{ __('head::autoProcess.credit_in_nobank') }}:</td>
                        <td>
                            <select name="credit_in_nobank" class="form-control w-100 mb-3 syncCheckBox">
                                <option value="9999999"
                                        @if (!empty($rule->rule_id) && $rule->income === null)
                                            selected
                                    @endif
                                ></option>
                                <option value="0"
                                        @if (!empty($rule->rule_id) && $rule->credit_in_nobank === 0)
                                            selected
                                    @endif
                                >{{__('table.No')}}</option>
                                <option value="1"
                                        @if (!empty($rule->rule_id) && $rule->credit_in_nobank === 1)
                                            selected
                                    @endif
                                >{{__('table.Yes')}}</option>
                            </select>
                        </td>
                    <tr/>

                    <tr>
                        <td><input type="checkbox" id="income" style="margin-left: 40px;"></td>
                        <td>{{ __('head::autoProcess.income') }}:</td>
                        <td>
                            <select name="income" class="form-control w-100 mb-3 syncCheckBox">
                                <option value="9999999"
                                        @if (!empty($rule->rule_id) && $rule->income === null)
                                            selected
                                    @endif
                                ></option>
                                <option value="0"
                                        @if (!empty($rule->rule_id) && $rule->income === 0)
                                            selected
                                    @endif
                                >{{__('table.No')}}</option>
                                <option value="1"
                                        @if (!empty($rule->rule_id) && $rule->income === 1)
                                            selected
                                    @endif
                                >{{__('table.Yes')}}</option>
                            </select>
                        </td>
                    <tr/>

                    <tr>
                        <td><input type="checkbox" id="income_avg" style="margin-left: 40px;"></td>
                        <td>{{ __('head::autoProcess.income_amount') }}:</td>
                        <td>
                            <div class="form-row">
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">min.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="income_avg_from"
                                       value="{{ old('income_avg_from') ?? (!empty($rule->income_avg_from) ? $rule->income_avg_from : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                                <span style="margin-left: 20px; margin-right: 10px; padding-top: 7px;"> - </span>
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">max.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="income_avg_to"
                                       value="{{ old('income_avg_to') ?? (!empty($rule->income_avg_to) ? $rule->income_avg_to : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                            </div>
                        </td>
                    <tr/>

                    <tr>
                        <td><input type="checkbox" id="ccr_total_percent" style="margin-left: 40px;"></td>
                        <td>{{ __('head::autoProcess.ccr_total_percent') }}:</td>
                        <td>
                            <div class="form-row">
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">min.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="ccr_total_percent_from"
                                       value="{{ old('ccr_total_percent_from') ?? (!empty($rule->ccr_total_percent_from) ? $rule->ccr_total_percent_from : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                                <span style="margin-left: 20px; margin-right: 10px; padding-top: 7px;"> - </span>
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">max.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="ccr_total_percent_to"
                                       value="{{ old('ccr_total_percent_to') ?? (!empty($rule->ccr_total_percent_to) ? $rule->ccr_total_percent_to : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                            </div>
                        </td>
                    <tr/>

                    <tr>
                        <td><input type="checkbox" id="age_border" style="margin-left: 40px;"></td>
                        <td>{{ __('head::autoProcess.age_border') }}:</td>
                        <td>
                            <div class="form-row">
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">min.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="age_border_from"
                                       value="{{ old('age_border_from') ?? (!empty($rule->age_border_from) ? $rule->age_border_from : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                                <span style="margin-left: 20px; margin-right: 10px; padding-top: 7px;"> - </span>
                                <span style="margin-left: 10px; margin-right: 10px; padding-top: 7px;">max.</span>
                                <input class="form-control syncCheckBox"
                                       type="number"
                                       minlength="0"
                                       maxlength="6"
                                       name="age_border_to"
                                       value="{{ old('age_border_to') ?? (!empty($rule->age_border_to) ? $rule->age_border_to : '') }}"
                                       style="text-align: left; width: 80px;"
                                >
                            </div>
                        </td>
                    <tr/>
                    <tr>
                        <td></td>
                        <td colspan="3" style="color:red; font-size: 13px;">Настройка за възраст е с по-голям приоритет
                            от всички останали! Т.е. другите ще бъдат игнорирани.
                        </td>
                    <tr/>

                    <tr>
                        <td>
                            <input type="hidden" name="refinance_request" value="0"/>
                            <input type="checkbox" name="refinance_request"
                                   value="1"
                                   class="skip-check cursor-pointer"
                                   id="refinance_request"
                                   style="margin-left: 40px;"
                                @checked(old('refinance_request', empty($rule) ? null : $rule?->refinance_request))
                            />
                        </td>
                        <td>
                            <label for="refinance_request" class="cursor-pointer">
                                {{ __('head::autoProcess.refinance_request') }}
                            </label>
                        </td>
                        <td></td>
                    <tr/>

                    <tr>
                        <td></td>
                        <td>
                            <label for="settlement_code" class="skip-check">
                                {{ __('Settlement Code') }}:
                                <span title="Кодове се взимат от МВР справка (PermanentAddress: LocationCode/SettlementCode). Кодове влизат в база с добавени нули." style="cursor: help; color: #007bff;">(i)</span>
                            </label>
                        </td>
                        <td>
                            <textarea name="settlement_code" style="width: 292px; height: 68px;">{{ old('settlement_code', !empty($rule->settlement_code) ? implode(',', $rule->settlement_code) : '') }}</textarea>
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>
                            <label for="location_code" class="skip-check">
                                {{ __('Location Code') }}:
                                <span title="
                                    Важно!!!
                                    - не може да има Настройка по Локейшън код, без да има закачен сетлмент код;
                                    - за един сетлмент код може да има закачени един и повече локейшън кодове,
                                    - ако има сетлмент код и за него няма локейшън кодве, значи важи за всички;

                                    Формат: settlement_code:{location_code,location_code}, xxx

                                    Примери: 40136:{00114, 53214},20677:{90132, 13914, 01109},

                                    Кодове се взимат от МВР справка (PermanentAddress: LocationCode/SettlementCode).
                                    Кодове вилзат в база с добавени нули, ако са по-малко от 5 символа.
                                " style="cursor: help; color: #007bff;">(i)</span>
                            </label>
                        </td>
                        <td>
                            <textarea name="location_code" style="width: 292px; height: 68px;">{{ old('location_code', !empty($rule_locatoin_code) ? $rule_locatoin_code : '') }}</textarea>
                            <span></span>
                        </td>
                    </tr>

                    </tbody>
                </table>
            </div>

            @if(!empty($rule->rule_id))
                <div id="autoProcessCrudBtnContainer" class="hidden-element">
                    <button type="button" class="btn btn-danger cancelBtn">{{ __('btn.Cancel') }}</button>
                    <button type="submit" class="btn btn-success approveBtn">{{ __('btn.Update') }}</button>
                </div>
                <button type="button" id="autoProcessEditBtn"
                        class="btn btn-info editBtn border-0">{{ __('btn.Edit') }}</button>
            @else
                <a href="{{route('head.autoProcessRules.list')}}"
                   class="btn btn-danger cancelBtn">{{ __('btn.Cancel') }}</a>
                <button type="submit" class="btn btn-success approveBtn">{{ __('btn.Create') }}</button>
            @endif
        </div>
    </form>
</div>
@push('scripts')
    <script>
        const approveBtn = $('#autoProcessCrudBtnContainer .approveBtn');
        const cancelBtn = $('#autoProcessCrudBtnContainer .cancelBtn');
        const editBtn = $('#autoProcessEditBtn');
        const actionBtnContainer = $('#autoProcessCrudBtnContainer');
        const autoProcessForm = $('#autoProcessForm');
        const allInputs = $('input');
        const allSelects = $('select');

        const isEdit = '{{ !empty($rule->rule_id) }}';

        isEdit && allInputs.prop('disabled', true);
        isEdit && allSelects.prop('disabled', true);

        actionBtnContainer.hide();

        cancelBtn.on('click', () => {
            window.location.href = '{{route('head.autoProcessRules.list')}}';
        })

        editBtn.add(approveBtn).on('click', () => {
            actionBtnContainer.toggle('fast');
            editBtn.toggle('fast', function () {
                if (this.style.display) {
                    allInputs.prop('disabled', false);
                    allSelects.prop('disabled', false);

                    allSelects.selectpicker('refresh');

                    return;
                }

                allInputs.prop('disabled', true);
                allSelects.prop('disabled', true);

                allSelects.selectpicker('refresh');
            });
        });

        isEdit && autoProcessForm.on('submit', e => {
            e.preventDefault();

            $.ajax({
                type: autoProcessForm[0].method,
                url: autoProcessForm[0].action,
                data: autoProcessForm.serialize(),
                headers: {
                    'Accept': 'application/json'
                },
                success: resp => {
                    for (const arg in resp) {
                        const el = $(`*[name^=${arg}]`);

                        if (!el) {
                            continue;
                        }

                        if (el.is('select')) {
                            if (Array.isArray(resp[arg])) {
                                el.selectpicker('val', resp[arg]);
                                continue;
                            }

                            if (typeof resp[arg] === 'boolean') {
                                resp[arg] = resp[arg] === true ? 1 : 0;
                            }

                            el.selectpicker('val', resp[arg]);
                            continue
                        }

                        el.val(resp[arg]);
                    }

                    allSelects.selectpicker('refresh');
                    window.location.reload();
                },
                error: err => {
                    const response = err.responseJSON;

                    if (response?.status === 'fail' && response.message) {
                        alert(response.message); // Or use toast instead
                    }

                    const errors = err.responseJSON && JSON.parse(err.responseJSON.message);
                    if (errors) {
                        for (const arg in errors) {
                            $(`*[name^=${arg}]`).addClass('is-invalid');
                            allSelects.selectpicker('refresh');
                        }
                    }
                }
            });
        })

        // set default checkboxes
        $(document).ready(function () {
            $.each($('.syncCheckBox'), function (idx, el) {
                let val = el.value;
                if (val == 9999999) {
                    val = '';
                }

                let name = getNameForElement($(el));

                if (val.length > 0) {
                    if ($('input[id="' + name + '"]').length > 0) {
                        $('input[id="' + name + '"]').prop('checked', true);
                    }
                }
            })
        });

        // TO BE CHECKED
        $('.syncCheckBox').on('change', function () {
            let val = this.value;
            if (val == 9999999) {
                val = '';
            }

            let nameVersus = '';
            let name = getNameForElement($(this));

            let checkboxVal = false;
            if (val.length > 0) {
                checkboxVal = true;
            } else {

                // check if min/max
                if (
                    $('input[name="' + name + nameVersus + '"]').length > 0
                    && $('input[name="' + name + nameVersus + '"]').val().length > 0
                ) {
                    return; // do nothing, since another option is not empty
                }
            }

            if ($('input[id="' + name + '"]').length > 0) {
                $('input[id="' + name + '"]').prop('checked', checkboxVal);

                // взаимоизключаващи се правила
                // AND и OR не могат да бъдат кликнати едновременно
                if (
                    $(this).attr('name') == 'ccr_and_overdue_principal_from'
                    || $(this).attr('name') == 'ccr_and_overdue_principal_to'
                ) {
                    $('input[name="ccr_or_overdue_principal_from"]').val('');
                    $('input[name="ccr_or_overdue_principal_to"]').val('');
                    $('input[id="ccr_or_overdue_principal"]').prop('checked', false);
                }

                if (
                    $(this).attr('name') == 'ccr_or_overdue_principal_from'
                    || $(this).attr('name') == 'ccr_or_overdue_principal_to'
                ) {
                    $('input[name="ccr_and_overdue_principal_from"]').val('');
                    $('input[name="ccr_and_overdue_principal_to"]').val('');
                    $('input[id="ccr_and_overdue_principal"]').prop('checked', false);
                }
            }
        });

        function getNameForElement(obj) {
            let name = obj.attr('name');

            if (name.endsWith('_from')) {
                name = replaceLast('_from', '', name);
                nameVersus = "_to";
            } else if (name.endsWith('_to')) {
                name = replaceLast('_to', '', name);
                nameVersus = "_from";
            }

            return name;
        }

        function replaceLast(find, replace, string) {
            let lastIndex = string.lastIndexOf(find);

            if (lastIndex === -1) {
                return string;
            }

            let beginString = string.substring(0, lastIndex);
            let endString = string.substring(lastIndex + find.length);

            return beginString + replace + endString;
        }

        // TO BE UNCHECKED
        $('input[type="checkbox"]').on('change', function () {
            let val = $(this).is(':checked');

            if (val == false) {
                let name = $(this).attr('id');

                if ($('select[name="' + name + '"]').length > 0) {
                    $('select[name="' + name + '"]').val('');
                } else {
                    if ($('input[name="' + name + '_from"]').length > 0) {
                        $('input[name="' + name + '_from"]').val('');
                    }
                    if ($('input[name="' + name + '_to"]').length > 0) {
                        $('input[name="' + name + '_to"]').val('');
                    }
                }
            } else {
                if (!$(this).hasClass('skip-check')) {
                    $(this).prop('checked', false); // yeah, thats a cool feature
                }
            }
        });

        $(document).ready(function () {
            $(".select2Tags").each(function (index, element) {
                $(this).select2({
                    tags: true,
                    width: "100%" // just for stack-snippet to show properly
                });

                // Handle the tag removal
                $(element).on('select2:unselect', function (e) {
                    var removedTag = e.params.data.id; // The tag that was unselected
                    // Find the corresponding option in the select element and remove the 'selected' attribute
                    $(element).find('option[value="' + removedTag + '"]').remove();
                });
            });
        });

    </script>
@endpush
