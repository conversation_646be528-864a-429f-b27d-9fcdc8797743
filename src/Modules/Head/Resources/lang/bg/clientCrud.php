<?php

return [
    'clientNotFound'                => 'Не съществува такъв клиент!',
    'clientCreatedSuccessfully'     => 'Успешно създаване на клиент',
    'clientCreationFailed'          => 'Неуспешно създаване на клиент',
    'clientUpdateFailed'            => 'Неуспешна промяна на клиент',
    'clientUpdatedSuccessfully'     => 'Клиента е променен успешно',
    'cantDeleteClientEmail'         => 'Клиент има активен онлайн заем, не може да изтриите емайл.',
    'clientDeletionFailed'          => 'Неуспешно изтриване на клиент',
    'clientDeletedSuccessfully'     => 'Успешно изтрит клиент',
    'unblockedSuccessfully'         => 'Успешно отблокиран клиент',
    'blockedSuccessfully'           => 'Успешно блокиран клиент',
    'blockedFailed'                 => 'Неуспешно блокиране на клиент',
    'unblockedFailed'               => 'Неуспешно отблокиране на клиент',
    'clientDisableFailed'           => 'Неуспешно деактивиране на клиент.',
    'clientDisableForbidden'        => 'Не може да деактивираш неактивен потребител.',
    'clientEnableFailed'            => 'Неуспено активиране на потребител',
    'clientEnabledSuccessfully'     => 'Успешно активиране на клиент',
    'clientDisabledSuccessfully'    => 'Успешно деактивиране на клиент',
    'clientEmptyPin'                => 'Липсва ЕГН',
    'clientUpdateSuccessfully'      => 'Успешна промяна на клиент',
    'LoanContactNotFound'           => 'Не съществува такъв контакт',
    'clientIsNotFromYourOffice'     => 'Нямате достъп до клиент от друг офис',
    'clientBlocked'                 => 'Клиента е блокиран',

    // error message for contact form in client crud blade
    'MaxLimitOfContacts'           => 'Достигнат максимум от контакти',
    'SuccessDeletedContact'        => 'Успешно изтриване на контакт',
    'FailDeletedContact'           => 'Неуспешно изтриване на контакт',

    //error message for guarant form in client crud blade
    'MaxLimitOfGuarants'           => 'Достигнат максимум от гаранти',
    'SuccessDeletedGuarant'        => 'Успешно изтриване на гарант',
    'FailDeletedGuarant'           => 'Неуспешно изтриване на гарант',

    'clientPhoneDeletionSuccess'   => 'Успешно изтриване на клиентски телефон',
    'clientAddressDeletionSuccess'   => 'Успешно изтриване на адрес',

    'InvalidPin' => 'Моля въведете валидно ЕГН.',
    'InvalidPinLength' => 'Дължината трябва да е :length символа.',
    'PaymentMethodDefaultOption' => 'Получаване на парите',
    'SelectConsultans' => 'Консултант',
    'ClientBank' => 'банка на клиента(по желание)',
    'ChooseProduct' => 'Моля изберете продукт.',
    'InvalidCardNumber' => 'Моля въведете валиден ЛК номер.',
    'clientAddressesReplaced'   => 'Успешно актуализиран на адрес',
    'CompanyDetailsHeader' => 'Детайли за фирма',
    'CompanyAddressHeader' => 'Седалище на фирма',
    'RepresentorDetailsHeader' => 'Детайли на представител',
    'CompanyInfoHeader' => 'ЕИК и Данъчен номер',
    'InvalidEik' => 'Моля въведете валидно ЕИК.',
    'InvalidEikLength' => 'Дължината трябва да е :length символа.',
];
