<?php

namespace Modules\Head\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;

class ClientPaymentScheduleRequest extends BaseRequest
{

    public function rules(): array
    {
        return [
            'clientId' => 'required|exists:client,client_id',
            'loanId' => 'nullable|exists:loan,loan_id',
            'currentDate' => 'nullable|string|date_format:"Y-m-d"',
            'showSavedPlanBtn' => 'nullable|integer',
        ];
    }
}
