<?php

namespace Modules\Head\Http\Requests;

use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Interfaces\ListSearchInterface;
use Modules\Head\Services\HeaderService;

final class LoanSearchRequest extends BaseRequest implements ListSearchInterface
{
    public function rules(): array
    {
        $loanTaskTypeRules = 'nullable|string|in:'
            . implode(',', array_keys(HeaderService::APPROVE_TASK_TYPES));

        return [
            'loan_id' => 'nullable|integer',
            'migration_id' => 'nullable|integer',
            'migration_db' => 'nullable|string',
            'client_id' => 'nullable|integer',
            'clientType' => 'nullable|numeric|in:0,1',
            'clientNames' => 'nullable|string|min:2|max:50',
            'clientPhone' => 'nullable|string|min:7|max:15',
            'active' => $this->getConfiguration('requestRules.active'),
            'loanStatusId.*' => 'nullable|numeric|exists:loan_status,loan_status_id',
            'loanTaskType' => $loanTaskTypeRules,
            'productTypeId' => 'nullable|numeric|exists:product_type,product_type_id',
            'product_id.*' => 'nullable|numeric|exists:product,product_id',
            'loanTypeId' => 'nullable|numeric|exists:loan_type,loan_type_id',
            'paymentMethodId' => 'nullable|numeric|exists:payment_method,payment_method_id',
            'officeId' => 'nullable|numeric|exists:office,office_id',
            'lastStatusUpdateAdministratorId' => 'nullable|numeric|exists:administrator,administrator_id',
            'sourceId' => 'nullable|string|in:' . implode(',', LoanSourceEnum::toArray()),
            'pin' => 'nullable|numeric',
            'guarantor_pin' => 'nullable|numeric',
            'amountApprovedFrom' => 'nullable|numeric',
            'amountApprovedТо' => 'nullable|numeric',
            'amountRequestedFrom' => 'nullable|numeric',
            'amountRequestedТо' => 'nullable|numeric',
            'loanPeriodFrom' => 'nullable|numeric|min:0',
            'loanPeriodTo' => 'nullable|numeric|min:0',
            'juridical' => 'nullable|numeric|in:0,1',
            'cession' => 'nullable|numeric|in:0,1',
            'fraud' => 'nullable|numeric|in:0,1',
            'updatedAt' => 'nullable|regex:/^([0-9]{2}\/[0-9]{2}\/[0-9]{4} - [0-9]{2}\/[0-9]{2}\/[0-9]{4})$/i',
            'order.*' => 'nullable',
            'createdAt' => [
                'nullable',
                'regex:' . $this->getDateValidationRegex()
            ],
            'limit' => 'nullable|numeric',
            'loansApproveAttemptSkipTill' => 'nullable|string',
            'consultant_ids' => 'nullable|array',
            'consultant_ids.*' => 'sometimes|integer|exists:consultant,consultant_id',
            'contact_phone' => 'nullable|numeric|max_digits:10',
            'client_name' => 'nullable|string',
        ];
    }

    public function getFilters(): array
    {
        $filters = $this->validated();
        if (
            !empty($filters['createdAt'])
            && !preg_match("/(\d{2}-\d{2}-\d{4}) - (\d{2}-\d{2}-\d{4})/", $filters['createdAt'])
        ) {
            $filters['createdAt'] = $filters['createdAt'] . ' - ' . $filters['createdAt'];
        }

        return $filters;
    }
}
