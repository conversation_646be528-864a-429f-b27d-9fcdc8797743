<?php

namespace Modules\Head\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Common\Http\Requests\BaseRequest;

class EmploymentEditRequest extends BaseRequest
{
    /**
     * @return string[]
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                'min:2',
                Rule::unique('employment')
                    ->ignore($this->id, 'employment_id')
            ],
            'score' => 'required|numeric',
        ];
    }
}
