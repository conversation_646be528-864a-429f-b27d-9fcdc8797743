<?php

namespace Modules\Head\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;

class CreateNoiRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'clientId' => 'nullable|exists:client,client_id',
            'loanId' => 'nullable|integer|exists:loan,loan_id',
            'reportType' => 'required|array',
            'reportType.*' => 'in:2,7,51',
        ];
    }
}
