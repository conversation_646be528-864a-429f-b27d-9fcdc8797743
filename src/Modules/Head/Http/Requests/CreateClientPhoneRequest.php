<?php

namespace Modules\Head\Http\Requests;

use Illuminate\Database\Query\Builder;
use Modules\Common\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

/**
 * @property-read int $client_id
 * @property-read string $number
 */
class CreateClientPhoneRequest extends BaseRequest
{

    /**
     * @return string[]
     */
    public function rules(): array
    {
        return [
            'client_id' => 'required|exists:client,client_id',
            'number' => [
                'required',
                'numeric',
                Rule::unique('client_phone')->where(function (Builder $query) {
                    $query->where('client_id', $this->client_id);
                    $query->where('number', $this->number);
                    $query->whereNull('deleted_at');
                })
            ],
        ];
    }
}