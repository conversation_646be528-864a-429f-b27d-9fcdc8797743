<?php

namespace Modules\Head\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;

class MvrReportRequest extends BaseRequest
{

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'clientId' => 'exists:client,client_id',
            'loanId' => 'nullable|exists:loan,loan_id',
            'reportType' => 'required|string',
        ];
    }
}