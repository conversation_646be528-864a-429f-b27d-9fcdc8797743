<?php

declare(strict_types=1);

namespace Modules\Head\Http\Requests\ClientsWithoutActive;

use Carbon\Carbon;
use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Models\Product;

final class SmsMessageRequest extends BaseRequest
{
    public function rules(): array
    {
        $validFrom = ['required_with:create_discount'];
        $validTo = ['required_with:create_discount'];

        if ($this->input('sms_valid_from_to')) {
            $dates = getDatesForFilter($this->input('sms_valid_from_to'));

            $validFrom[] = 'after_or_equal:' . today();
            $validTo[] = 'after_or_equal:valid_from';

            $this->merge([
                'valid_from' => Carbon::parse($dates['from'])->format('Y-m-d'),
                'valid_to' => Carbon::parse($dates['to'])->format('Y-m-d'),
            ]);
        }

        return [
            'filters' => 'string|nullable',
            'message' => 'required|string',
            'sms_create_discount' => 'boolean',
            'sms_discount_product_ids' => 'required_with:create_discount|array',
            'sms_discount_product_ids.*' => 'sometimes|required|integer|distinct|exists:' . Product::class . ',product_id',
            'sms_discount_percent' => 'required_with:create_discount|integer|between:1,100',
            'sms_valid_from_to' => 'required_with:create_discount|string|regex:/^\d{2}-\d{2}-\d{4} - \d{2}-\d{2}-\d{4}$/',
            'valid_from' => $validTo,
            'valid_to' => $validFrom,
        ];
    }

    public function messages(): array
    {
        return [
            'sms_valid_from_to.regex' => __('Грешен формат. Пример: :exampleDate', [
                'exampleDate' => date('d-m-Y') . ' - ' . date('d-m-Y')
            ]),
        ];
    }
}
