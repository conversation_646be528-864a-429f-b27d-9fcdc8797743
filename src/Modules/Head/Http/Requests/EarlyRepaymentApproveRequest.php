<?php

namespace Modules\Head\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Models\Setting;

class EarlyRepaymentApproveRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules()
    {
        return [
            'loanId' => 'required|numeric|exists:loan,loan_id',
            'repaymentDate' => 'required|date_format:' . Setting::SHOW_FORMAT,
            'principal' => 'required|numeric',
            'interest' => 'required|numeric',
            'penalty' => 'required|numeric',
            'lateInterest' => 'required|numeric',
            'taxes' => 'required|numeric',
            'sum' => 'required|numeric',
        ];
    }
}
