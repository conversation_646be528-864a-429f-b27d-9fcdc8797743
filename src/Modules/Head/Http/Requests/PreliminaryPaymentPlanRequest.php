<?php

namespace Modules\Head\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;
use Modules\Common\Http\Requests\BaseRequest;

class PreliminaryPaymentPlanRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'sum' => 'required|numeric|min:50|max:20000',
            'period' => 'required|integer|min:1|max:30',
            'discount' => 'nullable|integer|min:0|max:100',
            'productId' => 'required|exists:product,product_id',
        ];
    }

    /**
     * @return array
     */
    public function messages(): array
    {
        return [
            'sum.required' => __('head::loanCrud.sumRequired'),
            'sum.min' => __('head::loanCrud.sumMin'),
            'sum.max' => __('head::loanCrud.sumMax'),

            'period.required' => __('head::loanCrud.periodRequired'),
            'period.min' => __('head::loanCrud.periodMin'),
            'period.max' => __('head::loanCrud.periodMax'),

            'discount.min' => __('head::loanCrud.discountMin'),
            'discount.max' => __('head::loanCrud.discountMax'),

            'productId.required' => __('head::productCrud.pleaseChooseProduct'),
        ];
    }

    protected function failedValidation(Validator $validator): JsonResponse
    {
        throw new HttpResponseException(
            response()->json(
                $validator->errors(),
                400
            )
        );
    }
}
