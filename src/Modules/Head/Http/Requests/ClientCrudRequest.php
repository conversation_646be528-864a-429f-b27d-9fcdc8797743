<?php

namespace Modules\Head\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Models\Client;
use Modules\Head\Http\Middleware\ClientDtoSerializer;
use Modules\Sales\Http\Dto\ClientDto;

class ClientCrudRequest extends BaseRequest
{
    /**
     * @return array
     */
    public function rules()
    {
        /**
         * @var Client $client
         */
        $client = $this->route()->parameter('client');
        return [
            'client.first_name' => $this->getConfiguration('requestRules.firstName'),
            'client.middle_name' => $this->getConfiguration('requestRules.middleNameNullable'),
            'client.last_name' => $this->getConfiguration('requestRules.lastName'),
            'client.phone.*' => $this->getConfiguration('requestRules.commonPhoneNullable'),
            'client.phone.0' => $this->getConfiguration('requestRules.commonPhone'),
            'client.email.0' => [
                'nullable',
                'email:rfc',
                'min:7',
                'max:50'
            ],
            'client_idcard.pin' => [
                $this->getConfiguration('requestRules.pin'),
                Rule::unique('client', 'pin')
                    ->ignore(
                        $client?->getKey(),
                        'client_id'
                    ),
            ],
            'client.legal_status' => 'nullable|in:' . implode(',', Client::getLegalStatuses()),
            'client.citizenship_type' => 'nullable|in:' . implode(',', Client::getCitizenshipTypes()),
            'client.gender' => 'nullable|in:' . implode(',', Client::getGenders()),
            'client.legal_status_code' => 'nullable|in:' . implode(',', Client::getLegalStatusCodes()),
            'client.economy_sector_code' => 'nullable|in:' . implode(',', Client::getEconomySectorCodes()),
            'client.industry_code' => 'nullable|in:' . implode(',', Client::getIndustryCodes()),

            'client_idcard.idcard_number' => 'nullable|string',
            'client_idcard.city_id' => 'nullable|numeric|exists:city,city_id',
            'client_idcard.address' => 'nullable|min:2|max:100',
            'client_idcard.post_code' => $this->getConfiguration('requestRules.postCodeNullable'),
            'client_idcard.issue_date' => 'nullable|date',
            'client_idcard.valid_date' => $this->getConfiguration('requestRules.validDateNullable'),
            'client_idcard.lifetime_idcard' => 'required|integer',
            'client_idcard.issued_by' => 'nullable|string|max:100',
            'client_idcard.sex' => 'nullable|min:2|max:10',
            'client_idcard.idcard_issued_id' => 'nullable|numeric|exists:idcard_issued,idcard_issued_id',

            'client_address.city_id' => 'nullable|numeric|exists:city,city_id',
            'client_address.post_code' => $this->getConfiguration('requestRules.postCodeNullable'),
            'client_address.address' => 'nullable|min:2|max:100',

            'client_employer.city_id' => 'nullable|numeric|exists:city,city_id',
            'client_employer.name' => $this->getConfiguration('requestRules.nameNullable'),
            'client_employer.bulstat' => 'nullable|numeric|digits_between:0,13',
            'client_employer.address' => 'nullable|min:2|max:100',
            'client_employer.details' => 'nullable|min:2|max:100',
            'client_employer.position' => 'nullable|min:2|max:100',
            'client_employer.salary' => 'nullable|numeric|digits_between:1,8',
            'client_employer.experience' => 'nullable|numeric|digits_between:1,4',

            'client_bank_account.bank_id' => 'nullable|numeric|exists:bank,bank_id',
            'client_bank_account.bic' => 'nullable|min:2|max:100',
            'client_bank_account.iban' => 'nullable|min:2|max:100',
            'client_bank_account.main' => 'nullable|in:0,1',

            'guarant.*.pin' => $this->getConfiguration('requestRules.pinNullable'),
            'guarant.*.first_name' => $this->getConfiguration('requestRules.firstNameNullable'),
            'guarant.*.middle_name' => $this->getConfiguration('requestRules.middleNameNullable'),
            'guarant.*.last_name' => $this->getConfiguration('requestRules.lastNameNullable'),
            'guarant.*.phone' => $this->getConfiguration('requestRules.commonPhoneNullable'),
            'guarant.*.guarant_type_id' => 'nullable|numeric|exists:guarant_type,guarant_type_id',
            'guarant.*.idcard_number' => 'nullable|string',
            'guarant.*.idcard_issue_date' => 'nullable|date',
            'guarant.*.idcard_valid_date' => $this->getConfiguration('requestRules.validDateNullable'),
            'guarant.*.address' => 'nullable|min:2|max:100',

            'contact.*.name' => $this->getConfiguration('requestRules.nameNullable'),
            'contact.*.contact_type_id' => 'nullable|numeric|exists:contact_type,contact_type_id',
            'contact.*.phone' => $this->getConfiguration('requestRules.commonPhoneNullable'),
        ];
    }

    public function asDto(): ClientDto
    {
        return (new ClientDtoSerializer())->createClientDto($this->validated());
    }
}
