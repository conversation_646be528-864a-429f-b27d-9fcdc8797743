<?php

namespace Modules\Head\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Models\SaleTask;
use Modules\Sales\Http\Dto\LoanDto;
use Modules\Common\Models\PaymentMethod;

class LoanAjaxClientCardRequest extends BaseRequest
{
    /**
     * @return string[]
     */
    public function rules(): array
    {
        $rules = [
            'task' => 'nullable|string',
            'loan.loan_id' => 'required|numeric|exists:loan,loan_id',
            'loan.product_id' => 'required|numeric',
            'loan.loan_period' => 'required|numeric',
            'loan.loan_sum' => 'required|numeric',
            'loan.discount_percent' => 'required|numeric|min:0|max:100',
            'loan.office_id' => 'required|numeric|exists:office,office_id',
            'loan.payment_method' => 'required|numeric|exists:payment_method,payment_method_id',
            'loan.bank_account_id' => 'required|numeric',
            'refinanced_loans' => 'nullable|array',
            'refinanced_loans.*' => 'exists:loan,loan_id',
            'sale_task_id' => 'sometimes|integer|exists:' . SaleTask::getTableName() . ',sale_task_id',
            'updateTempData' => 'sometimes|numeric',
        ];

        if ($this->input('loan.payment_method') == PaymentMethod::PAYMENT_METHOD_BANK) {
//            $rules['loan.iban'] = 'required|string|min:20|max:22|regex:/^[A-Z0-9]+$/';
            $rules['loan.iban'] = 'required|string|min:16|max:34|regex:/^([a-zA-Z0-9]{16,34})+$/';
        } else {
            $rules['loan.iban'] = 'nullable|string';
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'loan.iban.regex' => __('head::clientCard.ibanValidationMessage'),
            'loan.iban.min' => __('head::clientCard.ibanMinMessage'),
            'loan.iban.max' => __('head::clientCard.ibanMaxMessage'),
        ];
    }

    public function asDto(): LoanDto
    {
        $data = $this->validated('loan');
        $data['loan_sum'] = floatToInt($data['loan_sum']);
        $data['refinanced_loan_ids'] = $this->validated('refinanced_loans');
        $data['administrator_id'] = getAdminId();

        if ($data['payment_method'] == PaymentMethod::PAYMENT_METHOD_EASYPAY) {
            $data['iban'] = null;
        }

        return LoanDto::getFrom($data);
    }
}
