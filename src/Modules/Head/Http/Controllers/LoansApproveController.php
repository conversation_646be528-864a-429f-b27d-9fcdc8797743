<?php

namespace Modules\Head\Http\Controllers;

use Illuminate\View\View;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Approve\Application\Action\ExportLoansForApprovalAction;
use Modules\Approve\Exports\LoansForApprovalExport;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Head\Application\Actions\LoansApproveDataAction;
use Modules\Head\Http\Requests\LoanSearchRequest;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class LoansApproveController extends BaseController
{

    public function list(
        LoanSearchRequest      $loanSearchRequest,
        LoansApproveDataAction $loansApproveDataAction
    ): View
    {
        return view(
            'head::loans-approve.list',
            $loansApproveDataAction->execute(
                $loanSearchRequest->getFilters(),
                $this->getPaginationLimit()
            )
        );
    }

    public function export(LoanSearchRequest $request, ExportLoansForApprovalAction $action): BinaryFileResponse
    {
        $rows = $action->execute($request->getFilters());
        return Excel::download(
            new LoansForApprovalExport($rows),
            'loan_for_approval_export_' . time() . '.xlsx'
        );
    }


    public function refreshApproved(
        LoanSearchRequest      $loanSearchRequest,
        LoansApproveDataAction $loansApproveDataAction
    ): View
    {
        $data = $loansApproveDataAction->fetchLoans($loanSearchRequest->getFilters(), $this->getPaginationLimit());
        $data['loans']->withPath(route('head.approveLoans.list'));

        return view('head::loans-approve.list-table', $data);
    }

}
