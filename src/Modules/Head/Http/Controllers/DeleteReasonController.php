<?php

namespace Modules\Head\Http\Controllers;

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\DeleteReason;
use Modules\Head\Application\Actions\DeleteReasonDataAction;
use Modules\Head\Exceptions\HeadException;
use Modules\Head\Http\Requests\BlockDeleteReasonRequest;
use Modules\Head\Services\ReasonService;

class DeleteReasonController extends BaseController
{
    protected string $pageTitle = 'Delete reason list';
    protected string $indexRoute = 'head.deleteReason.list';
    protected ReasonService $deleteReasonService;

    public function __construct(ReasonService $deleteReasonService)
    {
        $this->deleteReasonService = $deleteReasonService;

        parent::__construct();
    }

    public function list(
        DeleteReasonDataAction $deleteReasonDataAction
    ): View
    {
        $data = $deleteReasonDataAction->executeIndex();

        return view('head::delete-reason.index', $data);
    }

    public function create(
        DeleteReasonDataAction $deleteReasonDataAction
    ): View
    {
        $data = $deleteReasonDataAction->executeCreate();

        return view('head::delete-reason.crud', $data);
    }

    public function store(BlockDeleteReasonRequest $request): RedirectResponse
    {
        try {
            $this->deleteReasonService->createDeleteReason($request->validated());

            return redirect()
                ->route($this->indexRoute)
                ->with('success', __('head::deleteReasonCrud.CreatedSuccessfully'));
        } catch (\Throwable $t) {

            return $this->handleException(new HeadException($t->getMessage(), $t->getFile(), $t->getLine()));
        }

    }

    public function edit(
        DeleteReason           $deleteReason,
        DeleteReasonDataAction $deleteReasonDataAction
    )
    {
        $data = $deleteReasonDataAction->executeEdit($deleteReason);

        return view('head::delete-reason.crud', $data);
    }

    public function update(DeleteReason $deleteReason, BlockDeleteReasonRequest $request): RedirectResponse
    {
        try {
            $this->deleteReasonService->editDeleteReason($deleteReason, $request->validated());

            return redirect()
                ->route($this->indexRoute)
                ->with('success', __('head::deleteReasonCrud.UpdatedSuccessfully'));
        } catch (\Throwable $t) {
            return $this->handleException(new HeadException($t->getMessage(), $t->getFile(), $t->getLine()));
        }

    }

    public function delete(DeleteReason $deleteReason): RedirectResponse
    {
        try {
            $this->deleteReasonService->deleteDeleteReason($deleteReason);

            return redirect()
                ->route($this->indexRoute)
                ->with('success', __('head::deleteReasonCrud.DeletedSuccessfully'));
        } catch (\Throwable $t) {
            return $this->handleException(new HeadException($t->getMessage(), $t->getFile(), $t->getLine()));
        }
    }

    public function enable(DeleteReason $deleteReason): RedirectResponse
    {
        try {
            $this->deleteReasonService->enableDeleteReason($deleteReason);

            return redirect()
                ->route($this->indexRoute)
                ->with('success', __('head::deleteReasonCrud.EnabledSuccessfully'));
        } catch (\Throwable $t) {
            return $this->handleException(new HeadException($t->getMessage(), $t->getFile(), $t->getLine()));
        }
    }

    public function disable(DeleteReason $deleteReason): RedirectResponse
    {
        try {
            $this->deleteReasonService->disableDeleteReason($deleteReason);

            return redirect()
                ->route($this->indexRoute)
                ->with(
                    'success',
                    __('head::deleteReasonCrud.DisabledSuccessfully')
                );
        } catch (\Throwable $t) {
            return $this->handleException(new HeadException($t->getMessage(), $t->getFile(), $t->getLine()));
        }
    }
}
