<?php

namespace Modules\Head\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Product;
use Modules\Common\Traits\DatesAwareTrait;
use StikCredit\Calculators\LoanCalculator;

class PaymentSchedulePlanController extends BaseController
{
    use DatesAwareTrait;

    public function __construct(
        protected readonly LoanCalculator $loanCalculator
    ) {
        parent::__construct();
    }

    public function preview(Request $request): View|RedirectResponse
    {
        $args = $request->validate([
            'discount' => 'required|numeric',
            'period' => 'required|integer',
            'productId' => 'required|integer',
            'sum' => 'required|numeric',
            'interest' => 'sometimes|numeric',
            'penalty' => 'sometimes|numeric',
        ]);
        $args['amount'] = $args['sum'];


        /** @var Product $product */
        $product = Product::where('product_id', $args['productId'])->firstOrNew();

        if (!$product->exists) {
            return back()->with('error', __('No available product.'));
        }

        $airInterest = $product->getInterestRate($args['period'], $args['sum']);
        $aprInterest = $product->getPenaltyRate($args['period'], $args['sum']);
        $installmentModifier = $product->getInstallmentModifier();

        if (!$product->isFixed) {
            $airInterest = $args['interest'] ?? 0;
            $aprInterest = $args['penalty'] ?? 0;
        }

        $currentDate = $this->sqlDate()->format('Y-m-d');
        $startDate = getLoanStartDate(
            $currentDate,
            $currentDate,
            $installmentModifier,
            ($product->isPayday() ? $args['period'] : null)
        );

        $config['air'] = $airInterest;
        $config['apr'] = $aprInterest;
        $config['amount'] = (int) $args['amount'];
        $config['discountPercent'] = $args['discount'];
        $config['currentDate'] = $currentDate;
        $config['utilisationDate'] = $currentDate;
        $config['startFromDate'] = $startDate;
        $config['installmentModifier'] = $installmentModifier;
        $config['numberOfInstallments'] = $args['period'];
        $config['productType'] = $product->productGroup->name;

        $this->loanCalculator->build($config);

        $data['loan'] = $this->loanCalculator;
        $data['air'] = $airInterest;
        $data['apr'] = $aprInterest;

        return view('head::payment-schedule-plan.index', compact('data'));
    }
}
