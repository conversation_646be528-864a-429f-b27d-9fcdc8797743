<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Illuminate\View\View;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Head\Http\Requests\TabSwitchRequest;
use Modules\Head\Repositories\LoanRepository;
use Modules\Head\Services\ClientService;
use Modules\Head\Services\LoanService;

class PrevRequestsController extends BaseController
{

    public function __construct(
        private readonly ClientService  $clientService,
        private readonly LoanRepository $loanRepository
    )
    {
    }

    /**
     * @param TabSwitchRequest $request
     * @return View
     * @throws NotFoundException
     * @throws ProblemException
     */
    public function index(TabSwitchRequest $request): View
    {
        $client = $this->clientService->getClientById($request->integer('clientId'));

        $loanId = $request->integer('loanId');
        $loan = $loanId ? $this->loanRepository->getById($loanId) : null;

        $data = [
            'clientId' => $client->getKey(),
            'client' => $client,
            'loan' => $loan,
            'loans' => $this->loanRepository->getByClientId($client->getKey(), 99999),
        ];

        return view('head::prev-requests.index', $data);
    }
}
