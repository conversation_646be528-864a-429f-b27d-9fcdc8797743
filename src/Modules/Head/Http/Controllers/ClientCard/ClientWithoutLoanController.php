<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Illuminate\View\View;
use Modules\Head\Application\Actions\PrepareClientCardDataAction;

class ClientWithoutLoanController extends BaseClientCardController
{

    public function index(PrepareClientCardDataAction $prepareClientCardDataAction): View
    {
        $routeParams = $this->getRouteParams();
        $data = $prepareClientCardDataAction->execute($routeParams);

        /// load view
        return view('head::client-without-loan.index', $data);
    }
}
