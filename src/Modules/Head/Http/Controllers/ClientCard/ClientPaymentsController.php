<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Illuminate\View\View;
use Kris\LaravelFormBuilder\FormBuilder;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Payment;
use Modules\Head\FilterForms\ClientPaymentsFilterForm;
use Modules\Head\Http\Requests\ClientPaymentRequest;
use Modules\Head\Services\ClientService;
use Modules\Head\Services\LoanService;
use Modules\Payments\Repositories\PaymentRepository;

final class ClientPaymentsController extends BaseController
{
    public function __construct(
        private readonly ClientService $clientService,
        private readonly LoanService $loanService,
        private readonly FormBuilder $formBuilder
    ) {
        parent::__construct();
    }


    public function index(ClientPaymentRequest $request): View
    {
        $client = $this->clientService->getClientById($request->get('clientId'));

        $loan = null;
        if ($request->get('loanId')) {
            $loan = $this->loanService->getLoanById($request->get('loanId'));
        }

        $filters = $request->get('paymentsFilterForm', []);
        if ($loan) {
            $filters['loanId'] = $loan->getKey();
        }

        $filters['status'] = [PaymentStatusEnum::EASY_PAY_SENT->value, PaymentStatusEnum::DELIVERED->value];

        $data = [
            'client' => $client,
            'loan' => $loan,
            'paymentsSource' => Payment::getAllPaymentSources(),
            'payments' => app(PaymentRepository::class)->getPaymentsByClient($client, $filters, $this->getPaginationLimit()),
            'filterForm' => $this->formBuilder->create(ClientPaymentsFilterForm::class, [
                'route' => [
                    'head.client-payments.index',
                    [
                        'clientId' => $client->getKey(),
                        'loanId' => $loan?->getKey() ?? 0,
                    ]
                ]
            ])
        ];

        return view('head::client-payments.index', $data);
    }
}
