<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\SaleTask;
use Modules\Head\Application\Actions\PrepareClientCardDataAction;
use Modules\Sales\Repositories\SaleTaskRepository;

class ClientCardLoanTaskController extends BaseClientCardController
{
    //head/clientCard/{clientId}/{loanId}/(approve|sale|collect)/{taskId}
    public function index(
        PrepareClientCardDataAction $prepareClientCardAction
    ): View|RedirectResponse
    {
        $routeParams = $this->getRouteParams();
        /** @var Loan $loan */
        $loan = Loan::find($routeParams['loanId']);
        $admin = auth()->user();

        if (
            !empty($routeParams['task'])
            && $routeParams['task'] == 'sales' &&
            $routeParams['taskId']
        ) {
            $task = app(SaleTaskRepository::class)->getById($routeParams['taskId']);
            if ($task->status === SaleTask::SALE_TASK_STATUS_DONE) {
                return to_route('sales.saleTask.list')
                    ->with('fail', __('head::loanCrud.SaleTaskAlreadyClosed'));
            }
        }


        // when client sign his contract, and sale task still exists and agent try to handle it
        // we will close the task with msg
        if (
            !empty($routeParams['task'])
            && $routeParams['task'] == 'sales'
            && !$loan->isNew()
            && !empty($routeParams['taskId'])
        ) {
            app(SaleTaskRepository::class)->closeTaskById($routeParams['taskId']);
            return to_route('sales.saleTask.list')->with('fail', __('head::loanCrud.ClientAlreadySignTheLoan'));
        }


        if ($routeParams['task'] != 'collect') {
            if ($loan->isFinished() || $loan->hasStatus(LoanStatus::ACTIVE_STATUS_ID)) {
                return to_route('head.loans.list')->with(
                    'warning',
                    __("Loan (:loanId) is finished you can't process this loan", ['loanId' => $loan->getKey()])
                );
            }
        }

        //// if loan is active and try to access approve action
        if (
            in_array($routeParams['task'], ['sales', 'sale', 'approve'])
            && $loan?->isActive()
        ) {
            return to_route('head.client-with-loan.index', [
                'clientId' => $loan->client_id,
                'loanId' => $loan->getKey()
            ])->with('warning', __('head::loanCrud.AdminCanNotProcessActiveLoan'));
        }


        // check if properly processing (from listing, not open the link)
        if ($routeParams['task'] == 'approve') {
            if (
                in_array($loan->loan_status_id, [LoanStatus::SIGNED_STATUS_ID, LoanStatus::PROCESSING_STATUS_ID])
                && empty($loan->administrator_id)
            ) {
                return redirect()
                    ->route('approve.loan-decision.process', $loan->loan_id);
            }

            if ($loan->loan_status_id == LoanStatus::NEW_STATUS_ID && empty($loan->administrator_id)) {
                return redirect()
                    ->route('head.approveLoans.list', []);
            }
        }


        /// check if that admin can process this loan
        if (!$admin->can('processLoan', [$loan, $routeParams['task']])) {
            return redirect()
                ->route('head.approveLoans.list', [])
                ->with('warning', __('head::loanCrud.AdminCanNotProcessTheLoan'));
        }


        $data = $prepareClientCardAction->execute($routeParams);

        /// load view
        return view('head::client-card-loan-task.index', $data);
    }
}
