<?php

namespace Modules\Head\Http\Controllers\ClientCard;

use Illuminate\Http\RedirectResponse;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Representor;
use Modules\Head\Http\Requests\ClientCard\StoreRepresentorDataRequest;

class RepresentorEditDataController extends BaseController
{

    public function storeRepresentorData(
        Representor $representor,
        StoreRepresentorDataRequest $request
    ): RedirectResponse {
        /// if no valid representor go back
        if (!$representor) {
            return back()->with('fail', __('messages.error'));
        }

        //// update representor data
        if ($representor->update($request->validated())) {
            return back()->with('success', __('messages.success'));
        }

        return back()->with('fail', __('messages.error'));
    }
}