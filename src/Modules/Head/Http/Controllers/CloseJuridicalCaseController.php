<?php

namespace Modules\Head\Http\Controllers;

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Repositories\ClosedJuridicalCaseRepository;
use Modules\Head\Exceptions\LoanIsNotJuridical;
use Modules\Head\Repositories\LoanRepository;
use Modules\Sales\Domain\Exceptions\LoanNotFound;

class CloseJuridicalCaseController extends BaseController
{
    public function __construct(
        private readonly ClosedJuridicalCaseRepository $closedJuridicalCaseRepository,
        private readonly LoanRepository $loanRepository
    ) {
    }

    /**
     * @throws LoanNotFound
     * @throws LoanIsNotJuridical
     * @throws Exception
     */
    public function closeJuridicalCase(Loan $loan): RedirectResponse
    {
        if (!$loan->exists) {
            throw new LoanNotFound($loan->getKey());
        }

        if (!$loan->isJuridical()) {
            throw new LoanIsNotJuridical($loan->getKey());
        }

        $data = [
            'loan_id' => $loan->getKey(),
            'client_id' => $loan->client->getKey(),
            'administrator_id' => getAdminId(),
            'closed_at' => date('Y-m-d'),
        ];

        DB::transaction(function () use ($data, $loan) {
            /// create closed juridical case row
            $this->closedJuridicalCaseRepository->create($data);

            /// mark loan as closed juridical case
            $this->loanRepository->markAsClosedJuridicalCase($loan);
        });


        return back()->with('success', __('Success close juridical case.'));
    }


    public function writtenOffLoan(Loan $loan)
    {
        if (!$loan->exists || !$loan->closedJuridicalCase()) {
            return back()->with('error', __('messages.cantChangeStatusToWrittenOf'))
                ->withFragment('#paymentschedule');
        }

        $loan->setAttribute('loan_status_id', LoanStatus::WRITTEN_OF_STATUS_ID);
        if ($loan->saveQuietly()) {
            app(LoanRepository::class)->addLoanStatusHistory($loan);

            return back()->with('success', __('messages.loanStatusChangedToWrittenOf'))
                ->withFragment('#paymentschedule');
        }

        return back()->with('error', __('messages.generalErrorSomethingWrong'))
            ->withFragment('#paymentschedule');
    }
}
