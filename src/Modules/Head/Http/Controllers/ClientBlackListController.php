<?php

namespace Modules\Head\Http\Controllers;

use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Client;
use Modules\Head\Application\Actions\ClientBlackListDataAction;
use Modules\Head\Http\Requests\FilterRequests\ClientBlackListFilterRequest;

class ClientBlackListController extends BaseController
{
    public function list(
        ClientBlackListDataAction $action,
        ClientBlackListFilterRequest $request
    ): View {
        return view(
            'head::client-black-list.list',
            $action->execute(
                $request->validated(),
                $this->getPaginationLimit()
            )
        );
    }

    public function blackListDetails(Client $client): View
    {
        $client->load(['clientBlockHistories', 'clientUnblockBlockHistories']);

        $clientBlockHistories = collect($client->clientBlockHistories);
        $clientUnblockBlockHistories = collect($client->clientUnblockBlockHistories);
        $clientBlockHistory = $clientBlockHistories->merge($clientUnblockBlockHistories)
            ->sortBy('created_at', null, 'DESC');

        return view(
            'head::client-black-list.details',
            [
                'client' => $client,
                'clientBlockHistories' => $clientBlockHistory,
            ]
        );
    }

    public function historyBlockedClients(
        ClientBlackListDataAction $action,
        ClientBlackListFilterRequest $request
    ): View {
        return view(
            'head::client-black-list.history',
            $action->executeHistory(
                $request->validated(),
                $this->getPaginationLimit()
            )
        );
    }
}