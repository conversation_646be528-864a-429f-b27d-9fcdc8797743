<?php

namespace Modules\Head\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Contact;
use Modules\Common\Models\LoanContactActual;
use Modules\Head\Application\Actions\ClientCard\DeleteLoanContactAction;
use Modules\Head\Application\Actions\ClientCard\UpdateClientContactAction;
use Modules\Head\Http\Requests\ClientCard\ClientContactRequest;
use Modules\Sales\Application\Actions\CreateLoanContactAction;
use Modules\Sales\Http\Dto\ContactDto;

final class ClientContactsController extends BaseController
{

    public function create(
        Client<PERSON>ontactRequest    $request,
        CreateLoanContactAction $createLoanContactsAction
    ): RedirectResponse
    {
        $clientDto = ContactDto::from($request->validated());

        try {
            $createLoanContactsAction->execute($clientDto, $request->get('loan_id'));
        } catch (\Exception $exception) {
            return $this->backError($exception->getMessage());
        }

        return $this->backSuccess('head::clientCard.successCreateClientContact');
    }

    public function edit(
        ClientContactRequest      $request,
        Contact                   $contact,
        UpdateClientContactAction $updateClientContactAction
    ): RedirectResponse
    {
        $clientDto = ContactDto::from($request->all());

        if ($updateClientContactAction->execute($clientDto, $contact->getKey())) {
            return $this->backSuccess('head::clientCard.successUpdateClientContact');
        }

        return $this->backError('head::clientCard.failedUpdateClientContact');
    }

    public function destroy(
        LoanContactActual       $loanContactActual,
        DeleteLoanContactAction $deleteLoanContactAction
    ): RedirectResponse
    {
        if ($deleteLoanContactAction->execute($loanContactActual)) {
            return $this->backSuccess('head::clientCard.successDeleteClientContact');
        }

        return $this->backError('head::clientCard.failedDeleteClientContact');
    }
}
