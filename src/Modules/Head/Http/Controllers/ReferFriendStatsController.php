<?php

namespace Modules\Head\Http\Controllers;


use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Artisan;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Head\Application\Actions\ReferFriendStatsDataAction;
use Modules\Head\Http\Requests\ReferFriendStatsFilterRequest;

class ReferFriendStatsController extends BaseController
{
    public function list(
        ReferFriendStatsDataAction $action,
        ReferFriendStatsFilterRequest $request
    ): View {
        return view(
            'head::refer-friend-stats.list',
            $action->execute($request->validated(), $this->getPaginationLimit())
        );
    }

    public function refreshStats(): RedirectResponse
    {
        Artisan::call('script:update-referral-stats');

        return back()->with('success', __('messages.ReferFriendStatsController.successRefreshStats'));
    }
}