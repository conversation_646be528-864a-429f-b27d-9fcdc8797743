<?php

namespace Modules\Head\Http\Controllers;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Illuminate\View\View;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Head\Exports\ClientsInOverdueExport;
use Mo<PERSON>les\Head\Exports\UpcomingPaymentsExport;

class ReportsBirChoController extends BaseController
{
    public function index(): View
    {
        return view('head::reports-bircho.list');
    }

    public function clientsInOverdueExport()
    {
        $fileName = 'clients_in_overdue_' . now()->format('Ymd_His').'.xlsx';
        $filePath = 'exports/' . $fileName;

        Excel::store(new ClientsInOverdueExport, $filePath, 'public');

        return response()->download(storage_path($filePath), $fileName);
    }

    public function upcomingPaymentsExport()
    {
        $fileName = 'upcoming_payments_' . now()->format('Ymd_His').'.xlsx';
        $filePath = 'exports/' . $fileName;

        Excel::store(new UpcomingPaymentsExport, $filePath, 'public');

        return response()->download(storage_path($filePath), $fileName);
    }
}
