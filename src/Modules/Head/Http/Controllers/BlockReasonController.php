<?php

namespace Modules\Head\Http\Controllers;

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\BlockReason;
use Modules\Head\Application\Actions\BlockReasonDataAction;
use Modules\Head\Http\Requests\BlockDeleteReasonRequest;
use Modules\Head\Services\ReasonService;

class BlockReasonController extends BaseController
{
    protected ReasonService $blockReasonService;

    public function __construct(ReasonService $blockReasonService)
    {
        $this->blockReasonService = $blockReasonService;

        parent::__construct();
    }

    public function list(
        BlockReasonDataAction $blockReasonDataAction
    )
    {
        $data = $blockReasonDataAction->executeIndex();

        return view('head::block-reason.index', $data);
    }

    public function create(
        BlockReasonDataAction $blockReasonDataAction
    )
    {
        $data = $blockReasonDataAction->executeCreate();

        return view('head::block-reason.crud', $data);
    }

    public function store(
        BlockDeleteReasonRequest $request
    ): RedirectResponse
    {
        $this->blockReasonService->createBlockReason($request->validated());

        return redirect()
            ->route('head.blockReason.list')
            ->with('success', __('head::blockReasonCrud.CreatedSuccessfully'));
    }

    public function edit(
        BlockReason           $blockReason,
        BlockReasonDataAction $blockReasonDataAction
    ): View
    {
        $data = $blockReasonDataAction->executeEdit($blockReason);

        return view('head::block-reason.crud', $data);
    }

    public function update(BlockReason $blockReason, BlockDeleteReasonRequest $request): RedirectResponse
    {
        $this->blockReasonService->editBlockReason($blockReason, $request->validated());

        return redirect()
            ->route('head.blockReason.list')
            ->with('success', __('head::blockReasonCrud.UpdatedSuccessfully'));
    }

    public function delete(BlockReason $blockReason): RedirectResponse
    {
        $this->blockReasonService->deleteBlockReason($blockReason);

        return redirect()
            ->route('head.blockReason.list')
            ->with('success', __('head::blockReasonCrud.DeletedSuccessfully'));
    }

    public function enable(BlockReason $blockReason): RedirectResponse
    {
        $this->blockReasonService->enableBlockReason($blockReason);

        return redirect()
            ->route('head.blockReason.list')
            ->with('success', __('head::blockReasonCrud.EnabledSuccessfully'));
    }

    public function disable(BlockReason $blockReason): RedirectResponse
    {
        $this->blockReasonService->disableBlockReason($blockReason);

        return redirect()
            ->route('head.blockReason.list')
            ->with(
                'success',
                __('head::blockReasonCrud.DisabledSuccessfully')
            );
    }
}
