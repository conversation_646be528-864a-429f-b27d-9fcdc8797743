<?php

namespace Modules\Head\Forms\ClientCard;

use <PERSON>\LaravelFormBuilder\Form;

class UnblockClientForm extends Form
{

    public function buildForm(): void
    {
        $this->add('comment', 'textarea', [
            'label' => __('table.Comment'),
            'attr' => [
                'rows' => 5,
                'required' => 'required',
                'minlength' => 5,
            ]
        ]);

        if ($this->getData('client_id', false)) {
            $this->add('client_id', 'hidden', [
                'value' => $this->getData('client_id')
            ]);
        }
    }
}