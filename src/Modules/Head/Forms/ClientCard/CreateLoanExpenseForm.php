<?php

namespace Modules\Head\Forms\ClientCard;

use <PERSON>\LaravelFormBuilder\Form;
use Modules\Common\Models\Loan;

class CreateLoanExpenseForm extends Form
{
    public function buildForm(): void
    {
        /** @var Loan $loan *** */
        $loan = $this->getData('loan');
        if ($loan && $loan->isJuridical()) {
            $this->add('isJuridical', 'hidden', [
                'value' => 1
            ]);
        }

        $this
            ->add('amount', 'text', [
                'label' => __('table.Amount'),
                'attr' => [
                    'required' => 'required',
                    'data-parsley-pattern' => '^([0-9]*)(.[0-9]{1,2})?$',
                    'data-parsley-pattern-message' => __('Amount accept only number.'),
                ]
            ])
            ->add('comment', 'textarea', [
                'label' => __('table.Comment'),
                'attr' => [
                    'required' => 'required',
                    'rows' => 4,
                ]
            ]);

        if ($this->getData('client_id')) {
            $this->add('client_id', 'hidden', [
                'value' => $this->getData('client_id')
            ]);
        }

        if ($this->getData('loan_id')) {
            $this->add('loan_id', 'hidden', [
                'value' => $this->getData('loan_id')
            ]);
        }
    }
}
