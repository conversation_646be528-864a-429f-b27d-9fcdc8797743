<?php

namespace Modules\Head\Forms;

use <PERSON>\LaravelFormBuilder\Form;

class ClientPhoneForm extends Form
{

    /**
     * @return void
     */
    public function buildForm(): void
    {
        $this->add('number', 'text', [
            'label' => __('Client phone'),
            'attr' => [
                'required' => 'required',
                'minlength' => 5,
                'maxlength' => 20,
                'data-parsley-pattern' => config('validation.requestRules.commonPhoneParsley'),
                'data-parsley-pattern-message' => __('Невалиден телефонен номер.'),
            ]
        ]);

        if ($this->getData('client_id', false)) {
            $this->add('client_id', 'hidden', [
                'value' => $this->getData('client_id')
            ]);
        }
    }
}
