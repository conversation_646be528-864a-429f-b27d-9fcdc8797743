<?php

declare(strict_types=1);

namespace Modules\Head\Application\Actions\ClientsWithoutActiveLoan;

use Carbon\CarbonImmutable;
use Illuminate\Validation\ValidationException;
use Modules\Communication\Services\ManualSendSmsService;
use Modules\Head\Jobs\CreateDiscountsForClientsWithoutActiveLoanJob;
use Modules\Head\Repositories\ClientWithoutActiveLoanRepository;

final readonly class SendSmsMessageAction
{
    public function __construct(
        private ClientWithoutActiveLoanRepository $repository,
        private ManualSendSmsService $manualSendService,
    ) {
    }

    public function execute(array $data): array
    {
        $clientsIds = $this->repository->getBuilder(unserialize($data['filters'], ['allow_classes' => false]))
            ->get(['client_id'])->pluck('client_id')->toArray();

        if (empty($clientsIds)) {
            throw ValidationException::withMessages(['clients' => 'No clients found']);
        }

        if (isset($data['sms_create_discount'])) {
            CreateDiscountsForClientsWithoutActiveLoanJob::dispatch(
                $clientsIds,
                $data['sms_discount_product_ids'],
                $data['sms_discount_percent'],
                CarbonImmutable::parse($data['valid_from']),
                CarbonImmutable::parse($data['valid_to']),
            )->onQueue('manual-sms');
        }

        return $this->manualSendService->sendMessageViaQueue($clientsIds, $data['message']);
    }
}
