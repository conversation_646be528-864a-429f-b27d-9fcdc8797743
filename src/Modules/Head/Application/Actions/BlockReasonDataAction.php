<?php

namespace Modules\Head\Application\Actions;

use <PERSON>\LaravelFormBuilder\FormBuilder;
use Modules\Common\Models\BlockReason;
use Modules\Head\Forms\BlockReasonForm;
use Modules\Head\Repositories\BlockReasonRepository;

class BlockReasonDataAction
{
    public function __construct(
        private readonly BlockReasonRepository $blockReasonRepository,
        private readonly FormBuilder           $formBuilder
    )
    {
    }

    public function executeEdit(BlockReason $blockReason): array
    {
        $data['blockReason'] = $blockReason;
        $data['blockReasonForm'] = $this->formBuilder->create(BlockReasonForm::class, [
            'method' => 'POST',
            'route' => ['head.blockReason.update', $blockReason->getKey()],
            'data-parsley-validate' => 'true',
            'model' => $blockReason
        ]);

        return $data;
    }

    public function executeCreate(): array
    {
        $data['blockReasonForm'] = $this->formBuilder->create(BlockReasonForm::class, [
            'method' => 'POST',
            'route' => 'head.blockReason.store',
            'data-parsley-validate' => 'true'
        ]);

        return $data;
    }

    public function executeIndex(): array
    {
        $data['blockReasons'] = $this->blockReasonRepository->getByFliters();

        return $data;
    }

}
