<?php

namespace Modules\Head\Application\Actions;

use <PERSON>\LaravelFormBuilder\Form;
use <PERSON>\LaravelFormBuilder\FormBuilder;
use <PERSON>dules\Head\FilterForms\ClientFilterForm;
use Modules\Head\Repositories\BlockReasonRepository;
use Modu<PERSON>\Head\Repositories\ClientRepository;
use Modules\Head\Repositories\DeleteReasonRepository;

class ClientDataAction
{
    public function __construct(
        private readonly ClientRepository       $clientRepository,
        private readonly BlockReasonRepository  $blockReasonRepository,
        private readonly DeleteReasonRepository $deleteReasonRepository,
        private readonly FormBuilder            $formBuilder
    )
    {
    }

    public function execute(array $filters = [], int $perPage = 10): array
    {
        $data['clients'] = $this->clientRepository->getClientsByFilters($filters, $perPage);

        $data['blockReasons'] = $this->blockReasonRepository->getSelectOptions();
        $data['deleteReasons'] = $this->deleteReasonRepository->getSelectOptions();
        $data['clientFilterForm'] = $this->getClientFilterForm(
            $data['blockReasons'],
            $data['deleteReasons']
        );

        return $data;
    }

    protected function getClientFilterForm(
        array $blockReasons,
        array $deleteReasons,
    ): Form
    {
        return $this->formBuilder->create(ClientFilterForm::class, [
            'route' => 'head.clients.list',
            'data' => [
                'blockReasons' => $blockReasons,
                'deleteReasons' => $deleteReasons,
            ]
        ]);
    }
}
