<?php

declare(strict_types=1);

namespace Modules\Head\Application\Actions\OverdueStatsPhysicalOffices;

use Modules\Head\Exports\OverdueStatsPhysicalOfficesExport;
use Modules\Head\Repositories\OverdueStatsPhysicalOfficesRepository;

final readonly class ExportAction
{
    public function __construct(private OverdueStatsPhysicalOfficesRepository $repository)
    {
    }

    public function execute(array $filters): OverdueStatsPhysicalOfficesExport
    {
        return new OverdueStatsPhysicalOfficesExport($this->repository->getBuilder($filters));
    }
}
