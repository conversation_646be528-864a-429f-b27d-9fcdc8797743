<?php

namespace Modules\Head\Application\Actions;

use Illuminate\Support\Facades\Session;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\ProductSetting;
use Modules\Discounts\Repositories\DiscountByPhoneRepository;
use Modules\Head\Repositories\ClientRepository;
use Modules\Head\Services\LoanService;
use Modules\Product\Services\ProductService;
use Modules\Common\Models\Office;

readonly class PrepareProductSettingsDataAction
{
    public function __construct(
        private ProductService $productService,
        private LoanService $loanService,
        private ClientRepository $clientRepository,
        private DiscountByPhoneRepository $discountByPhoneRepository,
    ) {
    }

    public function execute(array $data): array
    {
        $productId = !empty($data['productId']) ? (int) $data['productId'] : null;
        if (empty($productId)) {
            return [];
        }

        $product = $this->productService->getById($productId);
        if (empty($product->product_id)) {
            return [];
        }

        /// get default amounts
        $defAmount = $this->productService
            ->getProductSettingByKey($product, ProductSetting::DEFAULT_AMOUNT_KEY);
        $minAmount = $this->productService
            ->getProductSettingByKey($product, ProductSetting::MIN_AMOUNT_KEY);
        $maxAmount = $this->productService
            ->getProductSettingByKey($product, ProductSetting::MAX_AMOUNT_KEY);

        $amountStep = $this->productService
            ->getProductSettingByKey($product, ProductSetting::AMOUNT_STEP_KEY);
        if (empty($amountStep)) {
            $amountStep = 50;
        }

        /// get default periods
        $defPeriod = $this->productService
            ->getProductSettingByKey($product, ProductSetting::DEFAULT_PERIOD_KEY);
        $minPeriod = $this->productService
            ->getProductSettingByKey($product, ProductSetting::MIN_PERIOD_KEY);
        $maxPeriod = $this->productService
            ->getProductSettingByKey($product, ProductSetting::MAX_PERIOD_KEY);
        $refAmount = 0;

        $currentLoanDiscount = null;
        $client = $this->clientRepository->getByPin($data['clientPin'] ?? '');
        $discounts = $this->getDiscounts($client, $product->getKey(), $data['phone'] ?? null);
        $maxDiscount = $this->getMaxDiscount($discounts);


        $loanId = null;
        $loanProductId = null;
        if (isset($data['loanId']) && (int)$data['loanId'] > 0) {
            $loan = $this->loanService->getLoanById($data['loanId']);
            $loanId = $loan->getKey();
            $loanProductId = $loan->product_id;

            /// when we have refinancing loans set min amount + 100.00
            $refinancingLoans = $loan?->refinancing()->get() ?? collect([]);
//            && $loan->product_id === $product->getKey()
            if ($refinancingLoans->count()) {

                $refAmount = 0;
                foreach ($refinancingLoans as $refLoan) {

                    $isForOnline = (Office::OFFICE_ID_WEB == $refLoan->office_id);
                    $refAmount += $refLoan->getAmountForRefinance($isForOnline);

                    // $refAmount += $refLoan->getEarlyRepaymentDebtDb();
                }
                $refAmount = $refAmount / 100;

                // $tmpMinAmount = ($refAmount + 10000) / 100;
                // if ($tmpMinAmount <= $maxAmount) {
                //     $minAmount = $tmpMinAmount;
                // }
            }

            if ($loan->product_id === $product->getKey()) {
                $defAmount = $loan->amount_approved / 100;
                $defPeriod = intval($loan->period_approved);
                $currentLoanDiscount = (int) $loan->discount_percent;

                if (intval($maxAmount) <= intval($defAmount) && !$product->isFixed) {
                    $maxAmount = $defAmount;
                }
            }

        }



        $defDiscountsOptions = $discounts;
        if (isset($defDiscountsOptions['adminDiscountLimit'])) {
            unset($defDiscountsOptions['adminDiscountLimit']);
        }
        $defaultDiscountSetting = $this->getMaxDiscount($defDiscountsOptions) ?? 0;

        /// check for active client discounts
        $isCurrentLoan = false;
        if ($loanId && $loanProductId === $product->getKey()) {
            $defaultDiscountSetting = $this->getFromTempSliderData(
                $productId, 'discount_percent', $currentLoanDiscount ?? 0, $loanId
            );
            $isCurrentLoan = true;
        }

        /// if client discount larger than admin
        /// set client discount to max-discount
        // if ($defaultDiscountSetting > $maxDiscount) {
        //     $maxDiscount = $defaultDiscountSetting;
        // } elseif (!$isCurrentLoan && ($discounts['discountByClient'] || $discounts['discountByPhone'])) {
        //     $defaultDiscountSetting = $maxDiscount;
        // }

        $sessionKey=null;
        if($loanId){
            $sessionKey = Loan::getTempLoanDataKey($loanId);
        }

        $tempData = Session::get($sessionKey, []);

        $defaultAmount = $this->getFromTempSliderData($productId, 'loan_sum', $defAmount, $loanId);
        $defaultPeriod = $this->getFromTempSliderData($productId, 'loan_period', $defPeriod, $loanId);

        /// check if has saved temp data
        if ($sessionKey && Session::has($sessionKey) && $tempData['loan']['product_id'] == $productId) {
            $data = Session::get($sessionKey);
            $defaultAmount = intval($data['loan']['loan_sum']);
            $defaultPeriod = $data['loan']['loan_period'];
            $defaultDiscountSetting = $data['loan']['discount_percent'] ?? 0;
        }



        return [
            'refAmount' => $refAmount,
            'isJuridical' => $product->legal_status,
            'amount' => [
                'default' => $defaultAmount,
                'start' => (int)$minAmount,
                'max' => (int)$maxAmount,
                'label' => 'лв',
                'step' => (int)$amountStep,
            ],
            'period' => [
                'default' => (int)$defaultPeriod,
                'start' => (int)$minPeriod,
                'max' => (int)$maxPeriod,
                'label' => ' ' . $product->getPeriodLabel($minPeriod),
            ],
            'discount' => [
                'default' => $defaultDiscountSetting,
                'start' => 0,
                'max' => $maxDiscount,
                'label' => ' %',
            ]
        ];
    }


    protected function getFromTempSliderData(int $productId, string $key, mixed $default, ?int $loanId): mixed
    {
        /// if open from client card and have valid loan
        /// return loan default
        if ($loanId) {
            return $default;
        }

        $sliderOldData = Session::get("tempSliderData");
        if (empty($sliderOldData['product_id'])) {
            return $default;
        }

        $sessionProductId = (int) $sliderOldData['product_id'];
        if ($sessionProductId !== $productId) {
            return $default;
        }

        if ($key === 'loan_sum' && Session::has("tempSliderData.{$key}")) {
            return Session::get("tempSliderData.{$key}") / 100;
        }

        return Session::get("tempSliderData.{$key}", $default);
    }

    /**
     * @param Client|null $client
     * @param int $productId
     * @param string|null $phone
     * @return array{discountByPhone: int, discountByClient: int, adminDiscountLimit: int}
     */
    private function getDiscounts(?Client $client, int $productId, ?string $phone = null): array
    {
        /**
         * @var Administrator $administrator
         */
        $administrator = auth()->user();

        $discountByPhone = 0;
        $discountByClient = 0;
        $adminDiscountLimit = $administrator->getAdminMaxDiscount();

        if ($client || $phone) {
            if (!$phone) {
                $phone = $client->phone;
            }

            $discountByPhone = (int) $this->discountByPhoneRepository->getPercentByPhoneAndProduct($phone, $productId);
            $discountByClient = (int) $client?->getDiscountForProduct($productId)?->percent;
        }

        return compact('discountByPhone', 'discountByClient', 'adminDiscountLimit');
    }

    private function getMaxDiscount(array $discounts): int
    {
        return max($discounts);
    }
}
