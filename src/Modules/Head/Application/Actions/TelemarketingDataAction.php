<?php

namespace Modules\Head\Application\Actions;

use Mo<PERSON>les\Head\FilterForms\TelemarketingFilterForm;
use Modules\Head\Repositories\LoanRepository;

readonly class TelemarketingDataAction
{
    public function __construct(
        private LoanRepository $loanRepository
    ) {
    }

    public function execute(array $filters, int $perPage = 10): array
    {
        $rows = $this->loanRepository->requestsBuilder($filters)->paginate($perPage);

        return [
            'filterForm' => TelemarketingFilterForm::create(),
            'rows' => $rows
        ];
    }
}