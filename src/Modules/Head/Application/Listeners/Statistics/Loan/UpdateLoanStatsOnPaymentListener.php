<?php

namespace Modules\Head\Application\Listeners\Statistics\Loan;

use Modules\Head\Domain\LoanStats;
use Modules\Payments\Domain\Events\LoanPaymentWasReceivedForStats;

class UpdateLoanStatsOnPaymentListener
{
    public function __construct(private LoanStats $loanStats){}

    public function handle(LoanPaymentWasReceivedForStats $event): void {

        if (!empty($event->payment->payment_id)) {
            $this->loanStats
                ->buildFromLoan($event->loan)

                ->setPayment($event->payment ?? null)
                ->setHasPayment(1)

                ->setAllDatesAtOnce()

                ->setFirstPaymentReceivedAt()
                ->setLastPaidDate()

                ->setCartonAmounts() // amounts + current overdue days + current overdue amount

                ->setDaysInUse()
                ->setDaysAmended()

                ->save();
        } else { // used when we add/reduce fees/dues
            $this->loanStats
                ->buildFromLoan($event->loan)

                ->setCartonAmounts() // amounts + current overdue days + current overdue amount

                ->save();
        }

    }
}
