<?php

namespace Modules\Head\Application\Listeners\Statistics\Loan;

use Modules\Head\Domain\LoanStats;
use Modules\Payments\Domain\Events\LoanWasRepaid;

class UpdateLoanStatsOnRepaymentListener
{
    public function __construct(private LoanStats $loanStats){}

    public function handle(LoanWasRepaid $event): void {

        $this->loanStats
            ->buildFromLoan($event->loan)
            ->setLastPaidDate()
            ->setRealRepaymentDate()
            ->setRepaymentDaysValue()
            ->save();
    }
}
