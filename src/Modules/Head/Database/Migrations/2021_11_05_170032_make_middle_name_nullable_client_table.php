<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientName;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table(Client::getTableName(), function (Blueprint $table) {
            $table->string('middle_name')->nullable()->change();
        });

        Schema::table(ClientName::getTableName(), function (Blueprint $table) {
            $table->string('middle_name')->nullable()->change();
        });
    }
};
