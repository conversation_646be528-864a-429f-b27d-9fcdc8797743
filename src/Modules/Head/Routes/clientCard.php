<?php

use Illuminate\Support\Facades\Route;
use Modules\Head\Http\Controllers\ClientCard\A4eReportsController;
use Modules\Head\Http\Controllers\ClientCard\CcrReportHistoryController;
use Modules\Head\Http\Controllers\ClientCard\ClientCardClientTaskController;
use Modules\Head\Http\Controllers\ClientCard\ClientCardDocumentsController;
use Modules\Head\Http\Controllers\ClientCard\ClientCardLoanTaskController;
use Modules\Head\Http\Controllers\ClientCard\ClientCommunicationController;
use Modules\Head\Http\Controllers\ClientCard\ClientPaymentScheduleController;
use Modules\Head\Http\Controllers\ClientCard\ClientPaymentsController;
use Modules\Head\Http\Controllers\ClientCard\ClientWithLoanController;
use Modules\Head\Http\Controllers\ClientCard\ClientWithoutLoanController;
use Modules\Head\Http\Controllers\ClientCard\CollectorTabController;
use Modules\Head\Http\Controllers\ClientCard\CompanyEditDataController;
use Modules\Head\Http\Controllers\ClientCard\JudicialTabController;
use Modules\Head\Http\Controllers\ClientCard\LetterNotificationController;
use Modules\Head\Http\Controllers\ClientCard\MvrReportsController;
use Modules\Head\Http\Controllers\ClientCard\NoiReportsController;
use Modules\Head\Http\Controllers\ClientCard\PrevRequestsController;
use Modules\Head\Http\Controllers\ClientCard\RepresentorEditDataController;
use Modules\Head\Http\Controllers\ClientCard\SystemLogController;
use Modules\Head\Http\Controllers\ClientCard\UpdatePaymentScheduleController;

Route::prefix('head')->group(function () {
    $idPattern = '[1-9][0-9]{0,5}';

    Route::post('/clientCard/{client}/update-data', [CompanyEditDataController::class, 'storeCompanyData'])
        ->name('head.storeCompanyData')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Customer Data')
        ->defaults('description', 'Update company data');

    Route::post(
        '/clientCard/{representor?}/representor-update-data',
        [RepresentorEditDataController::class, 'storeRepresentorData']
    )
        ->name('head.storeRepresentorData')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Customer Data')
        ->defaults('description', 'Update company representative data');

    Route::get('/clientCard/docs-attorney-pdf', [LetterNotificationController::class, 'docsAttorneyPdf'])
        ->name('head.docsAttorneyPdf')
        ->defaults('description', 'Generate debit card documents')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Documents')
        ->defaults('info_bubble', 'Може да създава документи за Дог. поръчка за дебитна карта');

    Route::get('/clientCard/{loan}/remaining-pdf', [LetterNotificationController::class, 'remainingPdf'])
        ->name('head.letter-notification.remainingPdf')
        ->defaults('description', 'Generate letter remaining obligation')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Documents')
        ->defaults('info_bubble', 'Може да създава писмо за оставащо задължение');

    Route::get('/clientCard/{loan}/remaining-email', [LetterNotificationController::class, 'remainingEmail'])
        ->name('head.letter-notification.remainingEmail')
        ->defaults('description', 'Send email remaning obligation')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Documents')
        ->defaults('info_bubble', 'Може да изпраща по емейл писмо за оставащо задължение');

    Route::get('/clientCard/{loan}/paid-pdf', [LetterNotificationController::class, 'paidPdf'])
        ->name('head.letter-notification.paidPdf')
        ->defaults('description', 'Download letter no obligation')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Documents')
        ->defaults('info_bubble', 'Може да свали писмо Удостоврение за липсва на задължение');

    Route::get('/clientCard/{loan}/paid-email', [LetterNotificationController::class, 'paidEmail'])
        ->name('head.letter-notification.paidEmail')
        ->defaults('description', 'Send email no obligation')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Documents')
        ->defaults('info_bubble', 'Може да изпрати по емейл писмо Удостоверение за липсва на задължение');

    /// ClientCardTaskController
    Route::get('/clientCard/{clientId}/task/{task}/{taskId?}', [ClientCardClientTaskController::class, 'index'])
        ->name('head.client-card-task.index');

    /// ClientCardLoanTaskController
    Route::get('/clientCard/{clientId}/{loanId}/{task}/{taskId?}', [ClientCardLoanTaskController::class, 'index'])
        ->where('clientId', $idPattern)
        ->name('head.loan-task.index');

    /// ClientWithLoanController
    Route::get('/clientCard/{clientId}/{loanId}', [ClientWithLoanController::class, 'index'])
        ->name('head.client-with-loan.index')
        ->defaults('description', 'Customer with active loan view')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Card View')
        ->defaults('info_bubble', 'Вижда клиентска карта в изглед - има активен кредит');

    /// ClientWithoutLoanController
    Route::get('/clientCard/{clientId}', [ClientWithoutLoanController::class, 'index'])
        ->name('head.client-without-loan.index')
        ->defaults('description', 'Customer without active loan view')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Card View')
        ->defaults('info_bubble', 'Вижда клиентска карта - в изглед няма активен кредит');

    /// CcrReportHistoryController
    Route::get('/ccr-report-history', [CcrReportHistoryController::class, 'index'])
        ->name('head.ccr-report-history.index')
        ->defaults('description', 'View tab Credit history')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Credit History')
        ->defaults('info_bubble', 'Вижда таб Кредитна история');

    Route::post('/ccr-report-history/create-new-ccr-report', [CcrReportHistoryController::class, 'createNewCcrReport'])
        ->name('head.ccr-report-history.create')
        ->defaults('description', 'Request new CCR report')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Credit History')
        ->defaults('info_bubble', 'Може да тегли нов репорт от ЦКР');

    /// NoiReportsController
    Route::get('/noi-reports', [NoiReportsController::class, 'index'])
        ->name('head.noi-reports.index')
        ->defaults('description', 'View tab Income')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Income')
        ->defaults('info_bubble', 'Вижда таб Доход в клиентска карта');

    Route::post('/noi-reports/create-new-noi-report', [NoiReportsController::class, 'createNewNoiReport'])
        ->name('head.noi-reports.create')
        ->defaults('description', 'Request new NOI report')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Income')
        ->defaults('info_bubble', 'Може да генерира нова справка от НОИ');

    /// MvrReportsController
    Route::get('/mvr-reports', [MvrReportsController::class, 'index'])
        ->name('head.mvr-reports.index')
        ->defaults('description', 'View MVR tab')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'MVR')
        ->defaults('info_bubble', 'Вижда таб МВР в клиентска карта');

    Route::post('/mvr-reports/create-new-mvr-report', [MvrReportsController::class, 'createNewMvrReport'])
        ->name('head.mvr-reports.create')
        ->defaults('description', 'Request new MVR report')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'MVR')
        ->defaults('info_bubble', 'Може да изтегли нов репорт от МВР');

    /// A4eReportsController
    Route::get('/a4e-reports', [A4eReportsController::class, 'index'])
        ->name('head.a4e-reports.index')
        ->defaults('description', 'View a4e reports in client card')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'A4E')
        ->defaults('info_bubble', 'Може да вижда репорти от а4е в клиентска карта таб А4Е.');

    Route::post('/a4e-reports/create', [A4eReportsController::class, 'createNewA4EReport'])
        ->name('head.a4e-reports.create')
        ->defaults('description', 'Request new a4e report')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'A4E')
        ->defaults('info_bubble', 'Може да поиска нов репорт за клиента от А4Е.');

    /// ClientCommunicationController
    Route::get('/client-communication', [ClientCommunicationController::class, 'index'])
        ->name('head.client-communication.index');

    Route::get('/client-communication/checkSettings', [ClientCommunicationController::class, 'checkSettings'])
        ->name('head.client-communication.checkSettings')
        ->defaults('description', 'Check communication settings for loan')
        ->defaults('module_name', 'Communication')
        ->defaults('controller_name', 'Check communication settings page')
        ->defaults('info_bubble', 'Вижда комуникационни настройки по поподаден кредит ИД');

    /// ClientPaymentsController
    Route::get('/client-payments', [ClientPaymentsController::class, 'index'])
        ->name('head.client-payments.index')
        ->defaults('description', 'View payments tab in client card')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Payments')
        ->defaults('info_bubble', 'Вижда таб Плащания в клиентска карта');

    /// ClientPaymentsController -> calendar
    Route::get('/client-payments-calendar-stats', [ClientPaymentScheduleController::class, 'calendarStats'])
        ->name('head.client-payments.calendarStats');

    /// UpdatePaymentScheduleController
    Route::get('/{loan}/update-payment-schedule', UpdatePaymentScheduleController::class)
        ->name('head.update-payment-schedule.update')
        ->defaults('description', 'Save repayment plan to selected date')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Repayment Schedule')
        ->defaults('info_bubble', 'Може да запази пог. план към избрана дата от таб Погасителен план');

    /// ClientPaymentScheduleController
    Route::get('/client-payment-schedule', [ClientPaymentScheduleController::class, 'index'])
        ->name('head.client-payment-schedule.index')
        ->defaults('description', 'View repayment schedule tab')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Repayment Schedule')
        ->defaults('info_bubble', 'Може да вижда таб Погасителен план от клиентска карта');

    Route::get('/client-payment-schedule/{loan}/edit', [ClientPaymentScheduleController::class, 'edit'])
        ->name('head.client-payment-schedule.edit')
        ->defaults('description', 'Edit repayment schedule')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Repayment Schedule')
        ->defaults('info_bubble', 'Може да редактира погасителния план на кредита от таб Погасителен план.');

    Route::post('/client-payment-schedule/{loan}', [ClientPaymentScheduleController::class, 'update'])
        ->name('head.client-payment-schedule.update')
        ->defaults('description', 'Update payment schedule');

    Route::get(
        '/client-payment-schedule/{loan}/generate-doc',
        [ClientPaymentScheduleController::class, 'generateNewPaymentScheduleDoc']
    )
        ->name('head.client-payment-schedule.generateNewPaymentScheduleDoc')
        ->defaults('description', 'Generate new repayment schedule document')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Repayment Schedule')
        ->defaults('info_bubble', 'Може да генерира нов документ Погасителен план от таб Погасителен план');

    Route::get('/client-payment-schedule/{loan}/skip-ccr', [ClientPaymentScheduleController::class, 'skipCcr'])
        ->name('head.client-payment-schedule.skipCcr')
        ->defaults('description', 'Exclude loan from CCR')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Repayment Schedule')
        ->defaults('info_bubble', 'Може да изключи кредит от ЦКР отчет');

    Route::get(
        '/client-payment-schedule/{loan}/mark-as-juridical',
        [ClientPaymentScheduleController::class, 'markAsJuridical']
    )
        ->name('head.client-payment-schedule.markAsJuridical')
        ->defaults('description', 'Mark loan as juridical')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Repayment Schedule')
        ->defaults('info_bubble', 'Може да маркира кредит като съдебен');

    /// SystemLogController
    Route::get('/system-log', [SystemLogController::class, 'index'])
        ->name('head.system-log.index');

    /// PrevRequestsController
    Route::get('/prev-requests', [PrevRequestsController::class, 'index'])
        ->name('head.prev-requests.index')
        ->defaults('description', 'View previous applications tab')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'General')
        ->defaults('info_bubble', 'Вижда таб Предишни заявки');

    /// PrevRequestsController
    Route::get('/client-documents/{clientId}/{loanId?}', [ClientCardDocumentsController::class, 'index'])
        ->name('head.client-documents.index');
        // ->defaults('description', 'View documents tab')
        // ->defaults('module_name', 'Client Card')
        // ->defaults('controller_name', 'Documents')
        // ->defaults('info_bubble', 'Вижда таб Документи в клиентска карта');

    /// Judicial tab
    Route::get('/judicial-tab/{clientId}/{loanId?}', [JudicialTabController::class, 'index'])
        ->name('head.judicial-tab.index')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Judicial')
        ->defaults('description', 'View juridical tab');

    Route::get('/judicial-tab/process/{clientId}/{loanId?}', [JudicialTabController::class, 'process'])
        ->name('head.judicial-tab.process');
});

