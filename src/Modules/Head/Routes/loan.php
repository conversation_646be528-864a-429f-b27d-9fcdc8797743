<?php

use Illuminate\Support\Facades\Route;
use <PERSON><PERSON><PERSON>\Head\Http\Controllers\ClientCard\UpdateLoanController;
use <PERSON><PERSON><PERSON>\Head\Http\Controllers\DiscountedLoansController;
use <PERSON><PERSON><PERSON>\Head\Http\Controllers\LoanController;
use <PERSON><PERSON><PERSON>\Head\Http\Controllers\LoansApproveController;
use Mo<PERSON><PERSON>\Head\Http\Controllers\PaymentSchedulePlanController;
use <PERSON><PERSON><PERSON>\Head\Http\Controllers\CloseJuridicalCaseController;

Route::prefix('head')->group(function () {
    $idPattern = '[1-9][0-9]{0,9}';

    Route::get('/close-juridical-case/{loan}', [CloseJuridicalCaseController::class, 'closeJuridicalCase'])
        ->name('head.close-juridical-case')
        ->defaults('description', 'View closed case button')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Repayment Schedule')
        ->defaults('info_bubble', 'Вижда бутон Приключено дело в таб Погасителен план');

    Route::get('/written-off-loan/{loan}', [CloseJuridicalCaseController::class, 'writtenOffLoan'])
        ->name('head.written-off-loan')
        ->defaults('description', 'View written-off button')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Repayment Schedule')
        ->defaults('info_bubble', 'Вижда бутон Отписан кредит в таб Погасителен план');

    /// LoansApproveController
    Route::get('/loans-approve/export', [LoansApproveController::class, 'export'])
        ->name('head.approveLoans.export')
        ->defaults('description', 'Export task list')
        ->defaults('module_name', 'Approve')
        ->defaults('controller_name', 'Tasks')
        ->defaults('info_bubble', 'Може да експортне задачи за одобрение');

    Route::get('/loans-approve', [LoansApproveController::class, 'list'])
        ->name('head.approveLoans.list')
        ->defaults('description', 'View tasks page')
        ->defaults('module_name', 'Approve')
        ->defaults('controller_name', 'Tasks')
        ->defaults('info_bubble', 'Вижда страница Задачи за одобрение');

    Route::get('/loans/refresh-approved', [LoansApproveController::class, 'refreshApproved'])
        ->name('head.approveLoans.refreshApproved');

    Route::get('/loans/activate/{loan}', [LoanController::class, 'activate'])
        ->name('head.loans.activate');

    Route::get('/loans/export/csv', [LoanController::class, 'exportCsv'])
        ->name('head.loans.export.csv')
        ->defaults('module_name', 'User Settings')
        ->defaults('controller_name', 'Loans Page')
        ->defaults('description', 'Export data to csv');

    Route::get('/loans/export/xlsx', [LoanController::class, 'exportXlsx'])
        ->name('head.loans.export.xlsx')
        ->defaults('module_name', 'User Settings')
        ->defaults('controller_name', 'Loans Page')
        ->defaults('description', 'Export data to xlsx');

    Route::get('/loans', [LoanController::class, 'list'])
        ->name('head.loans.list')
        ->defaults('description', 'View loans page')
        ->defaults('module_name', 'User Settings')
        ->defaults('controller_name', 'Loans Page')
        ->defaults('info_bubble', 'Вижда страница Кредити');

    Route::post('/loans/{loanId}/cancel-active-loan', [LoanController::class, 'cancelActiveLoan'])
        ->name('head.loans.cancel-active-loan')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Repayment Schedule')
        ->defaults('description', 'Cancel loan button');

    Route::post('/loans/{loanId}/remove-from-outer-collector', [LoanController::class, 'removeFromOuterCollector'])
        ->name('head.loans.remove-from-outer-collector')
        ->defaults('module_name', 'Client Card')
        ->defaults('controller_name', 'Repayment Schedule')
        ->defaults('description', 'Remove from external collection button');

    // used for migration
    Route::get('/loans/counters', [LoanController::class, 'counters'])
        ->name('head.loans.counters');

    // loans session
    Route::get('/loans/filters', [LoanController::class, 'getFilters'])
        ->name('head.loans.getFilters')
        ->defaults('description', 'Get loans filters');
    Route::put('/loans/filters', [LoanController::class, 'setFilters'])
        ->name('head.loans.setFilters')
        ->defaults('description', 'Update loans filters');
    Route::delete('/loans/filters', [LoanController::class, 'cleanFilters'])
        ->name('head.loans.cleanFilters')
        ->defaults('description', 'Cleanup filters');

    Route::get('/loans/payment-schedule-plan-preview', [PaymentSchedulePlanController::class, 'preview'])
        ->name('head.loans.showPreliminaryPaymentPlan');

    Route::post('/loan/update-loan-consultant', [UpdateLoanController::class, 'updateLoanConsultant'])
        ->name('head.loan.update-loan-consultant')
        ->defaults('description', 'Update loan admin');

    // Discounted Loans
    Route::get('/loans/discounted', [DiscountedLoansController::class, 'index'])
        ->name('head.loans.discounted')
        ->defaults('module_name', 'Reports')
        ->defaults('controller_name', 'Applications with Discount')
        ->defaults('description', 'View page');

    Route::get('/loans/discounted/export', [DiscountedLoansController::class, 'export'])
        ->name('head.loans.discounted.export')
        ->defaults('module_name', 'Reports')
        ->defaults('controller_name', 'Applications with Discount')
        ->defaults('description', 'Export data');
});
