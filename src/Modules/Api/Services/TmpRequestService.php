<?php

declare(strict_types=1);

namespace Modules\Api\Services;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Collection;
use Modules\Common\Models\Client;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\TmpRequest;

final readonly class TmpRequestService
{
    public function __construct(private TmpRequestHistoryService $historyService)
    {
    }

    /**
     * We need to first get the model to delete and move it to history through the delete event
     * https://laravel.com/docs/9.x/eloquent#events
     */
    public function deleteById(
        int $id,
        ?int $closeReasonId = null,
        ?CarbonInterface $closedAt = null,
        ?int $closedBy = null,
    ): bool|null {
        $model = TmpRequest::find($id);

        if ($model) {
            if ($closeReasonId || $closedAt || $closedBy) {
                $this->historyService->insertFromArray(
                    $model->getAttributes(),
                    $closedAt,
                    $closedBy,
                    $closeReasonId,
                );

                return $this->deleteQuietly($model);
            }

            return $this->delete($model);
        }

        return null;
    }

    /**
     * @warning Method is unsafe - There may be a model that is partially empty of data
     */
    public function delete(
        TmpRequest $model,
        ?int $closeReasonId = null,
        ?CarbonInterface $closedAt = null,
        ?int $closedBy = null,
    ): bool|null {
        if ($closeReasonId || $closedAt || $closedBy) {
            $this->historyService->insertFromArray(
                $model->getAttributes(),
                $closedAt,
                $closedBy,
                $closeReasonId,
            );

            return $this->deleteQuietly($model);
        }

        return $model->delete();
    }

    private function deleteQuietly(TmpRequest $model): bool|null
    {
        return $model->deleteQuietly();
    }

    public function deleteByClientIfHasActiveLoan(Client $client): void
    {
        if ($client->loans()->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID)->exists()) {
            $this->deleteByClient($client);
        }
    }

    /**
     * It is necessary to delete data sequentially so that deletion events occur and the data is moved to history
     */
    public function deleteByClient(Client $client): void
    {
        TmpRequest::where('phone', $client->phone)->chunkById(100, function (Collection $models) {
            $models->each(function (TmpRequest $model) {
                $this->delete($model);
            });
        });
    }

    /**
     * It is necessary to delete data sequentially so that deletion events occur and the data is moved to history
     */
    public function deleteByClientBeforeToday(Client $client): void
    {
        TmpRequest::where('phone', $client->phone)
            ->where('created_at', '<', Carbon::now()->startOfDay())
            ->chunkById(100, function (Collection $models) {
                $models->each(function (TmpRequest $model) {
                    $this->delete($model);
                });
            });
    }
}
