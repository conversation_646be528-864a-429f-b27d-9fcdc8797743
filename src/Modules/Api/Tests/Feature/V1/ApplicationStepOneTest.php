<?php

namespace Modules\Api\Tests\Feature\V1;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Modules\Api\Database\Seeders\Test\TmpRequestStepOneSeeder;
use Modules\Common\Database\Seeders\Test\ClientSeeder;
use Tests\TestCase;

class ApplicationStepOneTest extends TestCase
{
    use DatabaseTransactions;
    private array $headers = ['Token' => 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3'];
    private string $registerRequestUrl = '/api/v1/register-request';
    private array $registerRequestData = [
        "ip" => "**************",
        "browser" => "xxxx",
        "product_id" => 1,
        "amount_requested" => 100000,
        "period_requested" => 1,
        "phone" => "0897771897",
        "session_id" => 111,
        "agreements" => '[]',
        "last_page_accessed" => "http://lendivo.bg/get-test"
    ];

    private array $registerRequestExpectedResponse = [
        'success' => true,
        'response'=>[
            'request_id'=>0,
            'data'=>[
                'totalAmount' => 1000,
                'period' => 1,
                'periodLabel' => 'ден',
                'installmentAmount' => 1000,
                'interest' => 100,
                'paid_interest' => 0,
                'penalty' => 670,
                'paid_penalty' => 0,
                'installmentsCount' => 1,
                'totalIncreasedAmount' => 7.70,
                'discounts' => [],
                'product_id' => 1,
                'gpr' => 42.58
            ]
        ]
    ];

    public function testRegisterRequestHappyPath()
    {
        $response = $this->withHeaders($this->headers)
            ->postJson($this->registerRequestUrl, $this->registerRequestData);
        $expectedResponse = $this->registerRequestExpectedResponse;
        $expectedResponse['response']['request_id'] = (int)DB::getPdo()->lastInsertId();
        $response->assertStatus(200)->assertJson($expectedResponse);
    }

    public function testRegisterRequestExistingTmpRequest()
    {
        $this->seed(TmpRequestStepOneSeeder::class);
        $response = $this->withHeaders($this->headers)
            ->postJson($this->registerRequestUrl, $this->registerRequestData);
        $expectedResponse = $this->registerRequestExpectedResponse;
        $expectedResponse['response']['request_id'] = 9;
        $response->assertStatus(200)->assertJson($expectedResponse);
    }

    public function testRegisterRequestExistingClient()
    {
        $this->seed(ClientSeeder::class);
        $data = $this->registerRequestData;
        $data['phone'] = "0896667788";
        $response = $this->withHeaders($this->headers)
            ->postJson($this->registerRequestUrl, $data);
        $expectedResponse = [
            'success' => true,
            'response' => [
                'data' => [
                        'penalty' => 670,
                        'paid_penalty' => 0,
                        'installmentsCount' => 1,
                        'totalIncreasedAmount' => '7.70',
                        'discounts' =>[],
                        'paid_interest' => 0,
                        'interest' => 100,
                        'installmentAmount' => 1000,
                        'totalAmount' => 1000,
                        'period' => 1,
                        'product_id' => 1,
                        'gpr' => 42.58,
                        'periodLabel' => 'ден',
                    ],
                ],
        ];
        //it was disallowed before to register here, roman changed it
        $response->assertStatus(200)->assertJson($expectedResponse);
    }
}