<?php

namespace Modules\Api\Tests\Feature\V1\Auth;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Common\Database\Seeders\Test\ClientRelationsSeeder;
use Modules\Common\Database\Seeders\Test\NewPayday30LoanSeeder;
use Modules\Common\Models\Client;
use Modules\Common\Models\FileStorage;
use Tests\TestCase;

class GetClientFileTest extends TestCase
{
    use DatabaseTransactions;
    private array $headers = ['Token' => 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3'];
    private string $url = '/api/v1/get-client-file';
    private array $requestData = [
        "client_id" => 1,
        "file_id" => 1,
        "ip" => '**************',
        "browser" => 'xxxx',
    ];

    public function testGetFileHappyPath()
    {
        $this->seed(NewPayday30LoanSeeder::class);
        /** @var Client $client */
        $client = Client::find(1);
        $token = $client->createToken(Client::LOGIN_TOKEN_KEY, expiresAt: Carbon::now()->addMinutes(60))->plainTextToken;
        $this->headers['Authorization'] = 'Bearer '.$token;
        DB::table('file')->insert(
            [
                'file_id' => 1,
                'file_storage_id' => FileStorage::FILE_STORAGE_HARD_DISC_ONE_ID,
                'file_type_id' => 1,
                'hash' => 'ducimus',
                'file_path' => 'images/clients/',
                'file_size' => 12,
                'file_type' => 'jpg',
                'file_name' => 'testImage',
                'created_at' => now(),
                'created_by' => 1,
            ]
        );
        DB::table('document')->insert([
            'client_id' => NewPayday30LoanSeeder::CLIENT_ID,
            'loan_id' => NewPayday30LoanSeeder::LOAN_ID,
            'file_id' => 1,
            'document_template_id'=> 11,
            'content' => 'test',
            'variables' => '[]',
            'created_at' => now(),
        ]);
        if (!is_dir('/var/www/storage/images/clients/')) {
            mkdir('/var/www/storage/images/clients/', 0777, true);
        }
        file_put_contents('/var/www/storage/images/clients/testImage', 'string: base64');
        $response = $this->withHeaders($this->headers)->postJson($this->url, $this->requestData);
        $response->assertStatus(200)->assertJson([
            'success'=>true,
            'response' => base64_encode('string: base64')
        ]);
    }
}