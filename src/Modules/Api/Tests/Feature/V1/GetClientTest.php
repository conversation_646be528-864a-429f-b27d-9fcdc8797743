<?php

namespace Modules\Api\Tests\Feature\V1;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Carbon;
use Modules\Common\Database\Seeders\Test\ClientRelationsSeeder;
use Modules\Common\Database\Seeders\Test\NewPayday30LoanSeeder;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Models\Client;
use Tests\TestCase;

class GetClientTest extends TestCase
{
    use DatabaseTransactions;

    private array $headers = ['Token' => 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3'];
    private string $url = '/api/v1/get-client/1';
    private array $expectedResponse = [
        'client_id' => 1,
        'pin' => '5609100932',
        'idcard_number' => '9104188750',
        'first_name' => 'Калоян',
        'middle_name' => 'Патлеев',
        'last_name' => 'Илиев',
        'phone' => '0896667788',
        'email' => '<EMAIL>',
        'new' => 1,
        'dead' => 0,
        'type' => 'real',
        'legal_status' => 'individual',
        'citizenship_type' => 'local',
        'idcard' =>
            [
                'client_idcard_id' => 1,
                'client_id' => 1,
                'city_id' => 1,
                'idcard_issued_id' => 1,
                'pin' => '9104188750',
                'idcard_number' => '9104188750',
                'issue_date' => '2011-03-09',
                'valid_date' => '2024-03-09',
                'address' => 'Soedinenie 5',
            ],
        'notifications' => [
            'marketing' => [
                'call' => ['value' => 1],
                'sms' => ['value' => 1],
                'email' => ['value' => 1],
                'viber' => ['value' => 1],
            ],
            'collect' => [
                'call' => ['value' => 1],
                'sms' => ['value' => 1],
                'email' => ['value' => 1],
                'viber' => ['value' => 1],
            ],
        ],
        'loan_data' =>
            [
                'loan_id' => 1,
                'client_id' => 1,
                'product_id' => 1,
                'product_type_id' => 1,
                'loan_type_id' => 1,
                'discount_percent' => 10.00,
                'amount_requested' => 10.00,
                'amount_approved' => 10.00,
                'installments_requested' => 30,
                'installments_approved' => 30,
                'currency_id' => 1,
                'period_requested' => 30,
                'period_approved' => 30,
                'period_grace' => null,
                'loan_status_id' => 1,
                'last_status_update_administrator_id' => 1,
                'payment_method_id' => 1,
                'source' => LoanSourceEnum::CRM,
                'channel_id' => 1,
                'office_id' => 1,
                'administrator_id' => 1,
                'comment' => 'first default loan',
                'amount_rest' => 10.00,
                'interest_percent' => 0.10,
                'penalty_percent' => 0.10,
                'installment_modifier' => '+7 days',
                'installments' => [],
                'periodLabel' => 'дни',
                'actual_stats' => [
                    'loan_id' => 1,
                    'total_installments_count' => 1,
                ],
                'product' =>
                    [
                        'product_id' => 1,
                        'product_type_id' => 1,
                        'name' => 'До заплата Централа',
                        'trade_name' => 'До заплата',
                        'code' => 'CEN1',
                        'description' => 'Някакво описание',
                        'legal_status' => 'individual',
                    ],
            ],
    ];

    public function testHappyPath()
    {
        $this->seed(NewPayday30LoanSeeder::class);
        /** @var Client $client */
        $client = Client::find(1);
        $token = $client->createToken(Client::LOGIN_TOKEN_KEY, expiresAt: Carbon::now()->addMinutes(60))->plainTextToken;
        $this->headers['Authorization'] = 'Bearer '.$token;
        $response = $this->withHeaders($this->headers)->getJson($this->url);
        $response->assertStatus(200)->assertJson($this->expectedResponse);
    }

    public function testClientNotFound()
    {
        $this->seed(ClientRelationsSeeder::class);
        /** @var Client $client */
        $client = Client::find(1);
        $token = $client->createToken(Client::LOGIN_TOKEN_KEY, expiresAt: Carbon::now()->addMinutes(60))->plainTextToken;
        $this->headers['Authorization'] = 'Bearer '.$token;
        $response = $this->withHeaders($this->headers)->getJson('/api/v1/get-client/2');
        $response->assertStatus(400)->assertJson([
            'success' => false,
            'response' => [],
            'error' => 'Client with id 2 was not found'
        ]);
    }
}
