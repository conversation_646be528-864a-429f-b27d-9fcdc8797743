<?php

namespace Modules\Api\Tests\Feature\V1\Affiliate;

use Illuminate\Support\Str;
use Modules\Api\Tests\Feature\V1\BaseApiModuleTest;
use Modules\Common\Enums\AffiliateAttemptStatusEnum;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;

class ClientHasActiveLoanTest extends BaseApiModuleTest
{
    public function testWhenClientHasActiveLoan()
    {
        $response = $this->makePostApiRequest(route('api.registerAffiliateApplication'));
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'pin',
            'idcard_number',
            'email',
            'phone',
            'amount',
            'token',
            'affiliate',
        ]);

        $affiliate = $this->getAffiliate();

        // Randomly pick one
        $pin = Loan::where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID)->first()?->client?->pin;
        if (empty($pin)) {
            throw new \RuntimeException('Error in database no active loans');
        }

        $response = $this->makePostApiRequest(route('api.registerAffiliateApplication'), [
            "ip" => fake()->ipv4(),
            "browser" => fake()->userAgent(),
            "pin" => $pin,
            "idcard_number" => Str::substr($pin, 0, -1),
            "email" => fake()->email(),
            "phone" => fake()->numerify('0###0000000'),
            "amount" => '700.00',
            "token" => $affiliate->token,
            "affiliate" => $affiliate->name
        ]);
        $response->assertStatus(200);
        $response->assertJson([
            'status' => false,
            'loan_id' => null,
            'loan_status' => AffiliateAttemptStatusEnum::REJECTED->value,
        ]);

        $response->assertJsonStructure([
            'status',
            'loan_id',
            'loan_status',
        ]);
    }
}