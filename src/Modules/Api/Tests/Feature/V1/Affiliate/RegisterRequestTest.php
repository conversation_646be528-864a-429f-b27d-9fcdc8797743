<?php

namespace Modules\Api\Tests\Feature\V1\Affiliate;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Api\Tests\Feature\V1\BaseApiModuleTest;
use Modules\Common\Enums\AffiliateAttemptStatusEnum;

class RegisterRequestTest extends BaseApiModuleTest
{

    public function testRegisterRequest()
    {
        $response = $this->makePostApiRequest(route('api.registerAffiliateApplication'));
        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            'pin',
            'idcard_number',
            'email',
            'phone',
            'amount',
            'token',
            'affiliate',
        ]);

        $affiliate = $this->getAffiliate();

        // Get used PINs from the database
        $usedPins = DB::table('client')->pluck('pin')->toArray();
        // Get unused PINs from config
        $availablePins = array_diff(config('api.fake_pins'), $usedPins);

        // Randomly pick one
        $pin = fake()->randomElement($availablePins);
        if (empty($pin)) {
            $this->fail(
                'No available test PINs left in config(api.fake_pins) that aren’t used in DB. Please run php artisan migrate:fresh --seed'
            );

            return;
        }

        $response = $this->makePostApiRequest(route('api.registerAffiliateApplication'), [
            "ip" => fake()->ipv4(),
//            "browser" => fake()->userAgent(),
            "pin" => $pin,
            "idcard_number" => Str::substr($pin, 0, -1),
            "email" => fake()->email(),
            "phone" => fake()->numerify('0###0000000'),
            "amount" => '700.00',
            "token" => $affiliate->token,
            "affiliate" => $affiliate->name
        ]);
        $response->assertStatus(200);
        $response->assertJson([
            'status' => true,
            'loan_status' => AffiliateAttemptStatusEnum::RECEIVED->value,
        ]);

        $response->assertJsonStructure([
            'status',
            'loan_id',
            'loan_status',
        ]);
    }
}