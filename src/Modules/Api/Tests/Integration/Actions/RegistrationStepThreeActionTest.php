<?php

namespace Modules\Api\Tests\Integration\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Api\Application\Actions\RegistrationStepOneAction;
use Modules\Api\Application\Actions\RegistrationStepThreeAction;
use Modules\Api\Application\Actions\RegistrationStepTwoAction;
use Modules\Api\Domain\Entities\Agreements;
use Modules\Api\Domain\Entities\LoanRequest;
use Modules\Api\Domain\Entities\MvrReport as FakeReport;
use Modules\Api\Domain\Entities\Product;
use Modules\Api\Domain\Exceptions\MvrReportNotFound;
use Modules\Api\Http\Dto\TmpCreateDto;
use Modules\Api\Http\Dto\TmpFinalizeRequestDto;
use Modules\Api\Http\Dto\TmpUpdateDto;
use Modules\Api\Repositories\TmpRequestRepository;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Models\Client;
use Modules\Common\Models\MvrReport;
use Modules\Common\Models\TmpRequest;
use Modules\Head\Repositories\ClientRepository;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\ThirdParty\Services\MvrService;
use StikCredit\Calculators\LoanCalculator;
use Tests\TestCase;

class RegistrationStepThreeActionTest extends TestCase
{
    use DatabaseTransactions;

    public function testUpdateRequestHappyPath()
    {
        $firstStepDto = TmpCreateDto::from([
            'product_id' => 1,
            'amount_requested' => 60000,
            'period_requested' => 30,
            'phone' => '',
            'session_id' => '',
            'agreements' => ["personal_data"=>"on"],
            'last_page_accessed' => "/nova-zaqvka",
            'ip' => "**********",
            'browser' => '',
        ]);
        $requestId = app(RegistrationStepOneAction::class)->execute($firstStepDto)['request_id'];

        $secondStepDto = TmpUpdateDto::from([
            'request_id' => $requestId,
            'email' => "<EMAIL>",
            'pin' => "0343197228",
            'idcard_number' => "024603208",
            'payment_method_id' => 2,
            'iban' => 1,
            'ip' => "**********",
            'browser' => "Mozilla\/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko\/20100101 Firefox\/118.0",
        ]);

        $stepTwoAction = new RegistrationStepTwoAction(
            new LoanRequest(
                new TmpRequest(),
                app(TmpRequestRepository::class),
                app(ClientRepository::class),
                app(Product::class),
                new CurrentDate(),
                app(LoanCalculator::class),
                new FakeReport(
                    new MvrReport(),
                    $this->createMock(MvrService::class)
                ),
                app(Agreements::class),
            ),
            app(NewAppAction::class),
            app(ClientRepository::class),
        );

        $this->expectException(MvrReportNotFound::class);
        $stepTwoAction->execute($secondStepDto);

        $thirdStepDto = TmpFinalizeRequestDto::from([
            "request_id" => $requestId,
            "first_name" => "agent",
            "middle_name" => "test",
            "last_name" => "test",
            "valid_date" => "2024-09-12",
            "city_id" => "2",
            "address" => "dfgdfg 4",
            "district" => "dfgdg",
            "issue_date" => "2014-09-12",
            "ip" => "**********",
            "browser" => "Mozilla\/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko\/20100101 Firefox\/118.0",
            "session_id" => "9N5WXhqKmIyTUkGs7YZzcCkIoZJsQhacK7Umcik1",
            "last_page_accessed" => "\/lichni-danni-full"
        ]);

        /** @var Client $client */
        list($client, $loan) = app(RegistrationStepThreeAction::class)->execute($thirdStepDto);
        $this->assertNotNull($loan?->getKey());
        $this->assertEquals(60000, $loan->amount_approved);
        $this->assertEquals('024603208', $client->clientIdCards->first()->idcard_number);

    }
}