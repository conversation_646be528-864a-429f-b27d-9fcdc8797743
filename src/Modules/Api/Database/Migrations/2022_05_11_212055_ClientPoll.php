<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;
    
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'client_poll',
            function ($table) {
                $table->bigIncrements('client_poll_id');
                $table->integer('client_id')->unsigned()->index();
                $table->integer('rate')->nullable()->index();
                $table->integer('loan_id')->nullable()->index();
                $table->integer('email_template_id')->index();
                $table->integer('email_id')->index();
                $table->string('hash', 255)->index();

                $table->tableCrudFields(false, true);
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('client_poll');
    }
};
