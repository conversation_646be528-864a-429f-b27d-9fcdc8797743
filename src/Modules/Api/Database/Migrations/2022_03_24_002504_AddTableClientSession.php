<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'client_session',
            function ($table) {
                $table->bigIncrements('id');
                $table->integer('client_id')->unsigned()->nullable()->index();
                $table->string('token')->nullable()->index();
                $table->timestamp('valid_till', 0)->nullable()->index();
                $table->tableCrudFields();
                $table->foreign('client_id')->references('client_id')->on('client');
            }
        );
    }

    public function down()
    {
        Schema::table(
            'client_session',
            function (Blueprint $table) {
                $table->dropForeign('client_session_client_id_foreign');
            }
        );

        Schema::dropIfExists('client_session');
    }
};
