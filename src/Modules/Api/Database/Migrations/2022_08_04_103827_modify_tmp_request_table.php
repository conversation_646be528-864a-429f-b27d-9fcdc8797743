<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->table(
            'tmp_request',
            function ($table) {
                $table->unsignedInteger('annual_percentage_rate')->nullable()->change();
                $table->unsignedInteger('payment_method_id')->nullable()->change();
                $table->string('email')->nullable()->change();
                $table->string('first_name')->nullable()->change();
                $table->string('middle_name')->nullable()->change();
                $table->string('last_name')->nullable()->change();
                $table->string('pin')->nullable()->change();
                $table->string('idcard_number')->nullable()->change();
                $table->date('idcard_issue_date')->nullable()->change();
                $table->date('idcard_valid_date')->nullable()->change();
                $table->unsignedInteger('city_id')->nullable()->change();
                $table->string('address')->nullable()->change();
                $table->string('details')->nullable()->change();
                $table->smallInteger('refinance')->nullable()->change();
                $table->string('last_page_accessed')->nullable()->change();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tmp_request');
    }
};
