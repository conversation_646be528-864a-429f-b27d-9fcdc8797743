<?php

namespace Modules\Api\Domain\Entities;

use Modules\Common\Models\Agreement;
use Modules\Common\Models\ClientAgreement;
use Modules\Common\Models\TmpRequest;
use Modules\Head\Repositories\ClientAgreementRepository as Repo;

class Agreements
{
    private array $clientAgreements;
    public function __construct(private Repo $repo){}

    public function addToTmpRequest(int $tmpRequestId, array $agreements): self
    {
        $this->clientAgreements = [];
        foreach ($agreements as $agrName => $attValue) {
            $aId = Agreement::LIST[$agrName];
            $clientAgreement = $this->repo->getByAgreementIdAndTempRequestId($aId, $tmpRequestId);
            if(! $clientAgreement) {
                $clientAgreement = new ClientAgreement();
                $clientAgreement->agreement_id = $aId;
                $clientAgreement->tmp_request_id = $tmpRequestId;
            }
            $clientAgreement->value = in_array($attValue, ['on', '1']) ? 1 : 0;
            if($clientAgreement->save()){
                $this->clientAgreements[] = $clientAgreement;
            }
        }
        return $this;
    }

    public function clientAgreements(): array
    {
        return $this->clientAgreements;
    }

    public function finalize(TmpRequest $tmpRequest): self
    {
        //TODO: Why is only one agreement is being updated and not all of them?
        $ca = $this->repo->getByAgreementIdAndTempRequestId(
            Agreement::AGR_ID_PERSONAL_DATA,
            $tmpRequest->getKey()
        );

        if (!$ca) {
            return $this;
        }

        $ca->client_id = $tmpRequest->client_id;
        $ca->loan_id = $tmpRequest->loan_id;
        $ca->tmp_request_id = null;
        $this->repo->save($ca);

        return $this;
    }
}