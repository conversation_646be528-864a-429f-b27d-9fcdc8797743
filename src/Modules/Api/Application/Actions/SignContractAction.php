<?php

namespace Modules\Api\Application\Actions;

use Modules\Admin\Repositories\OfficeRepository;
use Modules\Api\Application\Traits\ProfileTokenValidationTrait;
use Modules\Api\Domain\Exceptions\ClientRequestHasAlreadyBeenProcessed;
use Modules\Api\Domain\Exceptions\LoanNotFoundById;
use Modules\Api\Http\Dto\SignContractDto;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Http\Dto\Dto;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanIp;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Services\ClientBankAccountService;
use Modules\Sales\Application\Actions\SignLoanAction;

readonly class SignContractAction implements ApiActionInterface
{
    use ProfileTokenValidationTrait;

    public function __construct(private SignLoanAction $signLoanAction)
    {
    }

    public function execute(SignContractDto|Dto $dto): array
    {
        /** @var Loan $loan */
        $loan = Loan::get($dto->loan_id);
        if (!$loan) {
            throw new LoanNotFoundById($dto->loan_id);
        }

        $this->validateClientToken($loan->client_id);

        if ($loan->loan_status_id !== LoanStatus::NEW_STATUS_ID) {
            throw new ClientRequestHasAlreadyBeenProcessed();
        }

        /// by default we set payment method easy pay
        /// if payment method not easy pay we need to change it
        if ($dto->source === LoanSourceEnum::AFFILIATE->value) {
            $bank_account_id = app(OfficeRepository::class)->getMainPaymentAccountId($dto->payment_method_id);

            $loan->setAttribute('bank_account_id', $bank_account_id);
            $loan->setAttribute('payment_method_id', $dto->payment_method_id);
            $loan->saveQuietly();

            if ($dto->payment_method_id === PaymentMethod::PAYMENT_METHOD_BANK) {
                $clientBankAccountService = app(ClientBankAccountService::class);

                /// create client bank account and set relation to loan
                $clientBankAccount = $clientBankAccountService->create($loan->client, $dto->iban);
                $clientBankAccountService->addRelationToLoan($clientBankAccount, $loan);
            }
        }

        $loan->refresh();
        $this->signLoanAction->execute($loan, $dto);

        $client = $loan->client;

        /// by default false;
        $hasVerifAction = false;

        /// if we has client check by client
        if(!empty($client->client_id)){
            $hasVerifAction = $client->hasVeriffAction();
        }

        $data = [
            'ip' => $dto->ip,
            'browser' => $dto->browser,
            'loan_status_id' => $loan->loan_status_id,
            'loan_id' => $loan->getKey(),
            'new_client' => $client->new,
            'has_action_verif' => $hasVerifAction,
        ];

        LoanIp::create($data);

        return $data;
    }
}
