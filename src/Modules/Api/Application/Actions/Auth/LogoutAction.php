<?php

namespace Modules\Api\Application\Actions\Auth;

use Illuminate\Support\Facades\Auth;
use Modules\Api\Application\Actions\ApiActionInterface;
use Modules\Api\Database\Entities\ClientSession;
use Modules\Api\Http\Dto\AuthDto;
use Modules\Common\Http\Dto\Dto;

readonly class LogoutAction implements ApiActionInterface
{
    public function execute(AuthDto|Dto $dto): array
    {
        $token = '';
        return ['token' => $token];
    }
}