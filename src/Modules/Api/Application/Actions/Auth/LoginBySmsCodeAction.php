<?php

namespace Modules\Api\Application\Actions\Auth;

use Carbon\Carbon;
use Modules\Api\Application\Actions\ApiActionInterface;
use Modules\Api\Database\Entities\ClientSession;
use Modules\Api\Domain\Exceptions\ClientNotFoundById;
use Modules\Api\Domain\Exceptions\Login\SmsCodeBelongsToDifferentClient;
use Modules\Api\Domain\Exceptions\Login\SmsCodeIsUsedOrExpired;
use Modules\Api\Domain\Exceptions\Login\SmsCodeNotFoundByCode;
use Modules\Api\Domain\Exceptions\Login\SmsCodeWasNotSaved;
use Modules\Api\Http\Dto\SmsCodeLoginDto;
use Modules\Api\Services\ApiService;
use Modules\Common\Http\Dto\Dto;
use Modules\Common\Models\Client;
use Modules\Common\Models\FailedLoginAttempt;
use Modules\Communication\Repositories\SmsLoginCodeRepository;

readonly class LoginBySmsCodeAction implements ApiActionInterface
{
    public function __construct(
        private SmsLoginCodeRepository $smsLoginCodeRepository
    ) {}

    public function execute(SmsCodeLoginDto|Dto $dto): array
    {
        /** @var Client $client */
        $client = Client::find($dto->client_id);
        if (!$client) {
            throw new ClientNotFoundById($dto->client_id);
        }

        $loginSms = $this->smsLoginCodeRepository->getUnusedByCode($dto->code);
        if (!$loginSms) {
            $exception = new SmsCodeNotFoundByCode($dto->code);
            FailedLoginAttempt::create([
                'login_ip' => $dto->ip,
                'client_id' => $dto->client_id,
                'login_code' => $dto->code,
                'message' => $exception->getMessage()
            ]);

            throw $exception;
        }

        if (!$this->smsLoginCodeRepository->isValid($loginSms)) {
            $exception = new SmsCodeIsUsedOrExpired($dto->code);

            FailedLoginAttempt::create([
                'login_ip' => $dto->ip,
                'client_id' => $dto->client_id,
                'login_code' => $dto->code,
                'message' => $exception->getMessage()
            ]);
            throw $exception;
        }

        if ($loginSms->client_id !== $dto->client_id) {
            $exception = new SmsCodeBelongsToDifferentClient($dto->code, $loginSms->client_id);

            FailedLoginAttempt::create([
                'login_ip' => $dto->ip,
                'client_id' => $dto->client_id,
                'login_code' => $dto->code,
                'message' => $exception->getMessage()
            ]);
            throw $exception;
        }

        if (strlen($dto->ip) > 255 || !$loginSms->useCode($dto->browser, $dto->ip)) {
            $exception = new SmsCodeWasNotSaved($loginSms->sms_login_code_id);

            FailedLoginAttempt::create([
                'login_ip' => $dto->ip,
                'client_id' => $dto->client_id,
                'login_code' => $dto->code,
                'message' => $exception->getMessage()
            ]);
            throw $exception;
        }


        /// by default false;
        $hasVerifAction = false;

        $token = '';
        if (!empty($client->client_id)){
            $apiService = app(ApiService::class);
            $token = $apiService->getProfileToken($client);

            $hasVerifAction = $client->hasVeriffAction();
        }

        return [
            'token' => $token,
            'loan' => $client->getLastLoanByStatuses(),
            'new_client' => (empty($client->client_id) ? true : ($client->new == 1 ? true : false)),
            'has_action_verif' => $hasVerifAction,
        ];
    }
}
