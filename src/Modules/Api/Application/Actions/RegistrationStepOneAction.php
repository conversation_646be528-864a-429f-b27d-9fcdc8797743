<?php

namespace Modules\Api\Application\Actions;

use Illuminate\Support\Facades\Cache;
use Modules\Api\Domain\Entities\LoanRequest;
use Modules\Api\Http\Dto\TmpCreateDto;
use Modules\Common\Http\Dto\Dto;
use Modules\Common\Repositories\UtmTrackerRepository;
use RuntimeException;
use StikCredit\Calculators\Calculator;

final readonly class RegistrationStepOneAction implements ApiActionInterface
{
    private const LOCK_TIMEOUT = 60;

    public function __construct(private LoanRequest $loanRequest)
    {
    }

    public function execute(TmpCreateDto|Dto $dto): array
    {
        $lock = Cache::lock('new-loan-' . $dto->phone, self::LOCK_TIMEOUT);

        if (!$lock->get()) {
            throw new RuntimeException('Repeated request received');
        }

        try {
            $tmpReq = $this->loanRequest->buildNew($dto)->dbModel();
            $loanCalculator = $this->loanRequest->calculator();
            $calc = $loanCalculator->installmentsCollection();
            $product = $this->loanRequest->product()->dbModel();

            /// if we have utm tracker_id set tmp_req.
            if ($dto->utm_tracker_id) {
                app(UtmTrackerRepository::class)->setTmpReqId($dto->utm_tracker_id, $tmpReq->getKey());
            }

            $result = [
                'request_id' => $tmpReq->getKey(),
                'data' => [
                    'penalty' => $calc->sum('penaltyAmount'),
                    'paid_penalty' => 0,
                    'installmentsCount' => $calc->count(),
                    'totalIncreasedAmount' => intToFloat(Calculator::sub($calc->sum('installmentAmount'), $tmpReq->amount_requested)),
                    'discounts' => [],
                    'paid_interest' => 0,
                    'interest' => $calc->sum('interest'),
                    'installmentAmount' => intToFloat($tmpReq->amount_requested),
                    'totalAmount' => intToFloat($tmpReq->amount_requested),
                    'period' => $tmpReq->period_requested,
                    'product_id' => $tmpReq->product_id,
                    'gpr' => $loanCalculator->gpr,
                    'periodLabel' => $product->getPeriodLabel($tmpReq->period_requested),
                    'last_due_date' => $calc->last()->dueDate,
                ]
            ];
        } finally {
            $lock->release();
        }

        return $result;
    }
}
