<?php

namespace Modules\Api\Application\Actions;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Modules\Api\Application\Traits\ProfileTokenValidationTrait;
use Modules\Api\Domain\Exceptions\LoanCalculatorProblem;
use Modules\Api\Domain\Exceptions\LoanNotFoundById;
use Modules\Common\Http\Dto\Dto;
use Modules\Common\Models\Loan;
use Modules\Head\Services\CalendarService;

// used for Client profile calendar
readonly class GetEarlyRepaymentStatsAction implements ApiActionInterface
{
    use ProfileTokenValidationTrait;

    public function execute(Dto $dto): array
    {
        $loan = Loan::find($dto->loan_id);
        if (!$loan) {
            throw new LoanNotFoundById($dto->loan_id);
        }

        $this->validateClientToken($loan->client_id);

        // if date is not passed, we use today by default
        $repaymentDate = Carbon::today()->format('Y-m-d');
        if (!empty($dto->repayment_date)) {
            $repaymentDate = Carbon::parse($dto->repayment_date)->format('Y-m-d');
        }


        // check if selected date is before loan activation, we use loan activation date.
        $utilisationDate = $loan->getUtilisationDate();
        if (Carbon::parse($repaymentDate)->getTimestamp() < $utilisationDate->getTimestamp()) {
            $repaymentDate = $utilisationDate->format('Y-m-d');
        }


        $stats = CalendarService::getRepaymentStatsForDate(
            $loan,
            $repaymentDate
        );


        return [
            'principal' => intToFloat($stats['outstanding_amount_principal']),
            'interests' => intToFloat($stats['interests']),
            'taxes' => intToFloat($stats['taxes']),
            'total' => intToFloat($stats['early']),
            'repaymentDate' => $repaymentDate,
        ];
    }
}
