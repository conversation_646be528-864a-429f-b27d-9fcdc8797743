<?php

namespace Modules\Api\Application\Actions;

use Modules\Api\Application\Traits\ProfileTokenValidationTrait;
use Modules\Api\Http\Dto\SignContractDto;
use Modules\Common\Http\Dto\Dto;
use Modules\Common\Models\Installment;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Head\Repositories\InstallmentRepository;

class GetLoanStatusAction implements ApiActionInterface
{
    use ProfileTokenValidationTrait;

    public function execute(SignContractDto|Dto $dto): array
    {
        /** @var Loan $loan */
        $loan = Loan::get($dto->loan_id);
        if (empty($loan?->loan_id)) {
            return [
                'success' => false,
                'response' => ''
            ];
        }

        $statusId = $loan->loan_status_id ?? null;

        $this->validateClientToken($loan->client_id);

        if (!empty($dto->client_id) && $dto->client_id != $loan->client_id) {
            throw new \Exception('Wrong loan!');
        }

        if (empty($statusId)) {
            return [
                'success' => false,
                'response' => ''
            ];
        }

        $loanData = $this->getLoanData($loan);

        return [
            'loan_status_id' => $statusId,
            'is_approved' => (int) (
                $loan->isApproved() || $statusId == LoanStatus::ACTIVE_STATUS_ID
            ),
            'loan_data' => $loanData,
        ];
    }

    public function getLoanData(Loan $loan): array
    {
        $dbCarton = $loan->getCartonDb();
        $nextInstallment = app(InstallmentRepository::class)->getNextInstallment($loan->getKey());

        $loanData = $loan->toArray();
        $loanData['iban'] = $loan->client->getMainBankAccountIban();
        $loanData['amount_requested'] = intToFloat($loanData['amount_requested']);
        $loanData['amount_approved'] = intToFloat($loanData['amount_approved']);
        $loanData['periodLabel'] = $loan->product->getPeriodLabel($loan->period_approved);
        $loanData['installments'] = $loan->getUnpaidInstallments()->toArray();

        //// for addon data
        $loanData['current_overdue_amount'] = intToFloat($dbCarton['current_overdue_amount']);
        $loanData['next_installment_seq_num'] = $nextInstallment->seq_num;

        $loanData['next_installment_due_date'] = 'изтекъл падеж';
        $loanData['next_installment_amount'] = intToFloat(0);

        /// by default set all outstanding amount
        $loanData['total_rest_next_payment'] = intToFloat($dbCarton['outstanding_amount_total']);

        if ($nextInstallment->due_date->greaterThanOrEqualTo(now())) {
            $loanData['next_installment_due_date'] = $nextInstallment->due_date->format('d.m.Y');
            $loanData['next_installment_amount'] = $nextInstallment->getPrimaryTotalRestAmount();

            $loanData['total_rest_next_payment'] = array_sum([
                $nextInstallment->getPrimaryTotalRestAmount(),
                intToFloat($dbCarton['current_overdue_amount'])
            ]);
        }

        if ($loan->isActive()) {
            $loanData['total_rest_amount'] = intToFloat($loan->getRegularRepaymentDebtDb());
            $loanData['total_rest_amount_early'] = intToFloat($loan->getTotalAmountForRefinanceForAllActiveLoans());
        } else {
            if ($loan->isApproved()) {
                $loanData['total_rest_amount'] = intToFloat($loan->amount_approved);
                $loanData['total_rest_amount_early'] = intToFloat($loan->amount_approved);
            }
        }

        return $loanData;
    }
}
