<?php

namespace Modules\Api\Http\Middleware;

use App;
use Closure;
use Illuminate\Http\Request;

class LocaleSet
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        App::setLocale($request->header('locale'));

        return $next($request);
    }
}
