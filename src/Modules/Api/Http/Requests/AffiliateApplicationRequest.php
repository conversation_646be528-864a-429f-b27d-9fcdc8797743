<?php

namespace Modules\Api\Http\Requests;

class AffiliateApplicationRequest extends BaseApiRequest
{
    protected function prepareForValidation()
    {
        $this->merge([
            'browser' => $this->input('browser') ?: 'unknown',
        ]);
    }
    
    public function rules(): array
    {
        return [
            'ip' => 'required',
            'browser' => 'required',
            'pin' => 'required|string|min:10|max:10',
            'idcard_number' => 'required|string|min:9|max:9',
            'email' => 'required|email:rfc',
            'phone' => 'required|string|min:5|max:12|regex:/^([0][0-9]*)$/',
            'amount' => 'required|numeric|min:100',
            'token' => 'required|string|exists:affiliate,token',
            'affiliate' => 'required|string',
        ];
    }
}