<?php

namespace Modules\Api\Http\Requests;

use Modules\Api\Http\Dto\ClientFileDto;
use Modules\Common\Http\Requests\BaseRequest;

class ClientFileRequest extends BaseRequest implements ApiRequestInterface
{
    public function rules()
    {
        return [
            'client_id' => 'sometimes|numeric',
            'file_id' => 'sometimes|numeric',
            'ip' => 'required',
            'browser' => 'required'
        ];
    }

    public function asDto(): ClientFileDto
    {
        return ClientFileDto::from($this->validated());
    }

}
