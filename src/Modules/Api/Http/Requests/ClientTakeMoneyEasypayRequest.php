<?php

namespace Modules\Api\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Interfaces\LoggableRequestInterface;

class ClientTakeMoneyEasypayRequest extends BaseRequest implements LoggableRequestInterface
{
    public function rules(): array
    {
        return [
            'encoded' => ['required', 'string'],
            'checksum' => ['required', 'string'],
        ];
    }
}
