<?php

namespace Modules\Api\Http\Requests;

use Modules\Api\Http\Dto\UpdateIdCardDto;
use Modules\Common\Http\Requests\BaseRequest;

class UpdateIdCardDataRequest extends BaseRequest implements ApiRequestInterface
{

    public function rules(): array
    {
        return [
            'ip' => 'required|string',
            'browser' => 'required|string',
            'client_id' => 'required|numeric|exists:client,client_id',
            'city_id' => 'required|numeric|exists:city,city_id',
            'first_name' => 'required|string',
            'middle_name' => 'nullable|string',
            'last_name' => 'required|string',
            'lifetime_idcard' => 'required|integer|in:0,1',
            'full_date' => 'required|string',
            'valid_date' => 'required|string',
            'issue_date' => 'required|string',
            'address' => 'required|string',
            'district' => 'required|string',
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'valid_date' => $this->full_date,
            'issue_date' => date('Y-m-d', strtotime('-10 years ' . $this->full_date)),
        ]);
    }

    public function asDto(): UpdateIdCardDto
    {
        return UpdateIdCardDto::from($this->validated());
    }
}
