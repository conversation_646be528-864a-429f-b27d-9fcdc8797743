<?php

namespace Modules\Api\Http\Commands;

use Modules\Sales\Http\Dto\NotificationSettingDto;

class UpdateEmailAndNotificationsFromApiCommand
{
    /** @param ?NotificationSettingDto[] $settings */
    public function __construct(
        private readonly int $clientId,
        private readonly ?string $email,
        private ?array $settings
    ){}

    public function clientId(): int
    {
        return $this->clientId;
    }

    public function email(): ?string
    {
        return $this->email;
    }

    public function settings(): ?array
    {
        return $this->settings;
    }
}