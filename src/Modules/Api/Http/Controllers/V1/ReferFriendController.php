<?php

namespace Modules\Api\Http\Controllers\V1;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Modules\Api\Application\ReferralMessagesEnum;
use Modules\Api\Http\Requests\ReferFriendRequest;
use Modules\Api\Services\ReferFriendService;
use Throwable;

class ReferFriendController extends Controller
{
    public function referFriend(
        ReferFriendRequest $request,
        ReferFriendService $referFriendService
    ): JsonResponse {
        $data = $request->validated();
        $clientId = $data['client_id'] ?? 0;
        $phones = $data['phones'] ?? [];

        $response = [
            'status' => true,
            'message' => '',
            'data' => ['phones' => []],
        ];

        $attemptExceeded = false;
        foreach ($phones as $phone) {
            try {
                if ($attemptExceeded) {
                    $response['data']['phones'][$phone] = ReferralMessagesEnum::YOU_HAVE_REACHED_THE_MAXIMUM_NUMBER_OF_REFERRALS->label();
                    continue;
                }

                $check = $referFriendService->canReferThisPhone($clientId, $phone);
                if (!$check['status']) {
                    $response['data']['phones'][$phone] = $check['message'];

                    // global stop, attempts exceeded
                    if ($check['message'] === ReferralMessagesEnum::YOU_HAVE_REACHED_THE_MAXIMUM_NUMBER_OF_REFERRALS->label()) {
                        $attemptExceeded = true;
                    }

                    continue;
                }

                $referral = $referFriendService->create([
                    'client_id' => $clientId,
                    'phone' => $phone,
                ]);

                if (!$referral?->exists) {
                    throw new Exception('Failed to create referral row. Client #' . $clientId . ', phone: ' . $phone);
                }
                $response['data']['phones'][$phone] = ReferralMessagesEnum::RECEIVED->label();

            } catch (Throwable $e) {
                Log::channel('api')->debug($e);
                $response['data']['phones'][$phone] = 'Internal server error.';
            }
        }

        return response()->json($response);
    }

}