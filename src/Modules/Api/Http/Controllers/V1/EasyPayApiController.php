<?php

namespace Modules\Api\Http\Controllers\V1;

use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Modules\Api\Application\Actions\GetClientInvoiceForEasyPayAction;
use Modules\Api\Application\Actions\RegisterPaymentFromEasyPayAction;
use Modules\Api\Http\Requests\ClientTakeMoneyEasypayRequest;
use Modules\Api\Http\Requests\GetClientInvoiceForEasyPayRequest;
use Modules\Api\Http\Requests\RegisterPaymentFromEasyPayRequest;
use Modules\Api\Http\Responses\ResponseHandler;
use Modules\Common\Models\AbstractTask;
use Modules\Common\Models\SaleTask;
use Modules\Common\Models\SaleTaskType;
use Modules\Payments\Application\Actions\ConfirmEasyPayDeliveryAction;
use Modules\Payments\Application\Actions\Task\ConfirmRefundAction;
use Modules\ThirdParty\Services\CurlEasyPayService;

class EasyPayApiController extends Controller
{
    public function __construct(
        private readonly ResponseHandler $handler
    ) {}

    public function registerMoneyTakingByClient(
        ClientTakeMoneyEasypayRequest $request,
        ConfirmEasyPayDeliveryAction $action
    ): JsonResponse {

        $result = [];

        try {
            $validated = $request->validated();
        } catch (\Throwable $e) {
            $result['success'] = false;
            $result['response'] = 'ERR=wrong_params';
            $result['msg'] = $e->getMessage() . ' ' . $e->getFile() . ':' . $e->getLine();

            Log::channel('easypay')->error('registerMoneyTakingByClient(): ' . $result['msg']);

            return response()->json($result);
        }

        try {

            // register epay request in Easypay microservice
            $curlEasyPayService = app(CurlEasyPayService::class);
            $result = $curlEasyPayService->notification($validated['encoded'], $validated['checksum']);
            $isMoneyTaken = $result['success'] ?? false;

            // we return success=false and response flags(ERR|NO)
            if (!$isMoneyTaken) {
                $result['msg'] = 'No result from epay microservice';

                Log::channel('easypay')->error(
                    'registerMoneyTakingByClient('
                    . $validated['encoded'] . ', '
                    . $validated['checksum'] . '): '
                    . $result['msg']
                );

                return response()->json($result);
            }

            // to be sure another check
            // get result, if success -> get payment id -> proceed
            if (empty($result['payment_id'])) {
                $result['success'] = false;
                $result['response'] = 'ERR';
                $result['msg'] = 'No payment id';

                Log::channel('easypay')->error(
                    'registerMoneyTakingByClient('
                    . $validated['encoded'] . ', '
                    . $validated['checksum'] . '): '
                    . $result['msg']
                );

                return response()->json($result);
            }
            if (empty($result['epay_status'])) {
                $result['success'] = false;
                $result['response'] = 'ERR';
                $result['msg'] = 'No epay status';

                Log::channel('easypay')->error(
                    'registerMoneyTakingByClient('
                    . $validated['encoded'] . ', '
                    . $validated['checksum'] . '): '
                    . $result['msg']
                );

                return response()->json($result);
            }

            $paymentId = (int) $result['payment_id'];
            unset($result['payment_id']);

            $epayStatus = strtolower($result['epay_status']);
            unset($result['epay_status']);

            Log::channel('easypay')->info(
                'registerMoneyTakingByClient('
                . $validated['encoded'] . ', '
                . $validated['checksum'] . ') - STATUS = ' . $epayStatus
                . ' - PAYMENT #' . $paymentId
            );

            // change payment status from EASY_PAY_SENT to DELIVERED and closes task if open
            if ('paid' == $epayStatus) {

                // change payment status close payment task.
                $payment = $action->execute($paymentId);

                try{
                    // attempt to close sale task - unreceived money
                    if (!empty($payment->loan_id)) {

                        $saleTask = SaleTask::where('loan_id', $payment->loan_id)
                            ->where('sale_task_type_id', SaleTaskType::SALE_TASK_TYPE_ID_UNRECEIVED_MONEY)
                            ->where('status', '!=', 'done')
                            ->first();

                        if (!empty($saleTask->sale_task_id)) {
                            $now = Carbon::now();
                            $saleTask->last_status_update_date = $now;
                            $saleTask->status = AbstractTask::TASK_STATUS_DONE;
                            $saleTask->deleted_at = $now;
                            $saleTask->deleted_by = 1;
                            $saleTask->deleted = 1;
                            $saleTask->active = 0;
                            $saleTask->details = 'client took money, automatically close sale_task';
                            $saleTask->save();
                        }
                    }
                } catch (\Throwable $e) {
                    Log::debug('ERROR ' . __METHOD__ . ': ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());
                }


                return response()->json($result);
            }

            // return money, repay loan
            if ('expired' == $epayStatus) {
                app(ConfirmRefundAction::class)->execute($paymentId);
                return response()->json($result);
            }

            throw new Exception('Wrong epay status = ' . $result['epay_status']);

        } catch (\Throwable $e) {
            $result['success'] = false;
            $result['response'] = 'ERR';
            $result['msg'] = $e->getMessage() . ' ' . $e->getFile() . ':' . $e->getLine();

            Log::channel('easypay')->error('registerMoneyTakingByClient(): ' . $result['msg']);

            return response()->json($result);
        }
    }

    /**
     * Before register payment we need to provide to Easypay client dues
     * Used for Easypay microservice
     */
    public function getClientInvoicesForEasypay(
        GetClientInvoiceForEasyPayRequest $request,
        GetClientInvoiceForEasyPayAction  $action
    ): JsonResponse {
        return $this->handler->jsonResponse($request, $action);
    }

    /**
     * Receive EasyPay payment notification
     */
    public function registerPaymentFromEasypay(
        RegisterPaymentFromEasyPayRequest $request,
        RegisterPaymentFromEasyPayAction  $action
    ): JsonResponse {
        return $this->handler->jsonResponse($request, $action);
    }
}
