<?php

namespace Modules\Api\Http\Responses;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Laravel\Sanctum\PersonalAccessToken;
use Modules\Api\Application\Actions\ApiActionInterface;
use Modules\Api\Application\Actions\RegistrationStepThreeAction;
use Modules\Api\Application\Actions\RegistrationStepTwoAction;
use Modules\Api\Http\Requests\ApiRequestInterface;
use Modules\Common\Interfaces\LoggableRequestInterface;
use Modules\Common\Models\Loan;
use Modules\Sales\Services\RawRequestService;

readonly class ResponseHandler
{
    public function __construct(
        private ResponseGeneratorFactory $generatorFactory,
        private RawRequestService $rawRequestService
    ) {
    }

    public function jsonResponse(ApiRequestInterface $request, ApiActionInterface $action): JsonResponse
    {
        $route = $request->route()->getName();
        $generator = $this->generatorFactory->make($route);

        $this->saveRawRequest($request);

        try {
            $data = $action->execute($request->asDto());
            $response = $generator->response(null);

            $success = true;
        } catch (\Throwable $e) {
            Log::channel('api')->error(
                'ResponseHandler(' . (!empty($route) ? $route : '') . '): '
                . $e->getMessage() . ','
                . $e->getFile() . ':'
                . $e->getLine()
            );
            report($e);

            $data = [];
            $response = $generator->response($e);
            $success = false;
        }

        $retArr = $response->get($data);
        $this->rawRequestService->saveResponse($retArr, $success);

        return Response::json($retArr, $response->httpCode());
    }

    public function jsonResponseSimple($request, array $data = [], bool $success = true): JsonResponse
    {
        $this->saveRawRequest($request, $data, $success);

        return Response::json($data, 200);
    }

    private function saveRawRequest($request, array $response = [], ?bool $success = null): void
    {
        $route = $request->route()->getName();

        $createData = [
            'requestString' => $request->asLoggableJson(),
            'token' => $request->header('Token'),
            'token2' => $request->bearerToken(),
            'browser' => $request->validated()['browser'] ?? null,
            'ip' => $request->validated()['ip'] ?? null,
        ];
        if (preg_match('/"client_id":"(\d+)"/', $createData['requestString'], $matches)) {
            $clientId = (int) $matches[1];
            if ($clientId) {
                $createData['client_id'] = $clientId;
            }
        }

        if (!empty($response)) {
            $createData['response'] = json_encode($response, JSON_PRETTY_PRINT);
        }
        if (!is_null($success)) {
            $createData['success'] = $success;
        }

        $this->rawRequestService->createRequest($createData, 'api');
    }
}
