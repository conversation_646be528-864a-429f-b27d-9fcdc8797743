<?php

namespace Modules\Api\Http\Responses\Endpoints\GetFile;

use Modules\Api\Domain\Exceptions\ClientHasNoLoans;
use Modules\Api\Domain\Exceptions\ClientNotFoundById;
use Modules\Api\Domain\Exceptions\DocumentTemplateNotFoundByProductAndType;
use Modules\Api\Domain\Exceptions\EmptyDocumentContent;
use Modules\Api\Http\Responses\AbstractResponse;
use Symfony\Component\HttpFoundation\Response;

class ExceptionResponse extends AbstractResponse implements GetFileResponseInterface
{
    public const HTTP_CODE = Response::HTTP_BAD_REQUEST;
    protected array $supportedExceptions = [
        ClientNotFoundById::class,
        ClientHasNoLoans::class,
        DocumentTemplateNotFoundByProductAndType::class,
        EmptyDocumentContent::class
    ];

    public function get(array $data = []): array
    {
        return [
            'success' => false,
            'response' => $data,
            'error' => $this->exception->getMessage()
        ];
    }
}