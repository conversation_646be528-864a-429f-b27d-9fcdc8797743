<?php

namespace Modules\Api\Http\Responses\Endpoints\RegisterPaymentFromEasyPay;

use Throwable;
use Modules\Api\Domain\Exceptions\EasyPay\ClientHasMoreThanOneActiveLoan;
use Modules\Api\Domain\Exceptions\EasyPay\ClientHasNoActiveLoans;
use Modules\Api\Domain\Exceptions\EasyPay\NoPaymentCreated;
use Modules\Api\Domain\Exceptions\EasyPay\NotExistingClient;
use Modules\Api\Http\Responses\AbstractResponse;
use Symfony\Component\HttpFoundation\Response;

class PaymentProblemResponse extends AbstractResponse implements RegisterPaymentFromEasyPayResponseInterface
{
    public const HTTP_CODE = Response::HTTP_BAD_REQUEST;
    public function matches(?Throwable $e): bool
    {
        return $e instanceof NoPaymentCreated
            || $e instanceof ClientHasNoActiveLoans
            || $e instanceof ClientHasMoreThanOneActiveLoan
            || $e instanceof NotExistingClient;
    }

    public function get(array $data = []): array
    {
        return [
            'success' => true,
            'payment_task_id' => $this->getException()->paymentTaskId
        ];
    }
}