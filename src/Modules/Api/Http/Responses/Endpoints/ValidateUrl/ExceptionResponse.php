<?php

namespace Modules\Api\Http\Responses\Endpoints\ValidateUrl;

use Modules\Api\Http\Responses\AbstractResponse;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;

class ExceptionResponse extends AbstractResponse implements ValidateUrlResponseInterface
{
    public const HTTP_CODE = Response::HTTP_BAD_REQUEST;

    protected array $supportedExceptions = [
        RuntimeException::class
    ];

    public function get(array $data = []): array
    {
        return [
            'success' => false,
            'response' => [],
            'error' => $this->exception->getMessage()
                . ',' . $this->exception->getFile()
                . ',' . $this->exception->getLine(),
        ];
    }
}
