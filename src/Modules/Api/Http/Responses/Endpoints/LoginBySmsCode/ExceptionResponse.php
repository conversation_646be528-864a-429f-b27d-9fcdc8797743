<?php

namespace Modules\Api\Http\Responses\Endpoints\LoginBySmsCode;

use Modules\Api\Domain\Exceptions\ClientNotFoundById;
use Modules\Api\Domain\Exceptions\Login\CustomMessage;
use Modules\Api\Domain\Exceptions\Login\SmsCodeBelongsToDifferentClient;
use Modules\Api\Domain\Exceptions\Login\SmsCodeIsUsedOrExpired;
use Modules\Api\Domain\Exceptions\Login\SmsCodeNotFoundByCode;
use Modules\Api\Domain\Exceptions\Login\SmsCodeWasNotSaved;
use Modules\Api\Http\Responses\AbstractResponse;
use Symfony\Component\HttpFoundation\Response;

class ExceptionResponse extends AbstractResponse implements LoginBySmsCodeResponseInterface
{
    public const HTTP_CODE = Response::HTTP_BAD_REQUEST;
    protected array $supportedExceptions = [
        ClientNotFoundById::class,
        SmsCodeNotFoundByCode::class,
        SmsCodeIsUsedOrExpired::class,
        SmsCodeBelongsToDifferentClient::class,
        SmsCodeWasNotSaved::class,
        CustomMessage::class,
    ];

    public function get(array $data = []): array
    {
        return [
            'success' => false,
            'response' => $data,
            'error' => $this->exception->getMessage()
        ];
    }
}
