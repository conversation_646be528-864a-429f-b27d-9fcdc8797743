<?php

namespace Modules\Api\Http\Responses\Endpoints\LoginBySmsCode;

use Throwable;
use Modules\Api\Http\Responses\AbstractResponse;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Symfony\Component\HttpFoundation\Response;

class HappyPathResponse extends AbstractResponse implements LoginBySmsCodeResponseInterface
{
    public const HTTP_CODE = Response::HTTP_OK;

    public function matches(?Throwable $e): bool
    {
        return is_null($e);
    }

    public function get(array $data = []): array
    {
        $redirect = [
            'url' => 'verify',
            'url_params' => [],
        ];
        /** @var Loan $loan */
        $loan = $data['loan'];
        $client = null;

        /// by default false;
        $hasVerifAction = false;

        if ($loan) {
            $redirect['url_params'] = [
                'loan_id' => $loan->loan_id,
                'loan_status_id' => $loan->loan_status_id,
            ];
            $redirect['url'] = $loan->loan_status_id === LoanStatus::NEW_STATUS_ID ? 'contract' : 'active.loan';
            $client = $loan->client;

            $hasVerifAction = $client->hasVeriffAction();
        }

        return [
            'success' => true,
            'response' => true,
            /// new client waiting true false
            'new_client' => (int)$client?->new,

            /**
             * has_action_verif (true|false)
             * Если клиент подал заявку, и не въбрал никакого действия (veriff),
             * Етот параметр используется только при опции когда у клиента есть завка на подпись, в других кейсах не исп
             * тоесть если client null, нет такого клиента то тогда етот параметр игнорится.
             *
             * Нова страница верификация
             * Създава се нова страничка за верификация с текст:
             * Не чакай. Верифицирай се и вземи парите веднага.
             * Бутон “Верифицирай ме”
             * Алтернативно, изчакай обаждане от оператор. Отнема до 30 минути в работно време.
             * Бутон “Ще изчакам”
             */
            'has_action_verif' => $hasVerifAction,
            'token' => $data['token'],
            'redirect' => $redirect
        ];
    }
}
