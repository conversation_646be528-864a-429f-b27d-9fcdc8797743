<?php

namespace Modules\Api\Http\Responses\Endpoints\GetRefinanceStats;

use Throwable;
use Modules\Api\Domain\Exceptions\ClientHasNoLoansToRefinance;
use Modules\Api\Http\Responses\AbstractResponse;
use Symfony\Component\HttpFoundation\Response;

class NoLoansToRefinanceResponse extends AbstractResponse implements GetRefinanceStatsResponseInterface
{
    public const HTTP_CODE = Response::HTTP_BAD_REQUEST;
    public function matches(?Throwable $e): bool
    {
        return $e instanceof ClientHasNoLoansToRefinance;
    }

    public function get(array $data = []): array
    {
        return [
            'success' => false,
            'response' => [
                'msg' => 'No loans to refinance',
            ],
        ];
    }
}