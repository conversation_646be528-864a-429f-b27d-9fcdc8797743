<?php

namespace Modules\Api\Http\Responses\Endpoints\FinalizeRequest;

use Throwable;
use Modules\Api\Domain\Exceptions\ClientAlreadyExists;
use Modules\Api\Http\Responses\AbstractResponse;
use Symfony\Component\HttpFoundation\Response;

class ClientAlreadyExistsResponse extends AbstractResponse implements FinalizeRequestResponseInterface
{
    public const HTTP_CODE = Response::HTTP_BAD_REQUEST;
    public function matches(?Throwable $e): bool
    {
        return $e instanceof ClientAlreadyExists;
    }

    public function get(array $data = []): array
    {
        return [
            'success' => false,
            'response' => [
                'redirect' => [
                    'url' => 'login',
                    'url_params' => $data,
                ],
            ],
        ];
    }
}
