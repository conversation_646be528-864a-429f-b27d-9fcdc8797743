<?php

namespace Modules\Api\Http\Responses\Endpoints\GetClientInvoiceForEasyPay;

use Throwable;
use Modules\Api\Domain\Exceptions\EasyPay\InvalidPinProvided;
use Modules\Api\Http\Responses\AbstractResponse;
use Symfony\Component\HttpFoundation\Response;

class WrongPinResponse extends AbstractResponse implements GetClientInvoiceForEasyPayResponseInterface
{
    public const HTTP_CODE = Response::HTTP_BAD_REQUEST;
    public function matches(?Throwable $e): bool
    {
        return $e instanceof InvalidPinProvided;
    }

    public function get(array $data = []): array
    {
        return [
            'success' => false,
            'response' => [
                'error_code' => 1012,
                'msg' => 'Wrong pin provided',
            ],
        ];
    }
}