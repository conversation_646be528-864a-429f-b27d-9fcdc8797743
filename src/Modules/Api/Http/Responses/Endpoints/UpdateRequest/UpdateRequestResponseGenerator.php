<?php

namespace Modules\Api\Http\Responses\Endpoints\UpdateRequest;

use Modules\Api\Http\Responses\ResponseGenerator;
use Modules\Api\Http\Responses\ResponseGeneratorInterface;

class UpdateRequestResponseGenerator extends ResponseGenerator implements ResponseGeneratorInterface
{
    protected string $serves = 'api.updateRequest';
    protected string $responseInterface = UpdateRequestResponseInterface::class;
}