<?php

namespace Modules\Api\Http\Responses\Endpoints\UpdateRequest;

use Throwable;
use Modules\Api\Domain\Exceptions\ClientAlreadyExistsWithAnotherPhone;
use Modules\Api\Http\Responses\AbstractResponse;
use Symfony\Component\HttpFoundation\Response;

class ClientAlreadyExistsWithAnotherPhoneResponse extends AbstractResponse implements UpdateRequestResponseInterface
{
    public const HTTP_CODE = Response::HTTP_BAD_REQUEST;
    public function matches(?Throwable $e): bool
    {
        return $e instanceof ClientAlreadyExistsWithAnotherPhone;
    }

    public function get(array $data = []): array
    {
        return [
            'success' => false,
            'response' => [
                'redirect' => [
                    'url' => 'support',
                    'url_params' => $data,
                ],
                'reason' => 'existing_pin',
            ],
        ];
    }
}
