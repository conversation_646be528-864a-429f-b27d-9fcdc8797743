<?php

// CCR get BNB report and save client report on credit level
// Проверява дали има съществуващи за месеца (днешната дата <= 15 число)
// ако съществува ползваме него, иначе теглим от БНБ нов
// В новата система ф-ция getReport()
// В старата:
$userCcr = $ccrLib->checkEgn($this->credit->egn, true);
$userCcr->updateTotal();

// Също така от репорта на БНБ теглиме такива данни:
// - Просрочена главница
// - Оставаща редовна главница
// - Усвоена сума
// - Разрешена сума
// - Предишни кредити от небанкови институции
// - Предишни кредити от банки

// Запазване на бнб + цкр статистики става при:
// - ръчен update на данни (някой в CRM натиснал - get new CCR data)
// - при създаване на кредит в наша система
