<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Application\Actions;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Modules\ThirdParty\Exports\ReportStatsExport;
use Modules\ThirdParty\Repositories\ReportStatsRepository;

final class ReportStatsExportAction
{
    public function __construct(private ReportStatsRepository $repository)
    {
    }

    public function execute(array $filters = []): ReportStatsExport
    {
        return new ReportStatsExport($this->repository->getBuilder($filters));
    }
}
