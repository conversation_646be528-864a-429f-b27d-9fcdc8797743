<?php

declare(strict_types=1);

namespace Modules\ThirdParty\Application\Actions;

use Carbon\Carbon;
use Modules\ThirdParty\Http\Forms\A4eReportsCheckFiltersForm;
use Modules\ThirdParty\Repositories\A4EReportRepository;

final class A4eReportsCheckListingAction
{
    public function __construct(private A4EReportRepository $repository)
    {
    }

    public function execute(array $filters, int $perPage = 10): array
    {
        if (!array_key_exists('created_at_from', $filters)) {
            $today = Carbon::today()->format('d-m-Y');
            $filters['created_at_from'] = $today . ' - ' . $today;
            $filters['created_at_to'] = $today;
        }

        return [
            'filterForm' => A4eReportsCheckFiltersForm::create(),
            'rows' => $this->repository->getRowsByFilters($filters, $perPage),
        ];
    }
}
