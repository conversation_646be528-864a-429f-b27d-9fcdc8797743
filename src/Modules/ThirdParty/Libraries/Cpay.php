<?php

namespace Modules\ThirdParty\Libraries;

use Modules\ThirdParty\Exceptions\CpayException;

/**
 * - dev env, dummy data
 */
class Cpay
{
    const API_URL = 'http://api.cpay.bg/index.php';
    const DEFAULT_CLIENT_NAME = 'stikcredit';
    const SKIP_FIELD_ID_CARD_PICTURE = 'lk_file';

    const DEFAULT_INSTALLMENT_CREDIT_ID = 57;
    const DEFAULT_SALARY_CREDIT_ID = 58;

    const STATUS_CODE_CREDIT_NEW = 1001;
    const STATUS_CODE_CREDIT_PROCESSING = 1002;
    const STATUS_CODE_CREDIT_APPROVED = 1003;
    const STATUS_CODE_CREDIT_SIGNED = 1004;
    const STATUS_CODE_CREDIT_CANCELED = 1005;
    const STATUS_CODE_CREDIT_PAID = 1008;

    const RESPONSE_CODE_KO = 5001;
    const RESPONSE_CODE_OK = 5002;

    const ERROR_NO_CPAY_DATA = 'Cpay data is empty';
    const ERROR_NO_CREDIT_ID = 'No credit id for CPay request';

    private static $cryptoKey;
    private static $companyName;
    private $orderedKeys = [
        'id',
        'type',
        'status',
        'amount',
        'period',
        'percent',
        'pmt', // installment amount
        'egn',
        'service_type',
        'dok_url',

        'fname',
        'mname',
        'lname',
        'personal_card',
        'pc_date',
        'pc_date_valid',
        'from_mvr',
        'gsm',
        'mail',

        'address',
        'city',
        'pk',

        'same',
        'address2',
        'city2',
        'pk2',

        'lk_file',

        'contact_person',
        'gsm2',

        'guarantee',
    ];

    public static $installmentCredit;
    public static $loanToSalary;

    public function __construct()
    {
        self::$cryptoKey = env('CPAY_KEY');

        self::$companyName = env(
            'CPAY_CLIENT_NAME',
            self::DEFAULT_CLIENT_NAME
        );

        self::$installmentCredit = env(
            'CPAY_CREDIT_ID_INSTALMENT',
            self::DEFAULT_INSTALLMENT_CREDIT_ID
        );

        self::$loanToSalary = env(
            'CPAY_CREDIT_ID_SALARY',
            self::DEFAULT_SALARY_CREDIT_ID
        );
    }

    /**
     * Check hash data
     *
     * @param string $hash
     * @param array $data
     *
     * @return bool
     */
    public static function checkHash(string $hash, array $data): bool
    {
        $checksum = self::checksum($data);
        if (null === $checksum) {
            return false;
        }

        $hashed = self::makehash($data, $checksum);
        return ($hash === $hashed);
    }

    /**
     * Send credit data to cpay
     *
     * @param CreditPhone $creditData
     * Format:
     * - credit_id
     * - status
     * - amount
     * - pmt
     * - egn
     *
     * @return bool
     * @throws CpayException
     */
    public static function sendData(array $creditData): bool
    {
        /**
         * Подготовка на документите към CPay
         */
        if (self::STATUS_CODE_CREDIT_APPROVED == $creditData['status']) {
            if (empty($creditData['credit_id'])) {
                throw new CpayException(self::ERROR_NO_CREDIT_ID);
            }

            $docUrls = self::getAllDocsRoutes($creditData['credit_id']);
            $creditData['dok_url'] = base64_encode(json_encode($docUrls));
        }

        /**
         * Подготовка на масив за изпращане
         */
        $data = self::getData($creditData);

        /**
         * Добавяне на hash
         */
        $checksum = self::checksum($data);
        $data['hash'] = self::makehash($data, $checksum);

        /**
         * Изпращаме данните
         */
        $curl = curl_init();
        curl_setopt_array(
            $curl,
            [
                CURLOPT_URL => self::API_URL . '?r=' . self::$companyName,
                CURLOPT_POST => 1,
                CURLOPT_RETURNTRANSFER => 1,
                CURLOPT_USERAGENT => 'POST Request',
                CURLOPT_POSTFIELDS => $data,
            ]
        );
        $responseJson = curl_exec($curl);
        curl_close($curl);

        $response = json_decode($responseJson);
        if (self::RESPONSE_CODE_OK == $response->code) {
            return true;
        }

        if (self::RESPONSE_CODE_KO == $response->code) {
            throw new CpayException(
                'Error: ' . self::RESPONSE_CODE_KO . $response->message
            );
        }

        throw new CpayException(
            'Unknown error (status: '. $creditData['status'] . '): '
            . $responseJson
        );
    }

    /**
     * Send response
     *
     * @param int $code
     * @param string $message
     *
     * @return json
     */
    public static function response(int $code, string $message): string
    {
        return json_encode([
            'code' => $code,
            'message' => $message,
        ]);
    }

    /**
     * Get checksum of data
     *
     * @param $array data
     *
     * @return int|null
     */
    private static function checksum(array $data): ?int
    {
        if (empty($data)) {
            return null;
        }

        if (isset($data[self::SKIP_FIELD_ID_CARD_PICTURE])) {
            unset($data[self::SKIP_FIELD_ID_CARD_PICTURE]);
        }

        return crc32(implode('', $data));
    }

    /**
     * Make data hash
     *
     * @param $data
     * @param $checksum
     *
     * @return string|null
     */
    private static function makehash(array $data, int $checksum): ?string
    {
        if (empty($data['id']) || empty($data['egn'])) {
            return null;
        }

        $line = $data['id'] . '|' . $data['egn'] . '|' . $checksum;
        $encryptedLine = self::getCryptoString($line);

        return sha1($encryptedLine);
    }

    /**
     * Get string encrypted with specific key
     * - ORD() - Convert the first byte of a string to a value between 0 and 255
     * - CHR() - Generate a single-byte string from a number
     *
     * @param  string $line
     * @return string
     */
    private static function getCryptoString(string $line): string
    {
        $result = '';

        for ($i = 0; $i < strlen($line); $i++) {
            $char = substr($line, $i, 1);
            $cryptoKeyChar = substr(
                self::$cryptoKey,
                ($i % strlen(self::$cryptoKey)) - 1,
                1
            );
            $result .= chr(ord($char) + ord($cryptoKeyChar));
        }

        return $result;
    }

    /**
     * [getAllDocsRoutes description]
     * @param  int    $creditId
     * @return array
     */
    private static function getAllDocsRoutes(int $creditId): array
    {
        $params = ['id' => $creditId];
        return [
            route('cpay.documents.application_for_extension', $params),
            route('cpay.documents.contract', $params),
            route('cpay.documents.credit_application', $params),
            route('cpay.documents.general_terms', $params),
            route('cpay.documents.plan', $params),
            route('cpay.documents.promissory_note', $params),
            route('cpay.documents.sef', $params),
        ];
    }

    /**
     * [getData description]
     * @param  array  $creditData
     * @return array
     */
    private static function getData(array $creditData): array
    {
        $data = [];

        /**
         * Сетване на масива в правилният ред!
         */
        $params = [
            'id',
            'status',
            'amount',
            'pmt',
            'egn',
            'dok_url',
        ];
        foreach ($params as $key) {
            if (isset($creditData[$key])) {
                $data[$key] = $creditData[$key];
            }
        }

        return $data;
    }
}
