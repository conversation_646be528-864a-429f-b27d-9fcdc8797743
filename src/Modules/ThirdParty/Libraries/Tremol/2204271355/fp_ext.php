<?php
class NRAPayment {
    public $id = 0;
    public $fdname = "";
    protected $fdcode = NULL;
    public $amount = NULL;
    public $currencyRate = 0;
    public function __construct($id, $fdname, $fdcode, $amount = null, $exchangeRate = null) {
        $this->id = $id;
        $this->fdname = $fdname;
        $this->fdcode = $fdcode;
        $this->amount = $amount;
        $this->currencyRate = $exchangeRate;
    }

    function getFDCode () {
        if($this->fdcode == null) {
            throw new SException("Този тип плащане не е програмирано във ФУ!", \Tremol\ServerErrorType::PaymentNotSupported);
        } else {
            return $this->fdcode;
        }
    }
}

class NRAPayments {
    public $SCash = null;
    public $SChecks = null;
    public $ST = null;
    public $SOT = null;
    public $SP = null;
    public $SSelf = null;
    public $SDmg = null;
    public $SCards = null;
    public $SW = null;
    public $SR1 = null;
    public $SR2 = null;
    public $CURRENCY = null;
    public function __construct($fdnames, $fdcodes, $paymentAmounts = null, $exchangeRate = null) {
        /** В брой */
        $this->SCash = new NRAPayment(0, $fdnames[0], $fdcodes[0], $paymentAmounts[0], null);
        /** С чек */
        $this->SChecks = new NRAPayment(1, $fdnames[1], $fdcodes[1], $paymentAmounts[1], null);
        /** Талони */
        $this->ST = new NRAPayment(2, $fdnames[2], $fdcodes[2], $paymentAmounts[2], null);
        /** Сума по външни талони */
        $this->SOT = new NRAPayment(3, $fdnames[3], $fdcodes[3], $paymentAmounts[3], null);
        /** Сума по амбалаж */
        $this->SP = new NRAPayment(4, $fdnames[4], $fdcodes[4], $paymentAmounts[4], null);
        /** Сума по вътрешно обслужване */
        $this->SSelf = new NRAPayment(5, $fdnames[5], $fdcodes[5], $paymentAmounts[5], null);
        /** Сума по повреди */
        $this->SDmg = new NRAPayment(6, $fdnames[6], $fdcodes[6], $paymentAmounts[6],null);
        /** Сума по кредитни/ дебитни карти */
        $this->SCards = new NRAPayment(7, $fdnames[7], $fdcodes[7], $paymentAmounts[7],null);
        /** Сума по банкови трансфери */
        $this->SW = new NRAPayment(8, $fdnames[8], $fdcodes[8], $paymentAmounts[8], null);
        /** Резерв 1 */
        $this->SR1 = new NRAPayment(9, $fdnames[9], $fdcodes[9], $paymentAmounts[9], null);
        /** Резерв 2 */
        $this->SR2 = new NRAPayment(10, $fdnames[10], $fdcodes[10], $paymentAmounts[10], null);
        /** Валутно плащане, което не е по номенклатурата на НАП */
        $this->CURRENCY = new NRAPayment(11, $fdnames[11], $fdcodes[10], $paymentAmounts[11], $exchangeRate);
    }
}

function endsWith($haystack, $needle)
{
    $length = strlen($needle);
    if ($length == 0) {
        return true;
    }
    return (substr($haystack, -$length) === $needle);
}

function fpIsNEW(\Tremol\FP $fp) {
    return !endsWith($fp->ReadVersion()->Model, "V2");
}

function fpGetNRAPayments(\Tremol\FP $fp, $isFpNew, $amounts) {
    $paymentNames = array();
    $paymentAmounts = array();
    $paymentFDCodes = array();
    $exchangeRate = null;
    for($i = 0 ; $i < 12; $i++) {
        $paymentNames[$i] = null;
        $paymentAmounts[$i] = null;
        if($isFpNew){ 
            $paymentFDCodes[$i] = (string)$i;
        } else {
            $paymentFDCodes[$i] = null;
        }
    }
    if($isFpNew) {
        $paymRes = $fp->ReadPayments();
        $paymentNames[0] = $paymRes->NamePayment0;
        $paymentNames[1] = $paymRes->NamePayment1;
        $paymentNames[2] = $paymRes->NamePayment2;
        $paymentNames[3] = $paymRes->NamePayment3;
        $paymentNames[4] = $paymRes->NamePayment4;
        $paymentNames[5] = $paymRes->NamePayment5;
        $paymentNames[6] = $paymRes->NamePayment6;
        $paymentNames[7] = $paymRes->NamePayment7;
        $paymentNames[8] = $paymRes->NamePayment8;
        $paymentNames[9] = $paymRes->NamePayment9;
        $paymentNames[10] = $paymRes->NamePayment10;
        $paymentNames[11] = $paymRes->NamePayment11;
        $exchangeRate = $paymRes->ExchangeRate;
        if($amounts !== null) {
            $paymentAmounts = $amounts;
        }
    } else {
        $paymResOld = $fp->ReadPayments_Old();
        $paymentNames[0 /** В брой */] = $paymResOld->NamePaym0;
        $paymentNames[(double)$paymResOld->CodePaym1] = $paymResOld->NamePaym1;
        $paymentNames[(double)$paymResOld->CodePaym2] = $paymResOld->NamePaym2;
        $paymentNames[(double)$paymResOld->CodePaym3] = $paymResOld->NamePaym3;
        $paymentNames[11  /** Валута */] = $paymResOld->NamePaym4;
        $exchangeRate = $paymResOld->ExRate;

        $paymentFDCodes[0] = '0';
        $paymentFDCodes[(double)$paymResOld->CodePaym1] = '1';
        $paymentFDCodes[(double)$paymResOld->CodePaym2] = '2';
        $paymentFDCodes[(double)$paymResOld->CodePaym3] = '3';
        $paymentFDCodes[11] = '4';

        if($amounts !== null) {
            $paymentAmounts[0 /** В брой */] = $amounts[0];
            $paymentAmounts[(double)$paymResOld->CodePaym1] = $amounts[1];
            $paymentAmounts[(double)$paymResOld->CodePaym2] = $amounts[2];
            $paymentAmounts[(double)$paymResOld->CodePaym3] = $amounts[3];
            $paymentAmounts[11  /** Валута */] = $amounts[4];
        }
    }
    return new NRAPayments($paymentNames, $paymentFDCodes, $paymentAmounts, $exchangeRate);
}

function fpPayWithCardAndCloseReceipt(\Tremol\FP $fp) {
    try {
        $amount = $fp->Subtotal(Tremol\OptionPrinting::No, Tremol\OptionDisplay::Yes);
        $payments = fpGetNRAPayments($fp, fpIsNEW($fp), null);
        $fp->Payment($payments->SCards->getFDCode(), Tremol\OptionChange::With_Change, $amount, null);
        $fp->CloseReceipt();
    }
    catch(Exception $ex) {
        handleException($ex);
    }
}

function fpReadDailyAvailableAmounts(\Tremol\FP $fp) {
    try {
        /** @type {NRAPayments} */
        $payRes = array();
        if(fpIsNEW($fp)) {
            $res = $fp->ReadDailyAvailableAmounts();
            $amounts = array($res->AmountPayment0, 
                            $res->AmountPayment1,
                            $res->AmountPayment2,
                            $res->AmountPayment3,
                            $res->AmountPayment4,
                            $res->AmountPayment5,
                            $res->AmountPayment6,
                            $res->AmountPayment7,
                            $res->AmountPayment8,
                            $res->AmountPayment9,
                            $res->AmountPayment10,
                            $res->AmountPayment11);
            $payRes = fpGetNRAPayments($fp, true, $amounts);
        } 
        else {
            $resOld = $fp->ReadDailyAvailableAmounts_Old();
            $amountsOld = array($resOld->AmountPayment0,
                                $resOld->AmountPayment1,
                                $resOld->AmountPayment2,
                                $resOld->AmountPayment3,
                                $resOld->AmountPayment4);
            $payRes = fpGetNRAPayments($fp, false, $amountsOld);
        }
        alert_msg($payRes->SCash->fdname.": ".(string)$payRes->SCash->amount."\r\n".
            $payRes->SCards->fdname.": ".(string)$payRes->SCards->amount."\r\n".
            $payRes->CURRENCY->fdname." (".(string)$payRes->CURRENCY->currencyRate."): ".(string)$payRes->CURRENCY->amount
        );
    }
    catch(Exception $ex) {
        handleException(ex);
    }
}


function canOpenReceipt(\Tremol\FP $fp) {
    try {
        $s = $fp->ReadStatus();
        return (!$s->Blocking_3_days_without_mobile_operator && !$s->DateTime_not_set && !$s->DateTime_wrong
                && !$s->FM_error && !$s->FM_full && !$s->FM_Read_only && !$s->No_FM_module
                && !$s->Hardware_clock_error
                && !$s->No_GPRS_Modem && !$s->No_SIM_card
                && !$s->No_task_from_NRA
                && !$s->Opened_Non_fiscal_Receipt && !$s->Opened_Fiscal_Receipt
                && !$s->Opened_Invoice_Fiscal_Receipt
                && !$s->Printer_not_ready_no_paper
                && !$s->Printer_not_ready_overheat
                && !$s->RAM_reset
                && !$s->Reports_registers_Overflow
                && !$s->SD_card_full
                && !$s->Unsent_data_for_24_hours
                && !$s->Wrong_SIM_card
                && !$s->Wrong_SD_card);
    } catch (Exception $ex) {
        handleException($ex);
    }
}

?>
