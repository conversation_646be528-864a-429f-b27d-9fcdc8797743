<?php

namespace Modules\ThirdParty\Database\Seeders;

use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\ThirdParty\Services\CcrService;
use Modules\ThirdParty\Traits\ReportTrait;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Client;

class CcrReportSeeder extends Seeder
{
    use ReportTrait;

    /**
     * @var CcrService
     */
    private CcrService $ccrService;

    /**
     * CcrReportSeeder constructor.
     *
     * @param CcrService $ccrService
     */
    public function __construct(CcrService $ccrService)
    {
        $this->ccrService = $ccrService;
    }

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $ccrReports = [];
        $faker = Faker::create();
        $allClients = Client::all();

        foreach ($allClients as $client) {
            $start = $this->timer();
            $response = $this->ccrService->getBnbReport(
                $client->pin
            );
            $end = $this->timer();
            $execTime = $this->calculateExecTime($start, $end, 5);
            $stats = $this->ccrService->getStats($response);
            $createdAt = now()->subDays($faker->numberBetween(1, 60));

            $report = [
                'pin' => $client->pin,
                'data' => $response,
                'active_percent' => $stats['active_percent'],
                'not_active_percent' => $stats['not_active_percent'],
                'total_percent' => $stats['total_percent'],
                'total_points' => $stats['total_points'],
                'exec_time' => $execTime,
                'last' => 0,
                'active' => 1,
                'deleted' => 0,
                'created_at' => $createdAt,
                'created_by' => Administrator::DEFAULT_ADMINISTRATOR_ID,
            ];

            // Three reports for each client and only one is last
            $ccrReports[] = $report;
            $report['created_at'] = $createdAt->copy()->addDays(20);
            $ccrReports[] = $report;
            $report['created_at'] = $createdAt->copy()->addDays(40);
            $report['last'] = 1;
            $ccrReports[] = $report;
        }

        DB::table('ccr_report')->insert($ccrReports);
    }
}

