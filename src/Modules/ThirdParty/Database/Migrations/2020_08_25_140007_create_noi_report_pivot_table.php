<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Models\NoiReport;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomPivotSchemaBuilder(DB::getSchemaBuilder())->create(
            'noi_report_pivot',
            function ($table) {
                $table->bigIncrements('noi_report_pivot_id');
                $table->integer('noi_report_id')->unsigned()->index();
                $table->integer('client_id')->unsigned()->index();
                $table->integer('loan_id')->unsigned()->index();
                $table->enum('name', NoiReport::getReportNames());
                $table->tableCrudFields(true, true);

                $table->foreign('noi_report_id')->references('noi_report_id')->on('noi_report')->onDelete('cascade');
                $table->foreign('client_id')->references('client_id')->on('client')->onDelete('cascade');
                $table->foreign('loan_id')->references('loan_id')->on('loan')->onDelete('cascade');
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'noi_report_pivot',
            function (Blueprint $table) {
                $table->dropForeign('noi_report_pivot_noi_report_id_foreign');
                $table->dropForeign('noi_report_pivot_client_id_foreign');
                $table->dropForeign('noi_report_pivot_loan_id_foreign');
            }
        );
        Schema::dropIfExists('noi_report_pivot');
    }
};
