<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

class CreateA4eReportTable extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'a4e_report',
            function ($table) {
                $table->bigIncrements('a4e_report_id')->unsigned()->index();
                $table->integer('client_id')->unsigned()->index();
                $table->integer('loan_id')->unsigned()->nullable()->index();
                $table->text('request');
                $table->text('response');
                $table->integer('score')->nullable();
                $table->integer('score_uncapped')->nullable();
                $table->decimal('exec_time', 11, 2)->nullable();
                $table->tableCrudFields(true, true);

                $table->foreign('client_id')->references('client_id')->on('client');
                $table->foreign('loan_id')->references('loan_id')->on('loan');
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'a4e_report',
            function (Blueprint $table) {
                $table->dropForeign('a4e_report_client_id_foreign');
                $table->dropForeign('a4e_report_loan_id_foreign');
            }
        );

        Schema::dropIfExists('a4e_report');
    }
}
