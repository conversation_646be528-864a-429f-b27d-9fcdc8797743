<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('mvr_report')) {
            $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
                'mvr_report',
                function ($table) {
                    $table->bigIncrements('mvr_report_id')->unsigned()->index();
                    $table->string('pin', 10)->index();
                    $table->text('data');
                    $table->decimal('exec_time', 11, 2)->nullable();
                    $table->tableCrudFields(true);
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mvr_report');
    }
};
