<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'ccr_report',
            function ($table) {
                $table->bigIncrements('ccr_report_id')->unsigned()->index();
                $table->string('pin', 12)->index();
                $table->text('data')->nullable();
                $table->text('parsed_data')->nullable();
                $table->decimal('active_percent', 11, 2)->unsigned()->nullable();
                $table->decimal('not_active_percent', 11, 2)->unsigned()->nullable();
                $table->decimal('total_percent', 11, 2)->unsigned()->nullable();
                $table->decimal('total_points', 11, 2)->unsigned()->nullable();
                $table->decimal('exec_time', 11, 2)->nullable();
                $table->tableCrudFields(true);
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ccr_report');
    }
};
