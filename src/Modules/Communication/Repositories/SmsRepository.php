<?php

namespace Modules\Communication\Repositories;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Modules\Common\Models\Office;
use Modules\Common\Models\Sms;
use Modules\Common\Repositories\BaseRepository;

final class SmsRepository extends BaseRepository
{
    public function getSameSmsForToday(string $phone, int $smsTemplateId): Collection
    {
        return Sms::where('phone', $phone)
            ->whereBetween('created_at', [
                Carbon::now()->startOfDay()->toDateTimeString(),
                Carbon::now()->endOfDay()->toDateTimeString(),
            ])
            ->where('sms_template_id', $smsTemplateId)
            ->get();
    }

    public function getFilterBy(array $filters = []): LengthAwarePaginator
    {
        $adminOfficeIds = getAdminOfficeIds();

        $sms = Sms::leftJoin('loan', 'loan.loan_id', '=', 'sms.loan_id');

        if (in_array(Office::OFFICE_ID_WEB, $adminOfficeIds)) {
            $sms->whereRaw('(loan.office_id IN (' . implode(',', $adminOfficeIds) . ') OR loan.loan_id IS NULL)');
        } else {
            $sms->whereRaw('(loan.office_id IN (' . implode(',', $adminOfficeIds) . ') AND loan.loan_id IS NOT NULL)');
        }

        return $sms
            ->filterBy($filters)
            ->select('sms.*')
            ->orderBy('sms_id', 'DESC')
            ->paginate($this->getPaginationLimit());
    }

    /**
     * @param int $limit
     * @param array $joins
     * @param array $where
     * @param array|string[] $order
     * @param bool $showDeleted
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getAll(
        int $limit,
        array $joins = [],
        array $where = [],
        array $order = [],
        bool $showDeleted = false
    ) {
        $builder = Sms::orderByRaw(
            implode(', ', $this->prepareOrderStatement($order))
        );

        $this->setJoins($joins, $builder);

        if (!empty($where)) {
            $builder->where($where);
        }

        return $builder->paginate($limit);
    }

    public function getById(int $smsId): ?Sms
    {
        return Sms::where('sms_id', $smsId)->first();
    }

    public function create(array $data): Sms
    {
        return Sms::create($data);
    }

    public function createQuietly(array $data): Sms
    {
        return Sms::withoutEvents(fn() => $this->create($data));
    }

    public function getByTemplateIdsAndDateRange(
        array $templateIds,
        Carbon $fromDate,
        Carbon $toDate,
        bool $sent = false
    ): Collection {
        $builder = Sms::where([
            //'response' => 'OK',
            'active' => 1,
            'deleted' => 0,
        ])
            ->whereIn('sms_template_id', $templateIds);

        if ($sent) {
            $builder->whereBetween('sent_at', [$fromDate, $toDate]);
        } else {
            $builder->whereBetween('created_at', [$fromDate, $toDate]);
        }

        return $builder->get();
    }
}
