<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    protected function getTables(): Collection
    {
        return collect(['sms_template' => 'name', 'viber_template' => 'name', 'email_template' => 'title']);
    }

    public function up()
    {
        $this->getTables()->each(function (string $field, string $table) {
            DB::query()->from($table)->whereNull($field)->update(
                [$field => 'undefined ' . random_int(0, 999)]
            );
            Schema::table($table, function (Blueprint $table) use ($field) {
                $table->string($field)->nullable(false)->change();
            });
        });
    }

    public function down()
    {
        $this->getTables()->each(function (string $field, string $table) {
            Schema::table($table, function (Blueprint $table) use ($field) {
                $table->string($field)->nullable()->change();
            });
        });
    }
};