<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration {
    use CustomSchemaBuilderTrait;

    public function up(): void
    {
        Schema::table(
            'communication_event_email_campaign',
            function (Blueprint $table) {
                $table->dropForeign('communication_event_email_campaign_communication_event_id_foreign');
                $table->dropForeign('communication_event_email_campaign_email_campaign_id_foreign');
            }
        );
        Schema::dropIfExists('communication_event_email_campaign');

        Schema::table(
            'communication_event_sms_campaign',
            function (Blueprint $table) {
                $table->dropForeign('communication_event_sms_campaign_communication_event_id_foreign');
                $table->dropForeign('communication_event_sms_campaign_sms_campaign_id_foreign');
            }
        );
        Schema::dropIfExists('communication_event_sms_campaign');

        Schema::table(
            'communication_event_viber_campaign',
            function (Blueprint $table) {
                $table->dropForeign('communication_event_viber_campaign_communication_event_id_foreign');
                $table->dropForeign('communication_event_viber_campaign_viber_campaign_id_foreign');
            }
        );
        Schema::dropIfExists('communication_event_viber_campaign');
    }

    public function down(): void
    {
        $this->getCustomPivotSchemaBuilder(DB::getSchemaBuilder())->create(
            'communication_event_email_campaign',
            function ($table) {
                $table->integer('communication_event_id')->unsigned()->index();
                $table->foreign('communication_event_id')->references('communication_event_id')
                    ->on('communication_event')
                    ->onDelete('cascade');
                $table->integer('email_campaign_id')->unsigned()->index();
                $table->foreign('email_campaign_id')->references('email_campaign_id')
                    ->on('email_campaign')
                    ->onDelete('cascade');
            }
        );
        $this->getCustomPivotSchemaBuilder(DB::getSchemaBuilder())->create(
            'communication_event_sms_campaign',
            function ($table) {
                $table->integer('communication_event_id')->unsigned()->index();
                $table->foreign('communication_event_id')->references('communication_event_id')
                    ->on('communication_event')
                    ->onDelete('cascade');
                $table->integer('sms_campaign_id')->unsigned()->index();
                $table->foreign('sms_campaign_id')->references('sms_campaign_id')
                    ->on('sms_campaign')
                    ->onDelete('cascade');
            }
        );

        $this->getCustomPivotSchemaBuilder(DB::getSchemaBuilder())->create(
            'communication_event_viber_campaign',
            function ($table) {
                $table->integer('communication_event_id')->unsigned()->index();
                $table->foreign('communication_event_id')->references('communication_event_id')
                    ->on('communication_event')
                    ->onDelete('cascade');
                $table->integer('viber_campaign_id')->unsigned()->index();
                $table->foreign('viber_campaign_id')->references('viber_campaign_id')
                    ->on('viber_campaign')
                    ->onDelete('cascade');
            }
        );
    }
};
