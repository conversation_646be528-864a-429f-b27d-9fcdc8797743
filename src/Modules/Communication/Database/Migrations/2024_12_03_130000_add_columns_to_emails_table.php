<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('email', static function (Blueprint $table) {
            $table->foreignId('status_id')
                ->nullable()
                ->index()
                ->references('id')
                ->on('email_statuses');

            $table->string('external_id')->nullable()->unique();
        });
    }

    public function down(): void
    {
        Schema::table('email', static function (Blueprint $table) {
            $table->dropConstrainedForeignId('status_id');
            $table->dropColumn('external_id');
        });
    }
};
