<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('marketing_task_messages_history', function (Blueprint $table) {
            $table->unsignedBigInteger('id')->index();
            $table->unsignedBigInteger('marketing_task_id')->index();
            $table->string('type');
            // add columns 'templatable_type' and 'templatable_id'
            $table->morphs('templatable');
            // add columns 'messageable_type' and 'messageable_id'
            $table->nullableMorphs('messageable');
            $table->timestamp('sent_at')->nullable();
            $table->string('details', 512)->nullable();

            $table->unique(['marketing_task_id', 'type']);

            $table->timestamp('archived_at')->useCurrent();
            $table->foreignId('archived_by')->constrained('administrator', 'administrator_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('marketing_task_messages_history');
    }
};
