<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Models\Sms;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'sms_template',
            function ($table) {
                $table->bigIncrements('sms_template_id');
                $table->string('key');
                $table->string('description');
                $table->json('variables');
                $table->string('text', 700);
                $table->enum('gender', config('communication.gender'))->nullable();
                $table->enum('type', Sms::getSmsTypes())->nullable();
                $table->tableCrudFields();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sms_template');
    }
};
