<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('sms', static function (Blueprint $table) {
            $table->dropColumn('sms_campaign_id');
        });

        Schema::dropIfExists('sms_campaign');

        Schema::dropIfExists('sms_source');
    }

    public function down(): void
    {
        //
    }
};
