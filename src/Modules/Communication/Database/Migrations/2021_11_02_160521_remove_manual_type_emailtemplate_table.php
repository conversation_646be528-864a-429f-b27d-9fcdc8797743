<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Modules\Common\Models\Email;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{

    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->table(
            'email_template',
            function (Blueprint $table) {
                $table->dropColumn('type');
            }
        );

        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->table(
            'email_template',
            function (Blueprint $table) {
                $table->enum('type', Email::getEmailTypes())->nullable();
                $table->smallInteger('manual')->default(0);
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
