<?php

declare(strict_types=1);

namespace Modules\Communication\Services;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Modules\Communication\Interfaces\CommunicationInterface;
use Modules\Communication\Jobs\ManualSendEmailByLoansJob;
use Modules\Communication\Jobs\ManualSendEmailJob;
use Modules\Communication\Models\EmailTemplate;
use RuntimeException;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

final readonly class ManualSendEmailService extends ManualSendService
{
    private const QUEUE_NAME = 'manual-email';

    public function sendMessageViaQueue(
        array $clientIds,
        string $subject,
        string $message,
        bool $areWebFirst = false,
    ): array {
        if (empty($clientIds)) {
            throw new RuntimeException('Client IDs not specified');
        }

        $template = $this->createTemplate($subject, $message);
        $this->saveStats($template, count($clientIds));
        $templateId = $template->getKey();

        Cache::put(self::QUEUE_NAME . "-template#$templateId", $template, now()->addMinutes(10));

        $recipientsCount = 0;

        $this->getClients($clientIds)->chunk(
            20,
            static function (Collection $clients) use ($templateId, $areWebFirst, &$recipientsCount) {
                ManualSendEmailJob::dispatch($clients, $templateId, $areWebFirst)->onQueue(self::QUEUE_NAME);
                $recipientsCount += $clients->count();
            }
        );

        return compact('templateId', 'recipientsCount');
    }

    public function sendMessageViaQueueByLoanIds(
        array $loanIds,
        string $subject,
        string $message,
        string $templateType = CommunicationInterface::TEMPLATE_TYPE_MARKETING,
    ): array {
        if (empty($loanIds)) {
            throw new RuntimeException('Loan IDs not specified');
        }

        $template = $this->createTemplate($subject, $message, $templateType);
        $this->saveStats($template, count($loanIds));
        $templateId = $template->getKey();

        Cache::put(self::QUEUE_NAME . "-template#$templateId", $template, now()->addMinutes(10));

        $recipientsCount = 0;

        $this->getLoans($loanIds)->chunk(
            20,
            static function (Collection $loans) use ($templateId, &$recipientsCount) {
                ManualSendEmailByLoansJob::dispatch($loans, $templateId)->onQueue(self::QUEUE_NAME);
                $recipientsCount += $loans->count();
            }
        );

        return compact('templateId', 'recipientsCount');
    }

    private function createTemplate(
        string $subject,
        string $message,
        string $templateType = CommunicationInterface::TEMPLATE_TYPE_MARKETING
    ): EmailTemplate {
        $now = now();

        return EmailTemplate::create([
            'key' => 'manual_send_' . $now->format('YmdHis'),
            'title' => $subject,
            'description' => $subject,
            'body' => $subject,
            'text' => $message,
            'variables' => array_values(
                array_unique(
                    array_merge(
                        $this->getUsedVars($subject),
                        $this->getUsedVars($message)
                    )
                )
            ),
            'gender' => CommunicationInterface::TEMPLATE_GENDER,
            'type' => $templateType,
            'manual' => CommunicationInterface::TEMPLATE_IS_MANUAL,
            'custom_template' => true,
            'created_at' => $now,
            'created_by' => getAdminId(),
        ]);
    }
}
