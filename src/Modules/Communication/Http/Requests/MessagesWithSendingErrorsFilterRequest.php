<?php

declare(strict_types=1);

namespace Modules\Communication\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;

final class MessagesWithSendingErrorsFilterRequest extends BaseRequest
{
    public function rules(): array
    {
        return [
            'loan_id' => 'integer|nullable',
            'client_id' => 'integer|nullable',
            'template_key' => 'string|nullable',
            'communication_type' => 'string|nullable',
            'templatable_id' => 'integer|nullable',
        ];
    }
}
