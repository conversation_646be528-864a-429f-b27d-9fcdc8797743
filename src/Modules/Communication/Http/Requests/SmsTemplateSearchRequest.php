<?php

namespace Modules\Communication\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Interfaces\ListSearchInterface;
use Modules\Common\Traits\DateBuilderTrait;

class SmsTemplateSearchRequest extends BaseRequest implements ListSearchInterface
{
    /**
     * @return array
     */
    public function rules()
    {
        return [
            'key' => 'nullable|string|max:255',
            'type' => 'nullable|string|max:255',
            'active' => $this->getConfiguration('requestRules.active'),
            'createdAt' => ['nullable', 'regex:' . DateBuilderTrait::$dateValidation],
            'updatedAt' => ['nullable', 'regex:' . DateBuilderTrait::$dateValidation],
        ];
    }
}

