<?php

declare(strict_types=1);

namespace Modules\Communication\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;

final class ManualMailingsStatsFilterRequest extends BaseRequest
{
    public function rules(): array
    {
        return [
            'type' => 'string|nullable',
            'templatable_id' => 'integer|nullable',
            'created_at' => ['nullable', 'regex:' . $this->getDateValidationRegex()],
        ];
    }
}
