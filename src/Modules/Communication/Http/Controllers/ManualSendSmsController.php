<?php

namespace Modules\Communication\Http\Controllers;

ini_set('max_execution_time', 6000);
ini_set('memory_limit', '1536M');

use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Communication\Forms\ManualSendSmsForm;
use Modules\Communication\Http\Requests\ManualSendSmsRequest;
use Modules\Communication\Imports\ManualSendSms\ImportClientIdsImport;
use Modules\Communication\Services\ManualSendSmsService;

final class ManualSendSmsController extends BaseController
{
    public function index(): View
    {
        return view('communication::manual-send-sms.index', [
            'manualSendForm' => ManualSendSmsForm::create([
                'url' => route('communication.manual-sms.manualSendSms'),
                'method' => 'POST',
                'enctype' => 'multipart/form-data',
                'data-parsley-validate' => 'true'
            ])
        ]);
    }


    public function manualSendSms(
        ManualSendSmsRequest $request,
        ManualSendSmsService $manualSendSmsService,
    ): RedirectResponse {
        try {
            $clientIds = $this->getClientIds($request);
            $smsText = $request->validated('sms_message');

            ['recipientsCount' => $recipientsCount, 'templateId' => $templateId]
                = $manualSendSmsService->sendMessageViaQueue($clientIds, $smsText, areWebFirst: true);
        } catch (Exception $exception) {
            Log::debug(
                __METHOD__ . ': '
                . $exception->getMessage() . ', '
                . $exception->getFile() . ':'
                . $exception->getLine()
            );

            return back()->with('fail', $exception->getMessage());
        }

        $link = route('communication.manual-mailings-stats', [
            'type' => 'sms', 'templatable_id' => $templateId
        ]);

        return back()->with('success', __('communication::messages.successManualMessageSend',
            compact('recipientsCount', 'link')
        ));
    }

    private function getClientIds(ManualSendSmsRequest $request): array
    {
        $clientIds = $request->get('clientId', []);

        // If we have a file, override client ids from file
        if ($request->hasFile('importClients')) {
            $clientIds = $this->getClientIdsFromFile($request->file('importClients'));
        }

        return $clientIds;
    }

    private function getClientIdsFromFile($file): array
    {
        $importClientIds = app(ImportClientIdsImport::class);
        Excel::import($importClientIds, $file);

        return $importClientIds->clientIds()->toArray();
    }
}
