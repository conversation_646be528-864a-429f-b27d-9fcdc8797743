<?php

namespace Modules\Communication\Http\Controllers;

use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\View\View;
use Modules\Admin\Services\OfficeService;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Changelog\ChangelogViberTemplate;
use Modules\Common\Models\Viber;
use Modules\Communication\Http\Requests\ViberTemplateEditRequest;
use Modules\Communication\Http\Requests\ViberTemplateSearchRequest;
use Modules\Communication\Models\ViberTemplate;
use Modules\Communication\Services\ViberTemplateService;
use Modules\Docs\Enums\PlaceholderEnum;

class ViberTemplateController extends BaseController
{
    protected ViberTemplateService $viberTemplateService;
    protected OfficeService $officeService;

    protected string $pageTitle = 'Viber template list';
    protected string $indexRoute = 'communication.viberTemplate.list';
    protected string $editRoute = 'communication.viberTemplate.edit';

    public function __construct(
        ViberTemplateService $viberTemplateService,
        OfficeService $officeService
    ) {
        $this->viberTemplateService = $viberTemplateService;
        $this->officeService = $officeService;
        parent::__construct();
    }

    /**
     * @return Application|Factory|View
     *
     * @throws Exception
     */
    public function list()
    {
        return view(
            'communication::viber-template.list',
            [
                'viberTemplates' => $this->getTableData(),
                'cacheKey' => $this->cacheKey,
                'getViberTypes' => Viber::getViberTypes()
            ]
        );
    }

    /**
     * @return Application|Factory|View
     *
     * @throws Exception
     */
    public function create()
    {
        $variables = PlaceholderEnum::getAllDocVarsGrouped();
        $getViberTemplateTypes = ViberTemplate::templateTypes();
        $offices = $this->officeService->getOffices();
        $logs = new LengthAwarePaginator([], 0, $this->getPaginationLimit());

        return view(
            'communication::viber-template.crud',
            compact('variables', 'getViberTemplateTypes', 'offices', 'logs')
        );
    }

    /**
     * @param ViberTemplateEditRequest $request
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function store(ViberTemplateEditRequest $request): RedirectResponse
    {
        $this->viberTemplateService->create(
            $request->validated()
        );

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('communication::viberTemplateCrud.viberTemplateCreatedSuccessfully'));
    }

    /**
     * @param ViberTemplate $viberTemplate
     *
     * @return RedirectResponse|View
     *
     * @throws Exception
     */
    public function edit(ViberTemplate $viberTemplate)
    {
        $variables = PlaceholderEnum::getAllDocVarsGrouped();
        $getViberTemplateTypes = ViberTemplate::templateTypes();
        $offices = $this->officeService->getOffices();
        $logs = $viberTemplate->logs()->paginate($this->getPaginationLimit());

        return view(
            'communication::viber-template.crud',
            compact(
                'variables',
                'viberTemplate',
                'getViberTemplateTypes',
                'offices',
                'logs'
            )
        );
    }

    /**
     * @param ViberTemplate $viberTemplate
     * @param ViberTemplateEditRequest $request
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function update(ViberTemplate $viberTemplate, ViberTemplateEditRequest $request): RedirectResponse
    {
        $this->viberTemplateService->update($viberTemplate, $request->validated());

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('communication::viberTemplateCrud.viberTemplateUpdatedSuccessfully'));
    }

    /**
     * @param ViberTemplate $viberTemplate
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function delete(ViberTemplate $viberTemplate): RedirectResponse
    {
        $this->viberTemplateService->delete($viberTemplate);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('communication::viberTemplateCrud.viberTemplateDeletedSuccessfully'));
    }

    /**
     * @param ViberTemplate $viberTemplate
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function enable(ViberTemplate $viberTemplate): RedirectResponse
    {
        $this->viberTemplateService->enable($viberTemplate);

        return redirect()
            ->route($this->indexRoute)
            ->with('success', __('communication::viberTemplateCrud.viberTemplateEnabledSuccessfully'));
    }

    /**
     * @param ViberTemplate $viberTemplate
     *
     * @return RedirectResponse
     *
     * @throws Exception
     */
    public function disable(ViberTemplate $viberTemplate): RedirectResponse
    {
        $this->viberTemplateService->disable($viberTemplate);

        return redirect()
            ->route($this->indexRoute)
            ->with(
                'success',
                __('communication::viberTemplateCrud.viberTemplateDisabledSuccessfully')
            );
    }

    public function revert(ViberTemplate $viberTemplate, ChangelogViberTemplate $logViberTemplate): RedirectResponse
    {
        $this->viberTemplateService->revert($viberTemplate, $logViberTemplate);

        return redirect()->back();
    }

    /**
     * @param ViberTemplateSearchRequest $request
     *
     * @return bool|RedirectResponse
     *
     * @throws Exception
     */
    public function setFilters(ViberTemplateSearchRequest $request)
    {
        return parent::setFiltersFromRequest($request);
    }

    /**
     * @return mixed
     *
     * @throws Exception
     */
    public function getTableData()
    {
        return $this->viberTemplateService->getByFilters(
            parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }

    /**
     * @param ViberTemplateSearchRequest $request
     *
     * @return array|string
     * @throws Exception|\Throwable
     */
    public function refresh(ViberTemplateSearchRequest $request)
    {
        parent::setFiltersFromRequest($request);

        return view(
            'communication::viber-template.list-table',
            [
                'viberTemplates' => $this->getTableData(),
            ]
        )->render();
    }
}
