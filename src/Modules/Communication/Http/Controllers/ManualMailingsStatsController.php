<?php

declare(strict_types=1);

namespace Modules\Communication\Http\Controllers;

use Illuminate\Contracts\View\View;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Communication\Application\Actions\ManualMailingsStatsIndexAction;
use Modules\Communication\Http\Requests\ManualMailingsStatsFilterRequest;

final class ManualMailingsStatsController extends BaseController
{
    public function index(
        ManualMailingsStatsFilterRequest $request,
        ManualMailingsStatsIndexAction $action
    ): View {
        return view(
            'communication::manual-mailings-stats.index',
            $action->execute($request->validated(), $this->getPaginationLimit())
        );
    }
}
