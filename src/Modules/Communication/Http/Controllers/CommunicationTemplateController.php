<?php

namespace Modules\Communication\Http\Controllers;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Modules\Admin\Services\OfficeService;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Communication\Http\Requests\CommunicationPreviewRequest;
use Modules\Communication\Http\Requests\OfficeSearchRequest;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Models\SmsTemplate;
use Modules\Communication\Services\CommunicationService;
use Response;
use Barryvdh\DomPDF\Facade\Pdf;

class CommunicationTemplateController extends BaseController
{
    private const CACHE_TTL = 60 * 60 * 24;

    private CommunicationService $communicationService;
    private string $previewKey = '%s_%d_%d_%s';
    private string $fileName = '%s_%d_%d';

    public function __construct(CommunicationService $communicationService)
    {
        $this->communicationService = $communicationService;

        parent::__construct();
    }

    private function getPreviewCacheKey(
        string $template,
        int    $templateId,
        int    $clientId,
        ?int   $loanId
    ): string
    {
        return sprintf(
            $this->previewKey,
            $template,
            $templateId,
            $clientId,
            !empty($loanId) ? $loanId : 'none'
        );
    }

    /**
     * @param CommunicationPreviewRequest $request
     * @param Client $client
     * @param null|Loan $loan
     *
     * @return JsonResponse
     * @throws Exception
     */
    public function preview(
        CommunicationPreviewRequest $request,
        Client                      $client,
        ?Loan                       $loan = null
    ): JsonResponse
    {
        $data = $request->validated();

        $vars = [];
        $officeId = $data['office_id'] ?? null;
        $vars = array_merge($vars, $this->getOfficeVars($officeId));

        return Response::json(
            $this->getTemplateData($client, $loan, $data, $vars)
        );
    }

    public function getTemplateData(
        Client $client,
        ?Loan  $loan,
        array  $data,
        array  $vars = []
    )
    {

        $result = $this->communicationService->templatePreview(
            $client,
            $loan,
            $data,
            $vars
        );

        $result['template_id'] = $data['template_id'];

        return $result;
    }

    public function download(
        CommunicationPreviewRequest $request,
        Client                      $client,
        ?Loan                       $loan = null
    ) {
        $data = $request->validated();

        $preview = $this->getTemplateData($client, $loan, $data);
        $text = is_array($preview) ? $preview['text'] : $preview->text;
        $template = is_array($preview) ? $preview['template'] : $preview->template;

        $pdf = Pdf::loadHTML($text);

        return $pdf->download(
            sprintf(
                $this->fileName,
                $template->translated_name,
                $client->getKey(),
                time()
            )
        );
    }

    public function send(
        CommunicationPreviewRequest $request,
        Client                      $client,
        ?Loan                       $loan = null
    )
    {
        // preparation
        $data = $request->validated();

        $officeId = $data['office_id'] ?? null;
        $vars = [];
        if (!empty($officeId)) {
            $vars['office_id'] = $officeId;
        }
        $vars = array_merge($vars, $this->getOfficeVars($officeId));

        $template['template_id'] = $data['template_id'];
        if ($data['template_type'] === CommunicationService::COMMUNICATION_TYPE_SMS) {
            $template['template'] = SmsTemplate::whereSmsTemplateId($data['template_id'])->firstOrFail();
        } elseif ($data['template_type'] === CommunicationService::COMMUNICATION_TYPE_EMAIL) {
            $template['template'] = EmailTemplate::whereEmailTemplateId($data['template_id'])->firstOrFail();
        }

        try {
            if (
                $data['template_type'] == CommunicationService::COMMUNICATION_TYPE_SMS
                && empty($client->phone)
            ) {
                throw new Exception(__('communication::communication.clientNoPhone'));
            }

            if (
                $data['template_type'] == CommunicationService::COMMUNICATION_TYPE_EMAIL
                && empty($client->email)
            ) {
                throw new Exception(__('communication::communication.clientNoEmail'));
            }

            $resp = $this->communicationService->manualSend(
                $client,
                $loan,
                $template,
                $vars
            );

            if (!$resp) {
                Session::flash('fail', __('communication::communication.error'));

                return response()->json([
                    'resp' => $resp,
                    'success' => false,
                    'message' => __('communication::communication.error')
                ]);
            }

        } catch (\Exception $e) {
            Log::debug(__METHOD__ . ': ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

            Session::flash('fail', $e->getMessage());
            return response()->json([
                'resp' => false,
                'success' => true,
                'message' => $e->getMessage()
            ]);
        }


        // pizduation
        Session::flash('success', __('communication::communication.success'));
        return response()->json([
            'resp' => $resp,
            'success' => true,
            'message' => __('communication::communication.success')
        ]);
    }

    private function getOfficeVars(?int $officeId = null): array
    {
        $vars = [];

        if (!empty($officeId)) {
            $office = Office::find($officeId);

            $phone = $office->phone;
            if (!empty($phone)) {
                $phone = preg_replace('/(\d{4})(\d{3})(\d{3})/', '$1-$2-$3', $phone);
            }

            $iban = '';
            $bankAccounts = $office->bankAccountsByPaymentMethod();
            if (!empty($bankAccounts)) {
                foreach ($bankAccounts as $ba) {
                    if (!empty($ba->account_iban)) {
                        $iban = $ba->account_iban;
                        break;
                    }
                }
            }

            $vars = [
                'office_phone_number' => $phone,
                'company_office_address' => $office->address,
                'office_working_hours_latin' => OfficeService::getWorkingTimeForSms($office),
                'company_office_iban' => $iban,
            ];
        }

        return $vars;
    }
}
