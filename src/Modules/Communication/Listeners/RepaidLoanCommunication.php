<?php

namespace Modules\Communication\Listeners;

use App\Providers\EventServiceProvider;
use Modules\Communication\Application\Enums\EmailTemplateKeyEnum;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Services\EmailService;
use Modules\Payments\Domain\Events\LoanWasRepaid;

/**
 * @see EventServiceProvider
 */
readonly class RepaidLoanCommunication
{
    public function __construct(
        private EmailService $emailService
    ) {}

    public function handle(LoanWasRepaid $event): void
    {
        $loan = $event->loan;
        if (!$loan->exists) {
            return;
        }


        if ($loan->client->email) {
            $this->emailService->sendByTemplateKeyAndLoanId(
                EmailTemplateKeyEnum::LOAN_REPAID->value,
                $loan->getKey()
            );
        }
    }
}
