<?php

namespace Modules\Communication\Jobs;

use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Log;
use Modules\Common\Jobs\CommonJob;
use Modules\Common\Models\Loan;
use Modules\Communication\Models\EmailTemplate;
use Modules\Communication\Services\EmailService;
use RuntimeException;

final class ManualSendEmailByLoansJob extends CommonJob
{
    public $timeout = 120;

    protected $logChannel = 'manualEmailErrors';
    protected $queueName = 'manual-email';

    public function __construct(
        private readonly Collection $loans,
        private readonly int $templateId,
    ) {
    }

    public function handle(EmailService $emailService): void
    {
        $template = Cache::remember(
            $this->queueName . "-template#$this->templateId",
            now()->addMinutes(10),
            fn() => EmailTemplate::find($this->templateId)
        );
        $this->loans->each(function (Loan $loan) use ($emailService, $template) {
            try {
                if (!$loan->client) {
                    throw new RuntimeException('Client not found');
                }

                $emailService->sendByTemplateWithChecks(
                    $template,
                    $loan->client,
                    $loan,
                    queue: $this->queueName,
                );
            } catch (Exception $e) {
                Log::channel($this->logChannel)->error(
                    __METHOD__ . ':' . $e->getMessage()
                );
            }
        });
    }
}
