<?php

namespace Modules\Communication\Forms;

use Modules\Common\FilterForms\BaseForm;

class ManualSendSmsForm extends BaseForm
{
    public function buildForm(): void
    {
        $this->add('importClients', 'file', [
            'attr' => [
                'class' => 'form-control'
            ],
        ]);

        $this
            ->add('clientId', 'select', [
                'label' => __('table.Client'),
                'attr' => [
                    'multiple' => 'multiple',
                    "data-find-client" => "true",
                    "data-req-route" => route('head.clients.search'),
                    'id' => 'clientId'
                ]
            ])
            ->add('sms_message', 'textarea', [
                'label' => __('table.Message'),
                'attr' => [
                    'rows' => 5,
                ]
            ]);
    }
}
