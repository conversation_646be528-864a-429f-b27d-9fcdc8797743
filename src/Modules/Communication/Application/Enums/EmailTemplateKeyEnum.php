<?php

namespace Modules\Communication\Application\Enums;

use App\Enums\EnumTrait;

/***
 * !!! IMPORTANT when you add new email template
 * you need to add settings of template in Modules/Communication/Config/email-templates.php
 *
 * and run: php artisan db:seed --class=\\Modules\\Communication\\Database\\Seeders\\AddNewEmailTemplatesSeeder
 */
enum EmailTemplateKeyEnum: string
{
    use EnumTrait;

    case NEW_APPLICATION_CREATED = 'new_application_created';
    case LOAN_PARAMS_CHANGED = 'loan_params_changed';
    case LOAN_APPROVED = 'loan_approved';
    case LOAN_REJECTED = 'loan_rejected';
    case LOAN_APPROVED_FOR_SMALLER_AMOUNT = 'approved_for_smaller_amount';
    case LOAN_APPROVED_FOR_SMALLER_AMOUNT_REF = 'approved_for_smaller_amount_ref';
    case LOAN_APPROVED_FOR_SMALLER_AMOUNT_REF_NOT_ENOUGH = 'approved_for_smaller_amount_ref_not_enough';

    case UPCOMING_DUE_INSTALMENT = 'upcoming_due_instalment';
    case CREDIT_OVERDUE_3 = 'credit_overdue_letter_3';
    case CREDIT_OVERDUE_12 = 'credit_overdue_letter_12';
    case CREDIT_OVERDUE_23 = 'credit_overdue_letter_23';
    case CREDIT_OVERDUE_60 = 'credit_overdue_letter_60';
    case CREDIT_OVERDUE_45 = 'credit_overdue_letter_45';
    case CREDIT_OVERDUE_90 = 'credit_overdue_letter_90';

    case SALES_TASK_EXIT_WRONG_PHONE_OWNER = 'sales_task_exit_wrong_phone';
    case SALES_TASK_EXIT_NO_SUCH_PHONE = 'sales_task_exit_no_such_phone';

    case COL_TASK_EXIT_WRONG_PHONE_OWNER = 'task_exit_wrong_phone';
    case COL_TASK_EXIT_NO_SUCH_PHONE = 'task_exit_no_such_phone';

    case EARLY_LOAN_REPAYMENT_DUE = 'early_loan_repayment_due';
    case LOAN_REPAID = 'loan_repaid';

    case OUTSTANDING_OBLIGATIONS_LETTER = 'outstanding_obligations_letter';
    case NO_OBLIGATIONS_LETTER = 'no_obligations_letter';
    case NO_INTEREST_SALE_TASK_EXIT = 'no_interest_sale_task_exit';

    case EMAIL_TYPE_REFINANCE_EXTEND_LOAN_REMAINDER = 'refinance_extend_loan_reminder';
    case EMAIL_TYPE_7_DAYS_EXTEND_LOAN_REMAINDER = '7_days_extend_loan_reminder';
    case EMAIL_TYPE_5_DAYS_OVERDUE_EXTEND_LOAN_REMAINDER = '5_days_overdue_extend_loan_reminder';
    case EMAIL_CLIENT_WITHOUT_LOAN_1 = 'client_without_loan_1';
    case EMAIL_CLIENT_WITHOUT_LOAN_2 = 'client_without_loan_2';
    case EMAIL_CLIENT_WITHOUT_LOAN_3 = 'client_without_loan_3';
    case UNFINISHED_REQUEST_TO_CONTRACT_15_DAYS = 'unfinished_request_to_contract_15_days';
}
