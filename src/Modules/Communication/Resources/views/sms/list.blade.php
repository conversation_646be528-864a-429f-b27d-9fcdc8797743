@extends('layouts.app')
@section('content')
    @push('styles')
        <style>
            /* Container to limit the height of the cell */
            .table-cell {
                max-width: 790px; /* Set max height */
                max-height: 50px; /* Set max height */
                overflow: hidden; /* Hide overflowing content */
                white-space: nowrap; /* Keep text on a single line */
                text-overflow: ellipsis; /* Add ellipsis for overflow */
                position: relative; /* Position relative for absolute positioning on hover */
                cursor: pointer; /* Change cursor to indicate hoverable area */
            }
        </style>
    @endpush
    <x-card-filter-form :filter-form="$smsFilterForm" query-string="true"/>

    <x-card>
        <x-slot:title>
            {{__('other.SendSms')}}
        </x-slot:title>

        <x-table>
            <x-slot:head>
                <th>{{__('communication::smsTable.SendAt')}}</th>
                <th>{{__('communication::smsTable.Text')}}</th>
                <th>{{__('table.Name')}}</th>
                <th>{{__('communication::smsTable.Administrator')}}</th>
                <th>{{__('table.Type')}}</th>
                <th>{{__('table.Manual')}}</th>
                <th>{{__('communication::smsTable.Identifier')}}</th>
                <th>{{__('communication::smsTable.Sender')}}</th>
                <th>{{__('table.Phone')}}</th>
                <th>{{__('communication::smsTable.Response')}}</th>
                <th>{{__('communication::smsTable.Tries')}}</th>
            </x-slot:head>
            @foreach($smses as $sms)
                <tr
                    @if(!$sms->active)
                        class="not-active"
                    @endif
                >
                    <td>{{ $sms->sent_at }}</td>
                    <td class="table-cell cursor-pointer" title="{!! $sms->text !!}">
                        {{ $sms->text }}
                    </td>
                    <td>{{ $sms->smsTemplate->key }}</td>
                    <td>{{ $sms->administrator?->username ?? ''}}</td>
                    <td>{{ $sms->type }}</td>
                    <td>{{ $sms->manual ? __('table.Yes') : __('table.No') }}</td>
                    <td>{{ $sms->identifier }}</td>
                    <td>{{ $sms->sender }}</td>
                    <td>{{ $sms->phone }}</td>
                    <td>{{ $sms->response }}</td>
                    <td>{{ $sms->tries }}</td>
                </tr>
            @endforeach
        </x-table>

        <x-table-pagination :rows="$smses"/>
    </x-card>
@endsection
