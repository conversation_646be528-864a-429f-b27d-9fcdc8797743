<?php

declare(strict_types=1);

namespace Modules\Communication\Console;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Administrator;
use Modules\Communication\Enums\MarketingTaskStatusEnum;
use stdClass;

final class ProcessOldMarketingTasks extends CommonCommand
{
    protected $signature = 'script:communication:process-old-marketing-tasks';
    protected $description = 'Archive old marketing tasks';

    public function handle(): void
    {
        $this->startLog($this->description);

        $total = $this->getTasksQuery()->count();
        $processed = 0;

        $this->getTasksQuery()
            ->whereNotNull('parent_id')
            ->where('created_at', '<', now()->subWeek())
            ->chunkById(1000, function (Collection $tasks) use (&$processed) {
                $processed += $this->archiveAndDeleteRecords($tasks->pluck('id'));
            }, 'id');

        $this->getTasksQuery()
            ->whereNull('parent_id')
            ->where('created_at', '<', now()->subDays(11))
            ->chunkById(1000, function (Collection $tasks) use (&$processed) {
                $processed += $this->archiveAndDeleteRecords($tasks->pluck('id'));
            }, 'id');

        $this->finishLog(['Total to archive: ' . $total, 'Processed: ' . $processed]);
    }

    private function getTasksQuery(): Builder
    {
        return DB::table('marketing_tasks')->whereIn('status', [
            MarketingTaskStatusEnum::COMPLETED->value,
            MarketingTaskStatusEnum::FAILED->value,
            MarketingTaskStatusEnum::SKIPPED->value,
        ])->select('id');
    }

    private function archiveAndDeleteRecords(Collection $taskIds): int
    {
        DB::transaction(static function () use ($taskIds) {
            $messagesQuery = DB::table('marketing_task_messages')->whereIn('marketing_task_id', $taskIds);
            $now = now();

            $messagesQuery->chunkById(400, static function (Collection $messages) use ($now) {
                DB::table('marketing_task_messages_history')->insert(
                    $messages->transform(static fn(stdClass $message) => [
                        'archived_at' => $now,
                        'archived_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                        ...(array) $message
                    ])->toArray()
                );
            }, 'id');
            $messagesQuery->delete();

            $tasksQuery = DB::table('marketing_tasks')->whereIn('id', $taskIds);
            $now = now();

            $tasksQuery->chunkById(250, static function (Collection $tasks) use ($now) {
                DB::table('marketing_tasks_history')->insert(
                    $tasks->transform(static fn(stdClass $task) => [
                        'archived_at' => $now,
                        'archived_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                        ...(array) $task
                    ])->toArray()
                );
            }, 'id');
            $tasksQuery->delete();
        });

        return $taskIds->count();
    }
}
