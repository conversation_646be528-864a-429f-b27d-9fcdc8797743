<?php

namespace Modules\Admin\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Artisan;
use Illuminate\View\View;
use Modules\Admin\Application\Actions\BlogPost\BlogPostIndexDataAction;
use Modules\Admin\Forms\BlogPostForm;
use Modules\Admin\Http\Requests\BlogPostRequest;
use Modules\Admin\Models\BlogPost;
use Modules\Common\Http\Controllers\BaseController;

class BlogPostController extends BaseController
{
    public function index(
        BlogPostIndexDataAction $blogPostIndexDataAction
    ): View
    {
        $data = $blogPostIndexDataAction->execute();

        return view('admin::blog-post.index', $data);
    }

    public function createBlogPost(): View
    {
        $data['blogPostForm'] = BlogPostForm::create([
            'method' => 'POST',
            'url' => route('admin.blog-post.storePost'),
            'data-parsley-validate' => 'true',
            'files' => true
        ]);

        return view('admin::blog-post.create', $data);
    }

    public function storePost(BlogPostRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['published_at'] = Carbon::parse($data['published_at'])->format('Y-m-d');
        $data['slug'] = str($data['title'])->slug()->value();

        $blogPost = BlogPost::whereSlug($data['slug'])->first();
        if ($blogPost) {
            $data['slug'] = "{$data['slug']}-" . time();
        }

        $blogPost = BlogPost::create($data);
        if ($blogPost->exists) {
            Artisan::call('optimize:clear');

            if ($request->hasFile('blogPostImage')) {
                $blogPost
                    ->addMedia($data['blogPostImage'])
                    ->toMediaCollection('blogPostImage');
            }

            return to_route('admin.blog-post.index')->with('success' . __('Success create blog post'));
        }

        return $this->backError('Error creating blog post');
    }

    public function edit(BlogPost $blogPost): View
    {
        $data['blogPost'] = $blogPost;
        $data['blogPostForm'] = BlogPostForm::create([
            'method' => 'PUT',
            'url' => route('admin.blog-post.updateBlogPost', $blogPost->getKey()),
            'data-parsley-validate' => 'true',
            'model' => $blogPost,
            'files' => true
        ]);
        $data['blogPostImage'] = $blogPost->getFirstMediaUrl('blogPostImage');

        return view('admin::blog-post.edit', $data);
    }

    public function updateBlogPost(BlogPostRequest $request, BlogPost $blogPost): RedirectResponse
    {
        $updateData = $request->validated();
        $updateData['published_at'] = Carbon::parse($updateData['published_at'])->format('Y-m-d');
        $updateData['active'] = $request->get('is_active') === 'yes' ? 1 : 0;

        if ($blogPost->update($updateData)) {
            Artisan::call('optimize:clear');

            if ($request->hasFile('blogPostImage')) {
                $blogPost
                    ->clearMediaCollection('blogPostImage')
                    ->addMedia($updateData['blogPostImage'])
                    ->toMediaCollection('blogPostImage');
            }

            return to_route('admin.blog-post.index')->with('success', __('Success update blog post'));
        }
        return $this->backError('Error update post');
    }

    public function destroy(BlogPost $blogPost): RedirectResponse
    {
        $blogPost->deleted_at = date('Y-m-d H:i:s');
        $blogPost->deleted_by = getAdminId();

        if ($blogPost->exists && $blogPost->save()) {
            Artisan::call('optimize:clear');
            return $this->backSuccess('Success delete blog post');
        }
        return $this->backError('Error delete blog post');
    }
}
