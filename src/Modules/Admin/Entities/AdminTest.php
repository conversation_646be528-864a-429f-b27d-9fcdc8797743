<?php

namespace Modules\Admin\Entities;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;

/**
 * @mixin IdeHelperAdminTest
 */
class AdminTest extends Model
{
    use HasFactory;

    protected $table = 'admin_test';
    protected $fillable = ['admin_test_period_id', 'administrator_id', 'client_id', 'loan_id', 'is_gypsy', 'processed_at'];

    public function testPeriod(): BelongsTo
    {
        return $this->belongsTo(
            AdminTestPeriod::class,
            'id',
            'admin_test_period_id'
        );
    }

    public function administrator(): BelongsTo
    {
        return $this->belongsTo(
            Administrator::class,
            'administrator_id',
            'administrator_id'
        );
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(
            Client::class,
            'client_id',
            'client_id'
        );
    }

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }
}
