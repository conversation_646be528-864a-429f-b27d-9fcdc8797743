<?php

namespace Modules\Admin\Repositories;

use Modules\Common\Models\TmpRequestStep;
use Modules\Common\Repositories\BaseRepository;

class TmpRequestStepRepository extends BaseRepository
{
    protected TmpRequestStep $tmpRequestStep;

    public function __construct(TmpRequestStep $tmpRequestStep)
    {
        $this->tmpRequestStep = $tmpRequestStep;
    }

    /**
     * @param int $limit
     * @param array $joins
     * @param array $where
     * @param array|string[] $order
     * @param bool $showDeleted
     *
     * @return mixed
     */
    public function getAll(
        int $limit,
        array $joins = [],
        array $where = [],
        array $order = ['active' => 'DESC', 'tmp_request_step_id' => 'DESC'],
        bool $showDeleted = false
    ) {
        $where = $this->checkForDeleted($where, $showDeleted);

        $builder = $this->tmpRequestStep::orderByRaw(
            implode(', ', $this->prepareOrderStatement($order))
        );
        $this->setJoins($joins, $builder);

        if (!empty($where)) {
            $builder->where($where);
        }

        return $builder->paginate($limit);
    }

    /**
     * @param int $tmpRequestStepId
     *
     * @return mixed
     */
    public function getById(int $tmpRequestStepId)
    {
        $tmpRequestStep = $this->tmpRequestStep::where(
            'tmp_request_step_id',
            '=',
            $tmpRequestStepId
        )->get();

        return $tmpRequestStep->first();
    }

    /**
     * @param array $data
     */
    public function create(array $data)
    {
        $tmpRequestStep = new TmpRequestStep();
        $tmpRequestStep->fill($data);
        $tmpRequestStep->save();
    }

    /**
     * @param TmpRequestStep $tmpRequestStep
     * @param array $data
     */
    public function update(TmpRequestStep $tmpRequestStep, array $data)
    {
        $tmpRequestStep->fill($data);
        $tmpRequestStep->save();
    }

    /**
     * @param TmpRequestStep $tmpRequestStep
     */
    public function delete(TmpRequestStep $tmpRequestStep)
    {
        $tmpRequestStep->delete();
    }

    /**
     * @param TmpRequestStep $tmpRequestStep
     */
    public function disable(TmpRequestStep $tmpRequestStep)
    {
        $tmpRequestStep->disable();
    }

    /**
     * @param TmpRequestStep $tmpRequestStep
     */
    public function enable(TmpRequestStep $tmpRequestStep)
    {
        $tmpRequestStep->enable();
    }
}
