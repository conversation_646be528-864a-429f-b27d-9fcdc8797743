<?php

namespace Modules\Admin\Repositories;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Models\AdministratorSetting;
use Modules\Common\Models\Setting;
use Modules\Common\Repositories\BaseRepository;
use Modules\Common\Services\WherePipeline\DataWrapper;

class SettingRepository extends BaseRepository
{
    /**
     * @param int $limit
     * @param DataWrapper $dataWrapper
     * @param array $joins
     * @param array $order
     *
     * @return mixed
     */
    public function getAll(
        DataWrapper $dataWrapper,
        int $limit,
        array $joins = [],
        array $order = []
    ) {
        $builder = DB::table('setting');
        $builder->select(DB::raw('setting.*'));

        $this->setJoins($joins, $builder);

        $this->setWheres($builder, $dataWrapper);

        if (!empty($order)) {
            foreach ($order as $key => $direction) {
                $builder->orderBy($key, $direction);
            }
        }

        $result = $builder->paginate($limit);
        $records = Setting::hydrate($result->all());
        $result->setCollection($records);

        return $result;
    }

    /**
     * @param array $data
     */
    public function create(array $data)
    {
        $setting = new Setting();
        $setting->fill($data);
        $setting->save();
    }

    /**
     * @param Setting $setting
     */
    public function delete(Setting $setting)
    {
        $setting->delete();
    }

    /**
     * @param Setting $setting
     */
    public function disable(Setting $setting)
    {
        $setting->disable();
    }

    /**
     * @param Setting $setting
     * @param array $data
     */
    public function update(Setting $setting, array $data)
    {
        $setting->fill($data);
        $setting->save();
    }

    /**
     * @param Setting $setting
     */
    public function enable(Setting $setting)
    {
        $setting->enable();
    }

    /**
     * @param int $settingTypeId
     *
     * @return Collection
     */
    public function getBySettingTypeId(
        int $settingTypeId,
        ?string $key = null
    ): Collection {

        $conditions = self::NOT_DEL_ACTIVE;
        $conditions['setting_type_id'] = $settingTypeId;
        if (!empty($key)) {
            $conditions['setting_key'] = $key;
        }

        return Setting::where($conditions)->get();
    }

    public function createFromEnum(SettingsEnum $settingsEnum): Setting
    {
        $setting = new Setting();
        $data = [
            'setting_key' => $settingsEnum->value,
            'name' => $settingsEnum->title(),
            'default_value' => $settingsEnum->default_value(),
            'description' => '',
            'setting_type_id' => $settingsEnum->typeId(),
        ];

        $setting->fill($data);
        $setting->save();

        return $setting;
    }

    public function getSetting(SettingsEnum $settingsEnum): Setting
    {
        $key = 'setting_obj_' . $settingsEnum->value;

        return \Cache::remember($key, 10 * 60, function () use($settingsEnum) {
            $conditions = self::NOT_DEL_ACTIVE;
            $conditions['setting_key'] = $settingsEnum->value;

            $setting = Setting::where($conditions)->first();
            if (!$setting) {
                $setting = $this->createFromEnum($settingsEnum);
            }

            return $setting;
        });
    }

    public function getAdminMaxDiscountValue(?int $adminId): float
    {
        if (!$adminId) {
            return 0.0;
        }
        $setting = AdministratorSetting::query()->where('administrator_id', $adminId)
            ->where('setting_key', SettingsEnum::max_discount_percent_administrator->value)->first();

        if ($setting) {
            return (float) $setting->value;
        }
        $setting = Setting::where(['setting_key' => SettingsEnum::max_discount_percent_administrator->value])->first();

        return $setting ? (float) $setting->default_value : 0.0;
    }
}
