<?php

namespace Modules\Admin\Repositories;

use Illuminate\Database\Eloquent\Model;
use Modules\Admin\Models\ClientRateUs;
use Modules\Common\Repositories\BaseRepository;

class ClientRateUsRepository extends BaseRepository
{
    public function __construct(
        private readonly ClientRateUs $clientRateUs
    )
    {
    }

    public function getDbModel(): Model
    {
        return $this->clientRateUs;
    }
}
