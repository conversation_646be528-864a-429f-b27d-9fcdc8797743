<?php

namespace Modules\Admin\FilterForms;

use Illuminate\Support\Facades\DB;
use Modules\Admin\Entities\Office;
use Modules\Common\FilterForms\BaseFilterForm;
use Modules\Common\Models\Role;

final class AdminFilterForm extends BaseFilterForm
{
    public function buildForm(): void
    {
        $this->add('name', 'text', [
            'label' => __('table.Name'),
            'value' => $this->request->get('name')
        ]);

        $this->add('username', 'text', [
            'label' => __('table.Username'),
            'value' => $this->request->get('username')
        ]);

        $this->addPhoneFilter('phone');
        $this->addEmailFilter('email');
        $this->addActiveInActiveFilter();
        $this->addDateFilterFromTo('created_at', __('table.CreatedAt'));
        $this->addDateFilterFromTo('updated_at', __('table.UpdatedAt'));
        $this->addOfficesFilter();
        $this->addRolesFilter();
    }

    private function addOfficesFilter(): void
    {
        $fieldName = 'office_ids';
        $choices = Office::where('active', '=', 1)
            ->where('deleted', '=', 0)
            ->orderBy(
                DB::raw("office_id = '" . \Modules\Common\Models\Office::OFFICE_ID_WEB . "'"),
                'desc'
            )
            ->orderBy('name')
            ->pluck('name', 'office_id')
            ->toArray();

        $this->add($fieldName, 'select', [
            'label' => __('table.Office'),
            'empty_value' => '',
            'selected' => $this->request->input($fieldName, ''),
            'choices' => $choices,
            'attr' => [
                'data-live-search' => 'true',
                'data-actions-box' => 'true',
                'multiple' => 'true',
            ]
        ]);
    }

    private function addRolesFilter(): void
    {
        $fieldName = 'role_ids';
        $this->add($fieldName, 'select', [
            'label' => __('table.FilterByRoles'),
            'empty_value' => '',
            'selected' => $this->request->input($fieldName, ''),
            'choices' => Role::where('active', 1)->orderBy('name')->pluck('name', 'id')->toArray(),
            'attr' => [
                'data-live-search' => 'true',
                'data-actions-box' => 'true',
                'multiple' => 'true',
            ]
        ]);
    }
}
