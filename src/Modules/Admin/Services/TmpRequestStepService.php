<?php

namespace Modules\Admin\Services;

use Modules\Admin\Exceptions\AdminException;
use Modules\Admin\Repositories\TmpRequestStepRepository;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Models\TmpRequestStep;
use Modules\Common\Services\BaseService;

class TmpRequestStepService extends BaseService
{
    private TmpRequestStepRepository $tmpRequestStepRepository;

    /**
     * AgreementService constructor.
     *
     * @param TmpRequestStepRepository $tmpRequestStepRepository
     */
    public function __construct(TmpRequestStepRepository $tmpRequestStepRepository)
    {
        $this->tmpRequestStepRepository = $tmpRequestStepRepository;

        parent::__construct();
    }

    /**
     * @param TmpRequestStep $tmpRequestStep
     * @param array $data
     *
     * @return mixed
     *
     * @throws ProblemException
     */
    public function update(TmpRequestStep $tmpRequestStep, array $data)
    {
        try {
            $this->tmpRequestStepRepository->update($tmpRequestStep, $data);
        } catch (\Exception $exception) {
            throw new AdminException(
                __('admin::tmpRequestStepCrud.EditionFailed'),
                $exception
            );
        }

        return $tmpRequestStep;
    }

    /**
     * @param array $data
     *
     * @return bool
     *
     * @throws ProblemException
     */
    public function create(array $data)
    {
//        try {
            $this->tmpRequestStepRepository->create($data);
//        } catch (\Exception $exception) {
//            throw new ProblemException(__('admin::tmpRequestStepCrud.CreationFailed'));
//        }

        return true;
    }

    /**
     * @param TmpRequestStep $tmpRequestStep
     *
     * @return bool
     *
     * @throws NotFoundException
     * @throws ProblemException
     */
    public function delete(TmpRequestStep $tmpRequestStep)
    {
        try {
            $this->tmpRequestStepRepository->delete($tmpRequestStep);
        } catch (\Exception $exception) {
            throw new AdminException(
                __('admin::tmpRequestStepCrud.DeletionFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @param TmpRequestStep $tmpRequestStep
     *
     * @return bool
     *
     * @throws NotFoundException
     * @throws ProblemException
     */
    public function disable(TmpRequestStep $tmpRequestStep)
    {
        if (!$tmpRequestStep->isActive()) {
            throw new ProblemException(__('admin::tmpRequestStepCrud.DisableForbidden'));
        }

        try {
            $this->tmpRequestStepRepository->disable($tmpRequestStep);
        } catch (\Exception $exception) {
            throw new AdminException(
                __('admin::tmpRequestStepCrud.DisableFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @param TmpRequestStep $tmpRequestStep
     *
     * @return bool
     *
     * @throws ProblemException
     */
    public function enable(TmpRequestStep $tmpRequestStep)
    {
        try {
            $this->tmpRequestStepRepository->enable($tmpRequestStep);
        } catch (\Exception  $exception) {
            throw new AdminException(
                __('admin::tmpRequestStepCrud.EnableFailed'),
                $exception
            );
        }

        return true;
    }

    /**
     * @param int $limit
     * @param array $data
     *
     * @return mixed
     */
    public function getByFilters(int $limit, array $data)
    {
        $joins = $this->getJoins($data);
        $whereConditions = $this->getWhereConditions($data);

        return $this->tmpRequestStepRepository->getAll(
            $limit,
            $joins,
            $whereConditions
        );
    }

    /**
     * @param array $data
     *
     * @return array
     */
    public function getJoins(array $data): array
    {
        return [];
    }
}
