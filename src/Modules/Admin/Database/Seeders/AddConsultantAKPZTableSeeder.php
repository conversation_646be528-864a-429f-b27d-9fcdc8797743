<?php

namespace Modules\Admin\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Database\Eloquent\Model;
use Modules\Collect\Models\OuterCollectorReport;
use Modules\Common\Models\Consultant;

// php artisan db:seed --class=\\Modules\\Admin\\Database\\Seeders\\AddConsultantAKPZTableSeeder
class AddConsultantAKPZTableSeeder extends Seeder
{
    public function run(): void
    {
        Consultant::firstOrCreate([
            'consultant_id' => OuterCollectorReport::AKPZ_CONSULTANT_ID,
            'name' => 'АКПЗ - външно събиране',
            'phone' => '0000000000',
            'active' => true
        ]);
    }
}
