<?php


namespace Modules\Admin\Database\Seeders;


use Illuminate\Database\Seeder;
use Modules\Common\Models\Administrator;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Office;

class AdministratorOfficeSeeder extends Seeder
{
    /**
     * @throws \Exception
     */
    public function run()
    {
        $offices = Office::all('office_id')->pluck('office_id');
        foreach ($offices as $office) {
            DB::table('administrator_office')->updateOrInsert(
                [
                    'administrator_id' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                    'office_id' => $office
                ]
            );
            DB::table('administrator_office')->updateOrInsert(
                [
                    'administrator_id' => Administrator::DEFAULT_ADMINISTRATOR_ID,
                    'office_id' => $office
                ]
            );
            DB::table('administrator_office')->insert(
                [
                    'administrator_id' => Administrator::DEFAULT_UNIT_TEST_USER_ID,
                    'office_id' => $office,
                ]
            );

        }

        $admins = Administrator::query()->where('administrator_id', '>=', '4')->select('administrator_id')->get()->pluck('administrator_id');
        foreach ($admins as $admin) {
            DB::table('administrator_office')->insert(
                [
                    'administrator_id' => $admin,
                    'office_id' => $offices->random(),
                ]
            );
        }
    }
}
