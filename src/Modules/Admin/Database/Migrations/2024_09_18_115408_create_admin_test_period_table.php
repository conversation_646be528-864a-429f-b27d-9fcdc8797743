<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up()
    {
        Schema::create('admin_test_period', function (Blueprint $table) {
            $table->id();
            $table->date('from'); // Date in Y-m-d format
            $table->date('to'); // Date in Y-m-d format
            $table->timestamps(); // created_at and updated_at
            $table->unsignedBigInteger('created_by'); // To store admin user who created this entry
            $table->boolean('active')->default(1); // To handle the active state (1 or 0)
        });
    }

    public function down()
    {
        Schema::dropIfExists('admin_test_period');
    }
};
