<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('city', function (Blueprint $table) {
            $table->bigIncrements('city_id');
            $table->string('code');
            $table->string('name');
            $table->integer('municipality_id');
            $table->string('slug');
            $table->foreign('municipality_id')
                ->references('municipality_id')
                ->on('municipality');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
         Schema::table('city', function (Blueprint $table) {
            $table->dropForeign('city_municipality_id_foreign');
        });

        Schema::dropIfExists('city');
    }
};
