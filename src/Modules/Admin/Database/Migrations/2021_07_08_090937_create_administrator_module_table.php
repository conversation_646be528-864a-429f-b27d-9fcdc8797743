<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Models\Module;
use Modules\Common\Models\Permission;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration {
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'administrator_module',
            function ($table) {
                $table->bigIncrements('administrator_module_id');
                $table->bigInteger('administrator_id')->unsigned();
                $table->enum('module', Module::getModuleNames())->index();
                $table->enum('type', Permission::getTypes());
                $table->timestamp('start_at', 0)->nullable();
                $table->timestamp('end_at', 0)->nullable();

                $table->foreign('administrator_id')->references('administrator_id')->on('administrator');

                $table->tableCrudFields();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table(
            'administrator_module',
            function (Blueprint $table) {
                $table->dropForeign('administrator_module_administrator_id_foreign');
            }
        );
        Schema::dropIfExists('administrator_module');
    }
};
