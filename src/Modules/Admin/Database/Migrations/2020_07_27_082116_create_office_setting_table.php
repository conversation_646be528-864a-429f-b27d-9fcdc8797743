<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomPivotSchemaBuilder(DB::getSchemaBuilder())->create(
            'office_setting',
            function ($table) {
                $table->unsignedBigInteger('office_id');
                $table->string('setting_key');
                $table->string('value')->nullable();

                $table->foreign('office_id')
                    ->references('office_id')
                    ->on('office')
                    ->onDelete('cascade');
                $table->foreign('setting_key')
                    ->references('setting_key')
                    ->on('setting')
                    ->onDelete('cascade');

                $table->primary(
                    ['office_id', 'setting_key'],
                );

                $table->tableCrudFields();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'office_setting',
            function (Blueprint $table) {
                $table->dropForeign('office_setting_office_id_foreign');
                $table->dropForeign('office_setting_setting_key_foreign');
            }
        );
        Schema::dropIfExists('office_setting');
    }
};
