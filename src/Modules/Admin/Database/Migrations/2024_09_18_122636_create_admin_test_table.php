<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up()
    {
        Schema::create('admin_test', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('admin_test_period_id');
            $table->unsignedBigInteger('administrator_id')->index(); // Admin who is reviewing the loan
            $table->unsignedBigInteger('client_id')->index();
            $table->unsignedBigInteger('loan_id')->index();
            $table->boolean('is_gypsy')->nullable()->index(); // 1 for Yes, 0 for No
            $table->timestamps(); // includes created_at and updated_at
            $table->timestamp('processed_at')->nullable()->index(); // Time when the loan was processed
        });
    }

    public function down()
    {
        Schema::dropIfExists('admin_test');
    }
};
