<?php

use Mo<PERSON>les\Admin\Http\Controllers\CloseReasonController;

$idPattern = '[1-9][0-9]{0,5}';

// ajax load of table with close reasons
Route::get('/close-reasons/refresh', [CloseReasonController::class, 'refresh'])
    ->name('admin.close-reasons.refresh')
    ->defaults('description', 'Ajax refresh close reasons table');
Route::get('/close-reasons/filters', [CloseReasonController::class, 'getFilters'])
    ->name('admin.close-reasons.getFilters')
    ->defaults('description', 'Get close reasons filters');
Route::put('/close-reasons/filters', [CloseReasonController::class, 'setFilters'])
    ->name('admin.close-reasons.setFilters')
    ->defaults('description', 'Update close reasons filters');
Route::delete('/close-reasons/filters', [CloseReasonController::class, 'cleanFilters'])
    ->name('admin.close-reasons.cleanFilters')
    ->defaults('description', 'Cleanup close reasons filters');

// close reasons
Route::get('/close-reasons', [CloseReasonController::class, 'list'])
    ->name('admin.close-reasons.list');
Route::get('/close-reasons/create', [CloseReasonController::class, 'create'])
    ->name('admin.close-reasons.create');
Route::post('/close-reasons/store', [CloseReasonController::class, 'store'])
    ->name('admin.close-reasons.store');
Route::get('/close-reasons/edit/{closeReason}', [CloseReasonController::class, 'edit'])
    ->name('admin.close-reasons.edit')
    ->where('id', $idPattern);
Route::post('/close-reasons/update/{closeReason}', [CloseReasonController::class, 'update'])
    ->name('admin.close-reasons.update')
    ->where('id', $idPattern);
Route::get('/close-reasons/delete/{closeReason}', [CloseReasonController::class, 'delete'])
    ->name('admin.close-reasons.delete')
    ->where('id', $idPattern);
Route::get('/close-reasons/enable/{closeReason}', [CloseReasonController::class, 'enable'])
    ->name('admin.close-reasons.enable')
    ->where('id', $idPattern);
Route::get('/close-reasons/disable/{closeReason}', [CloseReasonController::class, 'disable'])
    ->name('admin.close-reasons.disable')
    ->where('id', $idPattern);
