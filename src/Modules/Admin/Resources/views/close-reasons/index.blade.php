@extends('layouts.app')

@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <form id="closeReasonForm" class="form-inline card-body" action="{{ route('admin.close-reasons.list') }}"
                      method="PUT">
                    @csrf
                    <div class="form-row w-100">
                        <div class="form-group col-lg-2">
                            <input name="name" class="form-control w-100" type="text"
                                   placeholder="{{__('table.FilterByName')}}"
                                   value="{{ session($cacheKey . '.name') }}">
                        </div>
                        <div class="form-group col-lg-2">
                            <x-select-active active="{{ session($cacheKey . '.active') }}"/>
                        </div>
                        <div class="form-group col-lg-2">
                            <input type="text" autocomplete="off" name="createdAt" class="form-control w-100"
                                   id="createdAt"
                                   value="{{ session($cacheKey . '.createdAt') }}"
                                   placeholder="{{__('table.FilterByCreatedAt')}}">
                        </div>
                        <div class="form-group col-lg-2">
                            <input type="text" autocomplete="off" name="updatedAt" class="form-control w-100"
                                   id="updatedAt"
                                   value="{{ session($cacheKey . '.updatedAt') }}"
                                   placeholder="{{__('table.FilterByUpdatedAt')}}">
                        </div>
                        <div class="col-lg-12 mt-4">
                            <x-btn-filter/>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div id="btns-panel">
                        <x-btn-create url="{{ route('admin.close-reasons.create') }}" name="{{ __('btn.Create') }}"/>
                    </div>

                    <div id="main-table">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                <tr>
                                    <th scope="col">{{__('table.Name')}}</th>
                                    <th scope="col">{{__('table.Active')}}</th>
                                    <th scope="col">{{__('table.CreatedAt')}}</th>
                                    <th scope="col">{{__('table.CreatedBy')}}</th>
                                    <th scope="col">{{__('table.UpdatedAt')}}</th>
                                    <th scope="col">{{__('table.UpdatedBy')}}</th>
                                    <th scope="col">{{__('table.Actions')}}</th>
                                </tr>
                                </thead>
                                <tbody id="closeReasonTable">
                                @foreach($closeReasons as $closeReason)
                                    <tr>
                                        <td>{{ $closeReason->name }}</td>
                                        <td>{{ $closeReason->isActive() ? __('table.Yes') : __('table.No') }}</td>
                                        <td>{{ $closeReason->created_at != null ? $closeReason->created_at->format('d-m-Y H:i') : '' }}</td>
                                        <td>{{ $closeReason->getCreateAdmin() }}</td>
                                        <td>{{ $closeReason->updated_at != null ? $closeReason->updated_at->format('d-m-Y H:i') : '' }}</td>
                                        <td>{{ $closeReason->getUpdateAdmin() }}</td>
                                        <td class="button-div">
                                            <div class="button-actions">
                                                <x-btn-edit url="{{ route('admin.close-reasons.edit', $closeReason->close_reason_id) }}"/>
                                                <x-btn-delete url="{{ route('admin.close-reasons.delete', $closeReason->close_reason_id) }}"/>
                                                @if($closeReason->isActive())
                                                    <x-btn-disable url="{{ route('admin.close-reasons.disable', $closeReason->close_reason_id) }}"/>
                                                @else
                                                    <x-btn-enable url="{{ route('admin.close-reasons.enable', $closeReason->close_reason_id) }}"/>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                                <tfoot>
                                <tr id="pagination-nav">
                                    <td colspan="8">
                                        {{ $closeReasons->links() }}
                                    </td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="row">
        <div id="buttons">
            <x-btn-edit url="{{ route('admin.close-reasons.edit', 0) }}"/>
            <x-btn-delete url="{{ route('admin.close-reasons.delete', 0) }}"/>
            <x-btn-disable url="{{ route('admin.close-reasons.disable', 0) }}"/>
            <x-btn-enable url="{{ route('admin.close-reasons.enable', 0) }}"/>
        </div>
    </div>
@endsection
@push('scripts')
    <script type="text/javascript" src="{{ asset('js/pagination.js') }}"></script>
    <script type="text/javascript" src="{{ asset('js/customGrid.js') }}"></script>

    <script>
        loadDateRangePicker($("#createdAt, #updatedAt"));

        let urls = {
            list: "{{ route('admin.close-reasons.list') }}",
            cleanFilters: "{{ route('admin.close-reasons.cleanFilters') }}",
            setFilters: "{{ route('admin.close-reasons.setFilters') }}",
            refresh: "{{ route('admin.close-reasons.refresh') }}",
        };
        let translations = {
            yes: "{{__('table.Yes')}}",
            no: "{{__('table.No')}}"
        };
        let data = [
            'name',
            'active',
            'created_at',
            'creator',
            'updated_at',
            'updater',
        ];
        loadGrid(
            "closeReasonForm",
            "closeReasonTable",
            "close_reason_id",
            urls,
            data,
            "pagination-nav",
            translations
        );
    </script>
@endpush
