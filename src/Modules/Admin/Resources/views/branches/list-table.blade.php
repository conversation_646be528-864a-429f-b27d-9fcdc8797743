@foreach($branches as $branch)
    <tr
        @if(!$branch->active)
        class="not-active"
        @endif
    >
        <td>{{ $branch->name }}</td>
        <td>{{ $branch->isActive() ? __('table.Yes') : __('table.No')  }}</td>
        <x-timestamps :model="$branch"/>
        <td class="button-div">
            <div class="button-actions">
                <x-btn-edit
                    url="{{ route('admin.branches.edit',
                                     $branch->branch_id) }}"/>
                <x-btn-delete
                    url="{{ route('admin.branches.delete', $branch->branch_id) }}"/>
                @if($branch->isActive())
                    <x-btn-disable
                        url="{{ route('admin.branches.disable', $branch->branch_id) }}"/>
                @else
                    <x-btn-enable
                        url="{{ route('admin.branches.enable', $branch->branch_id) }}"/>
                @endif
            </div>
        </td>
    </tr>
@endforeach
<tr id="pagination-nav">
    <td colspan="7">
        {{ $branches->links() }}
    </td>
</tr>
