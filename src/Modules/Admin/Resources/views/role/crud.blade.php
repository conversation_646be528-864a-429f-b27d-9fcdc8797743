@extends('layouts.app')

@section('style')
    <link rel="stylesheet" href="{{ asset('css/admin-profile-styles.css') }}">
    <style>
        #Admin {
            margin: 0 !important;
        }
    </style>
@endsection

@section('content')
    @if($errors->has)
        @foreach($errors->all as $error)
            <div>{{$error}}</div>
        @endforeach
    @endif
    @php
        $action = !empty($role) ?
                    route('admin.roles.update', $role->id) :
                    route('admin.roles.store');
    @endphp

    <form method="POST"
          action="{{ $action }}"
          accept-charset="UTF-8" class="col-12" enctype='multipart/form-data'>
        @csrf
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="form-group">
                            <label for="name" class="control-label required">{{ __('table.Name') }}</label>
                            <input class="form-control" required="required" minlength="5"
                                   value="{{ old('name') ?? ($role->name ?? '')}}"
                                   name="name" type="text" id="name">
                        </div>
                        <div class="form-group">
                            <label for="name" class="control-label required">{{ __('table.Description') }}</label>
                            <input class="form-control" minlength="5"
                                   value="{{ old('description') ?? ($role->description ?? '')}}"
                                   name="description" type="text" id="description">
                        </div>
                        <div class="form-group">
                            <label for="name" class="control-label required">{{ __('table.Priority') }}</label>
                            <input class="form-control" required="required" minlength="1"
                                   value="{{ old('priority') ?? ($role->priority ?? '')}}"
                                   maxlength="@php \Auth::user()->getMaxPriority() @endphp"
                                   name="priority" type="number" id="priority">
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <h3 class="float-left">{{__('table.Permissions')}}
                                </h3>
                                <div class="float-right">
                                    @if(!empty($role))
                                        <x-button-bottom-bar
                                                url="{{route('admin.roles.list')}}"
                                                saveEditName="{{ __('btn.Update') }}"
                                                cancelName="{{ __('btn.Cancel') }}"
                                        />
                                    @else
                                        <x-button-bottom-bar
                                                url="{{route('admin.roles.list')}}"
                                                saveEditName="{{ __('btn.Create') }}"
                                                cancelName="{{ __('btn.Cancel') }}"
                                        />
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="form-check pl-1">
                                    <input type="checkbox" id="selectAllControllers" class="">
                                    <label for="selectAllControllers"><strong>Select all</strong></label><br>
                                </div>
                            </div>
                        </div>

                        @foreach($permissionsByGroups as $moduleName => $columns)
                            <div class="mb-4 allCheckBoxCont">
                                <div class="col-lg-12">
                                    <div class="form-check-inline">
                                        <input id="select{{$moduleName}}"
                                               type="checkbox"
                                               name="roles[]"
                                               data-module="{{$moduleName}}"
                                               value=""
                                               class="roleSelector"
                                        >
                                    </div>
                                    <a class="btn btn-collapse" style="width: 180px;" data-toggle="collapse"
                                       href="#{{$moduleName}}" role="button"
                                       aria-expanded="false"
                                       aria-controls="collapseExample">
                                        {{ $moduleName }} <i class="fa fa-play font-10"></i>
                                    </a>
                                </div>
                                <div class="row collapsing " id="{{$moduleName}}">
                                    @foreach($columns as $key => $value)
                                        <div class="col-lg-2 mt-3 mb-3">
                                            <div class="w-100">
                                                <div class="form-check ">
                                                    <input type="checkbox" id="{{ $moduleName.$key }}"
                                                           data-controller="{{ $moduleName.$key }}"
                                                           data-module="{{$moduleName}}"
                                                           class="selectByController classSelector">
                                                    <label
                                                            for="{{ $moduleName.$key }}"
                                                            class="sibling-text"><strong>{{ str_replace('Controller', '', $key)}}</strong></label>
                                                    <i class="toggle-icon-state fa fa-play font-10 mr-2"
                                                       data-module="{{$moduleName.$key}}"
                                                       aria-hidden="true"></i>
                                                </div>
                                                <div id="box{{ $moduleName.$key }}" class="hiden-checkbox">
                                                    @foreach($value as $controller)
                                                        <div class="form-check" title="{{$controller->info_bubble}}">
                                                            <input
                                                                    data-controller="{{$moduleName.$key}}"
                                                                    data-module="{{$moduleName}}"
                                                                    type="checkbox"
                                                                    id="{{$moduleName.$controller->id }}"
                                                                    name="permissions[]"
                                                                    class="permission"
                                                                    value="{{$controller->id}}"
                                                                    @if( !empty($role) ? $role->permissions->contains($controller) : false )
                                                                        checked="checked"
                                                                    @endif
                                                            >
                                                            <label class="ml-1" style="display: inline;cursor: pointer;"
                                                                   for="{{ $moduleName.$controller->id }}">
                                                                {{ $controller->description }}
                                                            </label>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </form>
@endsection

@push('scripts')
    <script src="{{ asset('dist/js/basic-roles-and-permission-checkbox.js') }}"></script>
    <script>
        getCheckController();
    </script>
@endpush
