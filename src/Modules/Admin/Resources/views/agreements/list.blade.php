@extends('layouts.app')

@section('content')
    @php
        /**
* @var \Modules\Common\Models\Agreement $agreement
 */
    @endphp
    <x-card-filter-form :filter-form="$agreementFilterForm"/>

    <x-card>
        <div class="form-group">
            <x-btn-create url="{{ route('admin.agreements.create') }}" name="{{ __('btn.Create') }}"/>
        </div>
        <!-- End ./form-group -->

        <x-table>
            <x-slot:head>
                <tr>
                    <th>{{__('table.Name')}}</th>
                    <th>{{__('table.Description')}}</th>
                    <th>{{__('table.Active')}}</th>
                    <th>{{__('table.CreatedAt')}}</th>
                    <th>{{__('table.CreatedBy')}}</th>
                    <th>{{__('table.UpdatedAt')}}</th>
                    <th>{{__('table.UpdatedBy')}}</th>
                    <th>{{__('table.Actions')}}</th>
                </tr>
            </x-slot:head>

            @foreach($agreements as $agreement)
                <tr
                        @if(!$agreement->active)
                            class="not-active"
                        @endif
                >
                    <td>{{ $agreement->name }}</td>
                    <td>{{ $agreement->description }}</td>
                    <td>{{ $agreement->isActive() ? __('table.Yes') : __('table.No') }}</td>
                    <td>{{ $agreement->created_at != null ? $agreement->created_at->format('d-m-Y H:i') : '' }}</td>
                    <td>{{ $agreement->getCreateAdmin() }}</td>
                    <td>{{ $agreement->updated_at != null ? $agreement->updated_at->format('d-m-Y H:i') : '' }}</td>
                    <td>{{ $agreement->getUpdateAdmin() }}</td>
                    <td>
                        <x-btn-edit url="{{ route('admin.agreements.edit', $agreement->getKey()) }}"/>
                        <x-btn-delete url="{{ route('admin.agreements.delete', $agreement->getKey()) }}"/>
                        @if($agreement->isActive())
                            <x-btn-disable url="{{ route('admin.agreements.disable', $agreement->getKey()) }}"/>
                        @else
                            <x-btn-enable url="{{ route('admin.agreements.enable', $agreement->getKey()) }}"/>
                        @endif
                    </td>
                </tr>
            @endforeach
        </x-table>

        <x-table-pagination :rows="$agreements"/>

    </x-card>

@endsection
