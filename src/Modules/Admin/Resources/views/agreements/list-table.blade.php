<table class="table">
    <thead>
    <tr>
        <th scope="col">{{__('table.Name')}}</th>
        <th scope="col">{{__('table.Description')}}</th>
        <th scope="col">{{__('table.Active')}}</th>
        <th scope="col">{{__('table.CreatedAt')}}</th>
        <th scope="col">{{__('table.CreatedBy')}}</th>
        <th scope="col">{{__('table.UpdatedAt')}}</th>
        <th scope="col">{{__('table.UpdatedBy')}}</th>
        <th scope="col">{{__('table.Actions')}}</th>
    </tr>
    </thead>
    <tbody>
    @foreach($agreements as $agreement)
        <tr
            @if(!$agreement->active)
            class="not-active"
            @endif
        >
            <td>{{ $agreement->name }}</td>
            <td>{{ $agreement->description }}</td>
            <td>{{ $agreement->isActive() ? __('table.Yes') : __('table.No') }}</td>
            <td>{{ $agreement->created_at != null ? $agreement->created_at->format('d-m-Y H:i') : '' }}</td>
            <td>{{ $agreement->getCreateAdmin() }}</td>
            <td>{{ $agreement->updated_at != null ? $agreement->updated_at->format('d-m-Y H:i') : '' }}</td>
            <td>{{ $agreement->getUpdateAdmin() }}</td>
            <td class="button-div">
                <div class="button-actions">
                    <x-btn-edit url="{{ route('admin.agreements.edit', $agreement->agreement_id) }}"/>
                    <x-btn-delete url="{{ route('admin.agreements.delete', $agreement->agreement_id) }}"/>
                    @if($agreement->isActive())
                        <x-btn-disable url="{{ route('admin.agreements.disable', $agreement->agreement_id) }}"/>
                    @else
                        <x-btn-enable url="{{ route('admin.agreements.enable', $agreement->agreement_id) }}"/>
                    @endif
                </div>
            </td>
        </tr>
    @endforeach
    </tbody>
    <tfoot>
    <tr id="pagination-nav">
        <td colspan="8">
            {{ $agreements->links() }}
        </td>
    </tr>
    </tfoot>
</table>
