@foreach($settings as $setting)
    <tr
        @if(!$setting->active)
        class="not-active"
        @endif
    >
        <td>{{ $setting->name }}</td>
        <td>{{ $setting->description }}</td>
        <td>{{ $setting->default_value }}</td>
        <td>{{ $setting->settingType->name }}</td>
        <td>{{ $setting->isActive() ? __('table.Yes') : __('table.No') }}</td>
        <x-timestamps :model="$setting"/>
        <td class="button-div">
            <div class="button-actions">

                <x-btn-edit
                    url="{{ route('admin.settings.edit', $setting->setting_key) }}"/>
                <x-btn-delete
                    url="{{ route('admin.settings.delete', $setting->setting_key) }}"/>
                @if($setting->isActive())
                    <x-btn-disable
                        url="{{ route('admin.settings.disable', $setting->setting_key) }}"/>
                @else
                    <x-btn-enable
                        url="{{ route('admin.settings.enable', $setting->setting_key) }}"/>
                @endif
            </div>

        </td>
    </tr>
@endforeach
<tr id="pagination-nav">
    <td colspan="10">
        {{ $settings->links() }}
    </td>
</tr>
