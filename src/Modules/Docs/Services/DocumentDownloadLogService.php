<?php

namespace Modules\Docs\Services;

use Auth;
use Illuminate\Support\Carbon;
use Modules\Common\Services\BaseService;
use Modules\Docs\Repositories\DocumentDownloadLogRepository;
use Modules\Docs\Repositories\DocumentRepository;

class DocumentDownloadLogService extends BaseService
{
    private DocumentDownloadLogRepository $documentTemplateRepository;
    private DocumentRepository $documentRepository;

    public function __construct(
        DocumentDownloadLogRepository $documentTemplateRepository,
        DocumentRepository $documentRepository
    ) {
        $this->documentTemplateRepository = $documentTemplateRepository;
        $this->documentRepository = $documentRepository;
        parent::__construct();
    }

    /**
     * @param $documentId
     *
     * @return mixed
     */
    public function create($documentId)
    {
        $document = $this->documentRepository->getById($documentId);

        $data['document_id'] = $document->document_id;
        $data['variables'] = $document->variables;
        $data['downloaded_by'] = Auth::user()->administrator_id;
        $data['downloaded_at'] = Carbon::now();
        $this->documentTemplateRepository->create($data);

        return $document;
    }

    /**
     * @param $documentId
     *
     * @return mixed
     */
    public function download($documentId)
    {
        $document = $this->documentRepository->getById($documentId);

        return public_path('/') . $document->file->file_path . $document->file->file_name;
    }

}
