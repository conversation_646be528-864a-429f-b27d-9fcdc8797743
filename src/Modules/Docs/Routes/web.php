<?php

use Illuminate\Support\Facades\Route;
use Modules\Docs\Http\Controllers\ClientDocumentController;
use Modules\Docs\Http\Controllers\DocumentController;
use Modules\Docs\Http\Controllers\DocumentDownloadLogController;
use Modules\Docs\Http\Controllers\DocumentTemplateController;

Route::group(['middleware' => ['auth']], function () {
    Route::prefix('docs')->group(function () {
        $idPattern = '[1-9][0-9]{0,9}';

        Route::get('/document-templates/vars', [DocumentTemplateController::class, 'varsTest'])
            ->name('docs.documentTemplate.varsTest')
            ->defaults('description', 'Check all variables for a given loan')
            ->defaults('module_name', 'User Settings')
            ->defaults('controller_name', 'Documents')
            ->defaults('info_bubble', 'Вижда страница Тест променливи. Може да подаде кредит ИД, за да види променливите по кредита.');

        Route::get('/document-templates', [DocumentTemplateController::class, 'list'])
            ->name('docs.documentTemplate.list')
            ->defaults('description', 'View Documents page')
            ->defaults('module_name', 'User Settings')
            ->defaults('controller_name', 'Documents')
            ->defaults('info_bubble', 'Вижда страница Документи');

        Route::get('/document-template/create', [DocumentTemplateController::class, 'create'])
            ->name('docs.documentTemplate.create')
            ->defaults('module_name', 'User Settings')
            ->defaults('controller_name', 'Documents')
            ->defaults('description', 'Create document template');

        Route::post('/document-template/store', [DocumentTemplateController::class, 'store'])
            ->name('docs.documentTemplate.store');

        Route::get('/document-template/edit/{documentTemplate}', [DocumentTemplateController::class, 'edit'])
            ->name('docs.documentTemplate.edit')
            ->where('id', $idPattern)
            ->defaults('module_name', 'User Settings')
            ->defaults('controller_name', 'Documents')
            ->defaults('description', 'Update document template');

        Route::get('/document-template/editHtml/{documentTemplate}', [DocumentTemplateController::class, 'editHtml'])
            ->name('docs.documentTemplate.editHtml')
            ->where('id', $idPattern);

        Route::post('/document-template/update/{documentTemplate}', [DocumentTemplateController::class, 'update'])
            ->name('docs.documentTemplate.update')
            ->where('id', $idPattern);

        Route::get('/document-template/delete/{documentTemplate}', [DocumentTemplateController::class, 'delete'])
            ->name('docs.documentTemplate.delete')
            ->where('id', $idPattern)
            ->defaults('module_name', 'User Settings')
            ->defaults('controller_name', 'Documents')
            ->defaults('description', 'Delete document template');

        Route::get('/document-template/enable/{documentTemplate}', [DocumentTemplateController::class, 'enable'])
            ->name('docs.documentTemplate.enable')
            ->where('id', $idPattern)
            ->defaults('module_name', 'User Settings')
            ->defaults('controller_name', 'Documents')
            ->defaults('description', 'Enable document template');

        Route::get('/document-template/disable/{documentTemplate}', [DocumentTemplateController::class, 'disable'])
            ->name('docs.documentTemplate.disable')
            ->where('id', $idPattern)
            ->defaults('module_name', 'User Settings')
            ->defaults('controller_name', 'Documents')
            ->defaults('description', 'Disable document template');

        // Document template session
        Route::get('/document-template/filters', [DocumentTemplateController::class, 'getFilters'])
            ->name('docs.documentTemplate.getFilters')
            ->defaults('description', 'Get document template filters');
        Route::put('/document-template/filters', [DocumentTemplateController::class, 'setFilters'])
            ->name('docs.documentTemplate.setFilters')
            ->defaults('description', 'Update document template filters');
        Route::delete('/document-template/filters', [DocumentTemplateController::class, 'cleanFilters'])
            ->name('docs.documentTemplate.cleanFilters')
            ->defaults('description', 'Cleanup filters');
        Route::get('/document-template/refresh', [DocumentTemplateController::class, 'refresh'])
            ->name('docs.documentTemplate.refresh')
            ->defaults('description', 'Ajax refresh document template table');

        Route::get('/document/sef-preview', [DocumentController::class, 'sefPreview'])
            ->name('docs.document.sefPreview');

        Route::post('/document/generateDocument', [DocumentController::class, 'generateDocument'])
            ->name('docs.document.generateDocument');

        Route::get('/document-download/{id}', [DocumentDownloadLogController::class, 'createAndDownload'])
            ->name('docs.documentDownloadLog.create')
            ->where('id', $idPattern);

        Route::post('/clientCard/upload-document', [ClientDocumentController::class, 'upload'])
            ->name('head.clientCard.uploadDocument')
            ->defaults('description', 'Upload document')
            ->defaults('module_name', 'Client Card')
            ->defaults('controller_name', 'Documents')
            ->defaults('info_bubble', 'Може да качва документ към досието на клиента от клиентска карта таб Документи');

        Route::get('/document-template/copy/{documentTemplate}', [DocumentTemplateController::class, 'createCopy'])
            ->name('docs.documentTemplate.copy')
            ->where('id', $idPattern)
            ->defaults('module_name', 'User Settings')
            ->defaults('controller_name', 'Documents')
            ->defaults('description', 'Duplicate document template');
    });
});

