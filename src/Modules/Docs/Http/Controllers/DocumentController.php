<?php

namespace Modules\Docs\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Exceptions\NotFoundException;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\DocumentTemplate;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Product;
use Modules\Docs\Http\Requests\DocumentGenerateAllDocumentsRequest;
use Modules\Docs\Http\Requests\DocumentGenerateRequest;
use Modules\Docs\Services\DocumentService;
use StikCredit\Calculators\Installments\DefaultInstallment;

class DocumentController extends BaseController
{
    protected DocumentService $documentService;

    protected string $pageTitle = 'Document template list';
    protected string $indexRoute = 'docs.documentTemplate.list';

    public function __construct(DocumentService $documentService)
    {
        $this->documentService = $documentService;
        parent::__construct();
    }

    public function sefPreview(Request $request, CurrentDate $currentDate): Response
    {
        $data = $request->validate([
            'productId' => 'required|integer|exists:' . Product::getTableName() . ',product_id',
            'amount' => 'required|numeric',
            'period' => 'required|numeric',
            'discount' => 'required|numeric',
        ]);

        /**
         * @var Product $product
         */
        $product = Product::whereProductId($data['productId'])->first();
        $installmentModifier = $product->getInstallmentModifier();
        $requestAmount = floatToInt($data['amount']);
        $loan_interest_rate = $product->getInterestRate($data['period'], $requestAmount);

        $documentTemplate = $product->documentTemplates()->where('document_template_type', 'sef')->first();
        $documentTemplate = $documentTemplate?->documentTemplate;
        if (!$documentTemplate) {
            throw new \Exception('No available SEF for this product');
        }

        $installmentsCount = 1;
        if (!$product->isPayday()) {
            $installmentsCount = $data['period'];
        }
        $content = $documentTemplate->content;

        $loanCalcs = $this->loanInstallmentListPrincipalInterest(
            $product,
            $data['period'],
            $requestAmount,
            $data['discount'],
            $currentDate
        );

        $vars = [
            '{loan_gpr}' => calculateGpr($loan_interest_rate / 100, $data['discount'] / 100),
            '{loan_gpr_in_writing}' => numberToText($loan_interest_rate / 100, $data['discount'] / 100),
            '{company_registered_address}' => config('company.address'),
            '{loan_amount}' => $data['amount'],
            '{loan_amount_in_writing}' => numberToBgWords($data['amount']),
            '{loan_amount_in_writing_simple}' => numberToText($data['amount']),

            '{loan_term}' => $data['period'],
            '{loan_term_in_writing}' => numberToText($data['period']) . ' ' . trans_choice(
                'product::product.keys.' . $product->getPeriod(),
                // $product->getPeriod(),
                $data['period'],
                [],
                'bg'
            ),
            '{loan_payment_period}' => __('docs::document.periods.' . $product->getPeriod()),
            '{loan_instalment_count}' => $installmentsCount,
            '{loan_instalment_count_in_writing}' => numberToText($installmentsCount, false, true),
            '{loan_instalment_list_principal_interest}' => $loanCalcs['loan_instalment_list_principal_interest'],
            '{total_due_principal_interest}' => $loanCalcs['total_due_principal_interest'],
            '{total_due_principal_interest_in_writing}' => numberToText($loanCalcs['total_due_principal_interest']),
            '{total_due_interest}' => $loanCalcs['total_due_interest'],
            '{total_due_interest_in_writing}' => numberToText($loanCalcs['total_due_interest']),

            '{loan_interest_rate}' => $loan_interest_rate,
            '{loan_interest_rate_in_writing}' => numberToText($loan_interest_rate),
            '{loan_last_instalment_date}' => Carbon::now()->modify("+{$data['period']} {$installmentModifier}")->format(
                'd.m.Y'
            ),
            '{loan_penalty_sum}' => $loanCalcs['total_due_penalty'],
            '{loan_penalty_sum_in_writing}' => numberToText($loanCalcs['total_due_penalty']),
            '{application_date}' => Carbon::now()->format('d.m.Y'),
            '{client_first_name}' => '.................. ',
            '{client_middle_name}' => '.................. ',
            '{client_surname}' => '.................. ',
            '{client_pin}' => '.................. ',
            '{document_creation_date}' => Carbon::now()->format('d.m.Y'),
        ];

        $content = strtr($content, $vars);
        $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadView('docs::pdf.view', [
            'content' => $content
        ]);

        return $pdf->stream("SEF-" . date('YmdHis'));
    }

    private function loanInstallmentListPrincipalInterest(
        Product $product,
        int $period,
        int $amount,
        int $discount,
        CurrentDate $currentDate
    ): array {
        $calculator = $product->getCalculator($period, $amount, $discount, $currentDate);

        $str = '';
        $amount = 0;
        $total_due_interest = 0;
        $total_due_penalty = 0;
        $calculator->installmentsCollection()->map(
            function (DefaultInstallment $defaultInstallment) use (&$str, &$amount, &$total_due_interest,&$total_due_penalty) {
                $str .= '<div>' . formatDate($defaultInstallment->dueDate, 'd.m.Y') . ' ' .
                    intToFloat(($defaultInstallment->principal + $defaultInstallment->interest))
                    . ' лв.</div>';

                $amount += ($defaultInstallment->principal + $defaultInstallment->interest);
                $total_due_interest += $defaultInstallment->interest;
                $total_due_penalty += $defaultInstallment->penaltyAmount;
            }
        );

        return [
            'loan_instalment_list_principal_interest' => $str,
            'total_due_principal_interest' => intToFloat($amount),
            'total_due_interest' => intToFloat($total_due_interest),
            'total_due_penalty' => intToFloat($total_due_penalty),
        ];
    }

    /**
     * Used for tests only
     *
     * @throws NotFoundException
     * @throws ProblemException
     */
    public function generateDocument(DocumentGenerateRequest $request): RedirectResponse|BinaryFileResponse
    {
        // if (isProd()) {
        //     throw new \Exception('Manual generating is forbidden on production!');
        // }

        $params = $request->validated();
        $loanId = (int) $params['loan_id'];
        $documentTemplateId = (int) $params['document_template_id'];

        $loan = Loan::where('loan_id', $loanId)->first();
        if (!$loan?->getKey()) {
            return redirect()->back()->with('fail', 'No loan with provided id found');
        }

        $docTemplate = DocumentTemplate::where('document_template_id', $documentTemplateId)->first();
        if (empty($docTemplate->document_template_id)) {
            return redirect()->back()->with('fail', 'No template with id #' . $documentTemplateId);
        }


        if ($docTemplate->type == DocumentTemplate::TPL_LEGAL_INFO) {
            $documentService = app(DocumentService::class);

            $filePath = $documentService->generatePdfDocumentForLoanByTemplate(
                $loan,
                $docTemplate
            );

            if ($filePath && file_exists(storage_path($filePath))) {
                $fileName = $docTemplate->name . '_loan_' . $loan->loan_id . '_' . time() . '.pdf';
                return response()->download(storage_path($filePath), $fileName);
            }

            return redirect()->back()->with('fail', 'Failed to generate document');
        }

        $url = $this->documentService->generatePdfDocumentForLoanByTemplateId(
            $loan,
            $documentTemplateId,
            true,
        );

        return $url ? redirect($url) : redirect()->back();
    }
}
