<?php

declare(strict_types=1);

namespace Modules\Docs\Console;

use Exception;
use Illuminate\Support\Facades\Storage;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Enums\DiskEnum;

/**
 * php artisan script:docs:clean-document-pdf-folder
 */
final class CleanDocumentPdfFolderCommand extends CommonCommand
{
    protected $signature = 'script:docs:clean-document-pdf-folder';
    protected $description = 'Clean document pdf folder';

    public function handle(): void
    {
        $this->startLog($this->description);

        $storage = Storage::disk(DiskEnum::DocumentsPdf->value);

        $files = $storage->files();

        $total = count($files);
        $processed = 0;

        foreach ($files as $file) {
            try {
                $storage->delete($file);
                $processed++;
            } catch (Exception $e) {
                report($e);
                $this->error($e->getMessage());
            }
        }

        $this->finishLog(['Total: ' . $total, 'Processed: ' . $processed]);
    }
}
