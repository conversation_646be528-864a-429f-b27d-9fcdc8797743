<?php

namespace Modules\Docs\Domain\DataSource;

use Modules\Common\Domain\DomainModel;
use Modules\Common\Models\Guarant as DbModel;

class Guarantor extends DomainModel
{
    private ?DbModel $dbModel;

    public function buildFromExisting(?DbModel $dbModel): self
    {
        $this->dbModel = $dbModel;
        return $this;
    }
    public function dbModel(): DbModel
    {
        return $this->dbModel ?? new DbModel();
    }
}