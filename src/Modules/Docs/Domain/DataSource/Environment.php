<?php

namespace Modules\Docs\Domain\DataSource;

use Modules\Common\Domain\CurrentDate;
use Modules\Docs\Enums\PlaceholderEnum as PE;

class Environment implements DataSource
{
    public function __construct(private CurrentDate $currentDate)
    {
    }

    public function getValueByPlaceholder(PE $placeholder): mixed
    {
        return match ($placeholder) {
            PE::TODAY_DATE,
            PE::TODAY_DATE_2 => $this->currentDate->todayString(),
            PE::COMPANY_PIN => config('company.bulstat'),
            PE::COMPANY_NAME => config('company.name_bg'),
            PE::COMPANY_NAME_LATIN => config('company.name_latin'),
            PE::COMPANY_PHONE => config('company.phone'),
            PE::COMPANY_EMAIL => config('company.email'),
            PE::COMPANY_WEB_PAGE => config('company.website'),
            PE::COMPANY_SMS_LOGIN_URL => config('company.sms_login_url'),
            PE::COMPANY_REGISTERED_ADDRESS => config('company.address'),
            PE::COMPANY_OFFICE_IBAN => config('company.iban'),
            PE::COMPANY_EASY_PAY_PIN => config('company.easy_pay'),
            PE::EMPLOYEE_NAME => getAdmin()->getName(),
            PE::MANAGER_SIGNATURE_IMAGE => $this->getManagerImageUrl(),
            PE::BLOG_LINK => config('company.website') . 'blog',
            PE::HOW_TO_PAY_LINK => config('company.website') . 'kak-da-platya',
            PE::FREQUENTLY_QUESTIONS_LINK => config('company.website') . 'kak-raboti',
            PE::OFFICES_INFO_LINK => config('company.website') . 'contact-us',
            default => '',
        };
    }

    public function getManagerImageUrl(): string
    {
        return asset('images/docs/manager_signature.png');
    }

    public function isSet(): bool
    {
        return true;
    }
}
