<?php

namespace Modules\Docs\Domain\Exceptions;

use Modules\Common\Domain\Exceptions\DomainException;
use Modules\Docs\Enums\PlaceholderEnum;

class ResponsibleClassNotFound extends DomainException
{
    public function __construct(PlaceholderEnum $placeholderEnum)
    {
        $this->baseMessage = sprintf('Responsible class not found for enum ' . $placeholderEnum->name);
        parent::__construct(get_defined_vars());
    }
}
