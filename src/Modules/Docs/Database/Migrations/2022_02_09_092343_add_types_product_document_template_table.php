<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{

    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('ALTER TABLE product_document_template DROP CONSTRAINT IF EXISTS product_document_template_document_template_type_check;');

        Schema::table(
            'product_document_template',
            function ($table) {
                $table->string('document_template_type')->nullable()->change();
            }
        );
    }
};
