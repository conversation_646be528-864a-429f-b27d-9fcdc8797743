<?php

namespace Modules\Docs\Repositories;

use Modules\Common\Models\DocumentDownloadLog;
use Modules\Common\Repositories\BaseRepository;

class DocumentDownloadLogRepository extends BaseRepository
{
    protected DocumentDownloadLog $documentDownloadLog;

    public function __construct(
        DocumentDownloadLog $documentDownloadLog
    )
    {
        $this->documentDownloadLog = $documentDownloadLog;
    }

    /**
     * @param array $data
     *
     * @return DocumentDownloadLog
     */
    public function create(array $data)
    {
        $documentDownloadLog = new DocumentDownloadLog();
        $documentDownloadLog->fill($data);
        $documentDownloadLog->save();

        return $documentDownloadLog;
    }
}
