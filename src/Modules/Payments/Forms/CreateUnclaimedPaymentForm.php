<?php

namespace Modules\Payments\Forms;

use <PERSON>\LaravelFormBuilder\Field;
use Modules\Common\FilterForms\BaseForm;
use Modules\Common\Models\PaymentMethod;

class CreateUnclaimedPaymentForm extends BaseForm
{
    public function buildForm(): void
    {
        $this->add('amount', Field::TEXT, [
            'label' => __('table.Amount'),
            'attr' => [
                'required' => 'required',
                'data-amount-formatter' => 'true',
            ]
        ]);

        $this->add('trn_id', Field::TEXT, [
            'label' => __('table.Transaction'),
            'attr' => [
                'required' => 'required',
            ]
        ]);

        $this->add('details', Field::TEXTAREA, [
            'label' => __('table.Details'),
            'attr' => [
                'required' => 'required',
                'rows' => 5
            ]
        ]);


        $paymentMethods = [
            PaymentMethod::PAYMENT_METHOD_EASYPAY => PaymentMethod::PAYMENT_METHOD_EASYPAY_DESC,
            // PaymentMethod::PAYMENT_METHOD_BANK => PaymentMethod::PAYMENT_METHOD_BANK_DESC, // IF you add a bank add logic for it in getBankAccountIdByPaymentMethod()
        ];
        $selectedId = PaymentMethod::PAYMENT_METHOD_EASYPAY;
        $this->add('payment_method_id', Field::SELECT, [
            'label' => __('table.PaymentMethod'),
            'empty_value' => __('Select option'),
            'choices' => $paymentMethods,
            'selected' => $selectedId,
            'attr' => [
                'required' => 'required',
            ]
        ]);
    }
}
