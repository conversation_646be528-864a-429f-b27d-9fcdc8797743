<?php

use Illuminate\Support\Facades\Route;
use Modules\Payments\Http\Controllers\PaymentTaskDecisionController;

Route::group(['middleware' => ['auth']], function () {
    Route::prefix('payments')->group(function () {
        // Sale decision crud
        Route::get('/payment-task-decision', [PaymentTaskDecisionController::class, 'list'])
            ->name('payments.paymentTaskDecision.list');

        Route::get('/payment-task-decision/create', [PaymentTaskDecisionController::class, 'create'])
            ->name('payments.paymentTaskDecision.create');

        Route::post('/payment-task-decision/store', [PaymentTaskDecisionController::class, 'store'])
            ->name('payments.paymentTaskDecision.store');

        Route::get('/payment-task-decision/edit/{paymentTaskDecision}', [PaymentTaskDecisionController::class, 'edit'])
            ->name('payments.paymentTaskDecision.edit');

        Route::post(
            '/payment-task-decision/update/{paymentTaskDecision}',
            [PaymentTaskDecisionController::class, 'update']
        )
            ->name('payments.paymentTaskDecision.update');

        Route::get(
            '/payment-task-decision/delete/{paymentTaskDecision}',
            [PaymentTaskDecisionController::class, 'delete']
        )
            ->name('payments.paymentTaskDecision.delete');

        Route::get(
            '/payment-task-decision/enable/{paymentTaskDecision}',
            [PaymentTaskDecisionController::class, 'enable']
        )
            ->name('payments.paymentTaskDecision.enable');

        Route::get(
            '/payment-task-decision/disable/{paymentTaskDecision}',
            [PaymentTaskDecisionController::class, 'disable']
        )
            ->name('payments.paymentTaskDecision.disable');
    }
    );
}
);
