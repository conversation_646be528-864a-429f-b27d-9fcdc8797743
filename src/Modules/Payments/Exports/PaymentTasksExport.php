<?php

namespace Modules\Payments\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PaymentTasksExport implements FromCollection, WithHeadings, WithColumnWidths, WithStyles
{
    /**
     * @var Collection
     */
    private Collection $rows;

    /**
     * LoanExport constructor.
     *
     * @param Collection $rows
     */
    public function __construct(Collection $rows)
    {
        $this->rows = $rows;
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return $this->rows;
    }

    public function headings(): array
    {
        return [
            __('table.Id'),
            __('table.Type'),
            __('table.Trans'),
            __('table.Source'),
            __('table.Amount'),
            __('table.clientNames'),
            __('table.Pin'),
            __('table.Phone'),
            __('table.CreatedAt'),
            __('table.Timer'),
            __('table.Status'),
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 7,
            'B' => 9,
            'C' => 12,
            'D' => 10,
            'E' => 10,
            'F' => 20,
            'G' => 12,
            'H' => 12,
            'I' => 15,
            'J' => 10,
            'K' => 9,
        ];
    }

    public function styles(Worksheet $sheet)
    {
//        foreach ($sheet->getColumnIterator('K', 'L') as $column) {
//            $column->getWorksheet()
//                ->getStyle($column->getColumnIndex().'1:'.$column->getColumnIndex().$sheet->getHighestDataRow())
//                ->getAlignment()
//                ->setWrapText(true);
//        }
    }
}
