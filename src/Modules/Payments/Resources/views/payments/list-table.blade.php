@php
    /**
     * @var \Modules\Common\Models\Payment $payment
     */
@endphp
<x-table>
    <x-slot:head>
        <tr>
            <th>{{__('table.Id')}}</th>
            <th>{{__('table.Office')}}</th>
            <th>{{__('table.FilterByDocCreatedAt')}}</th>
            <th>{{__('table.paymentSum')}}</th>
            <th>{{__('table.source')}}</th>
            <th>{{__('table.numberLoan')}}</th>
            <th>{{__('table.ClientFullName')}}</th>
            <th>{{__('table.Pin')}}</th>
            <th>{{__('table.reasonPayment')}}</th>
            <th>{{__('table.Status')}}</th>
            <th>{{__('table.processed')}}</th>
            <th class="text-center">
                <i class="fa fa-xl fa-close"></i>
            </th>
        </tr>
    </x-slot:head>

    @foreach($payments as $payment)
        <tr>
            <td class="text-center">
                <a href="{{route('payment.payments.details', $payment->getKey())}}" target="_blank">
                    {{ $payment->getKey() }}
                </a>
            </td>
            <td>{{ $payment->office?->name }}</td>
            <td>{{formatDate($payment->created_at, 'd.m.Y H:i:s')}}</td>
            <td class="{{$payment->isIncoming() && $payment->amount > 0 ? 'text-success' : 'text-danger'}}">
                {{ amount($payment->amount,'') }}
            </td>
            <td>{{ $payment->bankAccount?->name }}</td>
            <td>
                @if(!empty($payment->client_id) && !empty($payment->loan_id))
                    <a href="{{ route('head.clients.cardProfile', ['clientId' => $payment->client_id, 'second' => $payment->loan_id, '#paymentschedule']) }}"
                       target="_blank"
                    >
                        {{ $payment->loan_id }}
                    </a>
                @else
                    {{ $payment->loan_id }}
                @endif
            </td>
            <td style="min-width:200px;">{{$payment->client?->getFullName()}}</td>
            <td>{{$payment->client?->pin}}</td>
            <td style="max-width: 200px">{{$payment->description}}</td>
            <td class="{{$payment->isActive() ? 'text-success' : 'text-danger'}}">
                {{ $payment->isActive() ? __('table.Active') : __('table.Deleted') }}
            </td>
            <td>{{$payment->getProcessLabel()}}</td>
            <td style="font-weight: 500; text-align: center;">
                @if($payment->canBeRefunded())
                    <a href="{{ route('payment.refundPage', ['payment' => $payment]) }}" class="delete-payment">
                        <i class="fa fa-xl fa-close text-danger" title="Сторно на плащането"></i>
                    </a>
                @endif
                @if($payment->canBeDeleted())
                    <a href="{{ route('payment.payment.rollback', ['payment' => $payment]) }}" class="delete-payment">
                        <i class="fa fa-xl fa-close text-danger" title="Изтрий плащането"></i>
                    </a>
                @endif
            </td>
        </tr>
    @endforeach

</x-table>

<x-table-pagination :rows="$payments"/>

@push('scripts')
    <script>
        $(document).ready(function () {
            $(document).on('click', '.delete-payment', function (e) {
                if (!confirm('Сигурно ли искаш да изтриеш плащането?')) {
                    e.preventDefault();
                }
            });
        });
    </script>
@endpush
