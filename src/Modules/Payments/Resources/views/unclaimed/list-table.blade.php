@php
    /**
     * @var \Modules\Common\Models\UnclaimedMoney $row
     */
@endphp
<x-table>
    <x-slot:head>
        <tr>
            <th>{{__('table.Id')}}</th>
            <th>{{__('table.FilterByDocCreatedAt')}}</th>
            <th>{{__('table.unclaimedSum')}}</th>
            <th>{{__('table.unclaimedDirection')}}</th>
            <th>{{__('table.PaymentMethod')}}</th>
            <th>{{__('table.Transaction')}}</th>
            <th>{{__('table.Details')}}</th>
            <th>{{__('table.UnclaimedMoneyReturn')}}</th>
            <th>{{__('table.Actions')}}</th>
        </tr>
    </x-slot:head>

    @foreach($unclaimedMoneyRows as $row)
        <tr>
            <td>{{ $row->id }}</td>
            <td>{{ formatDate($row->created_at, 'd.m.Y H:i:s') }}</td>
            <td>{{ amount($row->amount) }}</td>
            <td>{{ $row->direction }}</td>
            <td>{{ $row->payment?->paymentMethod?->name }}</td>
            <td>{{ $row->payment?->document_number }}</td>
            <td>{{ $row->details }}</td>
            <td>{{ ($row->hasReturnRow() ? 'Да' : 'Не') }}</td>
            <td>
                @if($row->canBeReturned())
                    <a href="{{route('payment.unclaimed_money.storeReturnMoneyRequest', $row->getKey())}}"
                       class="btn btn-primary w-10">
                        {{__('Направи връщане')}}
                    </a>
                @endif
            </td>
        </tr>
    @endforeach

</x-table>

<x-table-pagination :rows="$unclaimedMoneyRows"/>
