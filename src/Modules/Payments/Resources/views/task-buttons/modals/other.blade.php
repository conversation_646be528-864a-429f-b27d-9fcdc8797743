@php
    $route  = route('payment.paymentTasks.completePaymentTask', ['paymentTask' => !empty($paymentTask) ? $paymentTask->getKey() : 0]);

    $loanId = (!empty($paymentTask) ? $paymentTask->loan_id : 0);
@endphp

<div id="addCommentModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <h4 class="font-weight-bold p-2">Причина за приключване на задачата</h4>

                <form id="addCommentDecisionForm"
                      action="{{$route}}" method="POST" class="p-2">
                    @csrf
                    <input type="hidden" name="payment_task_decision_id" value="{{ \Modules\Common\Models\PaymentTaskDecision::PAYMENT_TASK_DECISION_ID_OTHER }}">
                    <input type="hidden" name="payment_task_id" value="{{ !empty($paymentTask) ? $paymentTask->getKey() : 0 }}">
                    <div class="form-group">
                        <textarea id="decisionComment" name="comment" placeholder="Коментар"
                                  class="form-control btn-round-8px no-read-only" rows="5"></textarea>
                    </div>
                    <div class="w-100 d-flex justify-content-center">
                        <button type="button" class="btn btn-danger primary-ch-btn p-2 font-14 col-3 m-3"
                                data-dismiss="modal">{{ __('btn.Close') }}</button>
                        <button type="submit" name="action"
                                class="btn btn-success primary-ch-btn p-2 font-14 col-3 m-3">{{ __('btn.Update') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

