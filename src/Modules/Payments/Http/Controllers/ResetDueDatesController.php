<?php

namespace Modules\Payments\Http\Controllers;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\RedirectResponse;
use Modules\Common\Http\Controllers\BaseController;
use Modules\Common\Models\Loan;
use Modules\Payments\Application\Actions\ResetDueDatesAction;
use Modules\Payments\Http\Requests\ResetDueDatesRequest;

class ResetDueDatesController extends BaseController
{
    public function __invoke(
        Loan $loan,
        ResetDueDatesRequest $request,
        ResetDueDatesAction  $action
    ): RedirectResponse {

        DB::beginTransaction();
        try {

            $data = $request->validated();
            $action->execute($loan, $data['new_date']);

            DB::commit();

            return $this->backSuccess('Датите на вноски са променени.')->withFragment('#paymentschedule');

        } catch (\Throwable $e) {
            DB::rollBack();

            return back()->with('fail', 'Грешка при смяна на датите на вноски. ' . $e->getMessage())->withFragment('#paymentschedule');
        }
    }
}
