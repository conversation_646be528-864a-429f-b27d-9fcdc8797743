<?php

namespace Modules\Payments\Http\Controllers;

use Modules\Common\Http\Controllers\BaseController;
use Modules\Payments\Application\Actions\EasypayLog\EasypayLogListDataAction;
use Modules\Payments\Http\Requests\EasypayLogRequest;
use Modules\Payments\Services\PaymentService;

class EasypayLogController extends BaseController
{
    public function __construct(private PaymentService $paymentService)
    {
        parent::__construct();
    }

    public function listRequest(
        EasypayLogRequest        $request,
        EasypayLogListDataAction $easypayLogListDataAction
    ) {
        $filters = $request->validated();
        $data = $easypayLogListDataAction->execute($filters);

        return view('payments::easypay.list-request', $data);
    }

    public function refreshRequest(EasypayLogRequest $request)
    {
        parent::setFiltersFromRequest($request);

        return view(
            'payments::easypay.list-request-table',
            [
                'cacheKey' => $this->cacheKey,
                'rows' => $this->getTableData(),
            ]
        )->render();
    }

    public function getTableData(int $limit = null)
    {
        return $this->paymentService->getEasypayRequestLogs(
            $limit ?? parent::getTableLength(),
            session($this->cacheKey, [])
        );
    }
}
