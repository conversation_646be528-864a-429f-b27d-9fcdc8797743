<?php

namespace Modules\Payments\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Common\Enums\PaymentDeliveryEnum;
use Modules\Common\Http\Requests\BaseRequest;

class LoadLoansInfoRequest extends BaseRequest
{
    public function rules(): array
    {
        return [
            'selectedLoanIds.*' => 'required|integer|exists:loan,loan_id',
            'payment_amount' => 'nullable|numeric',
            'loanAction.*' => [
                'required',
                'string',
                Rule::enum(PaymentDeliveryEnum::class)
            ],
        ];
    }
}
