<?php

namespace Modules\Payments\Domain\Exception;

use Modules\Common\Domain\Exceptions\DomainException;
use Modules\Common\Enums\Payment\PaymentMethodEnum;

class PaymentHasIncorrectMethod extends DomainException
{
    public function __construct(int $id, PaymentMethodEnum $method)
    {
        $this->baseMessage = sprintf('Payment #%d has incorrect method %s', $id, $method->value);
        parent::__construct(get_defined_vars());
    }
}