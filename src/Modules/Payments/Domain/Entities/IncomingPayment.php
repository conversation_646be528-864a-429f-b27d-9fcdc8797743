<?php

namespace Modules\Payments\Domain\Entities;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Accounting\Services\AccountingService;
use Modules\Approve\Domain\Entities\Loan\LoanForApproval;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Entities\PaymentSnapshot;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\Payment\PaymentPurposeEnum;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Enums\PaymentDeliveryEnum;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Enums\PaymentProblemEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Events\Payment\AfterPaymentDistribute;
use Modules\Common\Jobs\CreatePaymentWhenHaveOutstandingAfterExtendLoanJob;
use Modules\Common\Models\Client;
use Modules\Common\Models\Currency;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Payment as DbModel;
use Modules\Common\Models\PaymentTask;
use Modules\Common\Models\PaymentTaskDecision;
use Modules\Common\Models\UnclaimedMoney;
use Modules\Communication\Jobs\SendSmsPaymentWasReceived;
use Modules\Head\Repositories\ClientRepository;
use Modules\Head\Repositories\InstallmentRepository;
use Modules\Head\Repositories\LoanRepository;
use Modules\Payments\Application\Actions\DeleteLoanExpenseOnLoanRepayment;
use Modules\Payments\Application\Dto\NewPaymentDto;
use Modules\Payments\Domain\Entities\Loan\LoanForRepayment;
use Modules\Payments\Domain\Events\LoanPaymentWasReceivedForStats;
use Modules\Payments\Domain\Events\LoanPaymentWasReceivedNew;
use Modules\Payments\Domain\Events\LoanWasRepaid;
use Modules\Payments\Domain\Events\RefinancingPaymentWasCreated;
use Modules\Payments\Domain\Exception\PaymentNotSaved;
use Modules\Payments\Repositories\PaymentRepository as Repo;
use Modules\Payments\Services\PaymentService;

class IncomingPayment extends DomainModel implements PaymentInterface
{
    const BUFFER_AMOUNT = 20;

    private ?Client $client = null;
    private ?DbLoan $loan = null;
    private ?DbLoan $tmpLoanForSms = null; // used for sms notification, if client has many active loans we take the last one
    private bool $loanRepaid = false;
    private int $restAmount = 0; // when client pays more, we keep the rest here

    public function __construct(
        private DbModel                        $dbModel,
        private readonly Repo                  $repo,
        private readonly LoanRepository        $loanRepo,
        private readonly CurrentDate           $currentDate,
        private readonly LoanForRepayment      $loanForRepayment,
        private readonly InstallmentRepository $instRepo,
        private readonly IncomingTask          $task,
        private readonly ClientRepository      $clientRepo
    )
    {
    }

    public function new(): self
    {
        return app()->make(self::class);
    }

    /****************************BUILDERS****************************/

    // used in: CreatePaymentsFromLoanApprovalListener (ON refinance)
    // only create payment with status NEW
    // and do a snapshot
    public function buildFromDtoForRefinanceOnApprove(NewPaymentDto $dto): DbModel
    {
        $this->setLoan($dto->loan_id, $dto)
            ->setClientId($dto->client_id)
            ->setCurrencyId($dto->currency_id)
            ->setOffice($dto->office_id)
            ->setPaymentMethod($dto->method)
            ->setBankAccountId($dto->bank_account_id)
            ->setDirection()
            ->setDescription($dto->description)
            ->setAdmin($dto->created_by)
            ->setCreated($dto->created_by)
            ->setSource($dto->source)
            ->setAmount($dto->amount)
            ->validateAmount()
            ->setPurposeAndDeliveryTypeBasedOnLastInstalmentDate()
            ->setProblem(PaymentProblemEnum::NONE)
            ->setStatus(PaymentStatusEnum::NEW)
            ->save();


        // creates snapshots for payments
        RefinancingPaymentWasCreated::dispatch($this->loan, $this->dbModel);


        return $this->dbModel;
    }

    // used in: CancelIncomingRefinancePaymentsAction (for refinance)
    // set cancel status to payment
    // set who handle it
    // remove payment snapshot, since we no need it
    public function buildFromRefinancePaymentOnCancel(DbModel $payment): DbModel
    {
        $this->dbModel = $payment;

        $this->setStatus(PaymentStatusEnum::CANCELED)
            ->removeSnapshot()
            ->setHandled()
            ->save();

        return $this->dbModel;
    }

    // used in: ConfirmRefundAction (for refinance)
    // set cancel status to payment
    // set who delete it
    // restore loan/installments/taxes/stats by snapshot
    // remove payment distribution
    public function buildFromRefinancePaymentOnRestore(DbModel $payment): DbModel
    {
        $this->dbModel = $payment;

        $this->restoreBySnapshot();

        return $this->dbModel;
    }

    // used in: DeliverIncomingRefinancePaymentsAction
    // make payment delivered
    // close loan
    // create accunting row, cashdesk stuff, etc
    public function buildFromRefinancePaymentOnActivate(DbModel $payment, ?int $bankAccountId = null): DbModel
    {
        $this->dbModel = $payment;
        $this->loan = $payment->loan;

        // validations
        $this->validateAmount();

        // if on payment task handling for bank, agent change bank account, we should it reset here
        if (!empty($bankAccountId)) {
            $this->setBankAccountId($bankAccountId);
        }


        $this->setStatus(PaymentStatusEnum::RECEIVED)
            ->setHandled()
            ->save();


        // we always close prev loans
        $this->loanRepaid = true;


        // trying to pay, payment delivery, accounting row, update stats, etc
        if (!$this->tryToPay(true)) {
            throw new \Exception('Failed to distribute payment and etc');
        }


        return $this->dbModel;
    }

    public function buildRefundFromPayment(DbModel $payment): DbModel
    {
        if ($payment->shouldSkipAccountingPaymentCreation()) {

            $newPayment = $this->createRefundedPayment($payment);

            if (empty($newPayment->payment_id)) {
                throw new \Exception('Failed to save refund payment, with negative amount');
            }

            return $newPayment;
        }

        $accRow = $payment->getAccountingPayment();
        if (empty($accRow->id)) {
            throw new \Exception('Payment has no accounting row!');
        }

        $newPayment = $this->createRefundedPayment($payment);

        if (empty($newPayment->payment_id)) {
            throw new \Exception('Failed to save refund payment, with negative amount');
        }

        // deal with accounting row
        $newAccRow = AccountingService::addAccountingRowForRefundedPayment(
            $accRow,
            $newPayment->payment_id
        );

        if (empty($newAccRow->id)) {
            throw new \Exception('Failed to save refund accounting row, with negative amount');
        }

        return $newPayment;
    }

    private function createRefundedPayment($parentPayment)
    {
        // deal with payment, create a negative copy
        $newPayment = $parentPayment->replicate();

        $newPayment->created_at = now();
        $newPayment->updated_at = now();
        $newPayment->handled_at = now();

        $adminId = getAdminId();
        $newPayment->created_by = $adminId;
        $newPayment->updated_by = $adminId;
        $newPayment->handled_by = $adminId;

        $newPayment->description = 'Сторно операция';
        $newPayment->amount = -1 * $newPayment->amount;
        $newPayment->description = 'Сторно операция';
        $newPayment->source = PaymentSourceEnum::REFUNDED_PAYMENT;
        $newPayment->problem = PaymentProblemEnum::NONE;
        $newPayment->status = PaymentStatusEnum::DELIVERED;
        $newPayment->parent_payment_id = $parentPayment->payment_id;
        $newPayment->save();

        return $newPayment;
    }

    // used in: ManualPaymentSaveAction - for manual payment, where we've calculated amount and purpose in advance
    // Important 1: in case of error we throw it to agent
    // Important 2: we dont send sms here, since it could be multi loans payment,
    //              we will send it at the final, after looping all, in: ManualPaymentSaveAction
    // Important 3: rest amount we calculate in ManualPaymentSaveAction, and here just set it
    public function buildFromDtoForManualPayment(NewPaymentDto $dto): self
    {
        // Just setup, nothing more
        $this->setLoan($dto->loan_id, $dto)
            ->setClientId($dto->client_id)
            ->setCurrencyId($dto->currency_id)
            ->setOffice($dto->office_id)
            ->setPaymentMethod($dto->method)
            ->setBankAccountId($dto->bank_account_id)
            ->setDirection()
            ->setAdmin($dto->created_by)
            ->setCreated($dto->created_by)
            // flexible setup based on input params
            ->setSource($dto->source)
            ->setSourceId($dto)
            ->setDescription($dto->description, $dto)
            ->setDocumentNumber($dto)
            ->setRestAmount($dto->rest_amount)
            // all magic is here: purpose, delivery_type, problem, status, etc
            // in case of problem not create a task but throws an exception to agent
            ->setMainPropsWithThrow($dto);

        // save payment and continue with pay process
        $this->save();


        // check if payment saved and prepare it fresh state
        if (empty($this->dbModel->payment_id)) {
            throw new \Exception('Failed to save payment in DB');
        }


        // else we're trying to pay, payment delivery, accounting row, update stats, etc
        if (!$this->tryToPay(true)) {
            throw new \Exception('Failed to distribute payment and etc');
        }


        return $this;
    }

    // almost same as buildFromDtoForManualPayment(), but with setting payment and handle of payment task
    // used in: ManualPaymentSaveAction - for manual payment, where we've handle undistributed payment
    public function buildFromDtoForManualPaymentWithPaymentAndTask(
        NewPaymentDto $dto,
        int           $paymentId,
        int           $paymentTaskId
    ): self {

        $payment = DbModel::find($paymentId);
        if (empty($payment->payment_id)) {
            throw new \Exception('Failed to find undistributed payment');
        }
        if ($payment->status != PaymentStatusEnum::RECEIVED) {
            throw new \Exception('Payment is in wrong status, delivery aborted');
        }

        $paymentTask = PaymentTask::find($paymentTaskId);
        if (empty($paymentTask->payment_task_id)) {
            throw new \Exception('Failed to find payment task for undistributed payment');
        }
        if ($paymentTask->status == PaymentTask::TASK_STATUS_DONE) {
            throw new \Exception('PaymentTask is in wrong status, delivery aborted');
        }

        // setup model to existing payment
        $this->dbModel = $payment;

        // Just setup, nothing more
        $this->setLoan($dto->loan_id, $dto)
            ->setClientId($dto->client_id)
            ->setCurrencyId($dto->currency_id)
            ->setOffice($dto->office_id)
            ->setPaymentMethod($dto->method)
            ->setBankAccountId($dto->bank_account_id)
            ->setDirection()
            // flexible setup based on input params
            ->setSource($dto->source)
            ->setSourceId($dto)
            ->setDescription($dto->description, $dto)
            ->setDocumentNumber($dto)
            ->setRestAmount($dto->rest_amount)
            // all magic is here: purpose, delivery_type, problem, status, etc
            // in case of problem not create a task but throws an exception to agent
            ->setMainPropsWithThrow($dto);

        // save payment and continue with pay process
        $this->save();


        // check if payment saved and prepare it fresh state
        if (empty($this->dbModel->payment_id)) {
            throw new \Exception('Failed to save payment in DB');
        }


        // else we're trying to pay, payment delivery, accounting row, update stats, etc
        if (!$this->tryToPay(true)) {
            throw new \Exception('Failed to distribute payment and etc');
        }


        // finish the game by closing the task
        $this->closePaymentTask($paymentTask);


        return $this;
    }

    // used in: CreateBankTransferAction & CreateIncomingPaymentFromEasyPayApiListener
    // this output of the method is used in BankTransferAction
    // thats why directly return string
    // Important 1: in case of error we create payment task
    // Important 2: when we get payment and detect client & loan -> send sms
    // Important 3: rest amount we calculate on the fly, based on full repayment amount
    public function buildFromDtoForAutomaticPayment(NewPaymentDto $dto): string
    {
        // Just setup, nothing more
        $this->setLoan($dto->loan_id, $dto)
            ->setClientId($dto->client_id)
            ->setCurrencyId($dto->currency_id)
            ->setOffice($dto->office_id)
            ->setPaymentMethod($dto->method)
            ->setBankAccountId($dto->bank_account_id)
            ->setDirection()
            ->setAdmin($dto->created_by)
            ->setCreated($dto->created_by)
            // flexible setup based on input params
            ->setSource($dto->source)
            ->setSourceId($dto)
            ->setDescription($dto->description, $dto)
            ->setDocumentNumber($dto)
            // all magic is here: amount, purpose, delivery_type, problem, status, etc
            ->setMainPropsWithProblem($dto);

        // save payment, since base on it we create paymen task in case of problem OR continue with pay process
        $this->save();


        // in case of easypay payment attempt, we need to update its link to payment
        if (
            !empty($dto->easy_pay_request)
            && !empty($dto->easy_pay_request->easy_pay_attempt_id)
            && !empty($this->dbModel->payment_id)
        ) {
            $easyPayAttempt = $dto->easy_pay_request;
            try {

                $easyPayAttempt->payment_id = $this->dbModel->payment_id;
                $easyPayAttempt->save();

            } catch (\Throwable $e) {
                Log::channel('easypay')->error(
                    'IncomingPayment() - Failed to update payment_id , #' . $easyPayAttempt->easy_pay_attempt_id
                );
            }
        }


        $smsLoan = null;
        // if 1 active loan we take it
        if (!empty($this->loan->loan_id)) {
            $smsLoan = $this->loan;
        }
        // if more then 1 active loan, we use one of them for sms
        if (!empty($this->tmpLoanForSms->loan_id)) {
            $smsLoan = $this->tmpLoanForSms;
        }
        if (!empty($smsLoan->loan_id)) {
            // no matter status of payment(or task) if we received it, we send sms to client, that the money is on our side
            app(SendSmsPaymentWasReceived::class)->sendForLoan(
                $smsLoan,
                (float) intToFloat($dto->amount)
            );
        }


        // if we habe problem, lets create a payment task and exit
        if ($this->dbModel->problem != PaymentProblemEnum::NONE) {
            $this->processTask();

            return 'task';
        }


        // check if payment saved
        if (empty($this->dbModel->payment_id)) {
            Log::channel('paymentsModule')->error('Failed to save IN payment: ' . json_encode($dto));

            return 'error';
        }


        // else we're trying to pay, payment delivery, accounting row, update stats, etc
        if (!$this->tryToPay()) {
            return 'task';
        }


        return 'success';
    }

    /****************************SETTERS****************************/

    private function setLoan(?int $loanId, NewPaymentDto $dto): self
    {
        // nullate repaid state in the begin
        $this->loanRepaid = false;


        // on manual payment we have it
        $loan = $dto->loan;

        // on bank import we have loan_id sometimes
        if (!$loan && $loanId) {
            $loan = $this->loanRepo->getById($loanId);
        }

        // on easypay we have only client id, so we need to get the client last active loan
        if (!empty($dto->source) && $dto->source == PaymentSourceEnum::EASY_PAY_API && !empty($dto->client_id)) {
            $lastclientLoan = $this->loanRepo->getLastActiveLoanByClientId($dto->client_id);
        }

        // if no loan found -> exit do nothing for now
        if (!$loan) {
            return $this;
        }

        $this->loan = $loan;
        $this->dbModel->loan_id = $loan->getKey();

        return $this;
    }

    private function setClientId(?int $clientId = null, ?string $pin = null): self
    {
        // we always try to set loan before setting client!

        $client = $this->dbModel->client;
        if (!$client && $clientId) {
            $client = $this->clientRepo->getById($clientId);
        }

        if (!$client && $pin) {
            $client = $this->clientRepo->getByPin($pin);
        }

        if (!$client) {
            return $this;
        }


        // special case, mostly from bank import
        // if loan_id not come, but we found a client, we can take his last active loan
        // and set the loan!
        if (!empty($client->client_id) && empty($this->dbModel->loan_id)) {
            $loan = $client->getLastLoanByStatuses([LoanStatus::ACTIVE_STATUS_ID]);
            if (!empty($loan->loan_id)) {

                $this->loan = $loan;
                $this->dbModel->loan_id = $loan->getKey();
            }
        }

        $this->client = $client;
        $this->dbModel->client_id = $client->getKey();

        return $this;
    }

    private function setCurrencyId(?int $currencyId = null): self
    {
        $this->dbModel->currency_id = $currencyId ?: $this->dbLoan()?->currency_id;
        if (!$this->dbModel->currency_id) {
            $this->dbModel->currency_id = Currency::BGN_CURRENCY_ID;
        }
        return $this;
    }

    private function setOffice(?int $officeId = null): self
    {
        if (empty($officeId)) {
            throw new Exception('No office provided');
        }

        $this->dbModel->office_id = $officeId;

        return $this;
    }

    private function setPaymentMethod(PaymentMethodEnum $method): self
    {
        $this->dbModel->payment_method_id = $method->id();

        return $this;
    }

    private function setBankAccountId(?int $bankAccountId = null): self
    {
        $this->dbModel->bank_account_id = $bankAccountId;

        return $this;
    }

    private function setDirection(): self
    {
        $this->dbModel->direction = 'in';

        return $this;
    }

    private function setAdmin(int $adminId): self
    {
        $this->dbModel->created_by = $adminId;
        $this->dbModel->created_at = $this->currentDate->now()->toDateTimeString();

        return $this;
    }

    private function setCreated(int $adminId): self
    {
        if ($this->dbModel->exists) {
            return $this;
        }
        $this->dbModel->created_at = now();
        $this->dbModel->created_by = $adminId;
        return $this;
    }

    private function setSource(PaymentSourceEnum $source): self
    {
        $this->dbModel->source = $source;
        return $this;
    }

    public function setSourceId(NewPaymentDto $dto): self
    {
        $sourceId = match ($this->dbModel->source) {
            // PaymentSourceEnum::CASH_DESK     => $dto->cash_operational_transaction?->getKey(), //  TODO: remove it after tests
            // PaymentSourceEnum::LOAN_REFINANCE   => $dto->parent_payment->getKey(), // now we create children first and will update it later
            PaymentSourceEnum::FILE_IMPORT => $dto->imported_payment->getKey(),
            PaymentSourceEnum::EASY_PAY_API => $dto->easy_pay_request->getKey(),
            PaymentSourceEnum::REFUNDED_PAYMENT => $dto->parent_payment->getKey(),
            default => null
        };

        if (empty($sourceId)) {
            return $this;
        }

        //
        $attribute = $this->dbModel->source->attributeName();
        if ($attribute) {
            $this->dbModel->setAttribute($attribute, $sourceId);
        }

        return $this;
    }

    private function setDescription(?string $description = null, ?NewPaymentDto $dto = null): self
    {
        // от файл импорт на банка - формираме description по полета: date, payment_basis, details
        // от ръчно плащамне - взимаме текст от формата попълнен от агент
        if ($description) {
            $this->dbModel->description = $description;
            return $this;
        }

        // за изипей го сглобяваме тук
        if ($this->dbModel->source === PaymentSourceEnum::EASY_PAY_API) {
            $easyPayAttempt = $dto->easy_pay_request;
            $this->dbModel->description = 'Входящо плащане Изипей' .
                '. EГН: ' . $easyPayAttempt->pin .
                '. Сума в стотинки: ' . $easyPayAttempt->amount .
                '. Сума в лева: ' . intToFloat($easyPayAttempt->amount) .
                '. Транзакция номер: ' . $easyPayAttempt->easy_pay_transaction_id .
                '. Заем # ' . $this->dbLoan()?->getKey() .
                '. Дължи към момента: ' . intToFloat($this->dbLoan()?->getAmountToPay()) . ' лева';
        }

        return $this;
    }

    private function setDocumentNumber(?NewPaymentDto $dto = null): self
    {
        if (!empty($dto->document_number)) {
            $this->dbModel()->document_number = $dto->document_number;

            return $this;
        }

        if (!empty($dto->source) && $dto->source === PaymentSourceEnum::EASY_PAY_API) {
            $this->dbModel->document_number = $dto->easy_pay_request->easy_pay_transaction_id;
        }

        return $this;
    }

    private function setRestAmount(int $restAmount): self
    {
        $this->restAmount = $restAmount;

        return $this;
    }

    private function setProblem(?PaymentProblemEnum $problem = null): self
    {
        if (empty($problem)) {
            $this->dbModel->problem = PaymentProblemEnum::NONE;

            return $this;
        }

        $this->dbModel->problem = $problem;

        return $this;
    }

    private function setStatus(?PaymentStatusEnum $status = null): self
    {
        $this->dbModel->status = $status;

        return $this;
    }

    private function setAmount(int $amount): self
    {
        $this->dbModel->amount = $amount;

        return $this;
    }

    private function setPurpose(?PaymentPurposeEnum $purpose = null): self
    {
        if (empty($purpose)) {
            $this->dbModel->purpose = PaymentPurposeEnum::UNKNOWN;

            return $this;
        }

        $this->dbModel->purpose = $purpose;

        return $this;
    }

    private function setHandled(): self
    {
        $this->dbModel->handled_at = now();
        $this->dbModel->handled_by = getAdminId();

        return $this;
    }

    private function removeSnapshot(): self
    {
        PaymentSnapshot::where('payment_id', $this->dbModel->payment_id)
            ->forceDelete();

        return $this;
    }

    private function restoreBySnapshot(): self
    {
        $paymentService = app(PaymentService::class);
        $paymentService->rollBack($this->dbModel);

        return $this;
    }

    // used only for refinance !!!
    // we have only 2 options here: early & normal repayment
    // if last loan installment is not passed -> early, else -> refinance
    private function setPurposeAndDeliveryTypeBasedOnLastInstalmentDate(): self
    {
        $lastInstallment = $this->loan->getLastInstallment();
        $instDate = Carbon::parse($lastInstallment->due_date);
        $today = Carbon::now()->startOfDay();


        if ($today->lte($instDate)) {
            $this->dbModel->purpose = PaymentPurposeEnum::LOAN_EARLY_REPAYMENT;
            $this->dbModel->delivery_type = PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT;

            return $this;
        }

        if ($today->gt($instDate)) {
            $this->dbModel->purpose = PaymentPurposeEnum::LOAN_PAYMENT;
            $this->dbModel->delivery_type = PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT;
        }

        return $this;
    }

    // Important: used only for automatic payments!
    // + amount
    // + problem
    // + purpose
    // + delivery_type
    // + status
    private function setMainPropsWithProblem($dto): self
    {
        $amountFromPayment = (int)$dto->amount; // int

        // I. basic checks first

        // client not found
        if (empty($this->dbModel->client_id)) {
            $this->dbModel->amount_received = $amountFromPayment;
            $this->dbModel->amount = $amountFromPayment;
            $this->dbModel->problem = PaymentProblemEnum::TYPE_CLIENT_NOT_FOUND;
            $this->dbModel->purpose = PaymentPurposeEnum::UNKNOWN;
            $this->dbModel->delivery_type = PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT;
            $this->dbModel->status = PaymentStatusEnum::RECEIVED;

            return $this;
        }

        // more then 1 active loan
        $clientLoans = $this->loanRepo->getAllActiveByClientId($this->dbModel->client_id);
        if ($clientLoans->count() > 1) {

            $oneOfActiveLoans = $clientLoans->first();
            if (!empty($oneOfActiveLoans->loan_id)) {
                $this->tmpLoanForSms = $oneOfActiveLoans;
            }

            $this->dbModel->amount_received = $amountFromPayment;
            $this->dbModel->amount = $amountFromPayment;
            $this->dbModel->problem = PaymentProblemEnum::TYPE_MORE_THAN_ONE_LOAN;
            $this->dbModel->purpose = PaymentPurposeEnum::UNKNOWN;
            $this->dbModel->delivery_type = PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT;
            $this->dbModel->status = PaymentStatusEnum::RECEIVED;

            return $this;
        }

        // loan not found
        if (empty($this->dbModel->loan_id) || empty($this->loan->loan_id)) {
            $this->dbModel->amount_received = $amountFromPayment;
            $this->dbModel->amount = $amountFromPayment;
            $this->dbModel->problem = PaymentProblemEnum::TYPE_LOAN_NOT_FOUND;
            $this->dbModel->purpose = PaymentPurposeEnum::UNKNOWN;
            $this->dbModel->delivery_type = PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT;
            $this->dbModel->status = PaymentStatusEnum::RECEIVED;

            return $this;
        }

        // loan not active
        if (!$this->loan->isActive()) {
            $this->dbModel->amount_received = $amountFromPayment;
            $this->dbModel->amount = $amountFromPayment;
            $this->dbModel->problem = PaymentProblemEnum::TYPE_LOAN_NOT_FOUND;
            $this->dbModel->purpose = PaymentPurposeEnum::UNKNOWN;
            $this->dbModel->delivery_type = PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT;
            $this->dbModel->status = PaymentStatusEnum::RECEIVED;

            return $this;
        }


        // II. amount checks

        // small amount
        if ($amountFromPayment <= 0) {
            $this->dbModel->amount_received = $amountFromPayment;
            $this->dbModel->amount = 0;
            $this->dbModel->problem = PaymentProblemEnum::TYPE_AMOUNT_EQUALS_ZERO;
            $this->dbModel->purpose = PaymentPurposeEnum::UNKNOWN;
            $this->dbModel->delivery_type = PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT;
            $this->dbModel->status = PaymentStatusEnum::RECEIVED;

            return $this;
        }


        // get amounts for validation
        $amounts = $this->dbLoan()->getTriMurti();
        $extendAmount = $amounts['extension'];
        $extendAmount15Days = $amounts['extension15'];
        $totalDueAmount = $amounts['regular'];
        $earlyRepaymentAmount = $amounts['early'];


        // too big amount
        if ($amountFromPayment > $totalDueAmount) {

            // set rest amount
            $this->restAmount = ($amountFromPayment - $totalDueAmount);

            $this->dbModel->amount_received = $amountFromPayment;
            $this->dbModel->amount = $totalDueAmount;
            $this->dbModel->problem = PaymentProblemEnum::NONE;
            $this->dbModel->purpose = PaymentPurposeEnum::LOAN_PAYMENT;
            $this->dbModel->delivery_type = PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT;
            $this->dbModel->status = PaymentStatusEnum::RECEIVED;

            // flag for closing loan
            $this->loanRepaid = true;

            return $this;
        }

        // III. great success (c)Borat

        // loan extension
        if ($amountFromPayment == $extendAmount) {
            $this->dbModel->amount_received = $amountFromPayment;
            $this->dbModel->amount = $amountFromPayment;
            $this->dbModel->problem = PaymentProblemEnum::NONE;
            $this->dbModel->purpose = PaymentPurposeEnum::LOAN_EXTENSION;
            $this->dbModel->delivery_type = PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION;
            $this->dbModel->status = PaymentStatusEnum::RECEIVED;

            return $this;
        }

        /// loan extension with 15 days
        if ($amountFromPayment == $extendAmount15Days) {
            $this->dbModel->amount_received = $amountFromPayment;
            $this->dbModel->amount = $amountFromPayment;
            $this->dbModel->problem = PaymentProblemEnum::NONE;
            $this->dbModel->purpose = PaymentPurposeEnum::LOAN_EXTENSION;
            $this->dbModel->delivery_type = PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION;
            $this->dbModel->status = PaymentStatusEnum::RECEIVED;

            return $this;
        }


        // early repayment (normal)
        if ($amountFromPayment == $earlyRepaymentAmount) {
            $this->loanRepaid = true;

            $this->dbModel->amount_received = $amountFromPayment;
            $this->dbModel->amount = $amountFromPayment;
            $this->dbModel->problem = PaymentProblemEnum::NONE;
            $this->dbModel->purpose = PaymentPurposeEnum::LOAN_EARLY_REPAYMENT;
            $this->dbModel->delivery_type = PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT;
            $this->dbModel->status = PaymentStatusEnum::RECEIVED;

            return $this;
        }


        // early repayment with +/- BUFFER_AMOUNT
        // the idea of it is to close early repayment if the earlyRepaymentAmount >= payment amount +/- BUFFER_AMOUNT
        // 1) because of bank payment done in Friday, but we get it on Monday, the earlyRepaymentAmount become bigger,
        // but the client already paid smaller amount, so we take it and reduce loan expense with the difference
        // 2) or if client due is 1495 for early repayment, and he paid 1500, we still could close a loan,
        // and put the rest to unclaimed money
        $erDifference = abs($earlyRepaymentAmount - $amountFromPayment);
        if ($erDifference <= 100 * self::BUFFER_AMOUNT) { // still goes to early repayment

            $allowedReducing = true; // based on this flag we will use BUFFER AMOUNT logic

            $rest = 0;
            $amount = $earlyRepaymentAmount;
            $amountForDelExpense = 0;

            if ($amountFromPayment > $earlyRepaymentAmount) {
                $rest = $amountFromPayment - $earlyRepaymentAmount;
            } else {

                // Important: when we reduce amount for repayment, we can not reduce principal
                // so final amount could not be LESS than total rest principal
                $totalRestPricipal = $amounts = $this->dbLoan()->getTotalRestPrincipal();

                if ($amountFromPayment < $totalRestPricipal) {
                    $allowedReducing = false;
                }

                $amount = $amountFromPayment;
                $amountForDelExpense = $earlyRepaymentAmount - $amountFromPayment;
            }


            if ($allowedReducing) {
                $this->loanRepaid = true;

                // set rest amount
                if ($rest > 0) {
                    $this->restAmount = $rest;
                }

                // delete loan expense in case of smaller payment amount
                if ($amountForDelExpense > 0) {
                    $snapshotId = app(DeleteLoanExpenseOnLoanRepayment::class)->execute(
                        $this->dbLoan(),
                        $amountForDelExpense
                    );

                    if (empty($snapshotId)) {
                        throw new \Exception('Snapshot for delete expense is not saved');
                    }

                    $this->dbModel->del_expense_snapshot_id = $snapshotId;
                }

                $this->dbModel->amount_received = $amountFromPayment;
                $this->dbModel->amount = $amount;
                $this->dbModel->problem = PaymentProblemEnum::NONE;
                $this->dbModel->purpose = PaymentPurposeEnum::LOAN_EARLY_REPAYMENT;
                $this->dbModel->delivery_type = PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT;
                $this->dbModel->status = PaymentStatusEnum::RECEIVED;

                return $this;
            }
        }



        // normal repayment
        $this->dbModel->amount_received = $amountFromPayment;
        $this->dbModel->amount = $amountFromPayment;
        $this->dbModel->problem = PaymentProblemEnum::NONE;
        $this->dbModel->purpose = PaymentPurposeEnum::LOAN_PAYMENT;
        $this->dbModel->delivery_type = PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT;
        $this->dbModel->status = PaymentStatusEnum::RECEIVED;


        if ($amountFromPayment == $totalDueAmount) {
            $this->loanRepaid = true;
        }


        return $this;
    }

    // + amount
    // + purpose
    // + delivery_type
    // + status
    private function setMainPropsWithThrow($dto): self
    {
        $amountFromPayment = (int)$dto->amount; // int

        // I. basic checks first

        if (empty($this->dbModel->client_id)) {
            throw new \Exception('Client not found');
        }

        if (empty($this->dbModel->loan_id)) {
            throw new \Exception('Loan not found');
        }

        if (empty($amountFromPayment)) {
            throw new \Exception('No amount provided');
        }

        if (empty($dto->purpose)) {
            throw new \Exception('No purpose provided');
        }

        if (empty($dto->source) || $this->dbModel->source != PaymentSourceEnum::MANUAL_CREATION) {
            throw new \Exception('Wrong source provided');
        }

        $deliveryType = match ($dto->purpose) {
            PaymentPurposeEnum::LOAN_PAYMENT => PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT,
            PaymentPurposeEnum::LOAN_EXTENSION => PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION,
            PaymentPurposeEnum::LOAN_EARLY_REPAYMENT => PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT,
            PaymentPurposeEnum::SERVICE_FEE => PaymentDeliveryEnum::SERVICE_FEE,
            default => null
        };
        if (empty($deliveryType)) {
            throw new \Exception('Failed to detect delivery type');
        }


        // TODO validate to big amount -> REST


        // II. setup

        $this->dbModel->amount_received = ($amountFromPayment + $this->restAmount);
        $this->dbModel->amount = $amountFromPayment;
        $this->dbModel->status = PaymentStatusEnum::RECEIVED;
        $this->dbModel->problem = PaymentProblemEnum::NONE;
        $this->dbModel->purpose = $dto->purpose;
        $this->dbModel->delivery_type = $deliveryType;


        if (
            in_array(
                $deliveryType->value,
                [PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT->value, PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT->value]
            ) && isset($dto->is_repaid) && $dto->is_repaid === true
        ) {
            $this->loanRepaid = true;
        }


        return $this;
    }

    private function tryToPay(bool $isManual = false): bool
    {
        DB::beginTransaction();
        try {

            // update payment.delivery
            // update pament.status to delivered
            // add payment_distribution
            // update on taxes
            // update on installments
            // accounting row
            LoanPaymentWasReceivedNew::dispatch(
                $this->dbModel,
                $this->loan
            );


            // change loan.status = repaid + ccr_finished
            if ($this->loanRepaid) {
                $this->loan = $this->loanForRepayment
                    ->buildFromExisting($this->loan)
                    ->setRepaid(
                        $this->dbModel,
                        ($this->dbModel->delivery_type == PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT)
                    )
                    ->dbModel();
            }


            // handle the rest, put it in the buffer table
            if ($this->restAmount > 0 && $this->dbModel->delivery_type != PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION) {
                $this->saveUnclaimedMoney();
            }


            // update client actual_stats
            LoanPaymentWasReceivedForStats::dispatch(
                $this->dbModel,
                $this->loan
            );


            DB::commit();

            // create payment for the rest
            if ($this->restAmount > 0 && $this->dbModel->delivery_type == PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION) {
                event(new AfterPaymentDistribute($this->dbModel));
            }

        } catch (\Throwable $e) {
            DB::rollBack();

            Log::channel('paymentsModule')->error(
                'tryToPay(#' . $this->dbModel->payment_id . ') failed: '
                . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine()
            );

            // Important: in case of auto processing payment we create task in something fails
            //            in case of manual processing we throws an error to agent
            if ($isManual) {
                throw $e;
            }

            // set problem to payment
            $this->dbModel->problem = PaymentProblemEnum::TYPE_PROBLEM_ORDERING;
            $this->dbModel->status = PaymentStatusEnum::RECEIVED;
            $this->dbModel->save();

            // create payment task (inside we change payment status from delivered to received)
            $this->processTask();


            return false;
        }


        try {

            // update bucket on payment
            if (!empty($this->dbModel->loan_id)) {
                \Artisan::call("script:assign-loans-to-buckets " . $this->dbModel->loan_id);
            }

        } catch (\Throwable $e) {
            Log::channel('paymentsModule')->error(
                'tryToPay(#' . $this->dbModel->payment_id . ') bucket update failed: '
                . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine()
            );
        }


        try {

            // remove from bucket if in overdue
            // send thanks email
            LoanWasRepaid::dispatchIf(
                $this->loanRepaid,
                $this->loan
            );

        } catch (\Throwable $e) {

            Log::channel('paymentsModule')->error(
                'tryToPay(#' . $this->dbModel->payment_id . ') stats failed: '
                . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine()
            );


            // Important: in case of auto processing payment we create task in something fails
            //            in case of manual processing we throws an error to agent
            if ($isManual) {
                throw $e;
            }
        }


        return true;
    }

    private function saveUnclaimedMoney(): void
    {
        // prepare desc
        $details = '';
        if (!empty($this->client->client_id)) {
            $details .= 'Клиент: ' . $this->client->getFullName() . ';';
            $details .= 'Егн: ' . $this->client->pin . ';';
        }
        if (!empty($this->loan->loan_id)) {
            $details .= 'Заем №: ' . $this->loan->loan_id . ';';
        }
        $details .= 'Плащане №: ' . $this->dbModel->payment_id . ';';
        if (!empty($this->dbModel->description)) {
            $details .= 'Описание на плащане: ' . $this->dbModel->description . ';';
        }

        $iban = $this->client?->getMainBankAccount()?->iban;
        if ($iban && $this->dbModel->isBank()) {
            $details .= ';' . $iban;
        }


        // save
        $obj = new UnclaimedMoney();
        $obj->payment_id = $this->dbModel->payment_id;
        $obj->direction = 'in';
        $obj->amount = $this->restAmount;
        $obj->details = $details;
        $obj->created_at = now();
        $obj->created_by = getAdminId();
        $obj->save();


        // nullate rest after it's been used
        $this->restAmount = 0;
    }

    /***************************VARIOUS*************************/

    private function validateAmount()
    {
        $paymentAmount = $this->dbModel->amount;
        $amountToPay = $this->loan->getEarlyRepaymentDebtDb();

        // if amount become higher
        // means task created in prev.day, but handle today
        // so we have some new accruals
        // means we need to rollback to snapshot when refinance is begin
        if ($amountToPay > $paymentAmount) {

            $refinancingLoanId = $this->loan->getRefinancingId();

            $refinancingLoan = DbLoan::find($refinancingLoanId);
            if (empty($refinancingLoan->loan_id)) {
                throw new \Exception('Not found loan from refinance #' . $refinancingLoanId);
            }

            // can not change active loan
            if ($refinancingLoan->isActive()) {
                throw new \Exception('loan #' . $refinancingLoanId . ' is already active');
            }

            app(LoanForApproval::class)
                ->loadById($refinancingLoan->loan_id)
                ->reActualizeRefinance();

            $amountToPay = $this->loan->getEarlyRepaymentDebtDb();
        }

        if ($paymentAmount != $amountToPay) {
            throw new \Exception('Refinanced payment amount could not cover loan dues(' . intToFloat($paymentAmount) . '/' . intToFloat($amountToPay) . ')');
        }

        return $this;
    }

    private function stopIfJuridical(DbLoan $loan)
    {
        if ($loan->isJuridical() && 1 != $loan->isClosedJuridicalCase) {
            throw new \Exception('Payment receiving stopped, loan #' . $loan->loan_id . 'is juridical!');
        }
    }

    public function closePaymentTask(PaymentTask $task): self
    {
        $this->task->loadFromPaymentTask($task)
            ->closeIfNeeded(
                getAdminId(),
                false,
                null,
                PaymentTaskDecision::PAYMENT_TASK_DECISION_ID_SAVE_PAYMENT
            );

        return $this;
    }

    public function processTask(): self
    {
        $task = $this->task->loadFromPayment($this->dbModel)
            ->processPayment($this->dbModel->created_by)
            ->dbModel();

        return $this;
    }

    private function save(): self
    {
        if (!$this->repo->save($this->dbModel)) {
            throw new PaymentNotSaved();
        }

        return $this;
    }

    /****************************GETTERS****************************/

    public function direction(): PaymentDirectionEnum
    {
        return PaymentDirectionEnum::IN;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }

    public function dbLoan(): ?DbLoan
    {
        return $this->loan ?: $this->dbModel->loan;
    }
}
