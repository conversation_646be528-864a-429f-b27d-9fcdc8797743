<?php

namespace Modules\Payments\Domain\Entities\Loan;

use Modules\Common\Domain\AggregateRootInterface;
use Modules\Common\Domain\CurrentDate;
use Modules\Common\Domain\DomainModel;
use Modules\Common\Domain\InstallmentsInterface;
use Modules\Common\Domain\LoanInterface;
use Modules\Common\Models\Loan as DbModel;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Payment;
use Modules\Head\Repositories\LoanRepository as Repo;
use Modules\Payments\Domain\Exception\LoanHasIncorrectStatus;
use Modules\Payments\Domain\Exception\LoanNotSaved;
use Modules\Sales\Domain\Exceptions\LoanNotFound;

class LoanForRepayment extends DomainModel implements LoanInterface, AggregateRootInterface
{
    public function __construct(
        private DbModel      $dbModel,
        private Repo         $repo,
        private CurrentDate  $currentDate
    )
    {
    }

    public static function loadSelf(int $id): self
    {
        return app()->make(LoanForRepayment::class)->loadById($id);
    }

    public function loadById(int $id): self
    {
        $existing = $this->repo->getById($id);
        if (!$existing) {
            throw new LoanNotFound($id);
        }
        return $this->buildFromExisting($existing);
    }

    public function buildFromExisting(DbModel $dbModel): self
    {
        $dbModel->refresh();
        $this->dbModel = $dbModel;
        return $this;
    }

    public function changeStatusToRepaid(Payment $payment): self
    {
        if (!$this->dbModel->wasFullyRepaid($payment->purpose->isEarlyRepayment())) {
            return $this;
        }

        return $this->setRepaid($payment);
    }

    public function setRepaid(Payment $payment, bool $earlyRepaid = false)
    {
        return $this->setAdmin($payment->handled_by)
            ->setStatus($earlyRepaid)
            ->save();
    }

    private function setAdmin(int $adminId): self
    {
        $this->dbModel->administrator_id = null;
        $this->dbModel->last_status_update_administrator_id = $adminId;
        return $this;
    }

    private function setStatus(bool $earlyRepaid = false): self
    {
        if ($this->dbModel->loan_status_id !== LoanStatus::ACTIVE_STATUS_ID) {
            throw new LoanHasIncorrectStatus($this->dbModel->getKey(), $this->dbModel->getStatus());
        }

        $this->dbModel->loan_status_id = LoanStatus::REPAID_STATUS_ID;
        $this->dbModel->early_repaid = ($earlyRepaid ? 1 : 0);

        return $this;
    }

    private function setCcrFlags(): self
    {
        // $this->dbModel->need_ccr_sync = 1;
        // $this->dbModel->set_need_ccr_sync_at = $this->currentDate->now();
        return $this;
    }

    private function save(): self
    {
        $this->dbModel->last_status_update_date = $this->currentDate->now();
        $this->dbModel->repaid_at = $this->currentDate->now();
        if (!$this->repo->save($this->dbModel)) {
            throw new LoanNotSaved($this->dbModel->getKey());
        }

        return $this;
    }

    public function dbModel(): DbModel
    {
        return $this->dbModel;
    }
}
