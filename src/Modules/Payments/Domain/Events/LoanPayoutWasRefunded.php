<?php

namespace Modules\Payments\Domain\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Payment;

class LoanPayoutWasRefunded
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(public Payment $payment, public Loan $loan) {}
}
