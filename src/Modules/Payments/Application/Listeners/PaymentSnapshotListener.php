<?php

namespace Modules\Payments\Application\Listeners;

use Modules\Common\Entities\PaymentSnapshot;
use Modules\Payments\Domain\Events\LoanPaymentWasReceivedNew;
use Modules\Payments\Domain\Events\RefinancingPaymentWasCreated;

class PaymentSnapshotListener
{
    public function handle(LoanPaymentWasReceivedNew|RefinancingPaymentWasCreated $event)
    {
        // set main objects for the game
        $loan = $event->loan;
        $payment = $event->payment;


        // in case of refinance payments we did a snapshot on creating, so we skip it on delivering
        if (!empty($payment->parent_payment_id)) {
            return ;
        }

        $taxes = $loan->getAllTaxes();
        $client = $loan->client;
        $loanStats = $loan->loanActualStats;
        $clientStats = $client->clientActualStats;
        $installments = $loan->getAllInstallments();


        // set snapshot
        $snapshot = new PaymentSnapshot();
        $snapshot->payment_id = $payment->payment_id;
        $snapshot->created_at = now();
        $snapshot->created_by = getAdminId();

        $snapshot->loan = $loan->toArray();
        $snapshot->loan_stats = $loanStats->toArray();
        $snapshot->client_stats = $clientStats->toArray();

        $installmentsArray = [];
        foreach ($installments as $installment) {
            $installmentsArray[$installment->installment_id] = $installment->toArray();
        }
        $snapshot->installments = $installmentsArray;

        $taxesArray = [];
        if ($taxes->count() > 0) {
            foreach ($taxes as $tax) {
                $taxesArray[$tax->tax_id] = $tax->toArray();
            }
        }
        $snapshot->taxes = $taxesArray;


        // save and go on
        $snapshot->save();
    }
}
