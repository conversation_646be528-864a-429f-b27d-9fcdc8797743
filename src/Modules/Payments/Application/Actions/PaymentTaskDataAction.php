<?php

namespace Modules\Payments\Application\Actions;

use Kris\LaravelFormBuilder\Form;
use Kris\LaravelFormBuilder\FormBuilder;
use Modules\Common\Enums\TaskStatusEnum;
use Modules\Common\Models\PaymentTask;
use Modules\Common\Traits\DateBuilderTrait;
use Modules\Payments\FilterForms\PaymentTaskFilterForm;
use Modules\Payments\Repositories\PaymentTaskRepository;

class PaymentTaskDataAction
{
    use DateBuilderTrait;

    public function __construct(
        private readonly PaymentTaskRepository $paymentTaskRepository,
        private readonly FormBuilder           $formBuilder
    )
    {
    }

    public function execute(array $filters = []): array
    {
        if (empty($filters)) {
            $filters = [
                'paymentTaskStatus' => [
                    TaskStatusEnum::NEW,
                    TaskStatusEnum::PROCESSING
                ],
                'showAfter' => $this->sqlDate()->format('Y-m-d H:i:s'),
            ];
        }

        $data['paymentTasks'] = $this->paymentTaskRepository->getPaginatorByFilters($filters);
        $data['paymentTaskFilterForm'] = $this->getPaymentTaskFilterForm();

        return $data;
    }

    protected function getPaymentTaskFilterForm(): Form
    {
        $options = ['route' => 'payment.paymentsTasks.list'];
        if(getAdmin()->hasPermissionTo('payment.paymentsTasks.export')) {
            $options['exportRoute'] = 'payment.paymentsTasks.export';
        }
        return $this->formBuilder->create(PaymentTaskFilterForm::class, $options);
    }
}
