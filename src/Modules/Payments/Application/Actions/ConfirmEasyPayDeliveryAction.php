<?php

namespace Modules\Payments\Application\Actions;

use Modules\Common\Models\Payment;
use Modules\Payments\Domain\Entities\OutgoingPayment;

class ConfirmEasyPayDeliveryAction
{
    public function __construct(private OutgoingPayment $outgoingPayment) {}

    public function execute(int $paymentId): Payment
    {
        return $this->outgoingPayment
            ->loadById($paymentId)
            ->changeStatusToDelivered(getAdminId())
            ->dbModel();
    }

    public function closePaymenTask(int $paymentId): Payment
    {
        return $this->outgoingPayment
            ->loadById($paymentId)
            ->closePaymentTask()
            ->dbModel();
    }
}
