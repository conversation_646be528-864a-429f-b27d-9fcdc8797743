<?php

namespace Modules\Payments\Application\Actions;

use Carbon\Carbon;
use Modules\Common\Models\Payment;
use Modules\Payments\Domain\Entities\IncomingPayment;

class RecalculatePaymentStatsAction
{
    public function __construct(private IncomingPayment $incomingPayment){}

    public function execute(Payment $payment): array
    {
        $dbModel = $payment;
        $loan = $payment->loan;

        $paymentDate = $payment->created_at;
        $activationDate = Carbon::parse($loan->activated_at)->startOfDay();


        $calculator = $loan->getCredit($payment->created_at->toDateTimeString());
        $carton = $calculator->loanCarton();
        $overdueAmount = $carton->currentOverdueAmount;

        $dueDate = null;
        $installments = $calculator->installmentsCollection();
        foreach ($installments as $installment) {
            if ($installment->isPaid()) {
                continue;
            }

            $dueDate = Carbon::parse($installment->dueDate)->startOfDay();
            break;
        }

        $dbModel->days_on_books = $activationDate->diffInDays(Carbon::parse($paymentDate), false);//Days passed since loan activation till payment
        $dbModel->months_on_books = ceil($dbModel->days_on_books / 30);//days divided 30 rounded UP
        $dbModel->payment_passed_days = $dueDate ? $dueDate->diffInDays(Carbon::parse($paymentDate), false) : 0; //difference in days between FIRST UNPAID installment.due_date and current date
        $dbModel->payment_overdue_days = $dbModel->payment_passed_days > 0 ? $dbModel->payment_passed_days : 0;//days loan was overdue on the moment of payment
        $dbModel->payment_overdue_amount = intToFloat($overdueAmount);
        $dbModel->save();

        return $dbModel->getChanges();
    }
}
