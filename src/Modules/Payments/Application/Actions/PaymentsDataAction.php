<?php

namespace Modules\Payments\Application\Actions;

use Kris\LaravelFormBuilder\Form;
use Kris\LaravelFormBuilder\FormBuilder;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Traits\DateBuilderTrait;
use Modules\Payments\FilterForms\PaymentsFilterForm;
use Modules\Payments\Repositories\PaymentRepository;

final class PaymentsDataAction
{
    use DateBuilderTrait;

    public function __construct(
        private readonly PaymentRepository $paymentRepository,
        private readonly FormBuilder       $formBuilder
    ) {}

    public function execute(array $filters = []): array
    {
        $filters['paymentStatus'] = [
            PaymentStatusEnum::DELIVERED->value, // изпратени и усвоени от клиент
            PaymentStatusEnum::UNKNOWN->value, // остатъка при получени плащания
            PaymentStatusEnum::EASY_PAY_SENT->value, // изпратени по Изипей
        ];
        if (isset($filters['paymentIsActive']) && intval($filters['paymentIsActive']) === 0) {
            $filters['paymentStatus'][] = PaymentStatusEnum::CANCELED->value;
        }

        $data['payments'] = $this->paymentRepository->getPaymentsFilterBy($filters);
        $data['paymentFilterForm'] = $this->getPaymentsFilterForm();

        return $data;
    }

    private function getPaymentsFilterForm(): Form
    {
        $options = ['route' => 'payment.payments.list'];
        if (getAdmin()->hasPermissionTo('payment.payments.export')) {
            $options['exportRoute'] = 'payment.payments.export';
        }

        return $this->formBuilder->create(PaymentsFilterForm::class, $options);
    }
}
