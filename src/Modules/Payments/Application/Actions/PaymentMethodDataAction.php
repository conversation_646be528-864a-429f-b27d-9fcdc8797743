<?php

namespace Modules\Payments\Application\Actions;

use Modules\Head\Repositories\PaymentMethodRepository;

readonly class PaymentMethodDataAction
{
    public function __construct(
        private PaymentMethodRepository $paymentMethodRepository
    )
    {
    }

    public function execute(): array
    {
        $data['paymentMethods'] = $this->paymentMethodRepository->getAllPayments();


        return $data;
    }
}
