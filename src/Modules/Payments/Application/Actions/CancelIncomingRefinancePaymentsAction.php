<?php

namespace Modules\Payments\Application\Actions;

use Modules\Common\Entities\AutoProcessSnapshot;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanRefinance;
use Modules\Common\Models\Payment;
use Modules\Head\Repositories\Loan\LoanRefinanceRepository;
use Modules\Payments\Domain\Entities\IncomingPayment;

// used in: CancelPaymentAction
// what it' doing:
// - cancel incoming refinance payments
// - remove snapshots for canceled payments
// - remove loan_refinance relations(save log)
readonly class CancelIncomingRefinancePaymentsAction
{
    public function __construct(private IncomingPayment $incomingPayment) {}

    public function executeByParentLoan(Loan $loan, Payment $outPayment): bool {

        if (!$loan->isRefinanceLoan()) {
            throw new \Exception('Failed to cancel refinance incoming payments, because of wrong loan type');
        }

        $refLoansRows = LoanRefinance::where('refinancing_loan_id', $loan->loan_id)
            ->where('active', 1)
            ->get();
        if ($refLoansRows->count() < 1) {
            return true; // the case when we have refinance, but refinanced loan was repaid earlier
        }

        $incomingPayments = $outPayment->getChildRefinancePayments();
        if ($incomingPayments->count() < 1) {
            throw new \Exception('Failed to cancel refinance incoming payments, no payments found for parent payment #' . $outPayment->payment_id);
        }

        if ($incomingPayments->count() != $refLoansRows->count()) {
            throw new \Exception('Failed to cancel refinance incoming payments, count of payments is not equal to the count of loans');
        }


        // cancel incoming payments
        // remove snapshots
        foreach ($incomingPayments as $incomingPayment) {
            $this->incomingPayment->new()->buildFromRefinancePaymentOnCancel($incomingPayment);
        }


        // deactivate and move loan_refinance int log + delete
        $refLogRepo = app(LoanRefinanceRepository::class);
        foreach ($refLoansRows as $refLoanRow) {

            // when we approve refinancing loan that cover active loan, we add interest & penalty for 1st inst if the loan is grace or if installment is accrued
            // so on cancel we need to rollback prev.state of grace loan
            // logic for task: https://trello.com/c/qF4P3UDd
            if ($loan->isOnlineLoan()) {
                $refLoan = $refLoanRow->refinancedLoan;
                $snapshot = AutoProcessSnapshot::getSnapshotForLoan($refLoan);
                if (!empty($snapshot->id) && empty($snapshot->reverted_at)) {
                    AutoProcessSnapshot::restoreSnapshot($refLoan, $snapshot);
                }
            }

            $refLogRepo->delete($refLoanRow);
        }


        return true;
    }
}
