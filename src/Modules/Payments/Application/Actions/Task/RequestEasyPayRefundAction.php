<?php

namespace Modules\Payments\Application\Actions\Task;

use Illuminate\Support\Facades\Log;
use Modules\Common\Enums\PaymentTaskNameEnum;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentTask;
use Modules\Payments\Application\Actions\Task\ConfirmRefundAction;
use Modules\ThirdParty\Services\CurlEasyPayService;

class RequestEasyPayRefundAction
{
    const EASYPAY_REQ_STATUS_OK = 'OK';
    const EASYPAY_REQ_STATUS_PROCESSING = 'PROCESSING';

    public function __construct(
        private CurlEasyPayService $curlEasyPayService,
        private ConfirmRefundAction $confirmRefundAction
    ) {}

    public function execute(
        Payment $payment,
        ?PaymentTask $paymentTask = null,
        bool $checkState = false
    ): string {

        if ($checkState) {
            if (!$payment->canSentRefundState()) {
                throw new \Exception('Wrong payment status');
            }
        } else {
            if (!$payment->canBeRefunded() || $payment->isRefundInProgress()) {
                throw new \Exception('Wrong payment status');
            }
        }


        // try refund
        $result = ($checkState ? $this->curlEasyPayService->refundState($payment) : $this->curlEasyPayService->refund($payment));
        // $result = $this->getFakeKo();
        // $result = $this->getFakeProcessing();
        // $result = $this->getFakeOk();


        // if refund failed -> do nothing
        if (empty($result['success']) || empty($result['request']['response_status'])) {
            return 'KO';
        }

        // if refund success(OK for sendMoney || OK/PROCESSING for refundState):
        // -> create incoming payment (RETURN)
        // -> update payment refund state
        // -> close loan
        // -> close payment task
        // -> close sale task (optional)
        $response = $result['request']['response_status'];

        if (
            self::EASYPAY_REQ_STATUS_OK == $response
            || self::EASYPAY_REQ_STATUS_PROCESSING == $response
            // || ($checkState && self::EASYPAY_REQ_STATUS_PROCESSING == $response)
        ) {

            try {

                $this->confirmRefundAction->executeByPayment(
                    $payment,
                    $paymentTask
                );

                $loan = $payment->loan;
                if (self::EASYPAY_REQ_STATUS_PROCESSING == $response) {
                    $loan->addMeta('easypay_refundes', 'processing');
                }

                return 'OK';

            } catch (\Throwable $e) {

                Log::debug('ERROR RequestEasyPayRefundAction - ' . $e->getMessage() . ', ' . $e->getFile() . ':' . $e->getLine());

                return 'KO';
            }
        }

        // if refund processing, we are changing task type so th agent could check refund state
        if (self::EASYPAY_REQ_STATUS_PROCESSING == $response) {

            // we do it only first time on refund, but not in refund state
            if (!$checkState) {

                $payment->refund_state = Payment::EASYPAY_REFUND_PROCESSING;
                $payment->save();

                if (!empty($paymentTask->payment_task_id)) {
                    $paymentTask->name = PaymentTaskNameEnum::WAIT_REFUND_STATE;
                    $paymentTask->save();
                }

            }

            return 'PROCESSING';
        }

        return 'KO';
    }

    private function getFakeKo(): array
    {
        return [
            'success' => false,
            'request' => ['response_status' => 'ERR'],
        ];
    }

    private function getFakeOk(): array
    {
        return [
            'success' => true,
            'request' => ['response_status' => 'OK'],
        ];
    }

    private function getFakeProcessing(): array
    {
        return [
            'success' => true,
            'request' => ['response_status' => 'PROCESSING'],
        ];
    }
}
