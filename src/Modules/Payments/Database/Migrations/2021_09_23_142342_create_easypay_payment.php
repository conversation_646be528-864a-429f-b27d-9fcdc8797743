<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{

    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'easypay_payment',
            function ($table) {
                $table->bigIncrements('easypay_payment_id');
                $table->integer('client_id')->unsigned()->nullable()->index();
                $table->integer('loan_id')->unsigned()->nullable()->index();
                $table->string('received_data')->nullable();
                $table->decimal('amount', 11, 2)->nullable()->index();
                $table->timestamp('paid_at')->nullable();
                $table->string('status')->nullable();
                $table->string('details')->nullable();

                $table->foreign('client_id')->references('client_id')->on('client');
                $table->foreign('loan_id')->references('loan_id')->on('loan');
                $table->tableCrudFields();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'easypay_payment',
            function (Blueprint $table) {
                $table->dropForeign('easypay_payment_client_id_foreign');
                $table->dropForeign('easypay_payment_loan_id_foreign');
            }
        );
        Schema::dropIfExists('easypay_payment');
    }
};
