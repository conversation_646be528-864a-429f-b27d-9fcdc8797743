<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Models\Tax;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration {
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'tax',
            function ($table) {
                $table->bigIncrements('tax_id');
                $table->bigInteger('client_id')->unsigned();
                $table->bigInteger('loan_id')->unsigned()->nullable();
                $table->bigInteger('installment_id')->unsigned()->nullable();
                $table->integer('currency_id')->unsigned()->index();

                $table->enum('type', Tax::getTypes())->nullable();
                $table->integer('amount')->nullable();
                $table->integer('rest_amount')->nullable();
                $table->integer('paid_amount')->nullable();
                $table->tinyInteger('paid')->default(0);
                $table->datetime('paid_at')->nullable();
                $table->datetime('date_from')->nullable();
                $table->text('comment')->nullable();
                $table->enum('status', Tax::getTaxStatuses())->nullable();

                $table->foreign('client_id')
                    ->references('client_id')
                    ->on('client');
                $table->foreign('loan_id')
                    ->references('loan_id')
                    ->on('loan');
                $table->foreign('installment_id')
                    ->references('installment_id')
                    ->on('installment');
                $table->foreign('currency_id')
                    ->references('currency_id')
                    ->on('currency');


                $table->tableCrudFields();
            }
        );
    }

    public function down(): void
    {
        Schema::table(
            'tax',
            function (Blueprint $table) {
                $table->dropForeign('tax_client_id_foreign');
                $table->dropForeign('tax_loan_id_foreign');
                $table->dropForeign('tax_installment_id_foreign');
                $table->dropForeign('tax_currency_id_foreign');
            }
        );
        Schema::dropIfExists('tax');
    }
};
