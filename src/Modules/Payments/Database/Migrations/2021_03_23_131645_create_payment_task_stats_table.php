<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use customschemabuildertrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getcustomschemabuilder(db::getschemabuilder())->create(
            'payment_task_stats',
            function ($table) {
                $table->bigincrements('id');
                $table->integer('payment_task_id')->unsigned()->index();
                $table->integer('administrator_id')->unsigned()->index();

                $table->datetime('start_at')->nullable();
                $table->datetime('end_at')->nullable();
                $table->integer('duration')->nullable();

                $table->foreign('payment_task_id')->references('payment_task_id')->on('payment_task');
                $table->foreign('administrator_id')->references('administrator_id')->on('administrator');
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'payment_task_stats',
            function (Blueprint $table) {
                $table->dropForeign('payment_task_stats_payment_task_id_foreign');
                $table->dropForeign('payment_task_stats_administrator_id_foreign');
            }
        );
        Schema::dropIfExists('payment_task_stats');
    }
};
