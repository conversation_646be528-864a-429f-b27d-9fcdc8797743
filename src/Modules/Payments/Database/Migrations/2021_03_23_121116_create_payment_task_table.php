<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Enums\TaskStatusEnum;
use Modules\Common\Enums\PaymentProblemEnum;
use Modules\Common\Models\PaymentTask;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration {
    use customschemabuildertrait;

    /**
     * run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getcustomschemabuilder(DB::getSchemaBuilder())->create(
            'payment_task',
            function ($table) {
                $table->bigincrements('payment_task_id');
                $table->string('name')->nullable();
                $table->integer('payment_id')->unsigned()->nullable();
                $table->integer('client_id')->unsigned()->nullable();
                $table->integer('loan_id')->unsigned()->nullable();
                $table->string('iban')->nullable();
                $table->string('bic')->nullable();
                $table->bigInteger('payment_method_id')->unsigned()->nullable();
                $table->decimal('amount', 11, 2)->nullable();
                $table->string('basis')->nullable();
                $table->timestamp('handled_at')->nullable();
                $table->smallInteger('handled_by')->index()->nullable();
                $table->enum('status', ['new','processing','done'])->default('new');//old values
                $table->enum('direction', PaymentTask::getDirection());
                $table->enum('type', PaymentProblemEnum::toArray());
                $table->smallInteger('success')->index()->nullable();
                $table->string('details')->nullable();

                $table->timestamp('start_at')->nullable();
                $table->timestamp('end_at')->nullable();
                $table->bigInteger('duration')->nullable();

                $table->dateTime('show_after')->default(DB::raw('CURRENT_TIMESTAMP'));
                $table->datetime('last_status_update_date')->nullable();
                $table->integer('parent_task_id')->unsigned()->index()->nullable();
                $table->integer('imported_payment_id')->unsigned()->nullable()->index();
                $table->integer('easypay_rid')->unsigned()->nullable()->index();

                $table->foreign('client_id')->references('client_id')->on('client');
                $table->foreign('loan_id')->references('loan_id')->on('loan');
                $table->foreign('payment_id')->references('payment_id')->on('payment');
                $table->foreign('payment_method_id')->references('payment_method_id')->on('payment_method');
                $table->foreign('imported_payment_id')->references('imported_payment_id')->on('imported_payment');

                $table->tableCrudFields();
            }
        );
    }

    /**
     * reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'payment_task',
            function (Blueprint $table) {
                $table->dropForeign('payment_task_client_id_foreign');
                $table->dropForeign('payment_task_loan_id_foreign');
                $table->dropForeign('payment_task_payment_id_foreign');
                $table->dropForeign('payment_task_payment_method_id_foreign');
                $table->dropForeign('payment_task_payment_imported_payment_id_foreign');
            }
        );

        Schema::dropIfExists('payment_task');
    }
};
