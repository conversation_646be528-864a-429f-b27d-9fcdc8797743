<?php

namespace Feature;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Database\Seeders\CashLoanSteps\ActiveCashLoanSeeder;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Enums\PaymentDeliveryEnum;
use Modules\Common\Enums\PaymentDirectionEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Models\Currency;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Office;
use Modules\Common\Models\Payment;
use Modules\Payments\Application\Actions\ManualPaymentSaveAction;
use Tests\TestCase;

class AllStepsBeforeIncomingTest extends TestCase
{
    use DatabaseTransactions;

    public function testIncomingPayment()
    {
        $this->seed(ActiveCashLoanSeeder::class);
        $loans = Loan::all();
        $this->assertCount(1, $loans);
        /** @var Loan $loan */
        $loan = $loans[0];
        $loanId = $loan->getKey();
        $data = [
            'loans'=>[$loanId],
            'loanPaymentAmount'=>[$loanId => 500],
            'loanAction'=>[$loanId => PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT->value],
            'payment_method_id'=>PaymentMethodEnum::CASH->id(),
            'payment_amount'=>40000,
            'office_id'=>Office::OFFICE_ID_NOVI_PAZAR_1,
            'description'=>'test payment',
            'document_number'=>1234,
            'payment_task_id'=>0,
            'payment_task_decision_id'=>0,
            'admin_id'=>2
        ];
        $sut = app(ManualPaymentSaveAction::class);
        $this->assertTrue($sut->execute($data));
        $expectedPayment = [
            'currency_id' => Currency::BGN_CURRENCY_ID,
            'amount' => $data['payment_amount'],
            'client_id' => $loan->client->client_id,
            'payment_method_id' => $data['payment_method_id'],
            'office_id' => $data['office_id'] ?? 1,
            'direction' => PaymentDirectionEnum::IN,
            'source' => PaymentSourceEnum::MANUAL_CREATION,
            'delivery_type' => PaymentDeliveryEnum::DELIVERY_LOAN_EARLY_REPAYMENT,
            'description' => $data['description'] ?? '',
            'status' => PaymentStatusEnum::DELIVERED,
            //'handled_at' => date('Y-m-d H:i:s'),
        ];
        $actualPayment = Payment::where(['loan_id'=>$loanId, 'direction'=>'in'])->first();
        foreach ($expectedPayment as $key=>$val){
            $this->assertEquals($val, $actualPayment->$key, $key);
        }
        $loan->refresh();
        $this->assertEquals(LoanStatus::REPAID_STATUS_ID, $loan->loan_status_id);
    }

}