<?php

namespace Modules\Payments\Tests\Feature\Application\Actions;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Approve\Domain\Entities\Loan\LoanForApproval;
use Modules\Approve\Domain\Events\LoanWasApproved;
use Modules\CashDesk\Application\Actions\GiveOutCashAction as Sut;
use Modules\CashDesk\Enums\CashOperationalTransactionDirectionEnum;
use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\Common\Database\Seeders\Test\ActiveInstallmentsSeeder;
use Modules\Common\Database\Seeders\Test\ActiveLoanSeeder;
use Modules\Common\Database\Seeders\Test\ApprovedLoanSeeder;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Models\Loan as DbLoan;
use Modules\Common\Models\LoanRefinance;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\PaymentTask;
use Tests\TestCase;

class ActivateCashLoanActionTest extends TestCase
{
    use DatabaseTransactions;

    public function testHappyPath()
    {
        $this->seed([
            ActiveInstallmentsSeeder::class,
            ApprovedLoanSeeder::class
        ]);
        /** @var DbLoan $dbLoan */
        $dbLoan = DbLoan::find(ApprovedLoanSeeder::LOAN_ID);
        $dbLoan->payment_method_id = PaymentMethod::PAYMENT_METHOD_CASH;
        $dbLoan->amount_approved = 10000;
        $dbLoan->office_id = 2;
        $dbLoan->save();

        $cash = new CashOperationalTransaction();
        $cash->office_id = 2;
        $cash->transaction_type = CashOperationalTransactionTypeEnum::INITIAL_BALANCE;
        $cash->direction = CashOperationalTransactionDirectionEnum::IN;
        $cash->amount = 10000;
        $cash->amount_signed = 10000;
        $cash->created_at = now();
        $cash->save();

        $lr = new LoanRefinance();
        $lr->refinanced_loan_id = ActiveLoanSeeder::LOAN_ID;
        $lr->refinancing_loan_id = ApprovedLoanSeeder::LOAN_ID;
        $lr->save();

        $loan = app()->make(LoanForApproval::class)->buildFromExisting($dbLoan);
        $this->assertCount(0, Payment::all());
        LoanWasApproved::dispatch($loan);
        $payments = Payment::all();
        $this->assertCount(2, $payments);
        $payment = $payments[0]->method() === PaymentMethodEnum::CASH ? $payments[0] : $payments[1];
        $sut = app()->make(Sut::class);
        $paymentTasks = PaymentTask::all();
        $this->assertCount(1, $paymentTasks);

//"Приспадане от усвоен к-т 5"
        $sut->execute($payment);
        /** @var Payment $payment */
        $payment = Payment::find($payment->getKey());
        $this->assertEquals(PaymentStatusEnum::DELIVERED, $payment->status);
        $paymentTasks = PaymentTask::all();
        $this->assertCount(0, $paymentTasks);
        $activatedLoan = DbLoan::find(ApprovedLoanSeeder::LOAN_ID);
        $this->assertEquals(LoanStatus::ACTIVE_STATUS_ID, $activatedLoan->loan_status_id);
        $refinancedLoan = DbLoan::find(ActiveLoanSeeder::LOAN_ID);
        $this->assertEquals(LoanStatus::REPAID_STATUS_ID, $refinancedLoan->loan_status_id);
        //Change by Dima, RefinancedLoans::makePayments doesn't make outgoing payment for cash
        $this->assertCount(2, Payment::all());
    }
}
