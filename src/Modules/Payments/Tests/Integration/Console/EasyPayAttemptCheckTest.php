<?php

namespace Integration\Console;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\EasyPayAttempt;
use Modules\Payments\Console\EasyPayAttemptHealthCheck;
use Tests\TestCase;

class EasyPayAttemptCheckTest extends TestCase
{
    use DatabaseTransactions;

    public function testEasyPayAttempt()
    {
        $easyPayAttempt = new EasyPayAttempt();
        $easyPayAttempt->amount = 1000;
        $easyPayAttempt->pin = '123123123';
        $easyPayAttempt->client_id = null;
        $easyPayAttempt->easy_pay_transaction_id = '123456789';
        $easyPayAttempt->sent_at = now()->subMinutes(10);
        $easyPayAttempt->created_by = Administrator::DEFAULT_EASYPAY_USER_ID;
        $easyPayAttempt->created_at = now()->subMinutes(5);
        $easyPayAttempt->type = 'BILLING';
        $easyPayAttempt->save();
        $this->artisan(EasyPayAttemptHealthCheck::class, ['source_name'=>PaymentSourceEnum::EASY_PAY_API->value])
            ->assertSuccessful()
            ->expectsOutputToContain(PaymentSourceEnum::EASY_PAY_API->name)
            ->expectsTable(['time', 'pin', 'amount'], [
                [
                    $easyPayAttempt->created_at->toDateTimeString(),
                    $easyPayAttempt->pin,
                    $easyPayAttempt->amount
                ]
            ]);
    }

}
