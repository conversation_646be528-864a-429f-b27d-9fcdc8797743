<?php

namespace Modules\Product\Http\Requests;

use Modules\Common\Http\Requests\BaseRequest;
use Modules\Common\Interfaces\ListSearchInterface;
use Modules\Common\Traits\DateBuilderTrait;

class ProductSearchRequest extends BaseRequest implements ListSearchInterface
{
    public function rules(): array
    {
        return [
            'name' => $this->getConfiguration('requestRules.nameNullable'),
            'code' => $this->getConfiguration('requestRules.nameNullable'),
            'trade_name' => $this->getConfiguration('requestRules.nameNullable'),
            'productTypeId' => 'nullable|exists:product_type,product_type_id',
            'updated_by' => 'nullable|exists:administrator,administrator_id',
            'product_office_id' => 'nullable|exists:office,office_id',
            'active' => $this->getConfiguration('requestRules.active'),
            'created_at' => ['nullable', 'regex:' . $this->getDateValidationRegex()],
            'updated_at' => ['nullable', 'regex:' . $this->getDateValidationRegex()],
            'legal_status' => 'nullable|in:individual,company',
        ];
    }
}
