<?php

namespace Modules\Common\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Common\Enums\LogActionEnum;
use Modules\Common\Models\Changelog\ChangelogSmsTemplate;
use Modules\Communication\Models\SmsTemplate;

class LogSmsTemplateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle(
        LogActionEnum $action,
        string $message,
        SmsTemplate $template,
        array $from,
        array $to
    ) {
        $fillKeys = [
            'name',
            'text',
            'type',
            'active',
            'gender',
            'manual',
            'deleted',
            'variables',
            'description',
            'sms_template_id',
        ];

        $logSmsTemplate = new ChangelogSmsTemplate([
            'sms_template_id' => $template->getKey(),
            'message' => $message,
            'action' => $action->value,
            'from' => collect($from)->only($fillKeys),
            'to' => collect($to)->only($fillKeys),
        ]);
        $logSmsTemplate->save();
    }
}