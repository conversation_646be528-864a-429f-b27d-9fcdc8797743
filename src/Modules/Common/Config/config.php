<?php

use Modules\Common\Libraries\InterfaceForLaravel\Driver\DriversEnum;

return [
    'name' => 'Common',
    'menu' => include('menu.php'),
    'loanAutoIncrement' => 10000,
    'max_loan_sum_cash' => 10000,
    'max_export_rows' => 10000, /// for large export will be created queue and send to email
    'interfaces_ttl' => 3600,
    'interfaces_driver' => DriversEnum::FILE,
    'failed_login' => [
        'local' => [
            '<EMAIL>',
            '<EMAIL>',
        ],
        'stage' => [
            '<EMAIL>',
            '<EMAIL>',
        ],
        'prod' => [
            '<EMAIL>',
        ],
    ]
];
