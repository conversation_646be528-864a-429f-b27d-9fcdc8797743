<?php

declare(strict_types=1);

namespace Modules\Common\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\Common\Models\Client;
use Modules\Common\Models\NoiReport;
use Modules\Common\Models\Payment;
use Modules\Common\Models\ProductDocumentTemplate;
use Modules\Common\Models\TmpRequest;
use Modules\Common\Observers\ClientObserver;
use Modules\Common\Observers\PaymentObserver;
use Modules\Common\Observers\TmpRequestObserver;
use Modules\Product\Observers\ProductDocumentTemplateObserver;
use Modules\ThirdParty\Observers\NoiReportObserver;

final class EventServiceProvider extends ServiceProvider
{
    protected $observers = [
        ProductDocumentTemplate::class => [ProductDocumentTemplateObserver::class],
        NoiReport::class => [NoiReportObserver::class],
        Client::class => [ClientObserver::class],
        Payment::class => [PaymentObserver::class],
        TmpRequest::class => [TmpRequestObserver::class],
    ];
}
