<?php

namespace Modules\Common\Models;

/**
 * @mixin IdeHelperRepresentor
 */
class Representor extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'representor';

    /**
     * @var string
     */
    protected $primaryKey = 'representor_id';

    /**
     * @var string[]
     */
    protected $guarded = [
        'representor_id',
        'active',
        'deleted',
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
        'deleted_at',
        'deleted_by',
        'enabled_at',
        'enabled_by',
        'disabled_at',
        'disabled_by',
    ];
}
