<?php

namespace Modules\Common\Models;


/**
 * @mixin IdeHelperVeriffApiLog
 */
class VeriffApiLog extends BaseModel
{
    const STATUS_OK = 'ok';
    const STATUS_KO = 'ko';
    const STATUS_NEW = 'new';

    protected $table = 'veriff_api_log';
    protected $primaryKey = 'id';
    protected $fillable = [
        'client_id',
        'action',
        'params',
        'response',
        'details',
        'status',
        'created_by',
        'created_at',
    ];

    public static function getStatuses(): array
    {
        return [
            self::STATUS_OK,
            self::STATUS_KO,
            self::STATUS_NEW,
        ];
    }
}
