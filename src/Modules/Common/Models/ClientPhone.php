<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Interfaces\HistoryInterface;

/**
 * @property int client_id
 * @property int client_phone_id
 * @property string $number
 * @property int $last
 * @property int $seq_num
 * @mixin IdeHelperClientPhone
 */
class ClientPhone extends BaseModel implements HistoryInterface
{
    /**
     * @var string
     */
    protected $table = 'client_phone';

    /**
     * @var string
     */
    protected $primaryKey = 'client_phone_id';

    /**
     * @var string
     */
    protected $historyClass = ClientHistory::class;

    /**
     * @var string[]
     */
    protected $fillable = [
        'client_id',
        'number',
        'last',
        'seq_num',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    public function loanPhone(): BelongsTo
    {
        return $this->belongsTo(LoanPhone::class, 'client_phone_id', 'client_phone_id');
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id', 'client_id');
    }

    public function getNumber(): string
    {
        return $this->number;
    }
}

