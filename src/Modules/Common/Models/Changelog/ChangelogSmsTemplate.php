<?php

namespace Modules\Common\Models\Changelog;

use Modules\Common\Enums\LogActionEnum;

/**
 * @property LogActionEnum $action
 * @mixin IdeHelperChangelogSmsTemplate
 */
class ChangelogSmsTemplate extends ChangelogModel
{

    protected $table = 'changelog.sms_template';
    protected $primaryKey = 'changelog_sms_template_id';
    protected string $targetKey = 'sms_template_id';

    protected $fillable = [
        'changelog_sms_template_id',
        'sms_template_id',

        'created_at',
        'created_by',
        'action',
        'message',
        'from',
        'to',
    ];
}
