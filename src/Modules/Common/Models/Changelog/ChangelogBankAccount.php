<?php

namespace Modules\Common\Models\Changelog;

use Modules\Common\Enums\LogActionEnum;

/**
 * @property LogActionEnum $action
 * @mixin IdeHelperChangelogBankAccount
 */
class ChangelogBankAccount extends ChangelogModel
{

    protected $table = 'changelog.bank_account';
    protected $primaryKey = 'changelog_bank_account_id';
    protected string $targetKey = 'bank_account_id';

    protected $fillable = [
        'changelog_bank_account_id',
        'bank_account_id',

        'created_at',
        'created_by',
        'action',
        'message',
        'from',
        'to',
    ];
}
