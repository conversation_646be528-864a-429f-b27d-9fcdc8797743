<?php

namespace Modules\Common\Models;

/**
 * @mixin IdeHelperBankExportLog
 */
class BankExportLog extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'bank_export_log';

    /**
     * @var string
     */
    protected $primaryKey = 'bank_export_log_id';

    /**
     * @var string[]
     */
    protected $guarded = [
        'bank_export_log_id',
        'active',
        'deleted',
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
        'deleted_at',
        'deleted_by',
        'enabled_at',
        'enabled_by',
        'disabled_at',
        'disabled_by',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function bankExportAttempt()
    {
        return $this->belongsTo(BankExportAttempt::class);
    }
}

