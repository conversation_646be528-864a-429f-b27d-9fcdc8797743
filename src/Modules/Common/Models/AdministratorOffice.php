<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Admin\Observers\AdministratorOfficeObserver;

/**
 * @property int $administrator_office_id
 * @property int $administrator_id
 * @property int $office_id
 * @property-read Administrator $administrator
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read Office $office
 * @property-read Administrator|null $processedBy
 * @property-read Administrator|null $updater
 * @mixin IdeHelperAdministratorOffice
 */
class AdministratorOffice extends BasePivot
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'administrator_office';

    protected $fillable = [
        'administrator_id',
        'office_id',
    ];

    protected static function boot()
    {
        parent::boot();

        self::observe(AdministratorOfficeObserver::class);
    }

    public function administrator()
    {
        return $this->belongsTo(
            Administrator::class,
            'administrator_id',
            'administrator_id'
        );
    }

    public function office()
    {
        return $this->belongsTo(
            Office::class,
            'office_id',
            'office_id'
        );
    }
}
