<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use K<PERSON>lik\ColumnSortable\Sortable;
use Modules\Common\Database\Collections\CustomEloquentCollection;

/**
 * @property int $installment_id
 * @property int $client_id
 * @property int $loan_id
 * @property int $seq_num
 * @property Carbon $due_date
 * @property string|null $accrued_total_amount
 * @property string|null $total_amount
 * @property string|null $principal
 * @property string|null $paid_principal
 * @property string|null $rest_principal
 * @property string|null $accrued_interest
 * @property string|null $interest
 * @property string|null $late_interest
 * @property string|null $paid_accrued_interest
 * @property string|null $paid_interest
 * @property string|null $rest_interest
 * @property string|null $paid_late_interest
 * @property string|null $accrued_penalty
 * @property string|null $penalty
 * @property string|null $late_penalty
 * @property string|null $paid_accrued_penalty
 * @property string|null $paid_penalty
 * @property string|null $rest_penalty
 * @property string|null $paid_late_penalty
 * @property int $overdue_days
 * @property string $overdue_amount
 * @property int $max_overdue_days
 * @property string $max_overdue_amount
 * @property string $status
 * @property int $paid
 * @property string|null $paid_at
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property int $has_payment
 * @property string|null $first_payment_received_at
 * @property int|null $days_passed_before_first_payment
 * @property string|null $max_overdue_date
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read Loan $loan
 * @property-read Client $client
 * @property-read CustomEloquentCollection<int, Payment> $payment
 * @property-read Administrator|null $processedBy
 * @property-read CustomEloquentCollection<int, Tax> $taxes
 * @property-read Administrator|null $updater
 * @mixin IdeHelperInstallment
 */
class Installment extends BaseModel
{
    use Sortable;

    public const INSTALLMENT_STATUS_SCHEDULED = 'scheduled';
    public const INSTALLMENT_STATUS_LATE = 'late';
    public const INSTALLMENT_STATUS_PAID = 'paid';
    public const INSTALLMENT_STATUS_PAID_EARLY = 'paid_early';

    private $dueDatePassed = null;

    protected $traitCasts = [
        'active' => 'boolean',
        'deleted' => 'boolean',
        'due_date' => 'date:d-m-Y',
        'created_at' => 'datetime:d-m-Y H:i',
        'updated_at' => 'datetime:d-m-Y H:i',
        'deleted_at' => 'datetime:d-m-Y H:i',
        'enabled_at' => 'datetime:d-m-Y H:i',
        'disabled_at' => 'datetime:d-m-Y H:i',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'deleted_by' => 'integer',
        'enabled_by' => 'integer',
        'disabled_by' => 'integer',
    ];
    protected $table = 'installment';
    protected $primaryKey = 'installment_id';

    protected $fillable = [
        'client_id',
        'loan_id',
        'seq_num',
        'due_date',
        'accrued_total_amount',
        'total_amount',
        'principal',
        'paid_principal',
        'rest_principal',
        'accrued_interest',
        'interest',
        'late_interest',
        'paid_accrued_interest',
        'paid_interest',
        'rest_interest',
        'paid_late_interest',
        'accrued_penalty',
        'penalty',
        'late_penalty',
        'paid_accrued_penalty',
        'paid_penalty',
        'rest_penalty',
        'paid_late_penalty',
        'overdue_days',
        'overdue_amount',
        'max_overdue_days',
        'max_overdue_amount',
        'status',
        'paid',
        'paid_at',
        'active',
        'deleted',
        'created_by',
        'updated_by',
        'deleted_by',
        'enabled_at',
        'enabled_by',
        'disabled_at',
        'disabled_by',
        'has_payment',
        'first_payment_received_at',
        'days_passed_before_first_payment',
        'max_overdue_date',
        'late_updated_at',
        'late_updated_log',
        'rest_late_penalty',
        'migration_db',
        'migration_id',
        'accrued_updated_at',
        'accrued_updated_log',
        'rest_late_interest'
    ];
    protected $historyClass = InstallmentHistory::class;

    public array $sortable = [
        'loan_id',
        'seq_num',
        'due_date',
        'primary_total_rest_amount',
    ];

    public function getExtendedDueDate(int $extendDays): Carbon
    {
        /// if not (+1 month) return default incrementing
        if ("+1 month" !== $this->loan->installment_modifier) {
            return $this->due_date->addDays($extendDays);
        }

        //// put in cache for 10sec to do not regenerate always
        $installmentsCollection = Cache::remember(__METHOD__, 5, function () use ($extendDays) {
            $loanConfig = $this->loan->getLoanConfig();
            $loanConfig['utilisationDate'] = $this->due_date->addDays($extendDays)->subMonth();
            $loanConfig['hasCustomPaymentSchedule'] = [];

            $installments = $this->loan->getCredit(customConfig: $loanConfig)
                ->installmentsCollection()
                ->toArray();

            $currentIndex = $this->seq_num;
            $reindexedInstallments = [];
            foreach ($installments as $installment) {
                $reindexedInstallments[$currentIndex] = $installment;
                $currentIndex++;
            }

            return $reindexedInstallments;
        });

        if (empty($installmentsCollection[$this->seq_num])) {
            throw new \Exception('Error installment not found');
        }

        return Carbon::parse($installmentsCollection[$this->seq_num]->dueDate);
    }

    public function installmentSnapshot(): HasOne
    {
        return $this->hasOne(InstallmentSnapshot::class, 'installment_id', 'installment_id');
    }

    public static function getInstallmentStatuses(): array
    {
        return [
            self::INSTALLMENT_STATUS_SCHEDULED,
            self::INSTALLMENT_STATUS_PAID,
            self::INSTALLMENT_STATUS_LATE,
        ];
    }

    public function getPreviousInstallment(): ?Installment
    {
        if ($this->seq_num == 1) {
            return null;
        }

        return Installment::where(
            [
                ['loan_id', '=', $this->loan_id],
                ['seq_num', '=', ($this->seq_num - 1)],
            ]
        )->first();
    }

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(
            Client::class,
            'client_id',
            'client_id'
        );
    }

    public function getNextInstallment(int $loanId): ?Installment
    {
        $nextInstallment = Installment::where('loan_id', $loanId)
            ->where('due_date', '>=', Carbon::now()->format('Y-m-d'))
            ->where('paid', 0)
            ->orderBy('seq_num', 'ASC')
            ->first();

        if ($nextInstallment) {
            return $nextInstallment;
        }

        /// if all installments is overdue
        /// or is a payday return the last installment

        return Installment::where('loan_id', $loanId)
            ->orderBy('seq_num', 'DESC')
            ->first();
    }

    public function isPaid(): bool
    {
        if ($this->paid == 1) {
            return true;
        }

        return false;
    }

    public function getRestLateInterest(): float
    {
        return (float) $this->rest_late_interest;
    }

    public function getRestLatePenalty(): float
    {
        return (float) $this->rest_late_penalty;
    }

    public function taxes()
    {
        return $this->hasMany(
            Tax::class,
            'installment_id',
            'installment_id'
        );
    }

    public function getCssLabel(): string
    {
        if ($this->status == self::INSTALLMENT_STATUS_SCHEDULED) {
            return ''; // bg-warning
        }

        if ($this->status == self::INSTALLMENT_STATUS_LATE) {
            return 'bg-danger-new';
        }

        return 'bg-success-new';
    }

    public function getPrimaryTotalAmount(): float
    {
        return round(
            (
                $this->principal
                + $this->interest
                + $this->penalty
                + $this->late_interest
                + $this->late_penalty
            ),
            2
        );
    }

    public function getPrimaryTotalRestAmount(): float
    {
        return round(
            (
                $this->rest_principal
                + $this->rest_interest
                + $this->rest_penalty
                + $this->rest_late_interest
                + $this->rest_late_penalty
            ),
            2
        );
    }

    public function getPrimaryTotalPaidAmount(): float
    {
        return round(
            (
                $this->paid_principal
                + $this->paid_interest
                + $this->paid_penalty
                + $this->paid_late_interest
                + $this->paid_late_penalty
            ),
            2
        );
    }

    public function getPrimaryAmount(): float
    {
        return round(
            (
                $this->principal
                + $this->interest
                + $this->penalty
            ),
            2
        );
    }

    public function getUnpaidPrimaryAmount(): float
    {
        return round(
            (
                $this->rest_principal
                + $this->rest_interest
                + $this->rest_penalty
            ),
            2
        );
    }

    public function getUnpaidTotalAmount(): float
    {
        return round(
            (
                ($this->principal + $this->interest + $this->penalty)
                + ($this->late_interest + $this->late_penalty)
                - ($this->paid_principal + $this->paid_interest + $this->paid_penalty)
                - ($this->paid_late_interest + $this->paid_late_penalty)
                + $this->getRestOfCollectorTaxes()
            ),
            2
        );
    }

    public function getRestOfCollectorTaxes(): float
    {
        $taxes = Tax::where('installment_id', $this->installment_id)
            ->where('paid', 0)
            ->where('amount', '>', 0)
            ->get();

        if ($taxes->count() < 1) {
            return 0;
        }

        $totalUnpaidAmount = $taxes->sum(function ($tax) {
            return $tax->amount - $tax->paid_amount;
        });

        return round($totalUnpaidAmount, 2);
    }

    public function getSumTaxesRestAmount(): int
    {
        if ($this->seq_num === 1) {
            $startDate = $this->loan->activated_at;
        } else {
            $startDate = self::where(['seq_num' => $this->seq_num - 1, 'loan_id' => $this->loan_id])
                ->first(['due_date'])
                ->due_date;
        }

        return Tax::where(['loan_id' => $this->loan_id, 'paid' => 0])
            ->whereBetween('created_at', [$startDate, $this->due_date])
            ->where('active', 1)
            ->sum('rest_amount');
    }

    public function getSumsOfTaxes()
    {
        if ($this->seq_num === 1) {
            $startDate = $this->loan->activated_at;
        } else {
            $startDate = self::where(['loan_id' => $this->loan_id, 'seq_num' => $this->seq_num - 1])
                ->first(['due_date'])
                ->due_date;
        }

        return Tax::where([
                'loan_id' => $this->loan_id,
                'active' => 1,
            ])
            ->whereRaw('(
                installment_id = ' . $this->installment_id . '
                OR (
                    installment_id IS NULL'
                    . " AND created_at >= '" . Carbon::parse($startDate)->startOfDay()->format('Y-m-d H:i:s') . "'"
                    . " AND created_at <= '" . Carbon::parse($this->due_date)->endOfDay()->format('Y-m-d H:i:s') . "'"
                . ')'
            . ')')
            ->selectRaw('
                SUM(amount) as total_amount,
                SUM(rest_amount) as total_rest_amount,
                SUM(paid_amount) as total_paid_amount
            ')
            ->first();
    }

    public function primaryTotalRestAmountSortable(Builder $query, string $direction): Builder
    {
        return $query->orderByRaw(
            'ROUND(rest_principal + rest_interest + rest_penalty + rest_late_interest + rest_late_penalty, 2) ' . $direction
        );
    }
}
