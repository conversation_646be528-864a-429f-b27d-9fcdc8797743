<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Storage;

/**
 * @mixin IdeHelperFile
 */
class File extends BaseModel
{
    protected $table = 'file';
    protected $primaryKey = 'file_id';
    protected $fillable = [
        'hash',
        'file_storage_id',
        'file_type_id',
        'file_path',
        'file_size',
        'file_type',
        'file_name',
        'comment',
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    public function fileStorage(): BelongsTo
    {
        return $this->belongsTo(FileStorage::class);
    }

    public function fileType(): BelongsTo
    {
        return $this->belongsTo(FileType::class, 'file_type_id', 'file_type_id');
    }

    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(
            Client::class,
            'client_file',
            'file_id',
            'client_id'
        );
    }

    public function emails(): BelongsToMany
    {
        return $this->belongsToMany(
            Email::class,
            'email_files',
            'email_id',
            'file_id'
        );
    }

    public function loans(): BelongsToMany
    {
        return $this->belongsToMany(Loan::class, 'loans_file');
    }

    public function document(): HasOne
    {
        return $this->hasOne(Document::class, 'file_id', 'file_id');
    }

    public function getCustomDocument(): ?Document
    {
        return Document::withTrashed()->where('file_id', $this->file_id)->first();
    }

    public function downloadFilePath(): string
    {
        $filepath = $this->file_path . $this->file_name;

        $storage = str_contains($this->file_path, 'client-doc') ? 'client-doc' : 'documents';

        return Storage::disk($storage)->url($filepath);
    }

    public function filepath(): ?string
    {
        if ($this->file_path && $this->file_name) {
            return storage_path() . '/' . $this->file_path . $this->file_name;
        }
        return null;
    }

    public function getBase64(): string
    {
        $filepath = $this->file_path . $this->file_name;
        $fullPath = storage_path() . '/' . $filepath;

        if (file_exists($filepath)) {
            $fileContents = file_get_contents($filepath);
            return base64_encode($fileContents);
        }

        return '';
    }
}
