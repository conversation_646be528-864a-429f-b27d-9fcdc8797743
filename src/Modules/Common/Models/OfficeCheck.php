<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Modules\Common\Database\Collections\CustomEloquentCollection;
use Modules\Common\Enums\ThirdPartyReportServiceEnum;

/**
 * Modules\Common\Models\OfficeCheck
 *
 * @property int $id
 * @property int $office_id
 * @property ThirdPartyReportServiceEnum $service_key service_key
 * @property Carbon|null $created_at when it was created
 * @property int|null $created_by
 * @property Carbon|null $updated_at when it was updated
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at when it was removed
 * @property int|null $deleted_by
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read Administrator|null $processedBy
 * @property-read Administrator|null $updater
 * @property-read Office|null $office
 * @method static CustomEloquentCollection<int, static> all($columns = ['*'])
 * @method static Builder|BaseModel filterBy(array $filters)
 * @method static CustomEloquentCollection<int, static> get($columns = ['*'])
 * @mixin IdeHelperOfficeCheck
 */
class OfficeCheck extends BaseModel
{
    public $table = 'office_check';

    public $primaryKey = 'office_check_id';

    protected $casts = [
        'service_key' => ThirdPartyReportServiceEnum::class
    ];

    protected $fillable = [
        'office_id',
        'service_key',
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
        'deleted_at',
        'deleted_by',
    ];

    public function office(): HasOne
    {
        return $this->hasOne(Office::class, 'office_id', 'office_id');
    }
}
