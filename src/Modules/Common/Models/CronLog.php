<?php

namespace Modules\Common\Models;

use Illuminate\Support\Carbon;

/**
 * @property int $cron_log_id
 * @property string $command
 * @property string|null $file
 * @property string|null $message
 * @property int|null $total
 * @property int|null $imported
 * @property int|null $attempt
 * @property string|null $total_exec_time
 * @property string|null $last_exec_time
 * @property Carbon|null $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property bool $active
 * @property bool $deleted
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read Administrator|null $processedBy
 * @property-read Administrator|null $updater
 * @mixin IdeHelperCronLog
 */
class CronLog extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'cron_log';

    /**
     * @var string
     */
    protected $primaryKey = 'cron_log_id';

    /**
     * @var string[]
     */
    protected $fillable = [
        'command',
        'file',
        'message',
        'total',
        'imported',
        'attempt',
        'total_exec_time',
        'last_exec_time',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    public static function start(
        string $command,
        ?string $file = null,
        ?string $message = null
    ): CronLog
    {
        $cronLog = new CronLog();
        $cronLog->command = $command;
        if($file) {
            $cronLog->file = $file;
        }
        if ($message) {
            $cronLog->message = $message;
        }
        $cronLog->save();

        return $cronLog;
    }

    /**
     * @param string $startAt
     * @param int|null $total
     * @param int|null $imported
     * @param string|null $msg
     *
     * @return $this|null
     */
    public function finish(
        string $startAt,
        int $total = null,
        int $imported = null,
        string $msg = null
    ): ?CronLog {
        // skip if nothing to import OR already imported
        if (
            (0 === $total && 0 === $this->total)
            || (null !== $this->imported && $this->imported === $this->total)
        ) {
            return null;
        }

        if (empty($this->attempt)) {
            $this->attempt = 1;
        } else {
            $this->attempt += 1;
        }

        if (null != $total) {
            $this->total = $total;
        }

        if (!empty($imported)) {
            $this->imported = empty($this->imported)
                ? $imported
                : $this->imported + $imported;
        }

        if (!empty($msg)) {
            $this->message = $msg;
        }

        $now = microtime(true);
        $this->last_exec_time = round(($now - $startAt), 2);
        $this->total_exec_time = round(($now - $this->created_at->getTimeStamp()), 2);

        $this->save();

        return $this;
    }
}
