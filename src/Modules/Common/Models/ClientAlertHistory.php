<?php

namespace Modules\Common\Models;

/**
 * @todo Check usages
 * @mixin IdeHelperClientAlertHistory
 */
class ClientAlertHistory extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'client_alert_history';

    protected $primaryKey = 'client_alert_history_id';

    /**
     * @var string[]
     */
    protected $fillable = [
        'details',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function clients()
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function cities()
    {
        return $this->belongsTo(City::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function clientAlertType()
    {
        return $this->belongsTo(ClientAlertType::class);
    }
}
