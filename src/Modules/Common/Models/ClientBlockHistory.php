<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperClientBlockHistory
 */
class ClientBlockHistory extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'client_block_history';

    protected $primaryKey = 'client_block_history_id';

    /**
     * @var string[]
     */
    protected $fillable = [
        'client_id',
        'block_reason_id',
        'comment',
        'last',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(
            Client::class,
            'client_id',
            'client_id'
        );
    }

    public function reason(): BelongsTo
    {
        return $this->belongsTo(
            BlockReason::class,
            'block_reason_id',
            'block_reason_id'
        );
    }
}
