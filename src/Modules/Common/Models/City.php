<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Modules\Common\Database\Collections\CustomEloquentCollection;

/**
 * @property int $city_id
 * @property string $code
 * @property string $name
 * @property int $municipality_id
 * @property string $slug
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property-read CustomEloquentCollection|ClientAddress[] $clientAddress
 * @property-read int|null $client_address_count
 * @property-read CustomEloquentCollection|ClientAlertHistory[] $clientAlertHistory
 * @property-read int|null $client_alert_history_count
 * @property-read CustomEloquentCollection|ClientEmployer[] $clientEmployer
 * @property-read int|null $client_employer_count
 * @property-read CustomEloquentCollection|ClientIdCard[] $clientIdCard
 * @property-read int|null $client_id_card_count
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read CustomEloquentCollection|GuarantAddress[] $guarantAddress
 * @property-read int|null $guarant_address_count
 * @property-read Administrator|null $handler
 * @property-read Municipality $municipality
 * @property-read Office|null $office
 * @property-read Administrator|null $processedBy
 * @property-read Administrator|null $updater
 * @mixin IdeHelperCity
 */
class City extends BaseModel
{
    protected $table = 'city';

    protected $primaryKey = 'city_id';

    protected $fillable = [
        'code',
        'name',
        'municipality_id',
        'slug',
    ];

    public function office(): BelongsTo
    {
        return $this->belongsTo(Office::class);
    }

    public function clientAddress(): HasMany
    {
        return $this->hasMany(ClientAddress::class, 'city_id', 'city_id');
    }

    public function guarantAddress(): HasMany
    {
        return $this->hasMany(GuarantAddress::class);
    }

    public function clientAlertHistory(): HasMany
    {
        return $this->hasMany(ClientAlertHistory::class);
    }

    public function clientEmployer(): HasMany
    {
        return $this->hasMany(ClientEmployer::class);
    }

    public function clientIdCard(): HasMany
    {
        return $this->hasMany(ClientIdCard::class);
    }

    public function municipality(): BelongsTo
    {
        return $this->belongsTo(
            Municipality::class,
            'municipality_id',
            'municipality_id'
        );
    }

    public static function getAll(bool $withPlaceholder = true): array
    {
        $rows = Cache::remember(
            'cities:forms',
            now()->addHours(24),
            fn() => self::where('deleted', '0')->orderBy('name', 'ASC')
                ->get(['city_id', 'name'])->toArray()
        );

        if (count($rows) < 1) {
            return [];
        }

        $result = [];
        foreach ($rows as $row) {
            if (!$withPlaceholder && $row['city_id'] === 99999) {
                continue;
            }
            $result[$row['city_id']] = $row['name'];
        }

        return $result;
    }
}
