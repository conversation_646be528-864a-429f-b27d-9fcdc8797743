<?php

namespace Modules\Common\Models;

/**
 * @todo delete
 * @mixin IdeHelperSource
 */
class Source extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'source';

    /**
     * @var string
     */
    protected $primaryKey = 'source_id';

    /**
     * @var string[]
     */
    protected $guarded = [
        'source_id',
        'active',
        'deleted',
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
        'deleted_at',
        'deleted_by',
        'enabled_at',
        'enabled_by',
        'disabled_at',
        'disabled_by',
    ];
}
