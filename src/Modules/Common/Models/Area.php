<?php

namespace Modules\Common\Models;

/**
 * @mixin IdeHelperArea
 */
class Area extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'area';

    /**
     * @var string
     */
    protected $primaryKey = 'area_id';

    /**
     * @var string[]
     */
    protected $guarded = [
        'area_id',
        'active',
        'deleted',
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
        'deleted_at',
        'deleted_by',
        'enabled_at',
        'enabled_by',
        'disabled_at',
        'disabled_by',
    ];

}

