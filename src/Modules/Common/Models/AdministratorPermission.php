<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @mixin IdeHelperAdministratorPermission
 */
class AdministratorPermission extends BasePivot
{
    use HasFactory;
    const CREATED_AT = null;
    const UPDATED_AT = null;

    protected $table = 'administrator_permission';
    protected $primaryKey = 'administrator_permission_id';

    protected $casts = [
        'permission_id' => 'int',
        'administrator_id' => 'int',
        'additional_info' => 'array',
    ];

    protected $fillable = [
        'permission_id',
        'model_type',
        'administrator_id',
        'additional_info',
    ];
}
