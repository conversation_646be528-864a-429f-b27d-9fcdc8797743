<?php

namespace Modules\Common\Models;

/**
 * @mixin IdeHelperMunicipality
 */
class Municipality extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'municipality';

    /**
     * @var string[]
     */
    protected $fillable = [
        'code',
        'name',
        'area_id',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cities()
    {
        return $this->hasMany(
            City::class,
            'municipality_id',
            'municipality_id'
        );
    }

    public function area()
    {
        return $this->belongsTo(
            Area::class,
            'area_id',
            'area_id'
        );
    }
}
