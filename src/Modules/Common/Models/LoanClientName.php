<?php

namespace Modules\Common\Models;

use Modules\Common\Interfaces\HistoryInterface;
/**
 * @property int loan_id
 * @property int client_name_id
 * @mixin IdeHelperLoanClientName
 */
class LoanClientName extends BaseModel implements HistoryInterface
{
    protected $table = 'loan_client_name';
    protected $primaryKey = 'id';
    protected $historyClass = LoanHistory::class;

    protected $fillable = [
        'client_name_id',
        'loan_id',
        'last',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function loan()
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }
}
