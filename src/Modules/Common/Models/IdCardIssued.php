<?php

namespace Modules\Common\Models;

use Illuminate\Support\Carbon;

/**
 * \Modules\Common\Models\IdCardIssued
 *
 * @property int $idcard_issued_id
 * @property string|null $name
 * @property int $last
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read Administrator|null $processedBy
 * @property-read Administrator|null $updater
 * @mixin IdeHelperIdCardIssued
 */
class IdCardIssued extends BaseModel
{
    protected $table = 'idcard_issued';

    protected $primaryKey = 'idcard_issued_id';
}
