<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Modules\Common\Interfaces\HistoryInterface;

/**
 * @property int $client_email_id
 * @property int $client_id
 * @property string|null $email
 * @property int $last
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property-read Client|null $client
 * @mixin IdeHelperClientEmail
 */
class ClientEmail extends BaseModel implements HistoryInterface
{
    /**
     * @var string
     */
    protected $table = 'client_email';

    protected $primaryKey = 'client_email_id';

    protected $historyClass = ClientHistory::class;


    /**
     * @var string[]
     */
    protected $fillable = [
        'client_id',
        'email',
        'last',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }
}
