<?php

namespace Modules\Common\Models;

use Eloquent;
use Illuminate\Support\Carbon;

/**
 * \Modules\Common\Models\FiscalReceipt
 *
 * @property int $fiscal_receipt_id
 * @property int $cash_operational_transaction_id
 * @property string $serial_number
 * @property string $operator_num
 * @property string $fm_num
 * @property string $fr_num
 * @property string $fr_date_time
 * @property string $urn
 * @property int $unique_num
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read Administrator|null $processedBy
 * @property-read Administrator|null $updater
 * @mixin IdeHelperFiscalReceipt
 */
class FiscalReceipt extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'fiscal_receipt';
    /**
     * @var string
     */
    protected $primaryKey = 'fiscal_receipt_id';

    protected $fillable = [
        'cash_operational_transaction_id',
        'operator_num',
        'fm_num',
        'fr_num',
        'fr_date_time',
        'urn',
        'unique_num',
        'serial_number',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];
}

