<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperClientReferral
 */
class ClientReferral extends BaseModel
{
    public const MAX_REFERRALS_PER_DAY = 10;

    protected $table = 'client_referral';

    protected $fillable = [
        'client_id',
        'loan_id',
        'phone',
        'sms_id',
    ];

    public function sms(): BelongsTo
    {
        return $this->belongsTo(Sms::class, 'sms_id', 'sms_id');
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class, 'client_id', 'client_id');
    }

    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class, 'loan_id', 'loan_id');
    }
}
