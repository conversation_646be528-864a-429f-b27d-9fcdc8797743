<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperClientUnBlockHistory
 */
class ClientUnBlockHistory extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'client_unblock_history';

    protected $primaryKey = 'client_unblock_history_id';

    /**
     * @var string[]
     */
    protected $fillable = [
        'client_id',
        'comment',
        'last',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(
            Client::class,
            'client_id',
            'client_id'
        );
    }
}
