<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Entities\CommunicationPivot;
use Modules\Communication\Events\CommunicationWasCreatedEvent;

/**
 * @property int $client_id
 * @property int $loan_id
 * @property int $administrator_id
 * @property int $created_by
 * @property string $text
 * @mixin IdeHelperCommunicationComment
 */
class CommunicationComment extends BaseModel
{
    protected $table = 'communication_comment';

    protected $primaryKey = 'communication_comment_id';

    protected $dispatchesEvents = [
        'created' => CommunicationWasCreatedEvent::class
    ];

    protected $fillable = [
        'client_id',
        'loan_id',
        'administrator_id',
        'text',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(
            Client::class,
            'client_id',
            'client_id'
        );
    }

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }

    public function administrator(): BelongsTo
    {
        return $this
            ->belongsTo(
                Administrator::class,
                'administrator_id',
                'administrator_id'
            )
            ->select(
                ['administrator_id', 'first_name', 'middle_name', 'last_name']
            );
    }
}
