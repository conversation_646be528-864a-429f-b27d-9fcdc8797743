<?php

namespace Modules\Common\Models;

use Illuminate\Support\Carbon;
use Modules\Common\Database\Collections\CustomEloquentCollection;

/**
 * @property string $setting_key
 * @property string $name
 * @property string $description
 * @property string $default_value
 * @property int $setting_type_id
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property-read CustomEloquentCollection|Administrator[] $administrators
 * @property-read int|null $administrators_count
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read CustomEloquentCollection|Office[] $offices
 * @property-read int|null $offices_count
 * @property-read Administrator|null $processedBy
 * @property-read SettingType $settingType
 * @property-read Administrator|null $updater
 * @method static where(int[] $conditions)
 * @method static Setting|null find(int $id)
 * @mixin IdeHelperAdministratorSetting
 */
class AdministratorSetting extends BaseModel
{
    protected $table = 'administrator_setting';
    protected $primaryKey = 'setting_key';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'setting_key',
        'name',
        'description',
        'default_value',
        'setting_type_id',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];
}
