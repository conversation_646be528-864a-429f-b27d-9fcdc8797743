<?php

namespace Modules\Common\Models;

use Modules\Communication\Models\SmsTemplate;

/**
 * @mixin IdeHelperOfficeSmsTemplate
 */
class OfficeSmsTemplate extends BasePivot
{
    /**
     * @var string
     */
    protected $table = 'office_sms_template';

    /**
     * @var string[]
     */
    protected $fillable = [
        'office_id',
        'sms_template_id',
    ];

    public $timestamps = false;

    public function office()
    {
        return $this->belongsTo(
            Office::class,
            'office_id',
            'office_id'
        );
    }

    public function smsTemplate()
    {
        return $this->belongsTo(
            SmsTemplate::class,
            'sms_template_id',
            'sms_template_id'
        );
    }
}
