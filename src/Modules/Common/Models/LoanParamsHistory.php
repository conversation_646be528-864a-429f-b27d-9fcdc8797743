<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Modules\Common\Database\Collections\CustomEloquentCollection;

/**
 * @property int $loan_params_history_id
 * @property int $loan_id
 * @property int|null $product_id
 * @property string $amount_requested
 * @property int|null $period_requested
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property int|null $discount_percent
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read Loan $loan
 * @property-read Administrator|null $processedBy
 * @property-read Product|null $product
 * @property-read Administrator|null $updater
 * @method static CustomEloquentCollection<int, static> all($columns = ['*'])
 * @method static Builder|BaseModel filterBy(array $filters)
 * @mixin IdeHelperLoanParamsHistory
 */
class LoanParamsHistory extends BaseModel
{
    protected $table = 'loan_params_history';

    protected $primaryKey = 'loan_params_history_id';

    protected $fillable = [
        'loan_params_history_id',
        'amount_requested',
        'period_requested',
        'loan_id',
        'discount_percent',
        'product_id',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    const CHANGES_TO_TRACK = [
        'amount_requested',
        'period_requested',
        'discount_percent',
        'product_id'
    ];

    public function loan()
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }

    public function product()
    {
        return $this->belongsTo(
            Product::class,
            'product_id',
            'product_id'
        );
    }

}
