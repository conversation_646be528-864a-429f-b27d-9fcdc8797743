<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;


/**
 * @property int $loan_contact_actual_id
 * @property int $client_id
 * @property int $loan_id
 * @property int $contact_id
 * @property int $contact_type_id
 * @property int $seq_num
 * @property Carbon|null $created_at
 * @property int|null $created_by
 * @property bool $active
 * @property bool $deleted
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property-read Client $client
 * @property-read Contact $contact
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read Loan $loan
 * @property-read Administrator|null $processedBy
 * @property-read Administrator|null $updater
 * @mixin IdeHelperLoanContactActual
 */
class LoanContactActual extends BaseModel
{
    const UPDATED_AT = null;
    protected $table = 'loan_contact_actual';
    protected $primaryKey = 'loan_contact_actual_id';
    protected $fillable = [
        'client_id',
        'loan_id',
        'contact_id',
        'contact_type_id',
        'seq_num',
        'created_at',
        'created_by',
    ];


    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id',
        );
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(
            Client::class,
            'client_id',
            'client_id',
        );
    }

    public function contact(): BelongsTo
    {
        return $this->belongsTo(
            Contact::class,
            'contact_id',
            'contact_id',
        );
    }
}
