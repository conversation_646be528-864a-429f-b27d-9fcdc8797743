<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperLoanStatus
 */
class LoanStatus extends BaseModel
{
    use HasFactory;

    public const NEW_STATUS_ID = 1;
    public const SIGNED_STATUS_ID = 2;
    public const PROCESSING_STATUS_ID = 3;
    public const APPROVED_STATUS_ID = 5;
    public const ACTIVE_STATUS_ID = 6;
    public const REPAID_STATUS_ID = 7;
    public const CANCELLED_STATUS_ID = 8;
    public const WRITTEN_OF_STATUS_ID = 9;

    public const STATUS_NEW = 'new';
    public const STATUS_SIGNED = 'signed';
    public const STATUS_PROCESSING = 'processing';
    public const STATUS_DISAPPROVED = 'disapproved';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_ACTIVE = 'active';
    public const STATUS_REPAID = 'repaid';
    public const STATUS_CANCELLED = 'cancelled';
    public const STATUS_WRITTEN_OFF = 'written_off';

    public const NOT_CHANGEABLE_STATUSES = [
        LoanStatus::ACTIVE_STATUS_ID,
        LoanStatus::REPAID_STATUS_ID,
        LoanStatus::CANCELLED_STATUS_ID,
        LoanStatus::WRITTEN_OF_STATUS_ID,
    ];

    public const FINAL_STATUSES = [
        LoanStatus::REPAID_STATUS_ID,
        LoanStatus::CANCELLED_STATUS_ID,
        LoanStatus::WRITTEN_OF_STATUS_ID,
    ];

    public const ACTIVE_STATUSES = [
        LoanStatus::ACTIVE_STATUS_ID,
        LoanStatus::REPAID_STATUS_ID,
        LoanStatus::WRITTEN_OF_STATUS_ID,
    ];

    public const STATUSES = [
        LoanStatus::NEW_STATUS_ID => LoanStatus::STATUS_NEW,
        LoanStatus::SIGNED_STATUS_ID => LoanStatus::STATUS_SIGNED,
        LoanStatus::PROCESSING_STATUS_ID => LoanStatus::STATUS_PROCESSING,
        LoanStatus::APPROVED_STATUS_ID => LoanStatus::STATUS_APPROVED,
        LoanStatus::ACTIVE_STATUS_ID => LoanStatus::STATUS_ACTIVE,
        LoanStatus::REPAID_STATUS_ID => LoanStatus::STATUS_REPAID,
        LoanStatus::CANCELLED_STATUS_ID => LoanStatus::STATUS_CANCELLED,
        LoanStatus::WRITTEN_OF_STATUS_ID => LoanStatus::STATUS_WRITTEN_OFF,
    ];

    public const STATUS_IDS = [
        LoanStatus::STATUS_NEW => LoanStatus::NEW_STATUS_ID,
        LoanStatus::STATUS_SIGNED => LoanStatus::SIGNED_STATUS_ID,
        LoanStatus::STATUS_PROCESSING => LoanStatus::PROCESSING_STATUS_ID,
        LoanStatus::STATUS_APPROVED => LoanStatus::APPROVED_STATUS_ID,
        LoanStatus::STATUS_ACTIVE => LoanStatus::ACTIVE_STATUS_ID,
        LoanStatus::STATUS_REPAID => LoanStatus::REPAID_STATUS_ID,
        LoanStatus::STATUS_CANCELLED => LoanStatus::CANCELLED_STATUS_ID,
        LoanStatus::STATUS_WRITTEN_OFF => LoanStatus::WRITTEN_OF_STATUS_ID,
    ];

    public const APPROVED_STATUSES = [
        LoanStatus::APPROVED_STATUS_ID,
        LoanStatus::ACTIVE_STATUS_ID,
        LoanStatus::REPAID_STATUS_ID,
        LoanStatus::WRITTEN_OF_STATUS_ID,
    ];

    public static function stateMap(int $statusId): array
    {
        //to what statuses can current status progress
        return match ($statusId) {
            self::NEW_STATUS_ID => [self::SIGNED_STATUS_ID, self::CANCELLED_STATUS_ID],
            self::SIGNED_STATUS_ID => [self::PROCESSING_STATUS_ID, self::CANCELLED_STATUS_ID],
            self::PROCESSING_STATUS_ID => [self::NEW_STATUS_ID, self::SIGNED_STATUS_ID, self::CANCELLED_STATUS_ID, self::APPROVED_STATUS_ID],
            self::APPROVED_STATUS_ID => [self::CANCELLED_STATUS_ID, self::ACTIVE_STATUS_ID],
            self::ACTIVE_STATUS_ID => [self::CANCELLED_STATUS_ID, self::REPAID_STATUS_ID, self::WRITTEN_OF_STATUS_ID],
            self::REPAID_STATUS_ID => [self::ACTIVE_STATUS_ID],//only for refinanced is refinancing is canceled
            self::CANCELLED_STATUS_ID, self::WRITTEN_OF_STATUS_ID => []
        };
    }

    public static function isAllowedChangeTo(int $fromStatusId, int $toStatusId): bool
    {
        return in_array($toStatusId, self::stateMap($fromStatusId));
    }


    protected $table = 'loan_status';
    protected $primaryKey = 'loan_status_id';

    /**
     * @var string[]
     */
    protected $guarded = [
        'loan_status_id',
        'active',
        'deleted',
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
        'deleted_at',
        'deleted_by',
        'enabled_at',
        'enabled_by',
        'disabled_at',
        'disabled_by',
    ];

    public function label(): string
    {
        return __('head::loanStatus.' . $this->loan_status_id);
    }

    public function getStatusCssClass(): string
    {
        return match ($this->name) {
            self::STATUS_NEW => 'bg-warning-new',
            self::STATUS_SIGNED => 'bg-cyan-new',
            self::STATUS_PROCESSING => 'bg-info-new',
            self::STATUS_APPROVED => 'bg-primary-new',
            self::STATUS_ACTIVE => 'bg-success-new',
            self::STATUS_REPAID => 'bg-secondary-new',
            self::STATUS_CANCELLED,
            self::STATUS_WRITTEN_OFF => 'bg-danger-new',
        };
    }

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_status_id',
            'loan_status_id'
        );
    }

    public static function getStatusesForA4e(): array
    {
        return [
            self::ACTIVE_STATUS_ID,
            self::REPAID_STATUS_ID,
            self::CANCELLED_STATUS_ID,
            self::WRITTEN_OF_STATUS_ID,
        ];
    }
}
