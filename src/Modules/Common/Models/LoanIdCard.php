<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Interfaces\HistoryInterface;

/**
 * @property int loan_id
 * @property int client_idcard_id
 * @mixin IdeHelperLoanIdCard
 */
class LoanIdCard extends BaseModel implements HistoryInterface
{
    protected $table = 'loan_idcard';
    protected $primaryKey = 'id';
    protected $historyClass = LoanHistory::class;

    protected $fillable = [
        'client_idcard_id',
        'loan_id',
        'last',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }
}
