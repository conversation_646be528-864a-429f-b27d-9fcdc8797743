<?php

namespace Modules\Common\Models;

use Illuminate\Support\Carbon;
use Modules\Common\Enums\SettingsEnum;
use Modules\Common\Exceptions\ProblemException;

/**
 * @property int $approve_decision_id
 * @property string $name
 * @property string $type
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read Administrator|null $processedBy
 * @property-read Administrator|null $updater
 * @mixin IdeHelperApproveDecision
 */
class ApproveDecision extends BaseModel
{
    public const APPROVE_DECISION_ID_CALL_LATER = 1;
    public const APPROVE_DECISION_ID_NO_ANSWER = 2;
    public const APPROVE_DECISION_ID_BUSY = 3;
    public const APPROVE_DECISION_ID_APPROVED = 4;
    public const APPROVE_DECISION_ID_CANCELED = 5;
    public const APPROVE_DECISION_ID_WRONG_PHONE = 6;
    public const APPROVE_DECISION_ID_WAITING_DOCUMENTS = 7;

    public const APPROVE_DECISION_CALL_LATER = 'call_later';
    public const APPROVE_DECISION_NO_ANSWER = 'no_answer';
    public const APPROVE_DECISION_BUSY = 'busy';
    public const APPROVE_DECISION_APPROVED = 'approved';
    public const APPROVE_DECISION_CANCELED = 'canceled';
    public const APPROVE_DECISION_WRONG_PHONE = 'wrong_phone';
    public const APPROVE_DECISION_WAITING_DOCUMENTS = 'waiting_documents';

    public const APPROVE_DECISION_TYPE_WAITING = 'waiting';
    public const APPROVE_DECISION_TYPE_FINAL = 'final';

    public const WAITING_APPROVE_DECISIONS = [
        self::APPROVE_DECISION_ID_NO_ANSWER => self::APPROVE_DECISION_NO_ANSWER,
        self::APPROVE_DECISION_ID_BUSY => self::APPROVE_DECISION_BUSY,
        self::APPROVE_DECISION_ID_CALL_LATER => self::APPROVE_DECISION_CALL_LATER,
        self::APPROVE_DECISION_ID_WAITING_DOCUMENTS => self::APPROVE_DECISION_WAITING_DOCUMENTS,
    ];

    const VALID_DECISION_IDS = [
        self::APPROVE_DECISION_CALL_LATER => self::APPROVE_DECISION_ID_CALL_LATER,
        self::APPROVE_DECISION_NO_ANSWER => self::APPROVE_DECISION_ID_NO_ANSWER,
        self::APPROVE_DECISION_BUSY => self::APPROVE_DECISION_ID_BUSY,
        self::APPROVE_DECISION_APPROVED => self::APPROVE_DECISION_ID_APPROVED,
        self::APPROVE_DECISION_CANCELED => self::APPROVE_DECISION_ID_CANCELED,
        self::APPROVE_DECISION_WRONG_PHONE => self::APPROVE_DECISION_ID_WRONG_PHONE,
        self::APPROVE_DECISION_WAITING_DOCUMENTS => self::APPROVE_DECISION_ID_WAITING_DOCUMENTS,
    ];

    /**
     * @var string
     */
    protected $table = 'approve_decision';

    /**
     * @var string
     */
    protected $primaryKey = 'approve_decision_id';

    /**
     * @var string[]
     */
    protected $fillable = [
        'name',
        'type',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    /**
     * @return string[]
     */
    public static function getAllApproveDecisions(): array
    {
        return [
            self::APPROVE_DECISION_ID_CALL_LATER => self::APPROVE_DECISION_CALL_LATER,
            self::APPROVE_DECISION_ID_NO_ANSWER => self::APPROVE_DECISION_NO_ANSWER,
            self::APPROVE_DECISION_ID_BUSY => self::APPROVE_DECISION_BUSY,
            self::APPROVE_DECISION_ID_APPROVED => self::APPROVE_DECISION_APPROVED,
            self::APPROVE_DECISION_ID_CANCELED => self::APPROVE_DECISION_CANCELED,
            self::APPROVE_DECISION_ID_WRONG_PHONE => self::APPROVE_DECISION_WRONG_PHONE,
            self::APPROVE_DECISION_ID_WAITING_DOCUMENTS => self::APPROVE_DECISION_WAITING_DOCUMENTS,
        ];
    }

    public static function getDecisionById(int $id): ?string
    {
        $ad = ApproveDecision::getAllApproveDecisions();
        return $ad[$id] ?? null;
    }

    /**
     * @param string $name
     *
     * @return int|null
     */
    public static function getIdByName(string $name): ?int
    {
        $dict = array_flip(self::getAllApproveDecisions());
        if (empty($dict[$name])) {
            return null;
        }

        return $dict[$name];
    }

    /**
     * Map call decisions to settings keys
     * @return array|SettingsEnum[]
     */
    public static function getSettingsMapping(): array
    {
        return [
            self::APPROVE_DECISION_ID_NO_ANSWER => SettingsEnum::no_answer_approve,
            self::APPROVE_DECISION_ID_BUSY => SettingsEnum::busy_approve,
            self::APPROVE_DECISION_ID_CALL_LATER => SettingsEnum::call_later_approve,
        ];
    }

    /**
     * @param int $approveDecisionId
     */
    public static function getSetting(int $approveDecisionId): ?SettingsEnum
    {
        $map = self::getSettingsMapping();
        if (empty($map[$approveDecisionId])) {
            return null;
        }

        return $map[$approveDecisionId];
    }

    /**
     * @param int $approveDecisionId
     *
     * @return bool
     * @throws ProblemException
     */
    public static function isWaitingDecision(int $approveDecisionId): bool
    {
        if (!in_array($approveDecisionId, array_keys(self::getAllApproveDecisions()))) {
            throw new ProblemException('Invalid approve decision id!');
        }

        return in_array(
            $approveDecisionId,
            [
                self::APPROVE_DECISION_ID_CALL_LATER,
                self::APPROVE_DECISION_ID_NO_ANSWER,
                self::APPROVE_DECISION_ID_BUSY,
            ]
        );
    }

    public static function isFinalDecision(int $approveDecisionId): bool
    {
        return !self::isWaitingDecision($approveDecisionId);
    }

    public function isFinal(): bool
    {
        return $this->approve_decision_id === self::APPROVE_DECISION_ID_CANCELED
            || $this->approve_decision_id === self::APPROVE_DECISION_ID_APPROVED;
    }

    /**
     * @param string $keysOrValues
     *
     * @return array
     */
    public static function getWaitingApproveDecisions(string $keysOrValues = ''): array
    {
        if ($keysOrValues === 'keys') {
            return array_keys(self::WAITING_APPROVE_DECISIONS);
        } elseif ($keysOrValues === 'values') {
            return array_values(self::WAITING_APPROVE_DECISIONS);
        }

        return self::WAITING_APPROVE_DECISIONS;
    }

    /**
     * @return string[]
     */
    public static function getTypes(): array
    {
        return [
            self::APPROVE_DECISION_TYPE_WAITING,
            self::APPROVE_DECISION_TYPE_FINAL,
        ];
    }
}
