<?php

namespace Modules\Common\Models;

/**
 * @mixin IdeHelperContactType
 */
class ContactType extends BaseModel
{
    protected $table = 'contact_type';

    protected $primaryKey = 'contact_type_id';

    protected $guarded = [
        'contact_type_id',
        'active',
        'deleted',
        'deleted_at',
        'deleted_by',
        'enabled_at',
        'enabled_by',
        'disabled_at',
        'disabled_by',
    ];

    protected $fillable = [
        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];
}
