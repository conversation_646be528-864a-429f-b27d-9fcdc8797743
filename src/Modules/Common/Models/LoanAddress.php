<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperLoanAddress
 */
class LoanAddress extends Model
{
    protected $table = 'loan_address';
    protected $primaryKey = 'id';

    protected $fillable = [
        'client_address_id',
        'loan_id',
        'last',
        'type',
        'created_at',
    ];

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }
}
