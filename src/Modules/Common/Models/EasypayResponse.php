<?php

namespace Modules\Common\Models;
/**
 * @todo will be deleted together with PaymentAutoDistributionService
 * now keep it for example old logic
 * @mixin IdeHelperEasypayResponse
 */
class EasypayResponse extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'easypay_response';

    /**
     * @var string
     */
    protected $primaryKey = 'easypay_response_id';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function loan()
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }

    /**
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function client()
    {
        return $this->belongsTo(
            Client::class,
            'client_id',
            'client_id'
        );
    }
}
