<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Modules\Approve\Events\ApproveAttempt\WaitingDocumentsEvent;
use Modules\Communication\Repositories\CommunicationCommentRepository;

/**
 * @property int $approve_attempt_id
 * @property int $loan_id
 * @property int $administrator_id
 * @property int $office_id
 * @property int $approve_decision_id
 * @property int|null $approve_decision_reason_id
 * @property int|null $skip_time
 * @property string|null $skip_till
 * @property int|null $skip_counter
 * @property string|null $details
 * @property string $start_at
 * @property string $end_at
 * @property int $total_time
 * @property int $last
 * @property bool $active
 * @property bool $deleted
 * @property Carbon $created_at
 * @property int|null $created_by
 * @property Carbon|null $updated_at
 * @property int|null $updated_by
 * @property Carbon|null $deleted_at
 * @property int|null $deleted_by
 * @property Carbon|null $enabled_at
 * @property int|null $enabled_by
 * @property Carbon|null $disabled_at
 * @property int|null $disabled_by
 * @property int|null $waiting_time
 * @property int|null $processing_time
 * @property-read ApproveDecision $approveDecision
 * @property-read ApproveDecisionReason|null $approveDecisionReason
 * @property-read Administrator|null $creator
 * @property-read Administrator|null $deleter
 * @property-read Administrator|null $handler
 * @property-read Loan $loan
 * @property-read Administrator|null $processedBy
 * @property-read Administrator|null $updater
 * @mixin IdeHelperApproveAttempt
 */
class ApproveAttempt extends BaseModel
{
    /**
     * @var string
     */
    protected $table = 'approve_attempt';

    /**
     * @var string[]
     */
    protected $fillable = [
        'loan_id',
        'administrator_id',
        'office_id',
        'approve_decision_id',
        'approve_decision_reason_id',
        'skip_time',
        'skip_till',
        'skip_counter',
        'details',
        'start_at',
        'end_at',
        'waiting_time',
        'processing_time',
        'total_time',
        'last',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    /**
     * @var string
     */
    protected $primaryKey = 'approve_attempt_id';

    protected static function boot(): void
    {
        parent::boot();

        static::created(function (ApproveAttempt $model) {
            if ($model->approve_decision_id === ApproveDecision::APPROVE_DECISION_ID_WAITING_DOCUMENTS) {
                WaitingDocumentsEvent::dispatch($model);
            }
        });

        static::saved(function (ApproveAttempt $model) {
            if ($model->details != '' && $model->wasRecentlyCreated) {
                $loan = $model->loan;
                $prefix = $loan->isApproved() ? 'Плащане: ' : 'Одобрение: ';

                app(CommunicationCommentRepository::class)->create([
                    'client_id' => $loan->client_id,
                    'loan_id' => $loan->loan_id,
                    'administrator_id' => getAdminId(),
                    'text' => ($prefix . $model->details),
                ]);
            }
        });
    }

    public function approveDecision(): BelongsTo
    {
        return $this->belongsTo(
            ApproveDecision::class,
            'approve_decision_id',
            'approve_decision_id'
        );
    }

    public function approveDecisionReason(): BelongsTo
    {
        return $this->belongsTo(
            ApproveDecisionReason::class,
            'approve_decision_reason_id',
            'approve_decision_reason_id'
        );
    }

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }

    public function getDecisionName(): string
    {
        return ApproveDecision::getDecisionById($this->approve_decision_id);
    }

    public function getDecisionReasonName(): string
    {
        return ApproveDecisionReason::getReasonById($this->approve_decision_reason_id);
    }

    public function isPaymentManuallyCancelled(): bool
    {
        return $this->approve_decision_reason_id
            === ApproveDecisionReason::APPROVE_DECISION_REASON_ID_PAYMENT_CANCELED;
    }

    public function isEasyPayRefund(): bool
    {
        return $this->approve_decision_reason_id
            === ApproveDecisionReason::APPROVE_DECISION_REASON_ID_EASYPAY_REFUND;
    }
}
