<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Interfaces\HistoryInterface;

/**
 * @property int client_bank_account_id
 * @property int loan_id
 * @mixin IdeHelperLoanBankAccount
 */
class LoanBankAccount extends BaseModel implements HistoryInterface
{
    protected $table = 'loan_bank_account';
    protected $primaryKey = 'id';
    protected $historyClass = LoanHistory::class;

    protected $fillable = [
        'client_bank_account_id',
        'loan_id',
        'last',
        'created_at',
    ];

    public function loan(): BelongsTo
    {
        return $this->belongsTo(
            Loan::class,
            'loan_id',
            'loan_id'
        );
    }

    public function clientBankAccount(): BelongsTo
    {
        return $this->belongsTo(
            ClientBankAccount::class,
            'client_bank_account_id',
            'client_bank_account_id'
        );
    }
}
