<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperApproveDecisionReason
 */
final class ApproveDecisionReason extends BaseModel
{
    public const APPROVE_DECISION_REASON_ID_DISAPPROVE_AMOUNT = 1;
    public const APPROVE_DECISION_REASON_ID_DISAPPROVE_PERIOD = 2;
    public const APPROVE_DECISION_REASON_ID_WRONG_IDENTITY = 3;
    public const APPROVE_DECISION_REASON_ID_FAKE_REQUEST = 4;
    public const APPROVE_DECISION_REASON_ID_OTHER = 5;
    public const APPROVE_DECISION_REASON_ID_NO_INTEREST = 6;
    public const APPROVE_DECISION_REASON_ID_WRONG_PHONE = 7;
    public const APPROVE_DECISION_REASON_ID_EASYPAY_REFUND = 8;
    public const APPROVE_DECISION_REASON_ID_PAYMENT_CANCELED = 9;
    public const APPROVE_DECISION_REASON_ID_NO_INCOME = 12;
    public const APPROVE_DECISION_REASON_ID_FAMILY_RELATION = 13;
    public const APPROVE_DECISION_REASON_ID_REF_LOW_LIMIT = 14;
    public const APPROVE_DECISION_REASON_ID_AUTO_PROCESS = 15;
    public const APPROVE_DECISION_REASON_ID_AUTO_PROCESS_OVERDUE_REFINANCE = 16;
    public const APPROVE_DECISION_REASON_ID_NOT_SIGNED_LAST_48H = 17;
    public const APPROVE_DECISION_REASON_ID_AUTO_PROCESS_PRE_APPROVED = 18;
    public const APPROVE_DECISION_REASON_ID_DISAPPROVE_COST_INCREASE = 19;
    public const APPROVE_DECISION_REASON_ID_CLIENT_IS_BLOCKED = 20;
    public const APPROVE_DECISION_REASON_ID_CLIENT_HAS_UNRECEIVED_MONEY = 21;
    public const APPROVE_DECISION_REASON_ID_USE_IBAN_OF_OTHER = 22;
    public const APPROVE_DECISION_REASON_ID_NO_ACTION_LAST_48H = 23;

    public const APPROVE_DECISION_REASON_DISAPPROVE_AMOUNT = 'disapprove_amount';
    public const APPROVE_DECISION_REASON_DISAPPROVE_PERIOD = 'disapprove_period';
    public const APPROVE_DECISION_REASON_DISAPPROVE_COST_INCREASE = 'disapprove_cost_increase';
    public const APPROVE_DECISION_REASON_WRONG_IDENTITY = 'wrong_identity';
    public const APPROVE_DECISION_REASON_FAKE_REQUEST = 'fake_request';
    public const APPROVE_DECISION_REASON_OTHER = 'other';
    public const APPROVE_DECISION_REASON_NO_INTEREST = 'no_interest';
    public const APPROVE_DECISION_REASON_WRONG_PHONE = 'wrong_phone';
    public const APPROVE_DECISION_REASON_EASYPAY_REFUND = 'easypay_refund';
    public const APPROVE_DECISION_REASON_PAYMENT_CANCELED = 'payment_canceled';
    public const APPROVE_DECISION_REASON_NO_INCOME = 'no_income';
    public const APPROVE_DECISION_REASON_FAMILY_RELATION = 'family_relation';
    public const APPROVE_DECISION_REASON_REF_LOW_LIMIT = 'refinance_low_limit';
    public const APPROVE_DECISION_REASON_AUTO_PROCESS = 'auto_process';
    public const APPROVE_DECISION_REASON_AUTO_PROCESS_OVERDUE_REFINANCE = 'auto_process_overdue_refinance';
    public const APPROVE_DECISION_REASON_AUTO_PROCESS_PRE_APPROVED = 'auto_process_pre_approved';
    public const APPROVE_DECISION_REASON_NOT_SIGNED_LAST_48H = 'not_signed_last_48h';
    public const APPROVE_DECISION_REASON_CLIENT_IS_BLOCKED = 'client_is_blocked';
    public const APPROVE_DECISION_REASON_CLIENT_HAS_UNRECEIVED_MONEY = 'client_has_unreceived_money';
    public const APPROVE_DECISION_REASON_USE_IBAN_OF_OTHER = 'use_iban_of_other';
    public const APPROVE_DECISION_REASON_NO_ACTION_LAST_48H = 'no_action_last_48h';

    public const VALID_REASON_IDS = [
        self::APPROVE_DECISION_REASON_DISAPPROVE_AMOUNT => self::APPROVE_DECISION_REASON_ID_DISAPPROVE_AMOUNT,
        self::APPROVE_DECISION_REASON_DISAPPROVE_PERIOD => self::APPROVE_DECISION_REASON_ID_DISAPPROVE_PERIOD,
        self::APPROVE_DECISION_REASON_DISAPPROVE_COST_INCREASE => self::APPROVE_DECISION_REASON_ID_DISAPPROVE_COST_INCREASE,
        self::APPROVE_DECISION_REASON_WRONG_IDENTITY => self::APPROVE_DECISION_REASON_ID_WRONG_IDENTITY,
        self::APPROVE_DECISION_REASON_FAKE_REQUEST => self::APPROVE_DECISION_REASON_ID_FAKE_REQUEST,
        self::APPROVE_DECISION_REASON_OTHER => self::APPROVE_DECISION_REASON_ID_OTHER,
        self::APPROVE_DECISION_REASON_NO_INTEREST => self::APPROVE_DECISION_REASON_ID_NO_INTEREST,
        self::APPROVE_DECISION_REASON_WRONG_PHONE => self::APPROVE_DECISION_REASON_ID_WRONG_PHONE,
        self::APPROVE_DECISION_REASON_EASYPAY_REFUND => self::APPROVE_DECISION_REASON_ID_EASYPAY_REFUND,
        self::APPROVE_DECISION_REASON_PAYMENT_CANCELED => self::APPROVE_DECISION_REASON_ID_PAYMENT_CANCELED,
        self::APPROVE_DECISION_REASON_NO_INCOME => self::APPROVE_DECISION_REASON_ID_NO_INCOME,
        self::APPROVE_DECISION_REASON_FAMILY_RELATION => self::APPROVE_DECISION_REASON_ID_FAMILY_RELATION,
        self::APPROVE_DECISION_REASON_REF_LOW_LIMIT => self::APPROVE_DECISION_REASON_ID_REF_LOW_LIMIT,
        self::APPROVE_DECISION_REASON_AUTO_PROCESS => self::APPROVE_DECISION_REASON_ID_AUTO_PROCESS,
        self::APPROVE_DECISION_REASON_AUTO_PROCESS_OVERDUE_REFINANCE => self::APPROVE_DECISION_REASON_ID_AUTO_PROCESS_OVERDUE_REFINANCE,
        self::APPROVE_DECISION_REASON_NOT_SIGNED_LAST_48H => self::APPROVE_DECISION_REASON_ID_NOT_SIGNED_LAST_48H,
        self::APPROVE_DECISION_REASON_AUTO_PROCESS_PRE_APPROVED => self::APPROVE_DECISION_REASON_ID_AUTO_PROCESS_PRE_APPROVED,
        self::APPROVE_DECISION_REASON_CLIENT_IS_BLOCKED => self::APPROVE_DECISION_REASON_ID_CLIENT_IS_BLOCKED,
        self::APPROVE_DECISION_REASON_CLIENT_HAS_UNRECEIVED_MONEY => self::APPROVE_DECISION_REASON_ID_CLIENT_HAS_UNRECEIVED_MONEY,
        self::APPROVE_DECISION_REASON_USE_IBAN_OF_OTHER => self::APPROVE_DECISION_REASON_ID_USE_IBAN_OF_OTHER,
        self::APPROVE_DECISION_REASON_NO_ACTION_LAST_48H => self::APPROVE_DECISION_REASON_ID_NO_ACTION_LAST_48H,
    ];

    public static function getReasonById(?int $id): ?string
    {
        if (!$id) {
            return null;
        }

        $arr = array_flip(self::VALID_REASON_IDS);
        return $arr[$id] ?? null;
    }

    protected $table = 'approve_decision_reason';

    protected $primaryKey = 'approve_decision_reason_id';

    protected $fillable = [
        'approve_decision_id',
        'name',
        'description',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];

    public function approveDecision(): BelongsTo
    {
        return $this->belongsTo(
            ApproveDecision::class,
            'approve_decision_id',
            'approve_decision_id'
        );
    }
}
