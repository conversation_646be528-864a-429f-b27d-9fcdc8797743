<?php

namespace Modules\Common\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * @mixin IdeHelperLoanOverdueStats
 */
class LoanOverdueStats extends BaseModel
{
    const KEY_OVERDUE_90D_DATE = 'overdue_date_90d';

    protected $table = 'loan_overdue_stats';
    protected $primaryKey = 'id';
    protected $fillable = [
        'client_id',
        'loan_id',
        'key',
        'val',

        'created_at',
        'created_by',
        'updated_at',
        'updated_by',
    ];
}
