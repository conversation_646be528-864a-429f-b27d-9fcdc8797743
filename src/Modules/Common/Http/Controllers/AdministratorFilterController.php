<?php

namespace Modules\Common\Http\Controllers;

use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Common\Http\Requests\AdministratorFilterRequest;
use Modules\Common\Models\AdministratorFilter;

class AdministratorFilterController extends Controller
{
    public function loadFilters(AdministratorFilter $administratorFilter): JsonResponse
    {
        if (!$administratorFilter->exists) {
            return response()->json([
                'success' => false,
                'message' => __('messages.AdministratorFilters.successSaveFilter')
            ]);
        }

        $params = $administratorFilter->params;
        $params['filter_id'] = $administratorFilter->getKey();

        return response()->json([
            'success' => true,
            'redirectTo' => route($administratorFilter->page_route, $params)
        ]);
    }

    public function storeAdminFilter(AdministratorFilterRequest $request)
    {
        $administratorFilter = AdministratorFilter::create($request->validated());

        if ($administratorFilter) {
            // Get the previous URL
            $previousUrl = url()->previous();
            $newUrl = $previousUrl . (parse_url(
                    $previousUrl,
                    PHP_URL_QUERY
                ) ? '&' : '?') . 'filter_id=' . $administratorFilter->getKey();

            return redirect($newUrl)->with('success', __('messages.AdministratorFilters.successSaveFilter'));
        }

        return back()->with('success', __('messages.generalErrorSomethingWrong'));
    }

    public function destroy(AdministratorFilter $administratorFilter): JsonResponse
    {

        $pageRoute = $administratorFilter->page_route;
        if ($administratorFilter->exists && $administratorFilter->delete()) {
            return response()->json([
                'success' => true,
                'redirectTo' => route($pageRoute)
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => __('messages.generalErrorSomethingWrong')
        ]);
    }
}
