<?php

namespace Modules\Common\Http\Dto;

use Spatie\LaravelData\Data;

abstract class SpatieDto extends Data implements Dto
{
    //for the future, we have decided to keep properties public for now
    public function __get($name)
    {
        return $this->$name;
    }

    public function cloneAndSetProperty($name, $value): self
    {
        $array = $this->toArray();
        $array[$name] = $value;
        return self::from($array);
    }
}