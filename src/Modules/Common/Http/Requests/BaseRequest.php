<?php

namespace Modules\Common\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Modules\Common\Exceptions\JsonException;
use Modules\Common\Http\Requests\Traits\RulesTrait;
use Modules\Common\Traits\DateBuilderTrait;
use Modules\Common\Traits\ValidationTrait;
use Spatie\LaravelData\Data;

class BaseRequest extends FormRequest
{
    use DateBuilderTrait, ValidationTrait, RulesTrait;

    public function authorize(): bool
    {
        return true;
    }

    public function asDtoObject(string $dtoClassName): Data
    {
        /// check if Dto class exists
        if (!class_exists($dtoClassName)) {
            throw new \Exception("Dto: {$dtoClassName} not found.");
        }

        //// get validated data form request
        $data = $this->validated();

        /// check if we have the build(); method in the Dto object
        /// first step create Dto with the request data $dtoClassName::from($data)
        /// second step call build($data); method with request data
        if (method_exists($dtoClassName, 'build')) {
            $dtoObject = $dtoClassName::from($data);
            $dtoObject->build($data);

            return $dtoObject;
        }

        /// return Dto object with parsed request $data
        return $dtoClassName::from($data);
    }

    protected function failedValidation(Validator $validator)
    {
        if ($this->wantsJson()) {
            throw new JsonException($validator->getMessageBag(), 400);
        }

        parent::failedValidation($validator);
    }

    public function asLoggableJson(): string
    {
        return response()->json($this->all());
    }
}
