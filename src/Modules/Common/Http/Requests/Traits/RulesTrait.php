<?php

namespace Modules\Common\Http\Requests\Traits;

trait RulesTrait
{
    protected function fillRuleLimitRule(array &$array)
    {
        $array['limit'] = 'nullable|numeric|in:10,25,50,100,0';
    }

    protected function fillRuleOrder(array &$array)
    {
        $array['order.*'] = 'nullable';
    }

    protected function fillRuleOffice(array &$array)
    {
        $array['office_id'] = 'numeric';
    }

    protected function fillRuleCreatedAt(array &$array)
    {
        $array['created_at'] = [
            'nullable',
            'regex:/^([0-9]{2}\-[0-9]{2}\-[0-9]{4} - [0-9]{2}\-[0-9]{2}\-[0-9]{4})|([0-9]{2}\-[0-9]{2}\-[0-9]{4})$/i',
        ];
    }
}