<?php

namespace Modules\Common\Statistics\Realtime;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\SaleAttempt;
use Modules\Common\Statistics\Realtime\DTO\DailyProcessingSaleAttemptTimeDTO;
use Spatie\LaravelData\DataCollection;

class DailyProcessingSaleAttemptTime
{

    /**
     * @param Carbon $date
     * @return DailyProcessingSaleAttemptTimeDTO[]|DataCollection
     */
    public function getByOfficeAndAdmin(Carbon $date): DataCollection
    {
        $processedTasks =
            $this->baseQuery($date)
                ->select([
                    'office_id',
                    'administrator_id',
                    DB::raw('sum(total_time) as total'),
                    DB::raw('avg(total_time) as ex_avg'),
                    DB::raw('max(total_time) as ex_max'),
                    DB::raw('min(total_time) as ex_min'),
                ])->groupBy(['office_id', 'administrator_id'])
                ->get();

        return DailyProcessingSaleAttemptTimeDTO::collection($processedTasks);
    }

    private function baseQuery(Carbon $date): Builder
    {
        return SaleAttempt::query()
            ->whereBetween('end_at', [(clone $date)->startOfDay(), (clone $date)->endOfDay()]);
    }
}
