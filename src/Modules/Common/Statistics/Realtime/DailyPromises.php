<?php

namespace Modules\Common\Statistics\Realtime;

use App\Statistics\BaseStatistic;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\BucketTask;
use Modules\Common\Models\BucketTaskHistory;
use Modules\Common\Models\CollectorDecision;
use Modules\Common\Models\Loan;
use Modules\Common\Statistics\Realtime\DTO\DailyPromisesDTO;
use Spatie\LaravelData\DataCollection;

class DailyPromises extends BaseStatistic
{

    public function getByOffice(Carbon $date): DataCollection
    {
        $tableName = (new BucketTaskHistory)->getTable();
        $childTable = Loan::getTableName();

        $processedTasks = $this->baseQuery($date)
            ->select([
                $childTable . '.office_id',
                DB::raw('count(' . $tableName . '.bucket_task_id) as total'),
            ])->groupBy([
                $childTable . '.office_id',
            ])->get();

        return DailyPromisesDTO::collection($processedTasks);
    }

    private function baseQuery(Carbon $date): Builder
    {
        $tableName = (new BucketTaskHistory)->getTable();
        $childTable = Loan::getTableName();

        return BucketTaskHistory::query()
            ->join($childTable, $childTable . '.loan_id', '=', $tableName . '.loan_id')
            ->whereBetween($tableName . '.show_after', [(clone $date)->startOfDay(), (clone $date)->endOfDay()])
            ->where($tableName . '.status', BucketTask::STATUS_DONE)
            ->where($tableName . '.collector_decision_id', CollectorDecision::PROMISE)
            ->whereDate($tableName . '.finished_at', '=', $date);
    }
}