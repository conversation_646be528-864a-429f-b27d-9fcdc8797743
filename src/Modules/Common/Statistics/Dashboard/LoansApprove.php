<?php

namespace Modules\Common\Statistics\Dashboard;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\StatsDaily;
use Modules\Common\Statistics\Dashboard\DTO\LoanApproveDTO;

class LoansApprove
{
    const TTL_CACHE = 5 * 60;

    public function getStatsByUser(Administrator $administrator, Carbon $date): LoanApproveDTO
    {
        // return Cache::remember(
        //     'stats_dashboard_loan_approve_' . $date->format('Y-m-d') . '_' . $administrator,
        //     self::TTL_CACHE,
        //     function () use ($administrator, $date) {
                $offices = $administrator->offices->pluck('office_id')->toArray();

                $day = $this->getByOfficeQuery($offices, [$date, $date])->first();
                $week = $this->getByOfficeQuery($offices, [(clone $date)->startOfWeek(), $date])->first();
                $month = $this->getByOfficeQuery($offices, [(clone $date)->startOfMonth(), $date])->first();

                return new LoanApproveDTO(
                    $day->total ?: 0,
                    $week->total ?: 0,
                    $month->total ?: 0,

                    $day->avg_time ?: 0,
                    $week->avg_time ?: 0,
                    $month->avg_time ?: 0,

                    $day->min_time ?: 0,
                    $week->min_time ?: 0,
                    $month->min_time ?: 0,

                    $day->max_time ?: 0,
                    $week->max_time ?: 0,
                    $month->max_time ?: 0,

                    now(),
                );
        //     }
        // );
    }


    private function getByOfficeQuery(array $offices, array $between): Builder
    {
        return $this->baseQuery($between)->whereIn('office_id', $offices);
    }

    private function baseQuery(array $between): Builder
    {
        return StatsDaily::query()->select([
            DB::raw('sum(processed_approve_attempt_count) as total'),
            DB::raw('sum(processed_approve_attempt_time_total)/sum(processed_approve_attempt_count) as avg_time_old'),
            DB::raw('avg(processed_approve_attempt_time_avg) as avg_time'), // since it could be for several offices, we take avg from averages
            DB::raw('min(processed_approve_attempt_time_min) as min_time'),
            DB::raw('max(processed_approve_attempt_time_max) as max_time'),
        ])->whereBetween('state_date', $between);
    }
}
