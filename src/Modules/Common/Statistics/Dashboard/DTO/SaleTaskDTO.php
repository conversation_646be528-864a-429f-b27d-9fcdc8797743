<?php

namespace Modules\Common\Statistics\Dashboard\DTO;

use Carbon\Carbon;
use Spatie\LaravelData\Data;

class SaleTaskDTO extends Data
{
    public function __construct(
        public int $countProcessedTasksToday,
        public int $countProcessedTasksWeek,
        public int $countProcessedTasksMonth,

        public int $avgProcessedTimeToday,
        public int $avgProcessedTimeWeek,
        public int $avgProcessedTimeMonth,

        public int $maxProcessedTimeToday,
        public int $maxProcessedTimeWeek,
        public int $maxProcessedTimeMonth,

        public int $minProcessedTimeToday,
        public int $minProcessedTimeWeek,
        public int $minProcessedTimeMonth,

        public Carbon $generatedAt,
    ) {
    }
}
