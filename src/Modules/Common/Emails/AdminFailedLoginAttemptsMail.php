<?php

namespace Modules\Common\Emails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class AdminFailedLoginAttemptsMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public Collection $adminFailedLoginAttempts
    ) {
    }

    public function envelope(): Envelope
    {
        $env = strtoupper(env('APP_ENV'));
        $prj = strtoupper(env('PROJECT'));

        return new Envelope(
            subject: $prj . ' -  Admin Failed Login Attempts  (' . $env . ')',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'communication::mail.failed-login-attempts.admin-failed',
        );
    }
}
