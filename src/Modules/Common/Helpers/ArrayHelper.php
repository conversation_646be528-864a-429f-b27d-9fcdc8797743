<?php

namespace Modules\Common\Helpers;

class ArrayHelper
{
    public static function twoArraysHaveSameStructure(array $a, array $b): bool
    {
        if(array_keys($a) !== array_keys($b)){
            return false;
        }
        foreach ($a as $i=>$val){
            if(is_array($val) !== is_array($b[$i])){
                return false;
            }
            if(is_array($val) && ! self::twoArraysHaveSameStructure($val, $b[$i]))
            {
                return false;
            }
        }
        return true;
    }
}