<?php

namespace Modules\Common\Helpers\TestHelpers;

use Faker\Factory as Faker;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Modules\Api\Http\Dto\SignContractDto;
use Modules\Approve\Application\Action\ApproveLoanAction;
use Modules\Approve\Application\Action\ProcessLoanAction;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\CashDesk\Application\Actions\GiveOutCashAction;
use Modules\CashDesk\Enums\CashOperationalTransactionDirectionEnum;
use Modules\CashDesk\Enums\CashOperationalTransactionTypeEnum;
use Modules\CashDesk\Models\CashOperationalTransaction;
use Modules\Common\Enums\Payment\PaymentMethodEnum;
use Modules\Common\Enums\PaymentDeliveryEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientAddress;
use Modules\Common\Models\ClientEmployer;
use Modules\Common\Models\ClientIdCard;
use Modules\Common\Models\Contact;
use Modules\Common\Models\Guarant;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Common\Models\Payment;
use Modules\Common\Models\PaymentMethod;
use Modules\Payments\Application\Actions\ManualPaymentSaveAction;
use Modules\Payments\Application\Actions\Task\ConfirmEasyPaySendingAction;
use Modules\Payments\Application\Actions\Task\ConfirmTransferToBankAction;
use Modules\Sales\Application\Actions\ExistingClientCreateLoanAction;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\Sales\Application\Actions\SignLoanAction;
use Modules\Sales\Http\Dto\LoanDto;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;

class LoanSteps
{
    public static function createNewClientAndBankLoan(int $amount): Loan
    {
        $faker = Faker::create();

        //// create loan data
        $idCardData = ClientIdCard::factory(1)->make();

        $clientSeqData = new Sequence(
            ['phone' => [$faker->numerify('0999######'), $faker->numerify('0999######')]]
        );
        $clientData = Client::factory(1)->make($clientSeqData);
        $clientEmployerData = ClientEmployer::factory(1)->make();
        $clientAddressData = ClientAddress::factory(1)->make();
        $clientGuarantData = Guarant::factory(1)->make();
        $clientContactData = Contact::factory(2)->make();

        $data = [
            "loan" => [
                "source_id" => 2,//old
                "channel_id" => 3,//old
                "administrator_id" => Administrator::DEFAULT_ADMINISTRATOR_ID,
                "product_id" => 1,
                "loan_sum" => $amount,
                "loan_period" => 30,
                "iban" => "**********************",
                "discount_percent" => 0,
                "office_id" => Office::OFFICE_ID_WEB,
                "payment_method" => PaymentMethod::PAYMENT_METHOD_BANK,
                "client_id" => null,
                "comment" => "kpomentariy test",
                "browser"=>"test_browser",
                "ip"=>"************"
            ],
            'client' => $clientData->first()->toArray(),
            'client_idcard' => $idCardData->first()->toArray(),
            'client_employer' => $clientEmployerData->first()->toArray(),
            'client_address' => $clientAddressData->first()->toArray(),
            'guarant' => $clientGuarantData->toArray(),
            'contact' => $clientContactData->toArray(),
            "clientMetaAction" => "identification_mvr_valid_date",
            "attempt" => [
                "start_at" => "2022-09-29 12:31:15"
            ],
        ];

        $dto = (new DtoSerializerNewClientLoan())->createClientDto($data);
        $loan = app(NewAppAction::class)->execute($dto)->dbLoan()->fresh();
        //Artisan::call('script:loan-stats-location-update', ['loanId'=>$loan->getKey()]);
        return $loan;
    }

    public static function signLoan(Loan $loan): Loan
    {
        $dto = new SignContractDto(
            $loan->getKey(),
            '**************',//'************',//'************',
            'sign_test_browser',
            $loan->client_id
        );
        $loan = app(SignLoanAction::class)->execute($loan, $dto)->dbModel();
        //Artisan::call('script:loan-stats-location-update', ['loanId'=>$loan->getKey()]);
        return $loan->fresh();
    }

    public static function approveLoan(Loan $loan): Loan
    {
        app(ProcessLoanAction::class)->execute($loan);
        return app(ApproveLoanAction::class)->execute(new DecisionDto(
            $loan->getKey(),
            1,
            ApproveDecision::APPROVE_DECISION_APPROVED,
            ApproveDecisionReason::APPROVE_DECISION_REASON_OTHER,
            '',
            null
        ))->fresh();
    }

    public static function activateBankLoan(Loan $loan): Loan
    {
        /** @var Payment $payment */
        $payment = Payment::where('loan_id', $loan->getKey())->first();
        app(ConfirmTransferToBankAction::class)->execute($payment);
        return $loan->fresh();
    }

    public static function activateEasyPayLoan(Loan $loan): Loan
    {
        /** @var Payment $payment */
        $payment = Payment::where('loan_id', $loan->getKey())->first();
        app(ConfirmEasyPaySendingAction::class)->execute($payment);
        return $loan->fresh();
    }

    public static function repayLoan(Loan $loan, int $amount): Loan
    {
        $data = [
            'loans'=>[$loan->getKey()],
            'loanPaymentAmount'=>[$loan->getKey()=>$amount],
            'loanAction'=>[$loan->getKey()=>PaymentDeliveryEnum::DELIVERY_LOAN_PAYMENT->value],
            'payment_method_id'=>PaymentMethod::PAYMENT_METHOD_BANK,
            'payment_amount'=>$amount,
            'office_id'=>Office::OFFICE_ID_WEB,
            'description'=>'test',
            'document_number'=>'test123',
            'payment_task_id'=>null,
            'payment_task_decision_id'=>null,
            'admin_id'=>Administrator::DEFAULT_ADMINISTRATOR_ID
        ];
        app(ManualPaymentSaveAction::class)->execute($data);
        return $loan->fresh();
    }

    public static function createCashLoan(Client $client, int $amount): Loan
    {
        $dto = new LoanDto(
            null,
            Office::OFFICE_ID_NOVI_PAZAR_1,
            Administrator::DEFAULT_ADMINISTRATOR_ID,
            PaymentMethodEnum::CASH->id(),
            3,
            $amount,
            30,
            0,
            [],
            null,
            null,
            '************',
            'create_test_browser',
        );
        $action = app(ExistingClientCreateLoanAction::class);
        $action->execute($client, $dto);
        return $action->getDbLoan()->fresh();
    }

    public static function activateCashLoan(Loan $loan): Loan
    {
        $cash = new CashOperationalTransaction();
        $cash->office_id = 2;
        $cash->transaction_type = CashOperationalTransactionTypeEnum::INITIAL_BALANCE;
        $cash->direction = CashOperationalTransactionDirectionEnum::IN;
        $cash->amount = $loan->amount_approved;
        $cash->amount_signed = $loan->amount_approved;
        $cash->created_at = now();
        $cash->save();

        /** @var Payment $payment */
        $payment = Payment::where('loan_id', $loan->getKey())->first();
        app(GiveOutCashAction::class)->execute($payment);
        $loan->refresh();
        return $loan;
    }

    public static function createEasyPayRefinancingLoan(Loan $loan, int $amount): Loan
    {
        $dto = new LoanDto(
            null,
            Office::OFFICE_ID_WEB,
            Administrator::DEFAULT_ADMINISTRATOR_ID,
            PaymentMethodEnum::EASY_PAY->id(),
            1,
            $amount,
            30,
            0,
            [$loan->getKey()],
            null,
            null,
            '************',
            'create_test_browser',
        );
        $action = app(ExistingClientCreateLoanAction::class);
        $action->execute($loan->client, $dto);
        return $action->getDbLoan()->fresh();
    }

    public static function postponeLoan(Loan $loan, int $amount): Loan
    {
        $data = [
            'loans'=>[$loan->getKey()],
            'loanPaymentAmount'=>[$loan->getKey()=>$amount],
            'loanAction'=>[$loan->getKey()=>PaymentDeliveryEnum::DELIVERY_LOAN_EXTENSION->value],
            'payment_method_id'=>PaymentMethod::PAYMENT_METHOD_CASH,
            'payment_amount'=>$amount,
            'office_id'=>Office::OFFICE_ID_NOVI_PAZAR_1,
            'description'=>'test',
            'document_number'=>'test123',
            'payment_task_id'=>null,
            'payment_task_decision_id'=>null,
            'admin_id'=>2
        ];
        app(ManualPaymentSaveAction::class)->execute($data);
        return $loan->fresh();

    }
}
