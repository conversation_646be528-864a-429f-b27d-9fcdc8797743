<?php

namespace Modules\Common\Helpers;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Modules\Common\Models\File;

class OtherHelper
{
    static public function clearDiffsChanges(array $from, array $to): Collection
    {
        $keys = collect(array_keys($from));
        $keys->push(...array_keys($to));

        return $keys->mapWithKeys(function ($key) use ($from, $to) {
            $from = $from[$key] ?? null;
            $to = $to[$key] ?? null;

            $value = null;
            if ($from !== $to) {
                $value = compact('from', 'to', 'key');
            }

            return [$key => $value];
        })->filter();
    }


    static function getVariablesFromText(string $content): array
    {
        $matches = [];
        preg_match_all('!\{[ ]*?([\w.]+?)[ ]*?\}!', $content, $matches);
        $matches = $matches[1];

        return array_values(array_unique($matches));
    }

    static function getFilePath(File $file): string
    {
        return Storage::path($file->file_path . $file->file_name);
    }
}
