<?php

namespace Modules\Common\Database\Seeders;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Seeder;
use Faker\Factory as Faker;
use Modules\Common\Models\Installment;

class InstallmentSeeder extends Seeder
{
    public function run()
    {
        $faker = Faker::create();

        foreach (range(1, 50) as $index) {
            DB::table('installment')->insert(
                [
                    'client_id' => $faker->numberBetween(1, 10),
                    'loan_id' => $faker->numberBetween(1, 10),
                    'seq_num' => $index,
                    'due_date' => $faker->dateTimeInInterval(
                        $startDate = '-5 days',
                        $interval = '+ 5 days',
                        $timezone = null
                    ),
                    'total_amount' => $faker->numberBetween(200, 500),
                    'status' => Arr::random(Installment::getInstallmentStatuses()),
                    'paid' => 1,
                    'overdue_days' => $faker->numberBetween(5, 10),
                    'overdue_amount' => $faker->numberBetween(50, 100),
                    'max_overdue_days' => $faker->numberBetween(5, 10),
                    'max_overdue_amount' => $faker->numberBetween(20, 50),
                    'paid_at' => $faker->dateTimeInInterval(
                        $startDate = '-5 days',
                        $interval = '+ 5 days',
                        $timezone = null
                    ),
                    'created_at' => now(),
                    'created_by' => 1,
                ]
            );
        }
    }
}
