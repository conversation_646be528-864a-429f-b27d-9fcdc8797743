<?php

namespace Modules\Common\Database\Seeders\Payments;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Api\Application\Actions\RegisterPaymentFromEasyPayAction;
use Modules\Api\Http\Dto\RegisterPaymentFromEasyPayDto;
use Modules\Common\Enums\LoanSourceEnum;
use Modules\Common\Enums\ProductTypeEnum;
use Modules\Common\Models\Channel;
use Modules\Common\Models\Client as DbClient;
use Modules\Common\Models\Currency;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\LoanType;
use Modules\Common\Models\Office;
use Modules\Common\Models\PaymentMethod;
use Modules\Head\Repositories\ClientRepository;

class UndistributedPaymentsSeeder extends Seeder
{
    public function run()
    {
        $action = app(RegisterPaymentFromEasyPayAction::class);
        $array = [
            'pin' => '1234556',
            'amount' => '10000',
            'date' => now()->format('Y-m-d'),
            'type' => 'BILLING',
            'transaction_id' => '9510114427003',
        ];
        $noClientPaymentDto = RegisterPaymentFromEasyPayDto::from($array);
        $action->execute($noClientPaymentDto);

        DB::table('client')->insert([
            'pin' => 9111000000,
            'idcard_number' => "9104188750",
            'first_name' => "Калоян",
            'middle_name' => "Noloan",
            'last_name' => "Илиев",
            'phone' => "0896667788",
            'legal_status' => DbClient::LS_INDV,
            'citizenship_type' => DbClient::CT_LOCAL,
            'email'=>'<EMAIL>'
        ]);

        $array = [
            'pin' => 9111000000,
            'amount' => '20000',
            'date' => now()->format('Y-m-d'),
            'type' => 'BILLING',
            'transaction_id' => '9510114427004',
        ];
        $noLoanPaymentDto = RegisterPaymentFromEasyPayDto::from($array);
        $action->execute($noLoanPaymentDto);

        //TWO LOANS CLIENT
        DB::table('client')->insert([
            'pin' => **********,
            'idcard_number' => "9104188750",
            'first_name' => "Калоян",
            'middle_name' => "Twoloan",
            'last_name' => "Илиев",
            'phone' => "0896667788",
            'legal_status' => DbClient::LS_INDV,
            'citizenship_type' => DbClient::CT_LOCAL,
            'email'=>'<EMAIL>'
        ]);
        $client = app(ClientRepository::class)->getByPin(**********);
        $loanArr = [
            'client_id' => $client->getKey(),
            'product_id' => 1,
            'product_type_id' => ProductTypeEnum::INSTALLMENT->id(),
            'loan_type_id' => LoanType::LOAN_TYPE_ID_NORMAL,
            'interest_percent'=>1000,
            'penalty_percent'=>1000,
            'installment_modifier'=>'+7 days',
            'discount_percent' => 10,
            'amount_requested' => 1000,
            'amount_approved' => 1000,
            'installments_requested' => 2,
            'installments_approved' => 2,
            'currency_id' => Currency::BGN_CURRENCY_ID,
            'period_requested' => 30,
            'period_approved' => 30,
            'period_grace' => null,
            'loan_status_id' => LoanStatus::ACTIVE_STATUS_ID,
            'client_discount_actual_id'=>null,
            'last_status_update_administrator_id' => 1,
            'administrator_id' => 1,
            'last_status_update_date' => '2022-10-10 11:11:11',
            'payment_method_id' => PaymentMethod::PAYMENT_METHOD_BANK,
            'source' => LoanSourceEnum::CRM,
            'channel_id' => Channel::PHONE_ID,
            'office_id' => Office::OFFICE_ID_WEB,
            'active'=>1,
            'amount_rest'=>1000,
            'comment' => 'active loan',
            'hash'=>md5(time() + rand(0, 9999)),
            'created_at'=>'2022-10-10 11:11:11',
        ];
        DB::table('loan')->insert([
            $loanArr,
            $loanArr
        ]);

        $array = [
            'pin' => **********,
            'amount' => '20000',
            'date' => now()->format('Y-m-d'),
            'type' => 'BILLING',
            'transaction_id' => '*************',
        ];
        $manyLoansPaymentDto = RegisterPaymentFromEasyPayDto::from($array);
        $action->execute($manyLoansPaymentDto);
    }
}
