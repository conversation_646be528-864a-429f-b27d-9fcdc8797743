<?php

namespace Modules\Common\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LoanContactHistorySeeder extends Seeder
{
    public function run()
    {
        $loanAutoIncrement = config('common.loanAutoIncrement');

        for ($i = 0; $i < 5; $i++) {
            DB::table('loan_contact_history')->insert(
                [
                    'loan_contact_actual_id' => rand(1, 15),
                    'client_id' => rand(1, 5),
                    'loan_id' => ($loanAutoIncrement + $i),
                    'contact_id' => $i+1,
                    'contact_type_id' => $i+1,
                    'seq_num' => 1,
                ]
            );
        }
    }
}