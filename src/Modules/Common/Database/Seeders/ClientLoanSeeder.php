<?php

namespace Modules\Common\Database\Seeders;

use Faker\Factory as Faker;
use Faker\Generator;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Bank;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientAddress;
use Modules\Common\Models\ClientEmployer;
use Modules\Common\Models\ClientIdCard;
use Modules\Common\Models\Contact;
use Modules\Common\Models\Guarant;
use Modules\Common\Models\LoanStatus;
use Modules\Common\Models\Office;
use Modules\Common\Models\PaymentMethod;
use Modules\Common\Models\ProductSetting;
use Modules\Product\Repository\ProductRepository;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\Sales\Application\Actions\SignLoanAction;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;

class ClientLoanSeeder extends Seeder
{
    public function __construct(
        private readonly ProductRepository $productRepository
    )
    {
    }

    public function run(): void
    {
        ini_set('memory_limit', '4096M');
        set_time_limit(60);
        Auth::loginUsingId(Administrator::SYSTEM_ADMINISTRATOR_ID, true);
        $faker = Faker::create();
        for ($i = 0; $i < 15; $i++) {
            $officeId = $i % 2 ? Office::OFFICE_ID_NOVI_PAZAR_1 : Office::OFFICE_ID_WEB;
            if ($officeId == Office::OFFICE_ID_WEB) {
                $productId = $faker->numberBetween(1, 2);
            } else {
                $productId = $faker->numberBetween(3, 4);
            }

            $product = $this->productRepository->getProductById($productId);
            $minAmount = $product->getSettingValueByKey(ProductSetting::MIN_AMOUNT_KEY);
            $maxAmount = $product->getSettingValueByKey(ProductSetting::MAX_AMOUNT_KEY);

            $minPeriod = $product->getSettingValueByKey(ProductSetting::MIN_PERIOD_KEY);
            if(!$minPeriod){
                $minPeriod = 3;
            }
            $maxPeriod = $product->getSettingValueByKey(ProductSetting::MAX_PERIOD);
            if(!$maxPeriod){
                $maxPeriod = 12;
            }
            $loanPeriod = $faker->numberBetween(intval($minPeriod), intval($maxPeriod));

            //// create loan data
            $idCardData = ClientIdCard::factory(1)->make();

            $clientSeqData = new Sequence(
                ['phone' => [$faker->numerify('0999######'), $faker->numerify('0999######')]]
            );
            $clientData = Client::factory(1)->make($clientSeqData);
            $clientEmployerData = ClientEmployer::factory(1)->make();
            $clientAddressData = ClientAddress::factory(1)->make();
            $clientGuarantData = Guarant::factory(1)->make();
            $clientContactData = Contact::factory(2)->make();

            $data = [
                "loan" => [
                    "source_id" => $faker->numberBetween(1, 5),//old
                    "channel_id" => $faker->numberBetween(1, 4),//old
                    "administrator_id" => 1,//old
                    "product_id" => $productId,
                    "loan_sum" => floatToInt($faker->randomElement(range((int)$minAmount, (int)$maxAmount, 50))),
                    "loan_period" => $loanPeriod,
                    "discount_percent" => 0, ///$faker->numberBetween(1, 15),
                    "office_id" => $officeId,
                    "payment_method" => $officeId == Office::OFFICE_ID_WEB
                        ? PaymentMethod::PAYMENT_METHOD_EASYPAY
                        : $faker->randomElement([PaymentMethod::PAYMENT_METHOD_BANK, PaymentMethod::PAYMENT_METHOD_CASH]
                        ),
                    "iban" => $this->fakeIban($faker),
                    "client_id" => null,
                    "comment" => "kpomentariy test"
                ],
                'client' => $clientData->first()->toArray(),
                'client_idcard' => $idCardData->first()->toArray(),
                'client_employer' => $clientEmployerData->first()->toArray(),
                'client_address' => $clientAddressData->first()->toArray(),
                'guarant' => $clientGuarantData->toArray(),
                'contact' => $clientContactData->toArray(),
                "clientMetaAction" => "identification_mvr_valid_date",
                "attempt" => [
                    "start_at" => "2022-09-29 12:31:15"
                ],
            ];

            $application = app()->make(NewAppAction::class);
            $dto = (new DtoSerializerNewClientLoan())->createClientDto($data);
            $application->execute($dto);
            //echo (microtime(true) - $time)." seconds \n";
        }
        app(SignLoanAction::class)->execute($application->getDbLoan());
    }


    public function transliterate(string $cyrillic): string
    {
        $cyr = [
            'Љ',
            'Њ',
            'Џ',
            'џ',
            'ш',
            'ђ',
            'ч',
            'ћ',
            'ж',
            'љ',
            'њ',
            'Ш',
            'Ђ',
            'Ч',
            'Ћ',
            'Ж',
            'Ц',
            'ц',
            'а',
            'б',
            'в',
            'г',
            'д',
            'е',
            'ё',
            'ж',
            'з',
            'и',
            'й',
            'к',
            'л',
            'м',
            'н',
            'о',
            'п',
            'р',
            'с',
            'т',
            'у',
            'ф',
            'х',
            'ц',
            'ч',
            'ш',
            'щ',
            'ъ',
            'ы',
            'ь',
            'э',
            'ю',
            'я',
            'А',
            'Б',
            'В',
            'Г',
            'Д',
            'Е',
            'Ё',
            'Ж',
            'З',
            'И',
            'Й',
            'К',
            'Л',
            'М',
            'Н',
            'О',
            'П',
            'Р',
            'С',
            'Т',
            'У',
            'Ф',
            'Х',
            'Ц',
            'Ч',
            'Ш',
            'Щ',
            'Ъ',
            'Ы',
            'Ь',
            'Э',
            'Ю',
            'Я'
        ];
        $lat = [
            'Lj',
            'Nj',
            'Dž',
            'dž',
            'š',
            'đ',
            'č',
            'ć',
            'ž',
            'lj',
            'nj',
            'Š',
            'Đ',
            'Č',
            'Ć',
            'Ž',
            'C',
            'c',
            'a',
            'b',
            'v',
            'g',
            'd',
            'e',
            'io',
            'zh',
            'z',
            'i',
            'y',
            'k',
            'l',
            'm',
            'n',
            'o',
            'p',
            'r',
            's',
            't',
            'u',
            'f',
            'h',
            'ts',
            'ch',
            'sh',
            'sht',
            'a',
            'i',
            'y',
            'e',
            'yu',
            'ya',
            'A',
            'B',
            'V',
            'G',
            'D',
            'E',
            'Io',
            'Zh',
            'Z',
            'I',
            'Y',
            'K',
            'L',
            'M',
            'N',
            'O',
            'P',
            'R',
            'S',
            'T',
            'U',
            'F',
            'H',
            'Ts',
            'Ch',
            'Sh',
            'Sht',
            'A',
            'I',
            'Y',
            'e',
            'Yu',
            'Ya'
        ];

        return str_replace($cyr, $lat, $cyrillic);
    }

    public function fakeIban(Generator $faker): string
    {
        $bic = $faker->randomElement(Bank::BANK_LIST)['bic'];
        $iban = $faker->iban('BG');

        return substr_replace($iban, $bic, 0, strlen($bic));
    }
}
