<?php

namespace Modules\Common\Database\Seeders\CashLoanSteps;

use Faker\Factory as Faker;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Auth;
use Modules\Approve\Application\Action\ApproveLoanAction;
use Modules\Approve\Application\Action\ProcessLoanAction;
use Modules\Approve\Presentation\Dto\DecisionDto;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientAddress;
use Modules\Common\Models\ClientEmployer;
use Modules\Common\Models\ClientIdCard;
use Modules\Common\Models\Contact;
use Modules\Common\Models\Guarant;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Common\Models\PaymentMethod;
use Modules\Sales\Application\Actions\NewAppAction;
use Modules\Sales\Http\Middleware\DtoSerializerNewClientLoan;

class ApprovedCashLoanSeeder extends Seeder
{
    public static Loan $loan;

    public function run(): void
    {
        ini_set('memory_limit', '4096M');
        set_time_limit(60);
        Auth::loginUsingId(Administrator::SYSTEM_ADMINISTRATOR_ID, true);
        $faker = Faker::create();

        $officeId = Office::OFFICE_ID_NOVI_PAZAR_1;

        //// create loan data
        $idCardData = ClientIdCard::factory(1)->make();

        $clientSeqData = new Sequence(
            ['phone' => [$faker->numerify('0999######'), $faker->numerify('0999######')]]
        );
        $clientData = Client::factory(1)->make($clientSeqData);
        $clientEmployerData = ClientEmployer::factory(1)->make();
        $clientAddressData = ClientAddress::factory(1)->make();
        $clientGuarantData = Guarant::factory(1)->make();
        $clientContactData = Contact::factory(2)->make();

        $data = [
            "loan" => [
                "source_id" => $faker->numberBetween(1, 5),//old
                "channel_id" => $faker->numberBetween(1, 4),//old
                "administrator_id" => 1,//old
                "product_id" => 3,
                "loan_sum" => 40000,
                "loan_period" => 20,
                "discount_percent" => 0,
                "office_id" => $officeId,
                "payment_method" => PaymentMethod::PAYMENT_METHOD_CASH,
                "client_id" => null,
                "comment" => "kpomentariy test"
            ],
            'client' => $clientData->first()->toArray(),
            'client_idcard' => $idCardData->first()->toArray(),
            'client_employer' => $clientEmployerData->first()->toArray(),
            'client_address' => $clientAddressData->first()->toArray(),
            'guarant' => $clientGuarantData->toArray(),
            'contact' => $clientContactData->toArray(),
            "clientMetaAction" => "identification_mvr_valid_date",
            "attempt" => [
                "start_at" => "2022-09-29 12:31:15"
            ],
        ];

        $dto = (new DtoSerializerNewClientLoan())->createClientDto($data);
        $loan = app(NewAppAction::class)->execute($dto)->dbLoan();
        //$loan = app(SignLoanAction::class)->execute($loan)->dbModel(); no signing for office
        $loan = app(ProcessLoanAction::class)->execute($loan)->dbModel();
        self::$loan = app(ApproveLoanAction::class)->execute(new DecisionDto(
            $loan->getKey(),
            1,
            ApproveDecision::APPROVE_DECISION_APPROVED,
            ApproveDecisionReason::APPROVE_DECISION_REASON_OTHER,
            '',
            null
        ));
    }
}
