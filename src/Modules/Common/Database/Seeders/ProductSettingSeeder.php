<?php

namespace Modules\Common\Database\Seeders;

use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Collection;
use Modules\Common\Models\Product;
use Modules\Common\Models\ProductProductSetting;
use Modules\Common\Models\ProductType;
use Modules\Common\Models\ProductSetting;

class ProductSettingSeeder extends Seeder
{
    /**
     * @return void
     */
    public function run(): void
    {
        $this->seedData(
            ProductSetting::getCommonSettings(),
            ProductSetting::PRODUCT_SETTING_TYPE_COMMON
        );
        $this->seedData(
            ProductSetting::getTermSettings(),
            ProductSetting::PRODUCT_SETTING_TYPE_TERM
        );

        /**
         * @var Product[]|Collection $products
         */
        $products = Product::all();
        $defProductSettings = ProductSetting::all();
        $productSettings = [];

        foreach ($products as $product) {
            foreach ($defProductSettings as $defProductSetting) {
                $value = $defProductSetting->default_value;
                if ($defProductSetting->name == ProductSetting::PERIOD_KEY) {
                    $value = $product->isPayday() ? 1 : Faker::create()->randomElement([30,14,7]);
                }
                if ($defProductSetting->name == ProductSetting::MAX_AMOUNT_KEY && $product->isPayday()) {
                    $value = 600;
                }
                if ($defProductSetting->name == ProductSetting::SEND_EMAIL_KEY) {
                    $value = 1;
                }

                $productSettings[$defProductSetting->product_setting_id] = [
                    'product_id' => $product->product_id,
                    'product_setting_id' => $defProductSetting->product_setting_id,
                    'name' => $defProductSetting->name,
                    'value' => $value,
                ];
            }
            $product->productSettings()->createMany($productSettings);
        }

        $settingsToRemove = [
            'min_discount_percent',
            'default_discount_percent',
            'max_discount_percent',
            'discount_step',
        ];
        //Soft deleting!! ProductSettingDiscountsRemovalSeeder
        foreach ($settingsToRemove as $settingKey) {
            $productSetting = ProductSetting::where('name', $settingKey)->first();

            if (!empty($productSetting)) {
                ProductProductSetting::where('product_setting_id', $productSetting->getKey())->delete();
                $productSetting->delete();
            }
        }
        //ProductSettingGracePeriodRefactoringSeeder
        $productSettingGracePeriod = ProductSetting::where('name', 'grace_period')->first();

        $productSettingGracePeriod->options = [
            'canGrace' => 1,
            'cantGrace' => 0,
        ];
        $productSettingGracePeriod->save();
        //ProductSettingExtendRefactoringSeeder
        $productSettingGracePeriod = ProductSetting::where('name', ProductSetting::EXTENSION_KEY)->first();

        $productSettingGracePeriod->options = [
            'canExtend' => 1,
            'cantExtend' => 0,
        ];
        $productSettingGracePeriod->value_type = ProductSetting::PRODUCT_SETTING_VALUE_TYPE_SELECT;
        $productSettingGracePeriod->save();
    }

    /**
     * @param array $data
     * @param string $type
     */
    protected function seedData(array $data, string $type): void
    {
        foreach ($data as $key => $value) {
            $setting = new ProductSetting();

            $fill = [
                'name' => $key,
                'type' => $type,
                'value_type' => ProductSetting::PRODUCT_SETTING_VALUE_TYPE_NUMBER,
                'default_value' => $value,
            ];
            if (is_array($value)) {
                $fill['value_type'] = ProductSetting::PRODUCT_SETTING_VALUE_TYPE_SELECT;
                $fill['options'] = $value;
                $fill['default_value'] = $value[array_rand($value)];
            }

            $setting->fill($fill);
            $setting->save();
        }
    }
}
