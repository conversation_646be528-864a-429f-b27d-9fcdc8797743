<?php

namespace Modules\Common\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;

class SourceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $variants = [
            'viber',
            'email',
            'www.stikcredit.bg',
            'izbiram.bg',
            'abv.bg',
            'www.google.bg',
            'www.facebook.com',
            'lendivo.bg'
        ];

        $sources = [];
        foreach ($variants as $variant) {
            $sources[] = [
                'name' => $variant,
                'active' => 1,
                'deleted' => 0,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ];
        }

        DB::table('source')->insert($sources);
    }
}
