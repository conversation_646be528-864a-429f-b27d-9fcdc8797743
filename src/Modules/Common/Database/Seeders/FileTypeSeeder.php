<?php

namespace Modules\Common\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Enums\FileTypeEnum;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\FileType;

class FileTypeSeeder extends Seeder
{
    public function run()
    {
        $fileTypes = array_map(static function (FileTypeEnum $item) {
            return [
                'name' => $item->value,
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ];
        }, FileTypeEnum::cases());

        DB::table('file_type')->insertOrIgnore($fileTypes);



        if(isLocal()) {
            $data = [
                [
                    'file_type_id' => FileType::getIdFromCode(FileTypeEnum::LEGAL_INFO),
                    'name' => FileTypeEnum::LEGAL_INFO->value,
                    'created_at' => now(),
                    'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                ],
                [
                    'file_type_id' => FileType::getIdFromCode(FileTypeEnum::A4E_PERFORMANCE_JSON),
                    'name' => FileTypeEnum::A4E_PERFORMANCE_JSON,
                    'created_at' => now(),
                    'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                ],
                [
                    'file_type_id' => FileType::getIdFromCode(FileTypeEnum::A4E_PERFORMANCE_ZIP),
                    'name' => FileTypeEnum::A4E_PERFORMANCE_ZIP,
                    'created_at' => now(),
                    'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
                ],
            ];
            DB::table('file_type')->insertOrIgnore($data);
        }
        DB::statement(
            "SELECT setval('file_type_file_type_id_seq', (SELECT MAX(file_type_id) FROM file_type) + 1);"
        );
    }
}
