<?php

namespace Modules\Common\Database\Seeders\Test\AllSteps;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AfterLoanRepaymentSeeder extends Seeder
{
    use WithoutModelEvents;

    const LOAN_ID = 10039;

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        foreach (self::database() as $tableName => $data){
            DB::table($tableName)->insert($data);
        }
    }

    public static function database(): array
    {
        return [
            'approve_agent_stats' => self::approveAgentStats(),
            'cash_operational_transaction' => self::cashOperationalTransaction(),
            'client' => self::client(),
            'client_actual_stats' => self::clientActualStats(),
            'client_address' => self::clientAddress(),
            'client_history' => self::clientHistory(),
            'client_idcard' => self::clientIdCard(),
            'client_name' => self::clientName(),
            'client_phone' => self::clientPhone(),
            'client_picture' => self::clientPicture(),
            'representor' => self::representor(),
            'client_representor' => self::clientRepresentor(),
            'contact' => self::contact(),
            'loan' => self::loan(),
            'approve_attempt' => self::approveAttempt(),
            'client_office' => self::clientOffice(),
            'installment' => self::installment(),
            'installment_history' => self::installmentHistory(),
            'loan_actual_stats' => self::loanActualStats(),
            'loan_address' => self::loanAddress(),
            'loan_client_name' => self::loanClientName(),
            'loan_contact_actual' => self::loanContactActual(),
            'loan_history' => self::loanHistory(),
            'loan_idcard' => self::loanIdCard(),
            'loan_meta' => self::loanMeta(),
            'loan_phone' => self::loanPhone(),
            'loan_product_setting' => self::loanProductSetting(),
            'loan_status_history' => self::loanStatusHistory(),
            'notification_setting' => self::notificationSetting(),
            'payment' => self::payment(),
            'payment_task' => self::paymentTask(),
            'sms' => self::sms(),
            'stats_daily' => self::statsDaily()
        ];
    }

    public static function approveAgentStats(): array
    {
        $data = '[
  {
    "approve_agent_stats_id": 12,
    "loan_id": 10039,
    "client_id": 1,
    "pin": "9104188750",
    "client_full_name": "Калоян Патлеев Илиев",
    "client_phone": "0896667788",
    "client_email": "",
    "office_id": 33,
    "is_online": false,
    "product_type": "payday",
    "payment_method": "Cash",
    "amount_requested": 20000,
    "amount_approved": 20000,
    "credit_limit": null,
    "period_requested": 7,
    "period_approved": 7,
    "discount_percent": 0.00,
    "interest_rate": 36.00,
    "penalty_rate": 213.09,
    "request_created_at": null,
    "loan_created_at": "'.now().'",
    "loan_signed_at": "'.now().'",
    "first_processing_agent_id": 2,
    "first_processing_agent_name": "Super Mega Admin",
    "first_processing_start_at": "'.now().'",
    "first_processing_end_at": "'.now().'",
    "first_decision_id": 4,
    "first_decision_reason_id": null,
    "first_waiting_time": 0,
    "first_processing_time": 0,
    "referer": null,
    "final_processing_agent_id": 2,
    "final_processing_agent_name": "Super Mega Admin",
    "final_processing_start_at": "'.now().'",
    "final_processing_end_at": "'.now().'",
    "final_decision_id": 4,
    "final_decision_reason_id": null,
    "final_waiting_time": 0,
    "final_processing_time": 0,
    "created_at": "'.now().'",
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function cashOperationalTransaction(): array
    {
        $data = '[
  {
    "cash_operational_transaction_id": 1,
    "office_id": 33,
    "transaction_type": "initial_balance",
    "direction": "in",
    "amount": "100000",
    "created_at": "'.now().'"
  },
  {
    "cash_operational_transaction_id": 2,
    "office_id": 33,
    "transaction_type": "loan_payout",
    "direction": "out",
    "amount": "20000",
    "created_at": "' . now() . '"
  }
]';
        return json_decode($data, true);
    }

    public static function approveAttempt(): array
    {
        $data = '[
  {
    "approve_attempt_id": 12,
    "loan_id": 10039,
    "administrator_id": 2,
    "office_id": 33,
    "approve_decision_id": 4,
    "approve_decision_reason_id": null,
    "skip_time": null,
    "skip_till": null,
    "skip_counter": 1,
    "details": "",
    "start_at": "'.now().'",
    "end_at": "'.now().'",
    "total_time": 0,
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "waiting_time": 0,
    "processing_time": 0
  }
]';
        return json_decode($data, true);
    }

    public static function client(): array
    {
        $data = '[
  {
    "client_id": 1,
    "pin": "9104188750",
    "idcard_number": "9104188750",
    "first_name": "Калоян",
    "middle_name": "Патлеев",
    "last_name": "Илиев",
    "phone": "0896667788",
    "email": "",
    "new": 0,
    "dead": 0,
    "blocked": 0,
    "type": "real",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "latin_names": null,
    "gender": null,
    "legal_status": "company",
    "citizenship_type": "unknown",
    "legal_status_code": "437",
    "economy_sector_code": "9",
    "industry_code": "96",
    "registered_in_ccr": 0,
    "need_ccr_sync": 1,
    "registered_in_ccr_at": null,
    "set_need_ccr_sync_at": null,
    "unset_need_ccr_sync_at": null,
    "verified": null,
    "birth_date": null,
    "first_name_latin": "Kaloyan",
    "middle_name_latin": "Patleev",
    "last_name_latin": "Iliev"
  }
]';
        return json_decode($data, true);
    }

    public static function clientActualStats(): array
    {
        $data = '[
  {
    "client_actual_stats_id": 1434,
    "client_id": 1,
    "date": "'.now()->format('Y-m-d').'",
    "credit_limit": null,
    "applications_count": 1,
    "approved_loans_count": 1,
    "disapproved_loans_count": 0,
    "repaid_loans_count": 1,
    "days_without_loan": 0,
    "lifetime_value_total": null,
    "current_overdue_days": 0,
    "max_overdue_days": 0,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "credit_limit_updated_at": null,
    "current_overdue_amount": 0,
    "max_overdue_amount": 0,
    "lifetime_value_total": 9.68
  }
]';
        return json_decode($data, true);
    }

    public static function clientAddress(): array
    {
        $data = '[
  {
    "client_address_id": 67,
    "client_id": 1,
    "type": "id_card",
    "city_id": 1,
    "post_code": "",
    "address": "Soedinenie",
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "address_is_match": "yes"
  },
  {
    "client_address_id": 68,
    "client_id": 1,
    "type": "current",
    "city_id": 1,
    "post_code": "",
    "address": "Soedinenie",
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "address_is_match": "yes"
  }
]';
        return json_decode($data, true);
    }

    public static function clientHistory(): array
    {
        $data = '[
  {
    "client_history_id": 1642,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.pin",
    "value_from": null,
    "value_to": "9104188750",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1643,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.idcard_number",
    "value_from": null,
    "value_to": "9104188750",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1644,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.phone",
    "value_from": null,
    "value_to": "0896667788",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1645,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.email",
    "value_from": null,
    "value_to": "",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1646,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.legal_status",
    "value_from": null,
    "value_to": "company",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1647,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.citizenship_type",
    "value_from": null,
    "value_to": "unknown",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1648,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.legal_status_code",
    "value_from": null,
    "value_to": "437",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1649,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.economy_sector_code",
    "value_from": null,
    "value_to": "9",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1650,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.industry_code",
    "value_from": null,
    "value_to": "96",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1651,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.first_name",
    "value_from": null,
    "value_to": "Калоян",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1652,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.middle_name",
    "value_from": null,
    "value_to": "Патлеев",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1653,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.last_name",
    "value_from": null,
    "value_to": "Илиев",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1654,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.first_name_latin",
    "value_from": null,
    "value_to": "Kaloyan",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1655,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.middle_name_latin",
    "value_from": null,
    "value_to": "Patleev",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1656,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.last_name_latin",
    "value_from": null,
    "value_to": "Iliev",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1657,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.need_ccr_sync",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1658,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client.created_by",
    "value_from": null,
    "value_to": "2",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1659,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.address",
    "value_from": null,
    "value_to": "Soedinenie",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1660,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.city_id",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1661,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.post_code",
    "value_from": null,
    "value_to": "",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1662,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.type",
    "value_from": null,
    "value_to": "id_card",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1663,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.last",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1664,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.address",
    "value_from": null,
    "value_to": "Soedinenie",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1665,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.city_id",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1666,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.post_code",
    "value_from": null,
    "value_to": "",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1667,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.type",
    "value_from": null,
    "value_to": "current",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1668,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_address.last",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1671,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_name.first_name",
    "value_from": null,
    "value_to": "Калоян",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1672,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_name.middle_name",
    "value_from": null,
    "value_to": "Патлеев",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1673,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_name.last_name",
    "value_from": null,
    "value_to": "Илиев",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1674,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_name.last",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1675,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.pin",
    "value_from": null,
    "value_to": "9104188750",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1676,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.idcard_number",
    "value_from": null,
    "value_to": "9104188750",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1677,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.idcard_issued_id",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1678,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.issue_date",
    "value_from": null,
    "value_to": "2011-03-09",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1679,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.valid_date",
    "value_from": null,
    "value_to": "2024-03-09",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1680,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.city_id",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1681,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.address",
    "value_from": null,
    "value_to": "Soedinenie",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1682,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_idcard.last",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1683,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_phone.number",
    "value_from": null,
    "value_to": "0896667788",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1684,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_phone.seq_num",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "client_history_id": 1685,
    "client_id": 1,
    "administrator_id": 2,
    "field": "client_phone.last",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function clientIdCard(): array
    {
        $data = '[
  {
    "client_idcard_id": 33,
    "client_id": 1,
    "city_id": 1,
    "idcard_issued_id": 1,
    "pin": "9104188750",
    "idcard_number": "9104188750",
    "issue_date": "2011-03-09",
    "valid_date": "2024-03-09",
    "post_code": null,
    "address": "Soedinenie",
    "sex": null,
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function clientName(): array
    {
        $data = '[
  {
    "client_name_id": 35,
    "client_id": 1,
    "first_name": "Калоян",
    "middle_name": "Патлеев",
    "last_name": "Илиев",
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function clientOffice(): array
    {
        $data = '[
  {
    "client_id": 1,
    "office_id": 33,
    "loan_id": 10039
  }
]';
        return json_decode($data, true);
    }

    public static function clientPhone(): array
    {
        $data = '[
  {
    "client_phone_id": 49,
    "client_id": 1,
    "number": "0896667788",
    "seq_num": 1,
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function clientPicture(): array
    {
        $data = '[
  {
    "client_picture_id": 35,
    "client_id": 1,
    "type": "mvr",
    "base64": "SomeLongBase64String",
    "source": "funky",
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "pin": "9104188750"
  }
]';
        return json_decode($data, true);
    }

    public static function clientRepresentor(): array
    {
        $data = '[
  {
    "client_representor_id": 23,
    "client_id": 1,
    "representor_id": 27,
    "last": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2
  }
]';
        return json_decode($data, true);
    }

    public static function contact(): array
    {
        $data = '[
  {
    "contact_id": 393,
    "pin": null,
    "phone": "0896667701",
    "email": null,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "name": "James Karter"
  },
  {
    "contact_id": 394,
    "pin": null,
    "phone": "0896667702",
    "email": null,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "name": "Boby Oneal"
  }
]';
        return json_decode($data, true);
    }

    public static function installment(): array
    {
        $data = '[
  {
    "installment_id": 46,
    "client_id": 1,
    "loan_id": 10039,
    "seq_num": 1,
    "due_date": "'.now()->addWeek()->toDateString().'",
    "accrued_total_amount": 200.00,
    "total_amount": 209.68,
    "principal": 200.00,
    "paid_principal": 200.00,
    "rest_principal": 0.00,
    "accrued_interest": 0.00,
    "interest": 1.40,
    "late_interest": 0.00,
    "paid_accrued_interest": 1.40,
    "paid_interest": 1.40,
    "rest_interest": 0.00,
    "paid_late_interest": 0.00,
    "accrued_penalty": 0.00,
    "penalty": 8.28,
    "late_penalty": 0.00,
    "paid_accrued_penalty": 8.28,
    "paid_penalty": 8.28,
    "rest_penalty": 0.00,
    "paid_late_penalty": 0.00,
    "overdue_days": 0,
    "overdue_amount": 0.00,
    "max_overdue_days": 0,
    "max_overdue_amount": 0.00,
    "status": "paid",
    "paid": 1,
    "paid_at": "'.now().'",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function installmentHistory(): array
    {
        $data = '[
  {
    "installment_history_id": 133,
    "installment_id": 46,
    "field": "status",
    "value_from": "scheduled",
    "value_to": "paid",
    "ip": null,
    "headers": null,
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "installment_history_id": 134,
    "installment_id": 46,
    "field": "paid",
    "value_from": "0",
    "value_to": "1",
    "ip": null,
    "headers": null,
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "installment_history_id": 135,
    "installment_id": 46,
    "field": "paid_at",
    "value_from": null,
    "value_to": "2023-06-02",
    "ip": null,
    "headers": null,
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function loan(): array
    {
        $data = '[
  {
    "loan_id": 10039,
    "client_id": 1,
    "product_id": 3,
    "product_type_id": 1,
    "loan_type_id": 1,
    "discount_percent": 0.00,
    "amount_requested": 20000,
    "amount_approved": 20000,
    "installments_requested": 1,
    "installments_approved": 1,
    "currency_id": 1,
    "period_requested": 7,
    "period_approved": 7,
    "period_grace": null,
    "loan_status_id": 7,
    "client_discount_actual_id": null,
    "last_status_update_administrator_id": 2,
    "last_status_update_date": "'.now().'",
    "payment_method_id": 3,
    "source_id": 1,
    "channel_id": 1,
    "office_id": 33,
    "administrator_id": null,
    "hash": "333b9597fe0a644fb474eb620489d78e",
    "comment": null,
    "juridical": 0,
    "cession": 0,
    "fraud": 0,
    "repaid_at": "'.now().'",
    "interest_updated_at": null,
    "loan_changed_at": "'.now().'",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "grace_until": null,
    "grace_updated": 0,
    "amount_rest": 200.00,
    "a4e_performance": 1,
    "a4e_performance_at": null,
    "a4e_performance_flag": null,
    "registered_in_ccr": 0,
    "need_ccr_sync": 1,
    "registered_in_ccr_at": null,
    "set_need_ccr_sync_at": "'.now().'",
    "unset_need_ccr_sync_at": null,
    "extended": 0,
    "ccr_finished": 0,
    "ccr_finished_at": null,
    "interest_percent": 3600,
    "penalty_percent": 21309,
    "installment_modifier": "+1 days",
    "period_final": 7
  }
]';
        $loanData = json_decode($data, true);
        $loanData[0]['approve_tasks'] = '["identification_new_client","identification_mvr_no_response","manual_approve"]';
        return $loanData;
    }

    public static function loanActualStats(): array
    {
        $data = '[
  {
    "loan_stats_id": 307,
    "loan_id": 10039,
    "date": "2023-06-02",
    "loan_limit": 10,
    "profit": 9.68,
    "approved": 1,
    "first_loan": 1,
    "has_payment": 1,
    "total_installments_count": 1,
    "paid_installments_count": 1,
    "unpaid_installments_count": 0,
    "current_installment": null,
    "current_installment_amount": null,
    "current_installment_date": null,
    "first_installment_date": "2023-06-09",
    "next_installment_date": null,
    "last_installment_date": "2023-06-09",
    "previous_loans_count": 0,
    "previous_applications_count": 1,
    "days_after_previous_loan": 0,
    "rate_overdue_interest": 0.00,
    "repaid_amount_principal": 200.00,
    "repaid_amount_interest": 1.40,
    "repaid_amount_penalty": 8.28,
    "repaid_amount_taxes": 0.00,
    "repaid_amount_total": 209.68,
    "due_amount_total": 0.00,
    "due_amount_total_interest": 0.00,
    "due_amount_total_penalty": 0.00,
    "due_amount_total_taxes": 0.00,
    "outstanding_amount_total": 0.00,
    "outstanding_amount_principal": 0.00,
    "outstanding_amount_interest": 0.00,
    "outstanding_amount_penalty": 0.00,
    "outstanding_amount_taxes": 0.00,
    "accrued_amount_total": 200.00,
    "accrued_amount_principal": 200.00,
    "accrued_amount_interest": 0.00,
    "accrued_amount_penalty": 0.00,
    "accrued_amount_taxes": 0.00,
    "current_overdue_days": 0,
    "current_overdue_amount": 0.00,
    "max_overdue_days": 0,
    "max_overdue_amount": 0.00,
    "overdue_amount_principal": 0.00,
    "overdue_amount_interest": 0.00,
    "overdue_amount_penalty": 0.00,
    "overdue_amount_taxes": 0.00,
    "overdue_amount_total": 0.00,
    "overdue_installments": 0,
    "last_paid_date": "2023-06-02",
    "contract_end_date": "2023-06-09",
    "repayment_date": "2023-06-09",
    "credit_limit": 10,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-06-02 08:21:03",
    "created_by": 2,
    "updated_at": "2023-06-02 08:21:03",
    "updated_by": 2,
    "total_overdue_penalty": 0.00,
    "total_overdue_interest": 0.00,
    "total_overdue_principal": 0.00,
    "rate_annual_percentage": 0.36,
    "total_interest": 1.40,
    "total_penalty": 0,
    "credit_limit_updated_at": null,
    "first_repayment_date": "2023-06-02",
    "prev_repayment_date": "2023-06-02"
  }
]';
        return json_decode($data, true);
    }

    public static function loanAddress(): array
    {
        $data = '[
  {
    "loan_id": 10039,
    "client_address_id": 68,
    "last": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "type": "id_card"
  },
  {
    "loan_id": 10039,
    "client_address_id": 67,
    "last": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "type": "id_card"
  }
]';
        return json_decode($data, true);
    }

    public static function loanClientName(): array
    {
        $data = '[
  {
    "loan_id": 10039,
    "client_name_id": 35,
    "last": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function loanContactActual(): array
    {
        $data = '[
  {
    "loan_contact_actual_id": 51,
    "client_id": 1,
    "loan_id": 10039,
    "contact_id": 393,
    "contact_type_id": 1,
    "seq_num": 1,
    "created_at": "'.now().'",
    "created_by": 2,
    "active": 1
  },
  {
    "loan_contact_actual_id": 52,
    "client_id": 1,
    "loan_id": 10039,
    "contact_id": 394,
    "contact_type_id": 1,
    "seq_num": 2,
    "created_at": "'.now().'",
    "created_by": 2,
    "active": 1
  }
]';
        return json_decode($data, true);
    }

    public static function loanHistory(): array
    {
        $data = '[
  {
    "loan_history_id": 1679,
    "loan_id": 10039,
    "field": "loan.client_id",
    "value_from": null,
    "value_to": "31",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1680,
    "loan_id": 10039,
    "field": "loan.office_id",
    "value_from": null,
    "value_to": "33",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1681,
    "loan_id": 10039,
    "field": "loan.loan_status_id",
    "value_from": null,
    "value_to": "2",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1682,
    "loan_id": 10039,
    "field": "loan.last_status_update_administrator_id",
    "value_from": null,
    "value_to": "2",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1683,
    "loan_id": 10039,
    "field": "loan.last_status_update_date",
    "value_from": null,
    "value_to": "2023-05-31 17:17:40",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1684,
    "loan_id": 10039,
    "field": "loan.loan_changed_at",
    "value_from": null,
    "value_to": "2023-05-31 17:17:40",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1685,
    "loan_id": 10039,
    "field": "loan.product_id",
    "value_from": null,
    "value_to": "3",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1686,
    "loan_id": 10039,
    "field": "loan.product_type_id",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1687,
    "loan_id": 10039,
    "field": "loan.payment_method_id",
    "value_from": null,
    "value_to": "3",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1688,
    "loan_id": 10039,
    "field": "loan.amount_requested",
    "value_from": null,
    "value_to": "20000",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1689,
    "loan_id": 10039,
    "field": "loan.amount_approved",
    "value_from": null,
    "value_to": "20000",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1690,
    "loan_id": 10039,
    "field": "loan.loan_type_id",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1691,
    "loan_id": 10039,
    "field": "loan.amount_rest",
    "value_from": null,
    "value_to": "20000",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1692,
    "loan_id": 10039,
    "field": "loan.discount_percent",
    "value_from": null,
    "value_to": "0",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1693,
    "loan_id": 10039,
    "field": "loan.period_requested",
    "value_from": null,
    "value_to": "7",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1694,
    "loan_id": 10039,
    "field": "loan.period_approved",
    "value_from": null,
    "value_to": "7",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1696,
    "loan_id": 10039,
    "field": "loan.period_final",
    "value_from": null,
    "value_to": "7",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1697,
    "loan_id": 10039,
    "field": "loan.interest_percent",
    "value_from": null,
    "value_to": "36.00",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1698,
    "loan_id": 10039,
    "field": "loan.penalty_percent",
    "value_from": null,
    "value_to": "213.09",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1699,
    "loan_id": 10039,
    "field": "loan.installment_modifier",
    "value_from": null,
    "value_to": "+1 days",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1700,
    "loan_id": 10039,
    "field": "loan.installments_requested",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1701,
    "loan_id": 10039,
    "field": "loan.installments_approved",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1702,
    "loan_id": 10039,
    "field": "loan.approve_tasks",
    "value_from": null,
    "value_to": "[\"identification_new_client\",\"identification_mvr_no_response\",\"manual_approve\"]",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1703,
    "loan_id": 10039,
    "field": "loan.currency_id",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1704,
    "loan_id": 10039,
    "field": "loan.source_id",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1705,
    "loan_id": 10039,
    "field": "loan.channel_id",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1706,
    "loan_id": 10039,
    "field": "loan.hash",
    "value_from": null,
    "value_to": "333b9597fe0a644fb474eb620489d78e",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1707,
    "loan_id": 10039,
    "field": "loan_address.client_address_id",
    "value_from": null,
    "value_to": "68",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1708,
    "loan_id": 10039,
    "field": "loan_address.last",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1709,
    "loan_id": 10039,
    "field": "loan_address.client_address_id",
    "value_from": null,
    "value_to": "67",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1710,
    "loan_id": 10039,
    "field": "loan_address.last",
    "value_from": null,
    "value_to": "1",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1711,
    "loan_id": 10039,
    "field": "loan_phone.client_phone_id",
    "value_from": null,
    "value_to": "49",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1713,
    "loan_id": 10039,
    "field": "loan_idcard.client_idcard_id",
    "value_from": null,
    "value_to": "33",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 1714,
    "loan_id": 10039,
    "field": "loan_client_name.client_name_id",
    "value_from": null,
    "value_to": "35",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 149,
    "loan_id": 10039,
    "field": "loan.loan_status_id",
    "value_from": "2",
    "value_to": "3",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"]}",
    "administrator_id": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 1,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 150,
    "loan_id": 10039,
    "field": "loan.last_status_update_date",
    "value_from": "2023-05-31 17:17:40",
    "value_to": "2023-05-31 17:17:40",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"]}",
    "administrator_id": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 1,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 151,
    "loan_id": 10039,
    "field": "loan.administrator_id",
    "value_from": null,
    "value_to": "2",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"]}",
    "administrator_id": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 1,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 152,
    "loan_id": 10039,
    "field": "loan.loan_status_id",
    "value_from": "3",
    "value_to": "5",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 153,
    "loan_id": 10039,
    "field": "loan.last_status_update_date",
    "value_from": "2023-05-31 17:17:40",
    "value_to": "2023-05-31 17:17:40",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 154,
    "loan_id": 10039,
    "field": "loan.administrator_id",
    "value_from": "2",
    "value_to": null,
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:40",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:40"
  },
  {
    "loan_history_id": 19,
    "loan_id": 10039,
    "field": "loan.loan_status_id",
    "value_from": "5",
    "value_to": "6",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:41",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:41"
  },
  {
    "loan_history_id": 20,
    "loan_id": 10039,
    "field": "loan.last_status_update_date",
    "value_from": "2023-05-31 17:17:40",
    "value_to": "2023-05-31 17:17:40",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-05-31 17:17:41",
    "created_by": 2,
    "updated_at": "2023-05-31 17:17:41"
  },
  {
    "loan_history_id": 4,
    "loan_id": 10039,
    "field": "loan.loan_status_id",
    "value_from": "6",
    "value_to": "7",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-06-02 08:26:17",
    "created_by": 2,
    "updated_at": "2023-06-02 08:26:17"
  },
  {
    "loan_history_id": 5,
    "loan_id": 10039,
    "field": "loan.last_status_update_date",
    "value_from": "2023-06-02 08:26:16",
    "value_to": "2023-06-02 08:26:17",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-06-02 08:26:17",
    "created_by": 2,
    "updated_at": "2023-06-02 08:26:17"
  },
  {
    "loan_history_id": 6,
    "loan_id": 10039,
    "field": "loan.repaid_at",
    "value_from": null,
    "value_to": "2023-06-02 08:26:17",
    "ip": "127.0.0.1",
    "headers": "{\"host\":[\"localhost:8000\"],\"user-agent\":[\"Symfony\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,*\\/*;q=0.8\"],\"accept-language\":[\"en-us,en;q=0.5\"],\"accept-charset\":[\"ISO-8859-1,utf-8;q=0.7,*;q=0.7\"],\"content-type\":[\"application\\/x-www-form-urlencoded\"]}",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-06-02 08:26:17",
    "created_by": 2,
    "updated_at": "2023-06-02 08:26:17"
  }
]';
        return json_decode($data, true);
    }

    public static function loanIdCard(): array
    {
        $data = '[
  {
    "loan_id": 10039,
    "client_idcard_id": 33,
    "last": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function loanMeta(): array
    {
        $data = '[
  {
    "meta_id": 75,
    "loan_id": 10039,
    "key": "discount_options_created",
    "value": "{\"loan_discount\":0,\"admin_discount\":0,\"client_discount\":null,\"sale_task_discount\":null,\"client_discount_actual_id\":null,\"administrator_id\":null,\"product_ids\":null}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "meta_id": 12,
    "loan_id": 10039,
    "key": "discount_options_approved",
    "value": "{\"loan_discount\":\"0.00\",\"admin_discount\":0,\"client_discount\":null,\"sale_task_discount\":null,\"client_discount_actual_id\":null,\"administrator_id\":null,\"product_ids\":null}",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function loanPhone(): array
    {
        $data = '[
  {
    "loan_id": 10039,
    "client_phone_id": 49,
    "last": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function loanProductSetting(): array
    {
        $data = '[
  {
    "loan_product_setting_id": 42,
    "loan_id": 10039,
    "product_id": 3,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-06-02 08:35:10",
    "created_by": 2,
    "period": "1"
  }
]';
        $decoded = json_decode($data, true);
        $decoded[0]['settings'] = '{"min_amount":"200","max_amount":"600","default_amount":"200","default_period":"7","period":"1","min_period":"3","max_period":"24","amount_step":"50","period_step":"1","extension":"1","grace_period":"1","grace_period_days":"30","send_email":"0","send_mail":"1","send_sms":"0","late_interest":"10.01","late_penalty":"10.01","late_penalty_days":"0","collector_tax":"0","collector_tax_days":"0","sms_tax":"0","mail_tax":"0"}';
        return $decoded;
    }

    public static function loanStatusHistory(): array
    {
        $data = '[
  {
    "loan_status_history_id": 148,
    "loan_id": 10039,
    "loan_status_id": 2,
    "date": "2023-06-02 08:32:33",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-06-02 08:32:33",
    "created_by": 2
  },
  {
    "loan_status_history_id": 46,
    "loan_id": 10039,
    "loan_status_id": 3,
    "date": "2023-06-02 08:32:33",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-06-02 08:32:33",
    "created_by": 1
  },
  {
    "loan_status_history_id": 47,
    "loan_id": 10039,
    "loan_status_id": 5,
    "date": "2023-06-02 08:32:33",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-06-02 08:32:33",
    "created_by": 2
  },
  {
    "loan_status_history_id": 142,
    "loan_id": 10039,
    "loan_status_id": 6,
    "date": "2023-06-02 08:32:33",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-06-02 08:32:33",
    "created_by": 2
  },
  {
    "loan_status_history_id": 3,
    "loan_id": 10039,
    "loan_status_id": 7,
    "date": "2023-06-02 08:32:33",
    "administrator_id": 2,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-06-02 08:32:33",
    "created_by": 2
  }
]';
        return json_decode($data, true);
    }

    public static function notificationSetting(): array
    {
        $data = '[
  {
    "notification_setting_id": 3143,
    "client_id": 1,
    "type": "system",
    "channel": "call",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3144,
    "client_id": 1,
    "type": "system",
    "channel": "sms",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3145,
    "client_id": 1,
    "type": "system",
    "channel": "email",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3146,
    "client_id": 1,
    "type": "system",
    "channel": "viber",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3147,
    "client_id": 1,
    "type": "information",
    "channel": "call",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3148,
    "client_id": 1,
    "type": "information",
    "channel": "sms",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3149,
    "client_id": 1,
    "type": "information",
    "channel": "email",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3150,
    "client_id": 1,
    "type": "information",
    "channel": "viber",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3151,
    "client_id": 1,
    "type": "marketing",
    "channel": "call",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3152,
    "client_id": 1,
    "type": "marketing",
    "channel": "sms",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3153,
    "client_id": 1,
    "type": "marketing",
    "channel": "email",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3154,
    "client_id": 1,
    "type": "marketing",
    "channel": "viber",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3155,
    "client_id": 1,
    "type": "sales",
    "channel": "call",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3156,
    "client_id": 1,
    "type": "sales",
    "channel": "sms",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3157,
    "client_id": 1,
    "type": "sales",
    "channel": "email",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3158,
    "client_id": 1,
    "type": "sales",
    "channel": "viber",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3159,
    "client_id": 1,
    "type": "approve",
    "channel": "call",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3160,
    "client_id": 1,
    "type": "approve",
    "channel": "sms",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3161,
    "client_id": 1,
    "type": "approve",
    "channel": "email",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3162,
    "client_id": 1,
    "type": "approve",
    "channel": "viber",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3163,
    "client_id": 1,
    "type": "collect",
    "channel": "call",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3164,
    "client_id": 1,
    "type": "collect",
    "channel": "sms",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3165,
    "client_id": 1,
    "type": "collect",
    "channel": "email",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  },
  {
    "notification_setting_id": 3166,
    "client_id": 1,
    "type": "collect",
    "channel": "viber",
    "value": 1,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function payment(): array
    {
        $data = '[
  {
    "payment_id": 12,
    "client_id": 1,
    "loan_id": 10039,
    "installment_id": null,
    "office_id": 33,
    "payment_method_id": 3,
    "currency_id": 1,
    "direction": "out",
    "date": null,
    "source": "loan_approval",
    "amount_received": null,
    "amount_resto": null,
    "amount": 20000,
    "correction": 0,
    "document_number": null,
    "description": "Усвоен к-т : 10039",
    "handled_at": "2023-06-02 08:21:03",
    "handled_by": 2,
    "relation_key": null,
    "status": "delivered",
    "processing_by": null,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-06-02 08:21:03",
    "created_by": 2,
    "updated_at": "2023-06-02 08:21:03",
    "delivery_type": null
  },
  {
    "payment_id": 23,
    "client_id": 1,
    "loan_id": 10039,
    "installment_id": null,
    "office_id": 1,
    "payment_method_id": 1,
    "currency_id": 1,
    "direction": "in",
    "date": null,
    "source": "file_import",
    "amount_received": null,
    "amount_resto": null,
    "amount": 20968,
    "correction": 0,
    "document_number": null,
    "description": "ЕГН. 9104188750 кредит: 10039 BG97BUIN10554246S5A7ET",
    "handled_at": "2023-06-02 08:21:03",
    "handled_by": 2,
    "relation_key": null,
    "status": "delivered",
    "processing_by": null,
    "active": 1,
    "deleted": 0,
    "created_at": "2023-06-02 08:21:03",
    "created_by": 2,
    "updated_at": "2023-06-02 08:21:03",
    "purpose": "loan_payment"
  }
]';
        $loanData = json_decode($data, true);
        $loanData[1]['delivery'] = '{"outstandingPenaltyAmount":{"amount":"8.28","column":"outstandingPenaltyAmount","vat_class":"A"},"outstandingInterestAmount":{"amount":"1.40","column":"outstandingInterestAmount","vat_class":"A"},"outstandingPrincipleAmount":{"amount":"200.00","column":"outstandingPrincipleAmount","vat_class":"A"}}';
        return $loanData;
    }

    public static function paymentTask(): array
    {
        $data = '[
  {
    "payment_task_id": 12,
    "name": "give_out_cash",
    "payment_id": 12,
    "client_id": 1,
    "loan_id": 10039,
    "iban": null,
    "bic": null,
    "payment_method_id": 3,
    "amount": 20000,
    "basis": null,
    "handled_at": "'.now().'",
    "handled_by": 2,
    "status": "done",
    "direction": "out",
    "type": "outgoing_transfer",
    "success": null,
    "details": null,
    "start_at": "'.now().'",
    "end_at": null,
    "duration": null,
    "show_after": "'.now().'",
    "last_status_update_date": null,
    "parent_task_id": null,
    "imported_payment_id": null,
    "easypay_rid": null,
    "active": 0,
    "deleted": 1,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function representor(): array
    {
        $data = '[
  {
    "representor_id": 27,
    "pin": null,
    "first_name": "1234567",
    "middle_name": "тестович",
    "last_name": "test company",
    "phone": "0896667701",
    "phone_additional": "0896667702",
    "email": "<EMAIL>",
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'"
  }
]';
        return json_decode($data, true);
    }

    public static function sms(): array
    {
        $data = '[
  {
    "sms_id": 12,
    "sms_template_id": 1,
    "administrator_id": 2,
    "module": null,
    "identifier": null,
    "text": "\"СТИК КРЕДИТ\" АД - Iskaneto ti za kredit e odobreno! Poseti ofisa ni, za da poluchish parite si.",
    "sender": "System",
    "phone": "0896667788",
    "response": null,
    "queue": null,
    "queued_at": null,
    "tries": 0,
    "sent_at": null,
    "comment": null,
    "active": 1,
    "deleted": 0,
    "created_at": "'.now().'",
    "created_by": 2,
    "updated_at": "'.now().'",
    "type": "system",
    "manual": 0
  }
]';
        return json_decode($data, true);
    }

    public static function statsDaily(): array
    {
        $data = '[
  {
    "stats_daily_id": 1,
    "created_at": "'.now().'",
    "updated_at": "'.now().'",
    "state_date": "'.now()->toDateString().'",
    "office_id": 33,
    "administrator_id": 2,
    "processed_sale_tasks_count": null,
    "processing_sale_attempt_time_total": null,
    "processed_approve_attempt_count": 1,
    "processed_approve_attempt_time_total": 385571,
    "kept_promises_count": null,
    "promises_count": null,
    "payments_count": null,
    "processed_tasks_count": null,
    "processing_sale_attempt_time_max": null,
    "processing_sale_attempt_time_min": null,
    "processing_sale_attempt_time_avg": null,
    "processed_approve_attempt_time_max": 385571,
    "processed_approve_attempt_time_min": 385571,
    "processed_approve_attempt_time_avg": 385571
  }
]';
        return json_decode($data, true);
    }


}
