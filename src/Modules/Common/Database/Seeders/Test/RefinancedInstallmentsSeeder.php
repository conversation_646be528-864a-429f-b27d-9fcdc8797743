<?php

namespace Modules\Common\Database\Seeders\Test;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Installment;

class RefinancedInstallmentsSeeder extends Seeder
{
    use WithoutModelEvents;

    const FIRST_DUE_DATE = '2022-11-09';

    public function run(): void
    {
        $this->call([
            RefinancedLoanSeeder::class,
        ]);
        DB::table('installment')->insert([
            [
                'client_id' => 1,
                'loan_id' => RefinancedLoanSeeder::LOAN_ID,
                'seq_num' => 1,
                'due_date'=> self::FIRST_DUE_DATE,
                'accrued_total_amount' => 1000.00,
                'total_amount' => 1016.60,
                'rest_principal' => 1000.00,
                'principal' => 1000.00,
                'paid_principal' => 0.00,
                'accrued_interest' => 0.00,
                'interest' => 60,
                'late_interest' => 0.00,
                'paid_accrued_interest' => 0.00,
                'paid_interest' => 0.00,
                'rest_interest' => 60,
                'paid_late_interest' => 0.00,
                'accrued_penalty' => 0.00,
                'penalty' => 90,
                'late_penalty' => 0.00,
                'paid_accrued_penalty' => 0.00,
                'paid_penalty' => 0.00,
                'rest_penalty' => 90,
                'paid_late_penalty' => 0.00,
                'overdue_days' => 0,
                'overdue_amount' => 0.00,
                'max_overdue_days' => 0,
                'max_overdue_amount' => 0.00,
                'status' => Installment::INSTALLMENT_STATUS_LATE,
                'paid' => 0,
                'active' => 1,
                'created_at' => '2022-10-10 11:24:26'
            ],
            [
                'client_id' => 1,
                'loan_id' => RefinancedLoanSeeder::LOAN_ID,
                'seq_num' => 2,
                'due_date'=> '2022-11-09 00:00:00',
                'accrued_total_amount' => 1000.00,
                'total_amount' => 1016.60,
                'rest_principal' => 1000.00,
                'principal' => 1000.00,
                'paid_principal' => 0.00,
                'accrued_interest' => 0.00,
                'interest' => 60,
                'late_interest' => 0.00,
                'paid_accrued_interest' => 0.00,
                'paid_interest' => 0.00,
                'rest_interest' => 60,
                'paid_late_interest' => 0.00,
                'accrued_penalty' => 0.00,
                'penalty' => 90,
                'late_penalty' => 0.00,
                'paid_accrued_penalty' => 0.00,
                'paid_penalty' => 0.00,
                'rest_penalty' => 90,
                'paid_late_penalty' => 0.00,
                'overdue_days' => 0,
                'overdue_amount' => 0.00,
                'max_overdue_days' => 0,
                'max_overdue_amount' => 0.00,
                'status' => Installment::INSTALLMENT_STATUS_SCHEDULED,
                'paid' => 0,
                'active' => 1,
                'created_at' => '2022-11-10 11:24:26'
            ]
        ]);

    }
}