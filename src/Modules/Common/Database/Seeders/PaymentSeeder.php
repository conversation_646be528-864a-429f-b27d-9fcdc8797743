<?php

namespace Modules\Common\Database\Seeders;

use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Modules\Common\Enums\Payment\PaymentSourceEnum;
use Modules\Common\Enums\PaymentStatusEnum;
use Modules\Common\Models\Client;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Payment;

class PaymentSeeder extends Seeder
{
    public function run()
    {
        Payment::unsetEventDispatcher();
        $loans = Loan::all();
        $loans->each(function (Loan $loan) {
            $faker = Faker::create();

            $amountReceived = $faker->numberBetween(20000, 100000);
            $amountResto = $faker->numberBetween(0, 5);
            $client = $loan->client;

            Payment::factory()->create([
                'client_id' => $client->getKey(),
                'loan_id' => $loan->getKey(),
                'installment_id' => $loan->installments()->first()->getKey(),
                'office_id' => $faker->numberBetween(1, 5),
                'currency_id' => $faker->numberBetween(1, 3),
                'status' => $faker->randomElement(
                    [
                        PaymentStatusEnum::DELIVERED,
                        PaymentStatusEnum::EASY_PAY_SENDING_FAILED,
                        PaymentStatusEnum::NEW,
                    ]
                ),
                'payment_method_id' => $faker->numberBetween(1, 2),
                'direction' => $faker->randomElement(['in', 'out']),
                'source' => $faker->randomElement(
                    [
                        PaymentSourceEnum::FILE_IMPORT,
                        PaymentSourceEnum::CASH_DESK,
//                        PaymentSourceEnum::LOAN_REFINANCE,
//                        PaymentSourceEnum::UNKNOWN,
                        PaymentSourceEnum::EASY_PAY_API,
                    ]
                ),
                'amount_received' => $amountReceived,
                'amount_resto' => $amountResto,
                'amount' => ($amountReceived - $amountResto),
                'description' => $faker->paragraph($nbSentences = 1, $variableNbSentences = true),
                'manual' => $faker->numberBetween(0, 1),
                'correction' => 1,
                'created_at' => now(),
                'created_by' => 1,
            ]);
        });
    }
}
