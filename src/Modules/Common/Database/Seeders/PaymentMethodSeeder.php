<?php

namespace Modules\Common\Database\Seeders;

use Faker\Factory as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\PaymentMethod;

class PaymentMethodSeeder extends Seeder
{
    public function run()
    {
        $faker = Faker::create();

        $paymentMethods = [
            [
                'payment_method_id' => PaymentMethod::PAYMENT_METHOD_BANK,
                'name' => PaymentMethod::PAYMENT_METHOD_BANK_DESC,
                'description' => $faker->paragraph($nbSentences = 2, $variableNbSentences = true),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'payment_method_id' => PaymentMethod::PAYMENT_METHOD_EASYPAY,
                'name' => PaymentMethod::PAYMENT_METHOD_EASYPAY_DESC,
                'description' => $faker->paragraph($nbSentences = 2, $variableNbSentences = true),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'payment_method_id' => PaymentMethod::PAYMENT_METHOD_CASH,
                'name' => PaymentMethod::PAYMENT_METHOD_CASH_DESC,
                'description' => $faker->paragraph($nbSentences = 2, $variableNbSentences = true),
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ],
            [
                'payment_method_id' => PaymentMethod::PAYMENT_METHOD_OFFSET,
                'name' => PaymentMethod::PAYMENT_METHOD_OFFSET_DESC,
                'description' => 'Usage only for refinanced/refinancing loans',
                'created_at' => now(),
                'created_by' => Administrator::SYSTEM_ADMINISTRATOR_ID,
            ]
        ];

        DB::table('payment_method')->insert($paymentMethods);

        DB::statement("SELECT setval('payment_method_payment_method_id_seq', (SELECT MAX(payment_method_id) FROM payment_method)+1);");
    }
}
