<?php

namespace Modules\Common\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Client;

class ClientFactory extends Factory
{
    protected $model = Client::class;

    public function definition(): array
    {
        $first_name = $this->faker->firstName();
        $middle_name = $this->faker->lastName();
        $last_name = $this->faker->lastName();

        return [
            'pin' => $this->faker->unique()->randomElement(ClientIdCardFactory::REAL_PIN_LIST),
            'idcard_number' => $this->faker->numerify('#########'),
            'deleted' => 0,
            'enabled_by' => Administrator::factory(),
            'active' => 0,
            'disabled_by' => Administrator::factory(),
            'enabled_at' => Carbon::now(),

            'first_name' => $first_name,
            'middle_name' => $middle_name,
            'last_name' => $last_name,

            'first_name_latin' => $this->transliterate($first_name),
            'middle_name_latin' => $this->transliterate($middle_name),
            'last_name_latin' => $this->transliterate($last_name),

            'email' => $this->faker->unique()->email,

            'updated_by' => Administrator::query()->inRandomOrder()->first()->getKey(),
            'deleted_by' => Administrator::query()->inRandomOrder()->first()->getKey(),
        ];
    }

    public function transliterate(string $cyrillic): string
    {
        $cyr = [
            'Љ',
            'Њ',
            'Џ',
            'џ',
            'ш',
            'ђ',
            'ч',
            'ћ',
            'ж',
            'љ',
            'њ',
            'Ш',
            'Ђ',
            'Ч',
            'Ћ',
            'Ж',
            'Ц',
            'ц',
            'а',
            'б',
            'в',
            'г',
            'д',
            'е',
            'ё',
            'ж',
            'з',
            'и',
            'й',
            'к',
            'л',
            'м',
            'н',
            'о',
            'п',
            'р',
            'с',
            'т',
            'у',
            'ф',
            'х',
            'ц',
            'ч',
            'ш',
            'щ',
            'ъ',
            'ы',
            'ь',
            'э',
            'ю',
            'я',
            'А',
            'Б',
            'В',
            'Г',
            'Д',
            'Е',
            'Ё',
            'Ж',
            'З',
            'И',
            'Й',
            'К',
            'Л',
            'М',
            'Н',
            'О',
            'П',
            'Р',
            'С',
            'Т',
            'У',
            'Ф',
            'Х',
            'Ц',
            'Ч',
            'Ш',
            'Щ',
            'Ъ',
            'Ы',
            'Ь',
            'Э',
            'Ю',
            'Я'
        ];
        $lat = [
            'Lj',
            'Nj',
            'Dž',
            'dž',
            'š',
            'đ',
            'č',
            'ć',
            'ž',
            'lj',
            'nj',
            'Š',
            'Đ',
            'Č',
            'Ć',
            'Ž',
            'C',
            'c',
            'a',
            'b',
            'v',
            'g',
            'd',
            'e',
            'io',
            'zh',
            'z',
            'i',
            'y',
            'k',
            'l',
            'm',
            'n',
            'o',
            'p',
            'r',
            's',
            't',
            'u',
            'f',
            'h',
            'ts',
            'ch',
            'sh',
            'sht',
            'a',
            'i',
            'y',
            'e',
            'yu',
            'ya',
            'A',
            'B',
            'V',
            'G',
            'D',
            'E',
            'Io',
            'Zh',
            'Z',
            'I',
            'Y',
            'K',
            'L',
            'M',
            'N',
            'O',
            'P',
            'R',
            'S',
            'T',
            'U',
            'F',
            'H',
            'Ts',
            'Ch',
            'Sh',
            'Sht',
            'A',
            'I',
            'Y',
            'e',
            'Yu',
            'Ya'
        ];

        return str_replace($cyr, $lat, $cyrillic);
    }
}
