<?php

namespace Modules\Common\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ApproveDecision;
use Modules\Common\Models\ApproveDecisionReason;

class ApproveDecisionReasonFactory extends Factory
{
    protected $model = ApproveDecisionReason::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->word(),
//            'enabled_by' => $this->faker->randomNumber(),
//            'updated_at' => $this->faker->word(),
            'deleted' => $this->faker->boolean(),
            'active' => $this->faker->boolean(),
//            'disabled_by' => $this->faker->randomNumber(),
//            'enabled_at' => $this->faker->date(),
//            'disabled_at' => $this->faker->date(),
            'approve_decision_id'=>ApproveDecision::factory(),
            'updated_by' => Administrator::factory(),
            'deleted_by' => Administrator::factory(),
        ];
    }
}
