<?php

namespace Modules\Common\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\City;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientAddress;

class ClientAddressFactory extends Factory
{
    protected $model = ClientAddress::class;

    public function definition(): array
    {
        return [
            'city_id' => $this->faker->numberBetween(1, 300),
            'address' => str_replace("\n", ' ', $this->faker->address),
            'post_code' => $this->faker->numberBetween(1111, 9999),
//            'type' => $this->faker->word(),
//            'post_code' => $this->faker->word(),
//            'address' => $this->faker->address(),
//            'last' => $this->faker->randomNumber(),
//            'active' => $this->faker->boolean(),
//            'deleted' => $this->faker->boolean(),
//            'created_at' => Carbon::now(),
//            'updated_at' => Carbon::now(),
//            'enabled_at' => Carbon::now(),
//            'enabled_by' => $this->faker->randomNumber(),
//            'disabled_at' => Carbon::now(),
//            'disabled_by' => $this->faker->randomNumber(),
//            'municipality' => $this->faker->word(),
//            'district' => $this->faker->word(),
//            'location' => $this->faker->word(),
//            'building_number' => $this->faker->word(),
//            'building_entrance' => $this->faker->word(),
//            'building_floor' => $this->faker->word(),
//            'building_apartment' => $this->faker->word(),
//
//            'client_id' => Client::factory(),
//            'city_id' => City::factory(),
//            'created_by' => Administrator::factory(),
//            'updated_by' => Administrator::factory(),
//            'deleted_by' => Administrator::factory(),
        ];
    }
}
