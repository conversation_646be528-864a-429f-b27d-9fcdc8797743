<?php

namespace Modules\Common\Database\factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Currency;
use Modules\Common\Models\LoanStatus;

class LoanStatusFactory extends Factory
{
    protected $model = LoanStatus::class;

    public function definition(): array
    {
        return [
            'name'=> $this->faker->word,
        ];
    }
}
