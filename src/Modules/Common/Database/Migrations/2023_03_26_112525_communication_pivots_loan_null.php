<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('communication_pivots', function (Blueprint $table) {
            $table->integer('loan_id')->unsigned()->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('communication_pivots', function (Blueprint $table) {
            $table->integer('loan_id')->unsigned()->default(0)->change();
        });
    }
};
