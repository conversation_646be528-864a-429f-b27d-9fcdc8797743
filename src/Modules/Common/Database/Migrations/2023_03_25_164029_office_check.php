<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Database\Libraries\BaseMigration;

return new class extends BaseMigration {
    public function up(): void
    {
        Schema::create('office_check', function (Blueprint $table) {
            $table->id('office_check_id');

            $this->refBigInt($table, 'office_id', 'office', 'office_id');
            $table->string('service_key')->comment('service key. see CheckServiceEnum');

            $this->created($table);
            $this->updated($table);
            $this->deleted($table);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('office_check');
    }
};
