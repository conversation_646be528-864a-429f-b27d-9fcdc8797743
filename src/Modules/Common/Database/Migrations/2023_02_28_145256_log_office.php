<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Database\Libraries\BaseMigration;

return new class extends BaseMigration {
    public function up()
    {
        Schema::create('changelog.office', function (Blueprint $table) {
            $table->uuid('changelog_office_id')->primary();

            $this->created($table);
            $this->deleted($table);
            $this->updated($table);
            $table->text('action');
            $table->text('message')->nullable();

            $this->refBigInt(
                $table,
                'office_id',
                'office',
                'office_id'
            )->nullable();

            $table->jsonb('from');
            $table->jsonb('to');
        });
    }

    public function down()
    {
        Schema::drop('changelog.office');
    }
};