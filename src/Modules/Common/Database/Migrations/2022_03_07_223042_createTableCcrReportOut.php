<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Models\CcrReportOut;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    private $table = 'ccr_report_out';

    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            $this->table,
            function (Blueprint $table) {
                $table->bigIncrements('report_id');
                $table->enum('type', CcrReportOut::getTypes())->nullable()->index();
                $table->text('details')->nullable();
                $table->text('data')->nullable();
                $table->integer('zip_file_id')->nullable()->index();
                $table->integer('csv_file_id')->nullable()->index();
                $table->tableCrudFields();

                $table->timestamp('download_zip_at', 0)->nullable();
                $table->bigInteger('download_zip_by')
                    ->unsigned()
                    ->nullable()
                    ->references('administrator_id')
                    ->on('administrator');

                $table->timestamp('download_csv_at', 0)->nullable();
                $table->bigInteger('download_csv_by')
                    ->unsigned()
                    ->nullable()
                    ->references('administrator_id')
                    ->on('administrator');
            }
        );
    }

    public function down()
    {
        Schema::dropIfExists($this->table);
    }
};
