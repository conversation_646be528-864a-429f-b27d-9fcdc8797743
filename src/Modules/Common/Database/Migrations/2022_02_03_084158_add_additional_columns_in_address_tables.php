<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Common\Models\ClientAddress;
use Modules\Common\Models\Guarant;
use Modules\Common\Models\GuarantAddress;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        foreach (
            [
                ClientAddress::getTableName(),
                GuarantAddress::getTableName(),
                Guarant::getTableName(),
            ]
            as $table) {
            Schema::table($table, function (Blueprint $table) {
                $table->string('municipality')->nullable();
                $table->string('district')->nullable();
                $table->string('location')->nullable();
                $table->string('building_number')->nullable();
                $table->string('building_entrance')->nullable();
                $table->string('building_floor')->nullable();
                $table->string('building_apartment')->nullable();
            });
        }
    }
};
