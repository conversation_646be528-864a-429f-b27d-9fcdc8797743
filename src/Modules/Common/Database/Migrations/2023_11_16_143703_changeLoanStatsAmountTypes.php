<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("
            ALTER TABLE loan_actual_stats ALTER COLUMN accrued_amount_late_interest TYPE numeric(11, 2);
        ");

        DB::statement("
            ALTER TABLE loan_stats_history ALTER COLUMN accrued_amount_late_interest TYPE numeric(11, 2);
        ");

        $columns = [
            'current_overdue_amount',
            'max_overdue_amount',
            'current_installment_amount',
            'repaid_amount_principal',
            'repaid_amount_interest',
            'repaid_amount_penalty',
            'repaid_amount_late_interest',
            'repaid_amount_late_penalty',
            'repaid_amount_taxes',
            'repaid_amount_total',
            'due_amount_total',
            'due_amount_total_interest',
            'due_amount_total_penalty',
            'due_amount_total_taxes',
            'due_amount_total_principal',
            'due_amount_total_late_interest',
            'due_amount_total_late_penalty',
            'outstanding_amount_principal',
            'outstanding_amount_interest',
            'outstanding_amount_penalty',
            'outstanding_amount_taxes',
            'outstanding_amount_late_interest',
            'outstanding_amount_late_penalty',
            'outstanding_amount_total',
            'outstanding_amount_total_no_taxes',
            'accrued_amount_principal',
            'accrued_amount_interest',
            'accrued_amount_penalty',
            'accrued_amount_late_interest',
            'accrued_amount_late_penalty',
            'accrued_amount_taxes',
            'accrued_amount_total',
        ];

        foreach ($columns as $column) {
            DB::statement("UPDATE loan_actual_stats SET $column = 0 WHERE $column IS NULL;");
            DB::statement("ALTER TABLE loan_actual_stats ALTER COLUMN $column SET DEFAULT 0;");
            DB::statement("ALTER TABLE loan_actual_stats ALTER COLUMN $column SET NOT NULL;");

            DB::statement("UPDATE loan_stats_history SET $column = 0 WHERE $column IS NULL;");
            DB::statement("ALTER TABLE loan_stats_history ALTER COLUMN $column SET DEFAULT 0;");
            DB::statement("ALTER TABLE loan_stats_history ALTER COLUMN $column SET NOT NULL;");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
