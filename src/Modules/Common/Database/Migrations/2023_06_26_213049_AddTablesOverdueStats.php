<?php

use Illuminate\Database\Migrations\Migration;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'client_overdue_stats',
            function ($table) {
                $table->bigIncrements('id');
                $table->integer('client_id')->unsigned()->nullable();
                $table->string('key')->index();
                $table->string('val')->nullable();
                $table->tableCrudFields();
            });

        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'loan_overdue_stats',
            function ($table) {
                $table->bigIncrements('id');
                $table->integer('client_id')->unsigned()->nullable();
                $table->integer('loan_id')->unsigned()->nullable();
                $table->string('key')->index();
                $table->string('val')->nullable();
                $table->tableCrudFields();
            });
    }
};
