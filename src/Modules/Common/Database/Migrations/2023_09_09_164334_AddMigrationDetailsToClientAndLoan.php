<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('client', function (Blueprint $table) {
            $table->string('migration_db')->nullable()->index();
            $table->integer('migration_provision_id')->nullable()->index(); // user_id
            $table->integer('migration_nefin_id')->nullable()->index(); // customer_id
        });

        Schema::table('loan', function (Blueprint $table) {
            $table->string('migration_db')->nullable()->index();
            $table->string('migration_provision_id')->nullable()->index();
            $table->string('migration_nefin_id')->nullable()->index();
        });

        Schema::table('installment', function (Blueprint $table) {
            $table->string('migration_db')->nullable()->index();
            $table->integer('migration_id')->nullable()->index();
        });

        Schema::table('tax', function (Blueprint $table) {
            $table->string('migration_db')->nullable()->index();
            $table->integer('migration_id')->nullable()->index();
        });

        Schema::table('consultant', function (Blueprint $table) {
            $table->string('migration_db')->nullable()->index();
            $table->integer('migration_id')->nullable()->index(); // users.id OR BizAdvisers.ADVISER_ID
        });

        Schema::table('administrator', function (Blueprint $table) {
            $table->string('migration_db')->nullable()->index();
            $table->integer('migration_id')->nullable()->index(); // users.id OR BizUsers.UserID
        });

        Schema::table('payment', function (Blueprint $table) {
            $table->string('migration_db')->nullable()->index();
            $table->integer('migration_id')->nullable()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
