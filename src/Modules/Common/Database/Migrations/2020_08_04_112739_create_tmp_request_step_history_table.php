<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomPivotSchemaBuilder(DB::getSchemaBuilder())->create(
            'tmp_request_step_history',
            function ($table) {
                $table->bigIncrements('tmp_request_step_history_id');
                $table->unsignedInteger('tmp_request_id')->index();
                $table->unsignedInteger('tmp_request_step_id')->index();
                $table->foreign('tmp_request_id')->references('tmp_request_id')->on('tmp_request')->onDelete('cascade');
                $table->foreign('tmp_request_step_id')->references('tmp_request_step_id')->on('tmp_request_step')->onDelete('cascade');

            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'tmp_request_step_history',
            function (Blueprint $table) {
                $table->dropForeign('tmp_request_step_history_tmp_request_id_foreign');
                $table->dropForeign('tmp_request_step_history_tmp_request_step_id_foreign');
            }
        );
        Schema::dropIfExists('tmp_request_step_history');
    }
};
