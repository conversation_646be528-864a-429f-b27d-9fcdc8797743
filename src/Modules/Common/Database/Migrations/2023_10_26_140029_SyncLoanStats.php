<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("ALTER TABLE public.loan_stats_history DROP COLUMN rate_interest;");
        DB::statement("ALTER TABLE public.loan_stats_history DROP COLUMN rate_penalty;");
        DB::statement("ALTER TABLE public.loan_stats_history ALTER COLUMN profit TYPE numeric(11,2) USING profit::numeric(11,2);");
        DB::statement("
            DO $$ 
            BEGIN 
                IF NOT EXISTS (
                    SELECT 1 
                    FROM information_schema.columns 
                    WHERE table_name='client_stats_history' 
                    AND column_name='credit_limit_updated_at'
                ) 
                THEN 
                    ALTER TABLE client_stats_history ADD COLUMN credit_limit_updated_at timestamp(0) NULL; 
                END IF; 
            END $$;
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
