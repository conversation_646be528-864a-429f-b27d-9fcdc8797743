<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Common\Models\Loan;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table(Loan::getTableName(), function (Blueprint $table) {
            $table->date('grace_until')->index()->nullable();
            $table->tinyInteger('grace_updated')->default(0)->index()->nullable();
        });
    }
};
