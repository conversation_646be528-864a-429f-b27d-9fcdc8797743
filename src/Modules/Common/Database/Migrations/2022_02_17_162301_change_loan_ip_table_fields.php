<?php

use Illuminate\Database\Migrations\Migration;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->table(
            'loan_ip',
            function ($table) {
            $table->bigIncrements('loan_ip_id');
            $table->dropColumn('client_ip_id');
            $table->dropColumn('last');
            $table->string('ip')->nullable();
            $table->string('browser')->nullable();
            $table->integer('loan_status_id')->unsigned()->index();
            $table->tableCrudFields();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
