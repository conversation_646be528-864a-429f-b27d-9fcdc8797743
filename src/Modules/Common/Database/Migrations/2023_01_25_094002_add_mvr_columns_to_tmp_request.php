<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tmp_request', function (Blueprint $table) {
            $table->string('first_name_latin', 255)->nullable();
            $table->string('middle_name_latin', 255)->nullable();
            $table->string('last_name_latin', 255)->nullable();
            $table->text('image')->nullable();
            $table->string('issue_by')->nullable();
            $table->integer('issue_by_city_id')->nullable();
            $table->string('issue_by_city_name', 255)->nullable();
            $table->string('city_name', 255)->nullable();
            $table->string('sex', 255)->nullable();
            $table->string('iban', 255)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tmp_request', function (Blueprint $table) {
            $table->dropColumn([
                'first_name_latin',
                'middle_name_latin',
                'last_name_latin',
                'image',
                'issue_by',
                'issue_by_city_id',
                'issue_by_city_name',
                'city_name',
                'sex',
                'iban',
            ]);
        });
    }
};
