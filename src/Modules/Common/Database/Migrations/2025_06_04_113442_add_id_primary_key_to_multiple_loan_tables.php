<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private array $tables = [
        'loan_bank_account',
        'loan_client_name',
        'loan_email',
        'loan_idcard',
        'loan_phone',
    ];

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        foreach ($this->tables as $tableName) {
            Schema::table($tableName, function (Blueprint $table) {
                // Adds an auto-incrementing BIGINT primary key column 'id'
                // and places it as the first column in the table.
                $table->id()->first();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        foreach ($this->tables as $tableName) {
            Schema::table($tableName, function (Blueprint $table) {
                // Drops the 'id' column that was added in the 'up' method.
                // This also removes the primary key constraint if 'id' was the sole primary key.
                $table->dropColumn('id');
            });
        }
    }
};
