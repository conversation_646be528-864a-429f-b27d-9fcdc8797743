<?php

use Illuminate\Database\Migrations\Migration;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->table(
            'product',
            function ($table) {
                $table->smallInteger('interest_term_active')->default(0);
                $table->smallInteger('interest_term_gpr')->default(0);
                $table->smallInteger('penalty_term_active')->default(0);
                $table->smallInteger('penalty_term_gpr')->default(0);
            });
    }
};
