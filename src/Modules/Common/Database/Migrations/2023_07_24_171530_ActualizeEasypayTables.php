<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('easypay_requests', function (Blueprint $table) {
            // change column type
            $table->decimal('amount', 11, 2)->change();
        });

        Schema::table('easypay_responses', function (Blueprint $table) {
            // change column type
            $table->decimal('amount', 11, 2)->change();
        });

        Schema::table('pay_checks', function (Blueprint $table) {
            // change column type
            $table->string('response_status', 255)->change();
            $table->text('response_data')->change();
        });

        Schema::table('pay_confirms', function (Blueprint $table) {
            // change column type
            $table->text('invoices')->change();
            $table->string('response_status', 5000)->change();
            $table->text('response_data')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
