<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('mvr_report_pivot', function (Blueprint $table) {
            $table->string('settlement_code')->nullable();
            $table->string('location_code')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('mvr_report_pivot', function (Blueprint $table) {
            $table->dropColumn(['settlement_code', 'location_code']);
        });
    }
};
