<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::table('loan', function (Blueprint $table) {
            $table->dateTime('activated_at')->nullable();
        });
        DB::statement('UPDATE loan SET activated_at = created_at;');
    }
};