<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loan_bucket_history', function (Blueprint $table) {
            // Add the archived_at column with a default value of NOW()
            $table->timestamp('archived_at')->default(now());

            // Add the archived_by column as an integer and make it nullable
            $table->integer('archived_by')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
