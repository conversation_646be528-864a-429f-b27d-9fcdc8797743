<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('utm_trackers', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->softDeletes();

            $table->bigInteger('tmp_request_id')->nullable()->index();
            $table->bigInteger('loan_id')->nullable()->index();

            $table->string('session_id')->index();
            $table->string('utm_id')->nullable()->index();
            $table->string('utm_source')->index();
            $table->string('utm_medium')->nullable()->index();
            $table->string('utm_campaign')->index();
            $table->string('utm_term')->nullable()->index();
            $table->string('utm_content')->nullable()->index();

            $table->string('ip')->index();
            $table->string('browser');
            $table->string('last_page_accessed');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('utm_trackers');
    }
};
