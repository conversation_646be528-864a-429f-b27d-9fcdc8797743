<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Models\LoanParamsHistory;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'loan_params_history',
            function ($table) {
                $table->bigIncrements('loan_params_history_id');
                $table->integer('loan_id')->unsigned()->index();
                $table->integer('tax_id')->unsigned()->index()->nullable();
                $table->decimal('amount_approved', 15, 2);
                $table->integer('period_approved')->unsigned()->nullable();
                $table->enum('direction', ['out', 'in'])->nullable();
                $table->foreign('loan_id')->references('loan_id')->on('loan');
                $table->foreign('tax_id')->references('tax_id')->on('tax');
                $table->tableCrudFields();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'loan_params_history',
            function (Blueprint $table) {
                $table->dropForeign('loan_params_history_loan_id_foreign');
                $table->dropForeign('loan_params_history_tax_id_foreign');
            }
        );

        Schema::dropIfExists('loan_params_history');
    }
};
