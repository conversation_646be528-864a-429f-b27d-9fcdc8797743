<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{

    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomSchemaBuilder(DB::getSchemaBuilder())->create(
            'guarant_address',
            function ($table) {
                $table->bigIncrements('guarant_address_id');
                $table->integer('guarant_id')->unsigned()->index();
                $table->integer('city_id')->unsigned()->nullable()->index();
                $table->string('post_code')->nullable();
                $table->string('address')->nullable();
                $table->foreign('guarant_id')->references('guarant_id')->on('guarant');
                $table->foreign('city_id')->references('city_id')->on('city');
                $table->tableCrudFields(true);
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            'guarant_address',
            function (Blueprint $table) {
                $table->dropForeign('guarant_address_guarant_id_foreign');
                $table->dropForeign('guarant_address_city_id_foreign');
            }
        );

        Schema::dropIfExists('guarant_address');
    }
};
