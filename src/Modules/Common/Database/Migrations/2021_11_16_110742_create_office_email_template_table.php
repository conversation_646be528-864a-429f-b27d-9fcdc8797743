<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Models\OfficeEmailTemplate;
use Modules\Common\Traits\CustomSchemaBuilderTrait;

return new class extends Migration
{
    use CustomSchemaBuilderTrait;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getCustomPivotSchemaBuilder(DB::getSchemaBuilder())->create(
            OfficeEmailTemplate::getTableName(),
            function ($table) {
                $table->bigIncrements('office_email_template_id');

                $table->integer('office_id')->unsigned()->index();
                $table->foreign('office_id')
                    ->references('office_id')
                    ->on('office')->onDelete('cascade');

                $table->integer('email_template_id')->unsigned()->index();
                $table->foreign('email_template_id')
                    ->references('email_template_id')
                    ->on('email_template')->onDelete('cascade');

                $table->tableCrudFields();
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table(
            OfficeEmailTemplate::getTableName(),
            function (Blueprint $table) {
                $table->dropForeign('office_email_template_email_template_id_foreign');
                $table->dropForeign('office_email_template_office_id_foreign');
            }
        );

        Schema::dropIfExists(OfficeEmailTemplate::getTableName());
    }
};
