<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\OfficeShift;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('easy_pay_attempt', function (Blueprint $table) {
            $table->bigIncrements('easy_pay_attempt_id');
            $table->bigInteger('payment_id')->unsigned()->nullable();
            $table->bigInteger('client_id')->unsigned()->nullable();
            $table->text('easy_pay_transaction_id');
            $table->timestamp('sent_at')->nullable();
            $table->text('pin');
            $table->integer('amount')->unsigned();
            $table->text('type');
            $table->timestamp('created_at');
            $table->bigInteger('created_by')->default(Administrator::DEFAULT_EASYPAY_USER_ID);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('easy_pay_attempt');
    }
};
