<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tmp_request_history', function (Blueprint $table) {
            $table->string('first_name_latin')->nullable();
            $table->string('middle_name_latin')->nullable();
            $table->string('last_name_latin')->nullable();
            $table->text('image')->nullable();
            $table->string('issue_by')->nullable();
            $table->unsignedBigInteger('issue_by_city_id')->nullable();
            $table->string('issue_by_city_name')->nullable();
            $table->string('city_name')->nullable();
            $table->string('sex')->nullable();
            $table->string('iban')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
