<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('refer_friend_stats', function (Blueprint $table) {
            $table->id('refer_friend_stats_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreignIdFor(\Modules\Common\Models\Client::class, 'client_id')
                ->constrained('client', 'client_id');

            $table->integer('count_referrals_total')->default(0);
            $table->integer('count_loans_total')->default(0);
            $table->integer('count_loans_approved_total')->default(0);
            $table->integer('count_loans_cancelled_total')->default(0);
            $table->decimal('total_approved_amount', 11, 2)->default(0);
            $table->decimal('total_return_amount', 11, 2)->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('refer_friend_stats');
    }
};
