<?php

namespace Modules\Common\Domain\Exceptions;

class ModelIsNotAnAggregateRoot extends DomainException
{
    public function __construct(string $class)
    {
        $this->baseMessage = sprintf('You have tried to call class %s that is not an aggregate root.
         If you really need to use this class directly, please make it implement AggregateRootInterface', $class);
        parent::__construct(get_defined_vars());
    }
}
