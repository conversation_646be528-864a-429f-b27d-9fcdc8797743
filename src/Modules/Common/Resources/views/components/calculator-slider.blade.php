@if($products)
    <div class="loan-slider">
        <ul class="nav nav-tabs nav-fill" id="officeProducts" role="tablist">
            @foreach($products as $productId => $productName)
                <li class="nav-item @if($loop->first) active @endif" role="presentation">
                    <button class="nav-link w-100"
                            data-toggle="tab" data-target="#slider-{{$productId}}"
                            type="button" role="tab" aria-controls="home" aria-selected="true"
                    >
                        {{ $productName }}
                    </button>
                </li>
            @endforeach
        </ul>

        <div class="tab-content mb-3" id="myTabContent">
            @foreach($products as $productId => $productName)
                <div class="tab-pane fade  @if($loop->first) active @endif"
                     id="slider-{{$productId}}" role="tabpanel"
                >
                    <single-product-slider
                        key="{{$productId}}"
                        productid="{{$productId}}"
                        loanid="{{$loanId}}"
                        client-pin="{{$clientPin}}"
                    />
                </div>
            @endforeach
        </div>
        <!-- End ./tab-content -->
    </div>
    <!-- End ./loan-slider -->
@else
    <div>
        <div class="alert alert-warning">
            No available products for this office
        </div>
    </div>
@endif

@if($refinancingLoans?->count())
    @php
        /**
        * @var \Modules\Common\Models\Loan $refinance
 * @var \Modules\Common\Models\Office $office
         */
    @endphp
            <!-- Container for showing ajax errors -->
    <div id="refinance-errors"></div>

    <h3>{{__('table.RefinanceLoans')}}</h3>
    <x-table table-id="refinance-loans">
        <x-slot:head>
            <tr>
                <th>{{__('#')}}</th>
                <th>{{__('table.Id')}}</th>
                <th>{{__('table.Pin')}}</th>
                <th>{{__('table.Amount')}}</th>
                <th>{{__('table.LastInstallmentDate')}}</th>
                <th>{{__('table.Status')}}</th>
                <th>{{__('table.OutstandingAmount')}}</th>
            </tr>
        </x-slot:head>
        @foreach($refinancingLoans as $refinance)
            @php
                $refLoanStatus = $refinance->loanStatus;
            @endphp
            <tr>
                <td class="text-center">
                    <input type="checkbox"
                           class="form-check remove-ref-loan"
                           value="{{$refinance->getKey()}}"
                            @checked(isset($refinancingLoanIds[$refinance->getKey()]))
                            @disabled($office?->isWeb())
                    />
                </td>
                <td>{{$refinance->getKey()}}</td>
                <td>{{$refinance->client->pin}}</td>
                <td>{{intToFloat($refinance->amount_approved)}}</td>
                <td>{{$refinance->getCredit()->installmentsCollection()->last()->dueDate}}</td>
                <td class="{{$refLoanStatus->getStatusCssClass()}}">
                    {{$refLoanStatus->label()}}
                </td>
                <td>{{intToFloat($refinance->getAmountForRefinance(1 == $refinance->office_id))}}</td>
            </tr>
        @endforeach
    </x-table>
@endif

@push('scripts')
    <script>
        window.loanId = '{{$loanId}}';
        window.officeId = '{{$officeId}}';
        window.fetchProductSettingsRoute = '{{route('common.slider.fetch-product-settings')}}';
        window.calculateLoanRoute = '{{route('common.slider.calculateLoan')}}';


        $(document).ready(function () {
            document
                .querySelectorAll('input.remove-ref-loan')
                .forEach(function (checkbox) {
                    checkbox.addEventListener('click', function () {
                        if (checkbox.checked) {
                            console.log('checked: ' + checkbox.value, $('input[name="refinanced_loans[]"][value="' + checkbox.value + '"]').length);
                            if ($('input[name="refinanced_loans[]"][value="' + checkbox.value + '"]').length === 0) {
                                document
                                    .querySelector('input[name="loan[office_id]"]')
                                    .insertAdjacentHTML('afterend', '<input type="hidden" name="refinanced_loans[]" value="' + checkbox.value + '"/>');
                            }
                        } else {
                            console.log('unChecked');
                            $('input[name="refinanced_loans[]"][value="' + checkbox.value + '"]').remove();
                        }
                    });
                });
        });
    </script>
@endpush
