<?php

namespace Modules\Common\Console;

use Modules\Common\Models\SmsLoginCode;

class WeeklyCleanerSmsLoginCodeCommand extends CommonCommand
{
    protected $name = 'script:weekly-cleaner-sms-login-code';
    protected $signature = 'script:weekly-cleaner-sms-login-code';
    protected $description = 'Clear sms_login_code table older than 7 days';

    public function handle(): void
    {
        $this->startLog($this->description);

        $affectedRows = SmsLoginCode::where('created_at', '<', now()->subDays(5))->forceDelete();

        $this->finishLog([
            'affectedRows: ' . $affectedRows,
        ]);
    }
}