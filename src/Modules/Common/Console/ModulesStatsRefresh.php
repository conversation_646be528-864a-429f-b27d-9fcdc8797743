<?php

namespace Modules\Common\Console;

use Illuminate\Support\Carbon;
use Modules\Common\Models\StatsDaily;
use Modules\Common\Statistics\Realtime\DailyApproveAttempt;
use Modules\Common\Statistics\Realtime\DailyKeptPromises;
use Modules\Common\Statistics\Realtime\DailyPayment;
use Modules\Common\Statistics\Realtime\DailyProcessedSaleTasks;
use Modules\Common\Statistics\Realtime\DailyProcessedTasks;
use Modules\Common\Statistics\Realtime\DailyProcessingSaleAttemptTime;
use Modules\Common\Statistics\Realtime\DailyPromises;
use Modules\Common\Statistics\Realtime\DTO\DailyApproveAttemptDTO;
use Modules\Common\Statistics\Realtime\DTO\DailyKeptPromisesDTO;
use Modules\Common\Statistics\Realtime\DTO\DailyPaymentDTO;
use Modules\Common\Statistics\Realtime\DTO\DailyProcessedSaleTasksDTO;
use Modules\Common\Statistics\Realtime\DTO\DailyProcessedTasksDTO;
use Modules\Common\Statistics\Realtime\DTO\DailyProcessingSaleAttemptTimeDTO;
use Modules\Common\Statistics\Realtime\DTO\DailyPromisesDTO;
use Symfony\Component\Console\Command\Command;

class ModulesStatsRefresh extends CommonCommand
{
    protected $name = 'script:refresh-module-stats';
    protected $signature = 'script:refresh-module-stats {--day=}';
    protected $description = 'Recalc daily statistics for Sales, Approve, Collect modules';

    public function handle(): int
    {
        $this->startLog();

        try {
            // Set period for re-calc
            $day = now();
            if ($this->option('day')) {
                $day = Carbon::parse($this->option('day'));
            }


            // Do refresh stats
            $done = $this->calcDay($day);


            $msg = 'Executed successfully';
            $this->finishLog([$msg, $this->executionTimeString()],$done, $done, $msg);
            return Command::SUCCESS;

        } catch (\Throwable $e) {
            $msg = 'Error: ' . $e->getMessage()
                . ', ' . $e->getFile()
                . ', ' . $e->getLine();

            $this->finishLog([$msg, $this->executionTimeString()],0, 0, $msg);
            return Command::FAILURE;
        }
    }

    public function calcDay(Carbon $day): bool
    {
        $defRow   = $this->defaultRow($day);
        $mainKeys = array_keys($defRow);


        $rows = StatsDaily::query()
            ->where('state_date', $day)
            ->get($mainKeys)
            ->mapWithKeys(function (StatsDaily $item) {

                $key = $this->makeKey(
                    $item->state_date,
                    $item->administrator_id,
                    $item->office_id
                );

                $val = $this->defaultRow(
                    $item->state_date,
                    $item->office_id,
                    $item->administrator_id
                );

                return [$key => $val];

            })->toArray();

        $rows = $this->dailyProcessedSaleTasks($day, $rows);
        $rows = $this->dailyProcessingSaleAttemptTime($day, $rows);
        $rows = $this->dailyApproveAttempt($day, $rows);
        $rows = $this->dailyKeptPromises($day, $rows);
        $rows = $this->dailyPromises($day, $rows);
        $rows = $this->dailyPayment($day, $rows);
        $rows = $this->dailyProcessedTasks($day, $rows);

        StatsDaily::query()->upsert($rows, ['state_date', 'office_id', 'administrator_id']);

        return true;
    }

    private function defaultRow(
        Carbon $date,
        int $officeId = 0,
        int $adminId = 0
    ): array {

        $dateString = $date->format('Y-m-d');

        return [
            'state_date' => $dateString,
            'office_id' => $officeId,
            'administrator_id' => $adminId,

            'processed_sale_tasks_count' => null,
            'processing_sale_attempt_time_total' => null,
            'processing_sale_attempt_time_max' => null,
            'processing_sale_attempt_time_min' => null,
            'processing_sale_attempt_time_avg' => null,

            'processed_approve_attempt_count' => null,
            'processed_approve_attempt_time_total' => null,
            'processed_approve_attempt_time_max' => null,
            'processed_approve_attempt_time_min' => null,
            'processed_approve_attempt_time_avg' => null,

            'kept_promises_count' => null,
            'promises_count' => null,
            'payments_count' => null,
            'processed_tasks_count' => null,
        ];
    }

    /**
     * @param Carbon $day
     * @param array $rows
     * @return array
     */
    public function dailyProcessedSaleTasks(Carbon $day, array $rows): array
    {
        $data = (new DailyProcessedSaleTasks())->getByOfficeAndAdmin($day);

        $data->each(function (DailyProcessedSaleTasksDTO $item) use (&$rows, $day) {

            // get prepared key
            $key = $this->initRowAndGetKey(
                $rows,
                $day,
                $item->office_id,
                $item->administrator_id
            );

            // by key update total
            $rows[$key]['processed_sale_tasks_count'] = $item->total;
        });

        return $rows;
    }

    /**
     * @param Carbon $day
     * @param array $rows
     * @return array
     */
    public function dailyProcessingSaleAttemptTime(Carbon $day, array $rows): array
    {
        $data = (new DailyProcessingSaleAttemptTime())->getByOfficeAndAdmin($day);

        $data->each(
            function (DailyProcessingSaleAttemptTimeDTO $item) use (&$rows, $day) {

                $key = $this->initRowAndGetKey(
                    $rows,
                    $day,
                    $item->office_id,
                    $item->administrator_id
                );

                $rows[$key]['processing_sale_attempt_time_total'] = $item->total;
                $rows[$key]['processing_sale_attempt_time_max'] = $item->ex_max;
                $rows[$key]['processing_sale_attempt_time_min'] = $item->ex_min;
                $rows[$key]['processing_sale_attempt_time_avg'] = $item->ex_avg;
            }
        );

        return $rows;
    }

    /**
     * @param Carbon $day
     * @param mixed $rows
     * @return array
     */
    public function dailyApproveAttempt(Carbon $day, mixed $rows): array
    {
        // $data = (new DailyApproveAttempt(...$this->getWorkTimeSettings()))->getByOfficeAndAdmin($day);
        $data = (new DailyApproveAttempt())->getByOfficeAndAdmin($day);

        $data->each(function (DailyApproveAttemptDTO $item) use (&$rows, $day) {

            $key = $this->initRowAndGetKey(
                $rows,
                $day,
                $item->office_id,
                $item->administrator_id
            );

            $rows[$key]['processed_approve_attempt_count'] = $item->total;
            $rows[$key]['processed_approve_attempt_time_total'] = $item->seconds_total;
            $rows[$key]['processed_approve_attempt_time_max'] = $item->seconds_max;
            $rows[$key]['processed_approve_attempt_time_min'] = $item->seconds_min;
            $rows[$key]['processed_approve_attempt_time_avg'] = $item->seconds_avg;
        });

        return $rows;
    }

    /**
     * @param Carbon $day
     * @param mixed $rows
     * @return array
     */
    public function dailyKeptPromises(Carbon $day, mixed $rows): array
    {
        $data = (new DailyKeptPromises())->getByOffice($day);
        $data->each(function (DailyKeptPromisesDTO $item) use (&$rows, $day) {
            $key = $this->initRowAndGetKey($rows, $day, $item->office_id);
            $rows[$key]['kept_promises_count'] = $item->total;
        });

        return $rows;
    }

    /**
     * @param Carbon $day
     * @param mixed $rows
     * @return array
     */
    public function dailyPromises(Carbon $day, mixed $rows): array
    {
        $data = (new DailyPromises())->getByOffice($day);
        $data->each(function (DailyPromisesDTO $item) use (&$rows, $day) {
            $key = $this->initRowAndGetKey($rows, $day, $item->office_id);
            $rows[$key]['promises_count'] = $item->total;
        });

        return $rows;
    }

    /**
     * @param Carbon $day
     * @param mixed $rows
     * @return array
     */
    public function dailyPayment(Carbon $day, mixed $rows): array
    {
        $data = (new DailyPayment())->getByOffice($day);
        $data->each(function (DailyPaymentDTO $item) use (&$rows, $day) {
            $key = $this->initRowAndGetKey($rows, $day, $item->office_id);
            $rows[$key]['payments_count'] = $item->total;
        });

        return $rows;
    }

    /**
     * @param Carbon $day
     * @param mixed $rows
     * @return mixed
     */
    public function dailyProcessedTasks(Carbon $day, mixed $rows): mixed
    {
        $data = (new DailyProcessedTasks())->getByOffice($day);
        $data->each(function (DailyProcessedTasksDTO $item) use (&$rows, $day) {
            $key = $this->initRowAndGetKey($rows, $day, $item->office_id);
            $rows[$key]['processed_tasks_count'] = $item->total;
        });

        return $rows;
    }

    // private function getWorkTimeSettings(): array
    // {
    //     return [
    //         'workDaysApproveSetting' => $this
    //             ->settingRepository
    //             ->getSetting(SettingsEnum::loan_department_work_days_approve)
    //             ->default_value,
    //         'workTimeFromApproveSetting' => $this
    //             ->settingRepository
    //             ->getSetting(SettingsEnum::loan_department_work_time_from_approve)
    //             ->default_value,
    //         'workTimeToApproveSetting' => $this
    //             ->settingRepository
    //             ->getSetting(SettingsEnum::loan_department_work_time_to_approve)
    //             ->default_value,
    //     ];
    // }

    private function initRowAndGetKey(&$rows, Carbon $date, int $officeId = 0, int $adminId = 0): string
    {
        $key = $this->makeKey($date, $adminId, $officeId);
        if (!isset($rows[$key])) {
            $rows[$key] = $this->defaultRow($date, $officeId, $adminId);
        }

        return $key;
    }

    private function makeKey(Carbon $date, int $adminId, int $officeId): string
    {
        $dateString = $date->format('Y-m-d');

        return $dateString . '_' . $officeId . '_' . $adminId;
    }
}
