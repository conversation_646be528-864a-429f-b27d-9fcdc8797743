<?php

namespace Modules\Common\Console\Migration;

ini_set('max_execution_time', 3000);
ini_set('memory_limit', '1024M');

use Illuminate\Support\Facades\DB;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Client;
use Modules\Common\Models\NotificationSetting;

class MigAddClientNotifSet extends CommonCommand
{
    protected $name = 'script:mig-client-notifications_set';
    protected $signature = 'script:mig-client-notifications_set {client_id?}';
    protected $description = 'Create client notifications settings if not exists';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $start = microtime(true);
        $this->log("--- START ---");
        $this->startLog($this->getClassName().'::'.__FUNCTION__);


        $clientId = (int) $this->argument('client_id');
        $msg = '';

        // // add notif settings for all clients
        // $res1 = $this->addNotifSettingsForClients($clientId);
        // $todo = $res1['todo'];
        // $done = $res1['done'];
        // $msg .= 'Total for adding: ' . $todo . ', added: ' . $done;

        // disable if has such setup in provision
        $res2 = $this->disableClientSettingsFromProvision($clientId);
        $todoDis = $res2['todo'];
        $doneDis = $res2['done'];
        $msg .= ', Total for disable: ' . $todoDis . ', disabled: ' . $doneDis;

        $this->finishLog([], $todo, $done, $msg);
        $this->log($msg);
        $this->log('Exec.time: ' . round((microtime(true) - $start), 2) . ' second(s)');
    }

    private function addNotifSettingsForClients($clientId): array
    {
        $types = ['system', 'information', 'marketing', 'sales', 'approve', 'collect'];
        $channels = ['call', 'sms', 'email', 'viber'];


        $todo = 0;
        $done = 0;

        if (!empty($clientId)) {
            $builder = DB::table('client')->where('client_id', $clientId);
        } else {
            $builder = DB::table('client as c')
                ->leftJoin('notification_setting as ns', 'ns.client_id', '=', 'c.client_id')
                ->where('ns.notification_setting_id', '=', null)
                ->where('c.client_id', '>', 0)
                ->orderBy('c.client_id')
                ->select('c.*');
        }

        $builder->chunkById(100, function ($clients) use ($types, $channels, &$todo, &$done) {

            $todo += $clients->count();

            foreach ($clients as $client) {

                // Fetch existing settings for the current client to avoid duplicates
                $existingSettings = DB::table('notification_setting')
                    ->where('client_id', $client->client_id)
                    ->pluck('value', DB::raw("CONCAT(type, '_', channel) as type_channel"))
                    ->all();

                $bulkInsertData = [];

                foreach ($types as $type) {
                    foreach ($channels as $channel) {
                        $typeChannelKey = $type . '_' . $channel;

                        // Only add to bulk insert if the setting doesn't exist
                        if (!array_key_exists($typeChannelKey, $existingSettings)) {
                            $bulkInsertData[] = [
                                'client_id' => $client->client_id,
                                'type' => $type,
                                'channel' => $channel,
                                'value' => 1,
                            ];
                        }
                    }
                }

                // Perform bulk insert if there are new settings to add
                if (!empty($bulkInsertData)) {
                    DB::table('notification_setting')->insert($bulkInsertData);
                    dump("Inserted " . count($bulkInsertData) . " settings for client {$client->client_id}");

                    $done++;
                }
            }
        }, 'c.client_id', 'client_id');

        return [
            'todo' => $todo,
            'done' => $done,
        ];
    }

    private function disableClientSettingsFromProvision($clientId): array
    {
        $provisionClientId = null;
        if (!empty($clientId)) {
            $client = DB::table('client')->where('client_id', $clientId)->first();
            if (!empty($client->migration_provision_id)) {
                $provisionClientId = $client->migration_provision_id;
            }
        }

        $builder = DB::connection('provision2')
            ->table('users')
            ->select('id', 'egn', 'notification_phone_call', 'notification_sms', 'notification_email')
            ->whereNotNull('egn')
            ->where(function($query) {
                $query->where('notification_phone_call', '=', '0')
                      ->orWhere('notification_sms', '=', '0')
                      ->orWhere('notification_email', '=', '0');
            });

        if (!empty($provisionClientId)) {
            $builder->where('id', $provisionClientId);
        }

        $todo = 0;
        $done = 0;
        $fail = [];
        $builder
            ->orderBy('id', 'ASC')
            ->chunkById(200, function ($rows) use(&$todo, &$done, &$fail) {

                $todo += $rows->count();
                foreach ($rows as $row) {

                    // search client in our DB
                    $client = Client::where('migration_provision_id', $row->id)
                        ->where('migration_db', 'provision')
                        ->first();

                    if (empty($client->client_id)) {
                        $fail[] = 'Unexisting pru#' . $row->id . ' - ' . $row->egn;
                        continue;
                    }

                    // get his notification setting
                    foreach (['notification_phone_call', 'notification_sms', 'notification_email'] as $channel) {

                        if ($row->$channel != 0) {
                            continue;
                        }

                        if ($channel == 'notification_phone_call') {
                            $nfst = NotificationSetting::where('client_id', $client->client_id)
                                ->where('channel', 'call')
                                ->where('type', 'marketing')
                                ->where('value', 1)
                                ->first();

                            if (empty($nfst->notification_setting_id)) {
                                $fail[] = 'Client #' . $client->client_id . ' -  has no setting: call';
                                continue;
                            }

                            $nfst->value = 0;
                            $nfst->save();
                        }

                        if ($channel == 'notification_email') {
                            $nfst = NotificationSetting::where('client_id', $client->client_id)
                                ->where('channel', 'email')
                                ->where('type', 'marketing')
                                ->where('value', 1)
                                ->first();

                            if (empty($nfst->notification_setting_id)) {
                                $fail[] = 'Client #' . $client->client_id . ' -  has no setting: email';
                                continue;
                            }

                            $nfst->value = 0;
                            $nfst->save();
                        }

                        if ($channel == 'notification_sms') {
                            $nfst1 = NotificationSetting::where('client_id', $client->client_id)
                                ->where('channel', 'sms')
                                ->where('type', 'marketing')
                                ->where('value', 1)
                                ->first();

                            $nfst2 = NotificationSetting::where('client_id', $client->client_id)
                                ->where('channel', 'viber')
                                ->where('type', 'marketing')
                                ->where('value', 1)
                                ->first();

                            if (empty($nfst1->notification_setting_id) && empty($nfst2->notification_setting_id)) {
                                $fail[] = 'Client #' . $client->client_id . ' -  has no setting: sms/viber';
                                continue;
                            }

                            if (!empty($nfst1->notification_setting_id)) {
                                $nfst1->value = 0;
                                $nfst1->save();
                            }
                            if (!empty($nfst2->notification_setting_id)) {
                                $nfst2->value = 0;
                                $nfst2->save();
                            }
                        }

                    }

                    $done++;
                }

            }, 'users.id', 'id');


        dump('------------------------------');
        dump('Failed count = ' . count($fail));
        if (!empty($fail)) {
            foreach ($fail as $failR) {
                dump($failR);
            }
        }


        return [
            'todo' => $todo,
            'done' => $done,
        ];
    }
}
