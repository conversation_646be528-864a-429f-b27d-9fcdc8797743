<?php

namespace Modules\Common\Console\Migration;

ini_set('max_execution_time', 5000);
ini_set('memory_limit', '1824M');

use Mockery\Exception;
use Modules\Common\Console\CommonCommand;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Loan;

class MigConsultantFIx extends CommonCommand
{
    use MigrationTrait;

    private string $migLogDbName = '';

    protected $name = 'script:mig-consultant-fix';
    protected $signature = 'script:mig-consultant-fix {office_db} {loan_id?}'; // nefin_office1, nefin_office2, nefin_online
    protected $description = 'Set proper consultant, creator, updater for loans';

    public function __construct()
    {
        parent::__construct();
    }

    private static function validateConnection(): void
    {
        $connectionParams = [
            'NEFIN_MS_URL',
            'NEFIN_MS_TOKEN',
        ];

        foreach ($connectionParams as $paramName) {
            if (empty(env($paramName))) {
                dd('Missing db connection: ' . $paramName);
            }
        }
    }

    public function handle(): void
    {
        $this->validateConnection();

        $this->log("--- START ---");
        $start = microtime(true);
        $this->startLog($this->getClassName().'::'.__FUNCTION__);


        $db = $this->argument('office_db');
        if (empty($db)) {
            dd('No db param provided');
        }
        $this->migLogDbName = $db;

        $loanId = (int) $this->argument('loan_id');
        if ($loanId) {
            $this->loanId = $loanId;
        }


        $todo = [];
        $done = [];
        if ($loanId) {

            $todo = [$loanId];

            $loan = Loan::where('loan_id', $loanId)
                ->where('migration_db', $this->migLogDbName)
                ->first();

            if ($this->proceedLoan($loan)) {
                $done = [$loanId];
            }

        } else {

            Loan::where('migration_db', $this->migLogDbName)
                ->whereNull('consultant_id')
                ->orderBy('loan_id')
                ->chunkById(
                    200,
                    function ($loans) use(&$todo, &$done) {
                        foreach ($loans as $loan) {
                            $todo = [$loan->loan_id];

                            if ($this->proceedLoan($loan)) {
                                $done = [$loan->loan_id];
                            }
                        }
                        dump('done 200#');
                    }
                );
        }

        $msg = 'Processed: ' . count($done) . ', Total: ' . count($todo);
        $this->finishLog([], count($todo), count($done), $msg);
        $this->log($msg);
        $this->log('Exec.time: ' . round((microtime(true) - $start), 2) . ' second(s)');
    }

    private function proceedLoan(Loan $loan)
    {
        // get proper nefin credit and office name
        $credit = $this->getNefinCredit($loan);


        // creator
        $createdBy = Administrator::SYSTEM_ADMINISTRATOR_ID;
        $crDataNefin = $this->getNefinCreatorData($credit, $this->migLogDbName);
        if (!empty($crDataNefin)) {
            $admin = $this->getOrCreateAdmin($crDataNefin, $loan->office_id);
            $createdBy = $admin->administrator_id;
        }


        // updater
        $lastStatusAdminId = null;
        $modData = $this->getNefinModifierData($credit, $this->migLogDbName);
        if (!empty($modData)) {
            $admin = $this->getOrCreateAdmin($modData, $loan->office_id);
            $lastStatusAdminId = $admin->administrator_id;
        }


        // consultant
        $consultantId = null;
        $consultantDataNefin = $this->getNefinConsultantData($credit, $this->migLogDbName);
        if (!empty($consultantDataNefin)) {
            $cons = $this->getOrCreateConsultant($consultantDataNefin, $loan->office_id);
            $consultantId = $cons->consultant_id;
        }



        // set propert office
        $loan->created_by = $createdBy;
        $loan->last_status_update_administrator_id = $lastStatusAdminId;
        $loan->consultant_id = $consultantId;
        $loan->save();


        return true;
    }

    private function getNefinCredit(Loan $loan)
    {
        $query = "
            SELECT
                credit.*,
                office.OFFICE_NAME,
                y.UserID, y.FirstName, y.SurName, y.LastName, y.UserName, y.Password,
                z.ADVISER_NAME, z.PHONE,
                cr.UserID as CrUserID, cr.FirstName as CrFirstName, cr.SurName as CrSurName, cr.LastName as CrLastName, cr.UserName as CrUserName, cr.Password as CrPassword
            FROM BizCreditHeaders credit
            JOIN BizOffices office ON office.OFFICE_ID = credit.OFFICE_ID
            LEFT JOIN BizUsers y ON y.UserID = credit.MODIFIED_BY
            LEFT JOIN BizAdvisers z ON z.ADVISER_ID = credit.ADVISER_ID
            LEFT JOIN BizUsers cr ON cr.UserID = credit.CREATED_BY
            WHERE
                credit.CREDIT_ID = '" . (int) $loan->migration_nefin_id ."'
        ";
        $nfRes = $this->getNefinDataFromMS($query, $loan->migration_db);
        if (!empty($nfRes['error'])) {
            throw new Exception(__METHOD__ . '(): ' . $nfRes['error']);
        }

        return (object) $nfRes['data'][0];
    }
}
