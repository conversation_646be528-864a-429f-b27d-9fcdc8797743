<?php

namespace Modules\Common\Console;

use Carbon\Carbon;
use Modules\Common\Models\LoanActualStats;
use Modules\Common\Models\Office;
use Symfony\Component\Console\Command\Command;

class LoanStatsLocation extends CommonCommand
{
    const BEFORE_MIN_CREATED = 60*24; // 1 day
    const BEFORE_MIN_SIGNED = 60*24*3; // 3 days

    protected $name = 'script:loan-stats-location-update';
    protected $signature = 'script:loan-stats-location-update {loanId?}';
    protected $description = 'Update location for newly created loans and for signed loan';

    protected int $total;
    protected int $processed;

    public function handle(): int
    {
        $loanId = (int) $this->argument('loanId');

        $this->handleCreation($loanId);
        $this->handleSign($loanId);

        return Command::SUCCESS;
    }

    private function handleCreation(int $loanId = 0): bool
    {
        $this->startLog(
            $this->getClassName().'::'.__FUNCTION__,
            "--- START".$this->getClassName() . '_create'
        );

        if ($loanId) {
            $loansStatCollection = LoanActualStats::where('loan_id', $loanId)->get();
        } else {
            $timeRange = Carbon::now()->subMinutes(self::BEFORE_MIN_CREATED)->format('Y-m-d H:i:s');
            $loansStatCollection = LoanActualStats::whereRaw("
                    loan_id IN (
                        select l.loan_id
                        from loan l
                        where
                            l.created_at >= '" . $timeRange . "'
                            AND l.office_id = '" . Office::OFFICE_ID_WEB . "'
                    )
                ")
                ->whereNotNull('creation_ip')
                ->whereNull('creation_location')
                ->get();
        }

        $done = 0;
        $todo = $loansStatCollection->count();

        if (!$todo) {
            $msg = 'Processed loans for creation location: ' . $done . ', Total: ' . $todo;
            $logMessages = [$msg, $this->executionTimeString()];
            $this->finishLog($logMessages, $todo, $done, $msg);

            return Command::FAILURE;
        }


        foreach ($loansStatCollection as $loanStat) {

            $ip = $loanStat->creation_ip;

            // skip if nothing found by IP from tmp request
            $dataLocation = getLocationDataByIp($ip);
            if (empty($dataLocation['latitude']) || empty($dataLocation['longitude'])) {
                /** @phpstan-ignore-next-line  */
                dump('skip #' . $loanStat->loan_id . ' - no coords for IP: ' . $ip);
                continue;
            }

            $dataForUpdate = [
                'country'           => $dataLocation['country'] ?? null,
                'city'              => $dataLocation['city'] ?? null,
                'latitude'          => $dataLocation['latitude'],
                'longitude'         => $dataLocation['longitude'],
                'coords_updated_at' => (Carbon::now())->format('Y-m-d'),
            ];

            $loanStat->creation_location = json_encode($dataForUpdate);
            $loanStat->save();

            $done++;
        }


        $msg = 'Processed loans for creation location: ' . $done . ', Total: ' . $todo;
        $logMessages = [$msg, $this->executionTimeString()];
        $this->finishLog($logMessages, $todo, $done, $msg);

        return Command::SUCCESS;
    }

    private function handleSign(int $loanId = 0): bool
    {
        $this->startLog(
            $this->getClassName().'::'.__FUNCTION__,
            "--- START ".$this->getClassName() . '_sign'
        );


        if ($loanId) {
            $loansStatCollection = LoanActualStats::where('loan_id', $loanId)->get();
        } else {
            $timeRange = Carbon::now()->subMinutes(self::BEFORE_MIN_SIGNED)->format('Y-m-d H:i:s');
            $loansStatCollection = LoanActualStats::whereRaw("
                    loan_id IN (
                        select l.loan_id
                        from loan l
                        where
                            l.created_at >= '" . $timeRange . "'
                            AND l.office_id = '" . Office::OFFICE_ID_WEB . "'
                    )
                ")
                ->whereNotNull('sign_ip')
                ->whereNull('sign_location')
                ->get();
        }

        $done = 0;
        $todo = $loansStatCollection->count();

        if (!$todo) {
            $msg = 'Processed loans for sign location: ' . $done . ', Total: ' . $todo;
            $logMessages = [$msg, $this->executionTimeString()];
            $this->finishLog($logMessages, $todo, $done, $msg);

            return Command::FAILURE;
        }


        foreach ($loansStatCollection as $loanStats) {

            $ip = $loanStats->sign_ip;

            // skip if nothing found by IP from tmp request
            $dataLocation = getLocationDataByIp($ip);
            if (empty($dataLocation['latitude']) || empty($dataLocation['longitude'])) {
                /** @phpstan-ignore-next-line  */
                dump('skip #' . $loanStats->loan_id . ' - no coords for IP: ' . $ip);
                continue;
            }

            $dataForUpdate = [
                'country'           => $dataLocation['country'] ?? null,
                'city'              => $dataLocation['city'] ?? null,
                'latitude'          => $dataLocation['latitude'],
                'longitude'         => $dataLocation['longitude'],
                'coords_updated_at' => (Carbon::now())->format('Y-m-d'),
            ];

            $loanStats->sign_location = json_encode($dataForUpdate);
            $loanStats->save();

            $done++;
        }


        $msg = 'Processed loans for sign location: ' . $done . ', Total: ' . $todo;
        $logMessages = [$msg, $this->executionTimeString()];
        $this->finishLog($logMessages, $todo, $done, $msg);

        return Command::SUCCESS;
    }
}
