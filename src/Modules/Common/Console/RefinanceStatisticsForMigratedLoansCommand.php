<?php

namespace Modules\Common\Console;

use Modules\Common\Jobs\RelateRefinanceMigratedLoansJob;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;

class RefinanceStatisticsForMigratedLoansCommand extends CommonCommand
{
    protected $name = 'script:refinance-statistics-for-migrated-loans';

    protected $signature = 'script:refinance-statistics-for-migrated-loans {client_id?}';

    protected $description = 'Update migrated loans';


    public function handle()
    {
        $this->startLog();

        $clientId = $this->argument('client_id');

        if (!$clientId) {
            $clientIds = Loan::whereNotNull('migration_db')
                ->where('office_id', Office::OFFICE_ID_WEB)
                ->groupBy('client_id')
                ->pluck('client_id');
        } else {
            $clientIds = collect([$clientId]);
        }


        $delaySec = 5;
        $clientIds->chunk(200)->each(function ($chunk) use (&$delaySec) {
            RelateRefinanceMigratedLoansJob::dispatch($chunk)
                ->onQueue('commands')
                ->delay(now()->addSeconds($delaySec));

            $delaySec += 5;
        });


        $this->finishLog();
    }

}
