<?php

namespace Modules\Common\Console;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Modules\Common\Models\ConsultantStats;
use Modules\Common\Models\Installment;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanStatus;

/**
 * This command will be run always on 01.* for get all rest amount to current date
 * and create initial row for consultant stats by office id & consultant id
 */
class CreateConsultantStatsForMonth extends CommonCommand
{
    protected $name = 'script:create-consultant-stats-for-month';
    protected $signature = 'script:create-consultant-stats-for-month {for_month}';
    protected $description = 'Create consultant stats';

    public function handle(): int
    {
        $this->startLog();
        $processedRows = 0;

        $forMonth = $this->argument('for_month');
        if (!$forMonth) {
            throw new \Exception('Error for month is required argument');
        }

        $forMonth = Carbon::createFromFormat('m-Y', $forMonth);
        $fromDate = $forMonth->startOfMonth()->startOfDay()->toDateTimeString();
        $toDate = $forMonth->endOfMonth()->endOfDay()->toDateTimeString();

        /// get all loans for consultants
        $loans = Loan::query()
            ->select(['loan_id', 'office_id', 'consultant_id', 'created_at'])
            /// get all loans filtered by installments due date for provided month
            ->whereHas('installments', function (Builder $builder) use ($fromDate, $toDate) {
                $builder
                    ->where('paid', 0)
                    ->whereBetween('due_date', [$fromDate, $toDate]);
            })
            ->whereNotNull('consultant_id')
            ->where('loan_status_id', LoanStatus::ACTIVE_STATUS_ID)
            ->get()
            ->groupBy('consultant_id');

        /// exit if no available loans to processing
        if (!$loans->count()) {
            $this->finishLog([], 0, 0, 'No available rows to processing');

            return Command::SUCCESS;
        }


        foreach ($loans as $consultantId => $consultantLoans) {
            foreach ($consultantLoans->groupBy('office_id') as $officeId => $officeLoans) {
                $processedRows++;

                $officeLoanIds = $officeLoans->pluck('loan_id')->toArray();

                /// create consultant stats
                $consultantStats = ConsultantStats::firstOrCreate([
                    'for_month' => $forMonth->format('Y-m-01'),
                    'office_id' => $officeId,
                    'consultant_id' => $consultantId,
                ]);

                /// get all unpaid installments for this month
                $unpaidInstallments = Installment::selectRaw(
                    '
                        COALESCE(SUM(rest_principal),0) as total_rest_principal,
                        COALESCE(SUM(rest_interest),0) as total_rest_interest,
                        COALESCE(SUM(rest_penalty),0) as total_rest_penalty,
                        COALESCE(SUM(rest_late_interest),0) as total_rest_late_interest,
                        COALESCE(SUM(rest_late_penalty),0) as total_rest_late_penalty
                    '
                )
                    ->where('paid', 0)
                    ->whereIn('loan_id', $officeLoanIds)
                    ->whereRaw("to_char(due_date,'YYYY-MM-DD') like '{$forMonth->format('Y-m')}%'")
                    ->first();

                $restAmount = floatToInt(array_sum([
                    $unpaidInstallments->total_rest_principal,
                    $unpaidInstallments->total_rest_interest,
                    $unpaidInstallments->total_rest_penalty,
                    $unpaidInstallments->total_rest_late_interest,
                    $unpaidInstallments->total_rest_late_penalty,
                ]));

                $consultantStats->setAttribute('rest_amount', $restAmount);
                $consultantStats->saveQuietly();
            }
        }

        $this->finishLog([
            'Created or updated consultant stats: ' . $processedRows
        ]);

        return Command::SUCCESS;
    }
}