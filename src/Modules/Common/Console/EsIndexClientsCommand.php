<?php

declare(strict_types=1);

namespace Modules\Common\Console;

use Elastic\Elasticsearch\Client;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

final class EsIndexClientsCommand extends CommonCommand
{
    private const INDEX = 'clients';

    protected $name = 'script:index-clients';
    protected $description = 'Index clients to elasticsearch';

    public function __construct(private readonly Client $es)
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $this->startLog($this->name);

        if (!$this->es->indices()->exists(['index' => self::INDEX])->asBool()) {
            $this->createIndex();
        }

        $count = 0;
        $withErrors = false;

        DB::table('client', 'c')
            ->selectRaw(
                "CONCAT_WS(' ',c.first_name,c.middle_name,c.last_name) as name, c.phone, c.email, c.pin, c.client_id"
            )
            ->whereNull('c.deleted_at')
            ->orderBy('c.client_id')
            ->chunk(500, function (Collection $clients) use (&$count, &$withErrors) {
                $params = [];

                foreach ($clients as $client) {
                    $params['body'][] = [
                        'update' => [
                            '_index' => self::INDEX,
                            '_id' => $client->client_id,
                        ]
                    ];

                    $params['body'][] = [
                        'doc' => [
                            'name' => $client->name,
                            'phone' => $client->phone,
                            'email' => $client->email,
                            'pin' => $client->pin,
                        ],
                        "doc_as_upsert" => true,
                    ];

                    $count++;
                }

                $responses = $this->es->bulk($params);

                if (!$responses->asBool()) {
                    $withErrors = true;
                }
            }
        );

        $this->finishLog(["Processed: {$count} clients", 'Errors: ' . ($withErrors ? 'yes' : 'no')]);
    }

    private function createIndex(): void
    {
        $response = $this->es->indices()->create(['index' => self::INDEX, 'body' => [
            'settings' => ['number_of_replicas' => 0],
            'mappings' => ['properties' => [
                'name' => ['type' => 'search_as_you_type'],
                'phone' => ['type' => 'search_as_you_type'],
                'email' => ['type' => 'search_as_you_type'],
                'pin' => ['type' => 'search_as_you_type'],
            ]],
        ]]);

        if (!$response->asBool()) {
            throw new \RuntimeException('Failed to create index');
        }
    }
}
