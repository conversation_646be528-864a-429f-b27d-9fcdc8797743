<?php

namespace Modules\Common\ModelFilters\Base;

use Illuminate\Database\Eloquent\Builder;
use Modules\Common\ModelFilters\ModelFilterAbstract;
use Modules\Common\Models\Loan;

class OfficeIdFilter extends ModelFilterAbstract
{
    public function handle(mixed $filterValue): void
    {
        if (! is_array($filterValue)) {
            $filterValue = [$filterValue];
        }

        if ($this->query->getModel() instanceof Loan) {
            $this->query->whereIn('office_id', $filterValue);
        } else {
            $this->query->whereHas('office', function (Builder $builder) use ($filterValue) {
                $builder->whereIn('office_id', $filterValue);
            });
        }
    }
}