<?php

namespace Modules\Common\ModelFilters\TmpRequest;

use Carbon\Carbon;
use Modules\Common\ModelFilters\ModelFilterAbstract;

class CreatedAtFilter extends ModelFilterAbstract
{
    /**
     * @param string $filterValue
     * @return void
     */
    public function handle(mixed $filterValue): void
    {
        $dates = explode(' - ', $filterValue);

        if (count($dates) == 2) {
            $dateFrom = new Carbon($dates[0]);
            $dateTo = new Carbon($dates[1]);

            $this->query->whereBetween('created_at', [
                $dateFrom->startOfDay()->toDateTimeString(),
                $dateTo->endOfDay()->toDateTimeString()
            ]);
        }
    }
}
