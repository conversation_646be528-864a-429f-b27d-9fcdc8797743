<?php

namespace Modules\Common\ModelFilters\ApproveAgentStats;

use Carbon\Carbon;
use Modules\Common\ModelFilters\ModelFilterAbstract;

class LoanCreatedAtFilter extends ModelFilterAbstract
{
    /**
     * @param string $filterValue
     * @return void
     */
    public function handle(mixed $filterValue): void
    {
        $dates = explode(' - ', $filterValue);
        if (count($dates) == 2) {
            $dateFrom = new Carbon($dates[0]);
            $dateTo = new Carbon($dates[1]);

        } else  if (preg_match("/([1-2][0-9]{3})/i", $filterValue)) {
            $dateFrom = new Carbon($filterValue);
            $dateTo = new Carbon($filterValue);
        }
        if (!empty($dateFrom) && !empty($dateTo)) {
            $this->query->whereBetween('loan_created_at', [
                $dateFrom->startOfDay()->toDateTimeString(),
                $dateTo->endOfDay()->toDateTimeString()
            ]);
        }
    }
}