<?php

namespace Modules\Common\ModelFilters\UnclaimedMoney;

use Carbon\Carbon;
use Modules\Common\ModelFilters\ModelFilterAbstract;

class CreatedAtFilter extends ModelFilterAbstract
{

    public function handle(mixed $filterValue): void
    {
        $dates = explode(' - ', $filterValue);
        if (count($dates) == 2) {
            $dateFrom = new Carbon($dates[0]);
            $dateTo = new Carbon($dates[1]);
        } else  if (
            preg_match("/([1-2][0-9]{3})-([0-9]{2})-([0-9]{2})/i", $filterValue)
            || preg_match("/([0-9]{2})-([0-9]{2})-([1-2][0-9]{3})/i", $filterValue)
        ) {
            $dateFrom = new Carbon($filterValue);
            $dateTo = new Carbon($filterValue);
        }

        if (!empty($dateFrom) && !empty($dateTo)) {
            $this->query->whereBetween('unclaimed_money.created_at', [
                $dateFrom->startOfDay()->toDateTimeString(),
                $dateTo->endOfDay()->toDateTimeString()
            ]);
        }
    }
}
