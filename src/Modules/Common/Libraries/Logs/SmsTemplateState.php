<?php

namespace Modules\Common\Libraries\Logs;

use Modules\Common\Enums\LogActionEnum;
use Modules\Common\Models\Changelog\ChangelogSmsTemplate;
use Modules\Communication\Models\SmsTemplate;

class SmsTemplateState extends LogStateAbstract
{
    public function __construct(
        private readonly SmsTemplate $template,
        protected LogActionEnum $action,
        protected ?string $message = null,
    ) {
    }

    private function beforeSave()
    {
        if (in_array($this->action, [LogActionEnum::disable, LogActionEnum::enable, LogActionEnum::delete])) {
            return;
        }

        if ($this->action === LogActionEnum::create) {
            $this->stateFrom();

            return;
        }

        $this->template->refresh();
        $this->stateTo();
        $this->cleanFromTo();
    }

    public function save()
    {
        $this->beforeSave();

        if (in_array($this->action, [LogActionEnum::update, LogActionEnum::revert]) && !$this->from && !$this->to) {
            return;
        }

        $logTemplate = new ChangelogSmsTemplate();
        $this->fillStandardFields($logTemplate);
        $logTemplate->setTargetKey($this->template->getKey());
        $logTemplate->save();
    }

    public function prepare(): array
    {
        $data = $this->template->only($this->template->getFillable());
        $data['offices'] = $this->getOfficeIds();

        return $data;
    }

    private function getOfficeIds(): array
    {
        return $this->template->offices()->allRelatedIds()->toArray();
    }
}