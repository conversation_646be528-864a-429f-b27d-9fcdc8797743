<?php

namespace Modules\Common\Libraries\Menu\DTO;

use Spa<PERSON>\LaravelData\Attributes\DataCollectionOf;
use <PERSON>tie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

class MenuItem extends Data
{
    public function __construct(
        #[DataCollectionOf(MenuItem::class)]
        public ?DataCollection $items = null,
        public ?string $title = null,
        public ?string $url = null,
        public ?string $icon = null,
        public null|string|array $route = null,
    ) {
    }

    public function getUrl(): ?string
    {
        if (is_array($this->route)) {
            return route(...$this->route);
        }
        if (is_string($this->route)) {
            return route($this->route);
        }

        return $this->url ?: 'javascript:void(0)';
    }

    public function getItems(): ?DataCollection
    {
        if (!$this->items) {
            $this->items = new DataCollection(MenuItem::class, []);
        }

        return $this->items;
    }
}