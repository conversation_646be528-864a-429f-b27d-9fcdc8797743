<?php

namespace Modules\Common\Enums;

enum PaymentStatusEnum: string
{
    //Can become sent/failed or straight away delivered.
    // Always created in this status after loan approval.
    //ignored by accountancy
    case NEW = 'new';

    //Status for incoming payment received by us, but not delivered to specific loan
    case RECEIVED = 'received';

    //Final status, the only one accountancy should care about
    case DELIVERED = 'delivered';

    //Final status. Can be achieved only manually. Ignored by accountancy and payment team
    case CANCELED = 'canceled';

    //Final status. Can be achieved only by handling INCOMING payment task with canceling decision,
    //means we take the money, but did not attached it to any client and put them into unclaimed buffer
    case UNKNOWN = 'unknown';

    /******************EASY PAY *************************/
    //Can be manually revered to new
    //Failure to sent via EasyPay API
    //ignored by accountancy
    case EASY_PAY_SENDING_FAILED = 'failed';

    //Can become delivered or refunded
    //Sending attempt happens automatically after payment creation
    //from position of accountancy we don't have that money anymore even if not delivered
    case EASY_PAY_SENT = 'sent';

    //Final status
    case REFUNDED = 'refunded';


    // Important: only for outgoing
    public function stateMap(): array
    {
        //to what statuses can current status progress
        return match ($this) {
            self::NEW => [self::EASY_PAY_SENT, self::EASY_PAY_SENDING_FAILED, self::DELIVERED, self::CANCELED],
            self::EASY_PAY_SENT => [self::DELIVERED],
            self::EASY_PAY_SENDING_FAILED => [self::EASY_PAY_SENT, self::CANCELED]
        };
    }

    public function isFinal(): bool
    {
        return match ($this) {
            self::DELIVERED, self::CANCELED => true,
            default => false
        };
    }

    public function hasStatus(?array $status): bool
    {
        if (!is_array($status)) {
            $status = [$status];
        }
        return in_array($this, $status);
    }

    public static function toArray(): array
    {
        $out = [];
        foreach (self::cases() as $case) {
            $out[] = $case->value;
        }

        return $out;
    }

    public static function selectOptions(): array
    {
        $response = [];

        foreach (self::cases() as $case) {
            $response[$case->value] = $case->value; /// todo create labels
        }

        return $response;
    }
}
