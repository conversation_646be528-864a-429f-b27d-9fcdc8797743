<?php

namespace Modules\Common\Traits;

use Modules\Common\Models\Loan;
use Modules\Common\Models\A4EReport;
use Modules\Common\Models\CreditLimit;

trait AutoProcessVerification
{
    public function getAutoProcessValidationErrors(
        ?Loan $loan = null,
        ?A4EReport $a4eReport = null,
        ?CreditLimit $creditLimit = null,
        bool $lastAttempt = true
    ): array {

        if (empty($loan?->loan_id)) {
            return ['no mandatory params: loan_id',  'There are no mandatory params: loan_id', false];
        }

        if (!$loan->isOnlineLoan()) {
            return ['only for online loans', "Loan #{$loan->loan_id} is not online", false];
        }

        $autoProcessRow = $loan->getLastAutoProcessRow();
        if (!empty($autoProcessRow->auto_process_id)) {
            return ['has auto-process row', "Loan #{$loan->loan_id} has auto-process row", false];
        }

        $loan->refresh();
        if (!$loan->isSigned()) {
            return ['only for signed loans', "Loan #{$loan->loan_id} is not signed, status = " . $loan->loan_status_id, false];
        }

        if ($loan->skip_auto_process->isTrue()) {
            return ['skip_auto_process', "Loan #{$loan->loan_id} has skip_auto_process flag", false];
        }

        // here when we are on RetryAutoProcessJob
        // if no a4e_report:
        // - 1 attempt -> we wait + retry
        // - 2 attempt -> we wait + retry
        // - 3 continue without a4e_report, may be we can do auto-cancel
        if (!$lastAttempt && empty($a4eReport?->a4e_report_id)) {
            return ['no mandatory params: a4e_report',  'There are no mandatory params: a4e_report', true];
        }

        // here when we are on RetryAutoProcessJob
        // if no credit limit:
        // - 1 attempt -> we wait + retry
        // - 2 attempt -> we wait + retry
        // - 3 continue without credit limit, may be we can do auto-cancel
        if (!$lastAttempt && empty($creditLimit?->credit_limit_id)) {
            return ['no mandatory params: credit_limit',  'There are no mandatory params: credit_limit', true];
        }

        return [null, null, false];
    }
}
