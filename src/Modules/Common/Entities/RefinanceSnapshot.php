<?php

namespace Modules\Common\Entities;

use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperRefinanceSnapshot
 */
class RefinanceSnapshot extends Model
{
    const UPDATED_AT = null;

    protected $table = 'refinance_snapshot';
    protected $primaryKey = 'id';

    protected $fillable = [
        'refinancing_loan_id',
        'refinanced_loan_id',
        'loan',
        'installments',
        'taxes',
        'loan_stats',
        'client_stats',
        'created_at',
        'created_by',
        'reverted_at',
        'reverted_by',
    ];

    protected $casts = [
        'loan' => 'json',
        'taxes' => 'json',
        'loan_stats' => 'json',
        'client_stats' => 'json',
        'installments' => 'json',
    ];
}
