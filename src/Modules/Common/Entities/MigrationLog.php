<?php

namespace Modules\Common\Entities;

use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperMigrationLog
 */
class MigrationLog extends Model
{
    protected $table = 'migration_log';
    protected $primaryKey = 'id';

    protected $fillable = [
        'db_name', // db from where we migrate
        'pin',
        'client_other_id', // id of client from where we migrate
        'client_id',
        'client_handled', // 1,0
        'client_relations_handled', // 1,0
        'loans_handled', // 1,0
        'loans_todo', // json
        'loans_migrated', // json
        'started_at',
        'ended_at',
        'details', // json
        'nefin_credit_id',
        'provision_credit_id',
    ];

    protected $casts = [
        'loans_todo' => 'json',
        'loans_handled' => 'json',
        'details' => 'json'
    ];
}
