<?php

namespace Modules\Common\Entities;

use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperPortfolioSnapshot
 */
class PortfolioSnapshot extends Model
{
    const UPDATED_AT = null;

    protected $table = 'portfolio_snapshot';
    protected $primaryKey = 'id';

    protected $fillable = [
        'office_id',
        'office_name',
        'loan_id',
        'status',
        'loan_start_date',
        'overdue_days',
        'loan_amount',
        'rest_principal',
        'accrued_interest',
        'accrued_forfeit',
        'loan_end_date',
        'flag_judicial',
        'flag_sold',
        'flag_fraud',
        'snapshot_date',
    ];
}
