<?php

namespace Modules\Common\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\ClientCardBox;

/**
 * @mixin IdeHelperClientCardBoxAdminOrd
 */
class ClientCardBoxAdminOrd extends Model
{
    protected $fillable = [
        'client_card_group',
        'client_card_box_id',
        'administrator_id',
        'client_card_col',
        'ord',
    ];


    public function clientCardBox(): BelongsTo
    {
        return $this->belongsTo(ClientCardBox::class);
    }

    public function administrator(): BelongsTo
    {
        return $this->belongsTo(Administrator::class, 'administrator_id', 'administrator_id');
    }
}
