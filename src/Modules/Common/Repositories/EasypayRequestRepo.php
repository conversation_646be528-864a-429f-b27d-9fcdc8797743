<?php

namespace Modules\Common\Repositories;

use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Common\Models\EasypayRequest;

class EasypayRequestRepo extends BaseRepository
{
    public function __construct(
        private readonly EasypayRequest $easypayRequest
    )
    {
    }

    public function getByFilters(array $filters = [], int $perPage = 10): LengthAwarePaginator
    {
        return $this->easypayRequest->filterBy($filters)
            ->with(['loan', 'client'])
            ->orderBy($this->easypayRequest->getKeyName(), 'DESC')
            ->paginate($perPage);
    }
}

