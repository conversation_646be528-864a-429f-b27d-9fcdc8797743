<?php

declare(strict_types=1);

namespace Modules\Common\Services;

use InvalidArgumentException;
use Modules\Common\Enums\AddressTypeEnum;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientAddress;
use Illuminate\Support\Facades\DB;
use Modules\Head\Repositories\ClientAddressRepository;
use Modules\Head\Services\ClientService;
use Modules\Sales\Http\Dto\AddressDto;

final readonly class ClientAddressService
{
    public function __construct(
        private ClientAddressRepository $dbRepo,
        private ClientService $clientService,
    ) {
    }

    /**
     * Handles and saves registration and actual addresses for a client.
     * @param Client $client
     * @param AddressDto[] $addressDtos
     * @return ClientAddress[]
     * @throws \Throwable
     */
    public function handle(Client $client, array $addressDtos): array
    {
        if (empty($addressDtos)) {
            throw new InvalidArgumentException('No address data provided');
        }

        foreach ($addressDtos as $addressDto) {
            if (! ($addressDto instanceof AddressDto)) {
                throw new InvalidArgumentException('Invalid address data provided');
            }
        }

        $addresses = [];
        DB::transaction(function () use ($addressDtos, &$addresses, $client) {
            $registrationAddressDto = $addressDtos[0];
            $addresses[] = $this->processSingleAddressType($client, AddressTypeEnum::IdCard, $registrationAddressDto);

            $actualAddressDto = $addressDtos[1] ?? null;
            $dtoForActualProcessing = $this->isAddressEffectivelyEmpty($actualAddressDto)
                ? $registrationAddressDto // Use registration data if actual is empty (AddressDto)
                : $actualAddressDto; // AddressDto

            $addresses[] = $this->processSingleAddressType($client, AddressTypeEnum::Current, $dtoForActualProcessing);

            $this->updateClientAddressMatchStatus($client);
        });

        return $addresses;
    }

    /**
     * Checks if a normalized address data array is effectively empty (all values are null).
     */
    private function isAddressEffectivelyEmpty(?AddressDto $addressDto): bool
    {
        if ($addressDto === null) {
            return true;
        }

        // An address is considered empty if its textual representation is empty.
        return $addressDto->address === '';
    }

    /**
     * Processes a single address type (registration or actual) for a client.
     * Finds or creates the address and sets it as the last active one.
     */
    private function processSingleAddressType(Client $client, AddressTypeEnum $addressType, AddressDto $addressDto): ClientAddress
    {
        if (!$addressDto->post_code) {
            $addressDto->post_code = '0';
        }

        $addressDto->type = $addressType->value;

        // Use AddressDto directly for finding and creating records
        $existingAddress = $this->findExistingClientAddress($client, $addressDto);

        if (! $existingAddress) {
            // Mark the client for CCR sync if no existing address was found
            $this->clientService->markForCcrSync($client);
        }

        $targetAddress = $existingAddress ?? $this->createClientAddressRecord($client, $addressDto);

        $this->setClientAddressAsLast($client, $targetAddress);

        return $targetAddress;
    }

    /**
     * Finds an existing, non-deleted client address matching the provided data.
     */
    private function findExistingClientAddress(Client $client, AddressDto $addressDto): ?ClientAddress
    {
        return ClientAddress::query()
            ->where('client_id', $client->getKey())
            ->where('type', $addressDto->type)
            ->where('deleted', 0)
            ->where('city_id', $addressDto->city_id)
            ->where('address', $addressDto->address)
            ->where('post_code', $addressDto->post_code)
            ->first();
    }

    /**
     * Creates a new client address record.
     */
    private function createClientAddressRecord(Client $client, AddressDto $addressDto): ClientAddress
    {
        return $this->dbRepo->create([
            'city_id' => $addressDto->city_id,
            'address' => $addressDto->address,
            'post_code' => $addressDto->post_code,
            // Note: DTO's 'type' and 'address_is_match' are not used here;
            // ClientAddress 'type' is from $addressType Enum, and 'address_is_match' is handled separately.
            'client_id' => $client->getKey(),
            'type' => $addressDto->type,
            'last' => 1,
        ]);
    }

    /**
     * Sets the given address as the 'last' active one for its type,
     * ensuring other addresses of the same type are not marked 'last'.
     */
    private function setClientAddressAsLast(Client $client, ClientAddress $targetAddress): void
    {
        // Unset 'last' flag for other addresses of the same type
        ClientAddress::where('client_id', $client->getKey())
            ->where('type', $targetAddress->type)
            ->where('client_address_id', '!=', $targetAddress->client_address_id)
            ->where('deleted', 0)
            ->update(['last' => 0]);

        // Ensure the target address is marked as 'last' and 'active'
        if ($targetAddress->last !== 1 || $targetAddress->active !== 1) {
            $targetAddress->last = 1;
            $targetAddress->active = 1;
            $targetAddress->save();
        }
    }

    /**
     * Updates the 'address_is_match' status on the client's last addresses.
     */
    private function updateClientAddressMatchStatus(Client $client): void
    {
        $lastRegistrationAddress = $this->dbRepo->getLastActive($client, AddressTypeEnum::IdCard);
        $lastActualAddress = $this->dbRepo->getLastActive($client, AddressTypeEnum::Current);

        $matchStatus = 'no'; // Default to 'no'
        $idsToUpdate = [];
        if (
            $lastRegistrationAddress
            && $lastActualAddress
            && $this->areClientAddressesSemanticallyIdentical($lastRegistrationAddress, $lastActualAddress)
        ) {
            $matchStatus = 'yes';
        }

        if ($lastRegistrationAddress) {
            $idsToUpdate[] = $lastRegistrationAddress->client_address_id;
        }
        if ($lastActualAddress && $lastActualAddress->client_address_id !== $lastRegistrationAddress?->client_address_id) {
            // Add actual address ID only if it's different from registration to avoid duplicate update on same record
            $idsToUpdate[] = $lastActualAddress->client_address_id;
        }
        
        if (!empty($idsToUpdate)) {
            ClientAddress::whereIn('client_address_id', array_unique($idsToUpdate))
                ->update(['address_is_match' => $matchStatus]);
        }
    }

    /**
     * Compares two client addresses for semantic equality (same city, postcode, address string).
     */
    public function areClientAddressesSemanticallyIdentical(ClientAddress $addressOne, ClientAddress $addressTwo): bool
    {
        return $addressOne->city_id === $addressTwo->city_id &&
            $addressOne->post_code === $addressTwo->post_code &&
            $addressOne->address === $addressTwo->address;
    }
}
