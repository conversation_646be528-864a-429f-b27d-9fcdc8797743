<?php

namespace Modules\Common\Services\WherePipeline\Pipes;

use Closure;
use Modules\Common\Services\WherePipeline\BasePipeline;
use Modules\Common\Services\WherePipeline\DataWrapper;
use Modules\Common\Traits\DateBuilderTrait;

class WhereUpdatedAt extends BasePipeline
{
    use DateBuilderTrait;

    public function handle(DataWrapper $dataWrapper, Closure $next): DataWrapper
    {
        if (
            !empty($dataWrapper->getData()['updated_at'])
            && preg_match(self::$dateRangeRegex, $dataWrapper->getData()['updated_at'])
        ) {
            $where = $dataWrapper->getWhere();
            $extractedDates = $this->extractDates($dataWrapper->getData()['updated_at']);

            $where[] = [
                $this->getKeyName('updated_at', $dataWrapper->getPrefix()),
                '>=',
                $extractedDates['from'],
            ];
            $where[] = [
                $this->getKeyName('updated_at', $dataWrapper->getPrefix()),
                '<=',
                $extractedDates['to'],
            ];

            $dataWrapper->setWhere($where);
        }

        return $next($dataWrapper);
    }
}
