<?php

declare(strict_types=1);

namespace Modules\Common\Services;

use Modules\Common\Enums\AddressTypeEnum;
use Modules\Common\Models\Client;
use Modules\Common\Models\ClientAddress;
use Modules\Common\Models\Loan;
use Modules\Common\Models\LoanAddress;
use Modules\Common\Models\LoanStatus;
use Modules\Head\Repositories\ClientAddressRepository;
use RuntimeException;

final readonly class LoanAddressService
{
    /**
     * Updates or creates a LoanAddress link for a given loan and address type.
     * Sets the 'last' flag to 1 for the targetClientAddress and 0 for others of the same type for the loan.
     */
    public function updateOrCreateLoanAddressLink(
        Loan $loan,
        AddressTypeEnum $addressType,
        ClientAddress $targetClientAddress,
    ): ?LoanAddress {
        $loanId = $loan->getKey();
        $addressTypeValue = $addressType->value;

        // Set 'last' to 0 for all addresses of this type for the current loan.
        LoanAddress::where('loan_id', $loanId)
            ->where('type', $addressTypeValue)
            ->where('last', 1)
            ->update(['last' => 0]);

        // A target client address is provided.
        $targetClientAddressId = $targetClientAddress->client_address_id;

        // Find or create the link for the targetClientAddressId and make it the active and "last" one.
        return LoanAddress::updateOrCreate(
            [
                'loan_id' => $loanId,
                'type' => $addressTypeValue,
                'client_address_id' => $targetClientAddressId,
            ],
            [ // Values to set/update to
                'last' => 1, // This is now the last active address of this type for this loan
                // 'updated_by' => auth()->id(), // Optional: if tracking user who made change
            ]
        );
    }
}
