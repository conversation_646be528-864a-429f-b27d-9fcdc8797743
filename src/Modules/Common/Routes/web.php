<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Common\Http\Controllers\CitiesController;
use Modules\Common\Http\Controllers\ClientCardBoxesController;
use Modules\Common\Http\Controllers\CommonController;
use Modules\Common\Http\Controllers\SliderCalculatorController;
use Payments\Http\Controllers\PaymentMethodController;
use Modules\Common\Http\Controllers\AdministratorFilterController;

Route::prefix('common')->middleware(['auth'])->group(function () {
    /// AdministratorFilterController
    Route::post('/create-administrator-filter', [AdministratorFilterController::class, 'storeAdminFilter'])
        ->name('create-administrator-filter');

    Route::get('/{administratorFilter}/load-filter', [AdministratorFilterController::class, 'loadFilters'])
        ->name('load-filter');

    Route::get('/{administratorFilter}/delete-administrator-filter', [AdministratorFilterController::class, 'destroy'])
        ->name('delete-administrator-filter');

    /// SliderCalculatorController
    Route::get('/fetch-product-settings', [SliderCalculatorController::class, 'fetchProductSettings'])
        ->name('slider.fetch-product-settings')
        ->defaults('description', 'Fetch loan product settings')
        ->defaults('module_name', 'User Settings')
        ->defaults('controller_name', 'Loan Calculator')
        ->defaults('info_bubble', 'Взема настройки на избраният кредитен продукт');

    Route::get('/calculate-loan', [SliderCalculatorController::class, 'calculateLoan'])
        ->name('slider.calculateLoan')
        ->defaults('description', 'View loan calculator')
        ->defaults('module_name', 'User Settings')
        ->defaults('controller_name', 'Loan Calculator')
        ->defaults('info_bubble', 'Вижда кредитен калкулатор на стр. Нова заявка и в клиентска карта');


    /// CitiesController
    Route::get('/cities', [CitiesController::class, 'getCitiesByName'])
        ->name('loans.cities');

    Route::get('/city/{city}', [CitiesController::class, 'getCityById'])
        ->name('loans.city');

    Route::get('/cityName/{city}', [CitiesController::class, 'getCityName'])
        ->name('loans.getCityName');

    Route::get('/client-card-boxes', [ClientCardBoxesController::class, 'index'])
        ->name('client-card-boxes.index')
        ->defaults('description', 'View all client card boxes')
        ->defaults('module_name', 'Platform Settings')
        ->defaults('controller_name', 'Client Card Boxes')
        ->defaults('info_bubble', 'Може да вижда всички боксове в клиентска карта от страница Настройки');

    Route::get('/client-card-boxes/{client_card_box}/edit', [ClientCardBoxesController::class, 'edit'])
        ->name('client-card-boxes.edit')
        ->defaults('module_name', 'Platform Settings')
        ->defaults('controller_name', 'Client Card Boxes')
        ->defaults('description', 'Edit client card box');

    Route::post('/client-card-boxes/{client_card_box}', [ClientCardBoxesController::class, 'updateBox'])
        ->name('client-card-boxes.update-box')
        ->defaults('module_name', 'Platform Settings')
        ->defaults('controller_name', 'Client Card Boxes')
        ->defaults('description', 'Update client card box');

    Route::get('/client-card-boxes/save-box-order', [ClientCardBoxesController::class, 'saveBoxOrder'])
        ->name('client-card-boxes.save-box-order');
});


Route::middleware('throttle:3,1')->group(function () {
    Route::get('/sms-restart', [CommonController::class, 'smsRestart'])
        ->name('sms-restart.smsRestart');

    Route::post('/sms-restart', [CommonController::class, 'smsRestart'])
        ->name('sms.do.restart');
});
