<?php

namespace StikCredit\Calculators\PaymentDistribution;

class DefaultPaymentDistribution extends AbstractPaymentDistribution
{

    public function __construct(
        public int     $index,
        public int     $paymentId,
        public string  $identifier,
        public string  $originIdentifier,
        public string  $paymentDate,
        public string  $distributionDate,
        public int     $distributedAmount,
        public int     $distributedAmountBefore,
        public int     $distributedAmountAfter,
        public ?string $description = '',
        public ?string $hexColor = null,
        public ?int    $itemId = null,
    )
    {
    }

    public function build(array $args): DefaultPaymentDistribution
    {
        return $this;
    }

    public function isLate(): bool
    {
        return in_array($this->originIdentifier, ['outstandingLatePenaltyAmount', 'outstandingLateInterestAmount']);
    }

    public function getLabel(): string
    {
        $labels = [
            'outstandingLatePenaltyAmount' => '[НЛ] Неустойка за просрочие (Principal&Penalty)',
            'outstandingLateInterestAmount' => '[НЛ] Неустойка за просрочие (Interest)',
            'outstandingPenaltyAmount' => 'Редовно плащане на (Неустойка)',
            'outstandingInterestAmount' => '[РЛ] Редовно плащане на (Лихва)',
            'outstandingPrincipleAmount' => 'Редовно плащане на (Главница)',
            'payLoanFees' => 'Плащане на други разходи',
        ];

        return $labels[$this->originIdentifier] ?? "n/a";
    }
}
