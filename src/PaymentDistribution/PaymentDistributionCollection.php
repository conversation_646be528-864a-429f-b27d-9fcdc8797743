<?php

namespace StikCredit\Calculators\PaymentDistribution;

use Illuminate\Support\Collection;
use StikCredit\Calculators\Abstract\AbstractLoanCollection;
use StikCredit\Calculators\Installments\DefaultInstallment;
use StikCredit\Calculators\Installments\InstallmentsCollection;
use StikCredit\Calculators\LateAmount\DefaultLateAmount;
use StikCredit\Calculators\LateAmount\LateAmountCollection;
use StikCredit\Calculators\Outstanding\DefaultOutstanding;
use StikCredit\Calculators\Outstanding\OutstandingAmountCollection;
use StikCredit\Calculators\Overdue\DefaultOverdue;
use StikCredit\Calculators\Overdue\OverdueAmountCollection;
use StikCredit\Calculators\Paid\DefaultPaidAmount;
use StikCredit\Calculators\Paid\PaidAmountCollection;
use StikCredit\Calculators\Payments\DefaultPayment;
use StikCredit\Calculators\Payments\ReceivedPaymentsCollection;
use StikCredit\Calculators\Traits\DatesAwareTrait;
use StikCredit\Calculators\Traits\MathAwareTrait;

/**
 * @method DefaultPaymentDistribution first()
 * @method DefaultPaymentDistribution last()
 * @method DefaultPaymentDistribution get(int $key, $default = null)
 * @property-read DefaultInstallment $isPaid;
 * @property-read DefaultInstallment $isDue;
 * @property-read DefaultInstallment $paidDate;
 */
class PaymentDistributionCollection extends AbstractLoanCollection
{
    use DatesAwareTrait, MathAwareTrait;

    public function build(): PaymentDistributionCollection
    {
        return $this;
    }

    /**
     * @throws \Exception
     */
    public function execute(array $hasCustomPaymentSchedule): PaymentDistributionCollection
    {
        /** @var DefaultPayment[]|ReceivedPaymentsCollection $receivedPaymentsCollection */
        $receivedPaymentsCollection = $this->diContainer->get(ReceivedPaymentsCollection::class);

        /** @var DefaultOutstanding[]|OutstandingAmountCollection $outstandingAmountCollection */
        $outstandingAmountCollection = $this->diContainer->get(OutstandingAmountCollection::class);

        /** @var DefaultInstallment[]|InstallmentsCollection $installmentsCollection */
        $installmentsCollection = $this->diContainer->get(InstallmentsCollection::class);

        /** @var DefaultOverdue[]|OverdueAmountCollection $overdueAmountCollection */
        $overdueAmountCollection = $this->diContainer->get(OverdueAmountCollection::class);

        /**
         * @var PaidAmountCollection $paidAmountCollection
         */
        $paidAmountCollection = $this->diContainer->get(PaidAmountCollection::class);

        $distributionStrategy = $this->getDistributionStrategies();

//        if (!empty($hasCustomPaymentSchedule)) {
//            $outstandingAmountCollection->setLateAmountsFromCustom($hasCustomPaymentSchedule);
//        }

        foreach ($receivedPaymentsCollection as $payment) {
            $amount = $payment->amount;

            /// if payment date more than current date skip
            if ($this->sqlDate($payment->createdAt)->getTimestamp() > $this->sqlDate()->getTimestamp()) {
                continue;
            }

            //// ако плащането е тип предсрочно погасяване
            /// и няма къстъм погасителен план правим билд от начислени сумми до момента
            if ($payment->isClosingPayment() && !$this->loanConfig->hasCustomPaymentSchedule) {
                $outstandingAmountCollection->buildFromAccrued();
            }

//            if ($payment->isClosingPayment() && $this->loanConfig->hasCustomPaymentSchedule) {
//                $outstandingAmountCollection->buildFromCustomSchedule($hasCustomPaymentSchedule);
//            }

            /** @var DefaultOutstanding $outstandingAmount * */
            foreach ($outstandingAmountCollection as $index => $outstandingAmount) {
                $installment = $installmentsCollection->get($index);
                /** @var DefaultPaidAmount $defaultPaidAmount ** */
                $defaultPaidAmount = $paidAmountCollection->get($index);

                /// if installment is paid go to next row
                if ($outstandingAmount->isPaid() || $installment->isPaid) {
                    continue;
                }

                //// first time pay grouped loan fees
                $outstandingAmount->payLoanFees($payment, $amount, $this);
                if ($amount <= 0) {
                    break;
                }

//                if (empty($hasCustomPaymentSchedule)) {
                $outstandingAmount->calculateLateAmountToDate($payment->createdAt);
//                }

                /// pay all other amounts
                foreach ($distributionStrategy as $distributionIdentifier) {
                    $distributionAmount = $outstandingAmount->$distributionIdentifier;
                    if ($distributionAmount == 0) {
                        continue;
                    }

                    if ($amount <= 0) {
                        break;
                    }

                    if ($distributionAmount >= $amount) {
                        $distributionAmount = $amount;
                    }

                    /// set paid amounts
                    $defaultPaidAmount->setPaidAmount($distributionIdentifier, $distributionAmount);

                    ///
                    /// create payment distribution object
                    $identifier = "[{$index}][{$distributionIdentifier}]";
                    $paymentDistribution = new DefaultPaymentDistribution(
                        $index,
                        $payment->paymentId,
                        $identifier,
                        $distributionIdentifier,
                        $payment->createdAt,
                        $installment->dueDate,
                        $distributionAmount,
                        $amount,
                        ($amount - $distributionAmount),
                        '',
                        $payment->hexColor
                    );
                    $this->push($paymentDistribution);


                    $outstandingAmount->$distributionIdentifier = ($outstandingAmount->$distributionIdentifier - $distributionAmount); /// set outstanding amount
                    $amount = ($amount - $distributionAmount); /// update payment amount

                    /// add description
                    if (in_array($distributionIdentifier, ['outstandingLateInterestAmount', 'outstandingLatePenaltyAmount'])) {
                        $template = "Late amount %s outstanding amount %s for %s overdue days.";

                        $overdueDays = $installment->getOverdueDaysToDate($payment->createdAt);
                        if ($installment->overdueDaysFromLastPaymentDate) {
                            $overdueDays = $installment->overdueDaysFromLastPaymentDate;
                        }
                        $paymentDistribution->description = sprintf($template,
                            $this->intToFloat($distributionAmount),
                            $this->intToFloat($outstandingAmount->getPaymentScheduleOutstanding()),
                            $overdueDays,
                        );
                    }

                    /// if installment is paid
                    /// refresh paid amounts & overdue days
                    if ($outstandingAmount->isPaid()) {
                        $installment->isPaid = true;
                        $installment->partiallyPaid = false;
                        $installment->paidDate = !empty($hasCustomPaymentSchedule[$index]['paid_at']) ? $hasCustomPaymentSchedule[$index]['paid_at'] : $payment->createdAt;
                        if ($payment->createdAt >= $installment->dueDate) {
                            $installment->lastPaymentDate = $payment->createdAt;
                        }
                        $installment->getOverdueDays();

                        break; /// exit if is paid
                    }
                } /// end distribution strategy


                if (!$outstandingAmount->isPaid()) {
                    $installment->paidDate = $payment->createdAt;
                    if ($payment->createdAt >= $installment->dueDate) {
                        $installment->lastPaymentDate = $payment->createdAt;
                    }
                    $installment->partiallyPaid = true;
                }
            }

            /// set overdue amount
            $outstandingAmountCollection->each(function (DefaultOutstanding $defaultOutstanding) use ($overdueAmountCollection) {

                $installment = $this->getInstallment($defaultOutstanding->index);
                $overdueAmount = $overdueAmountCollection[$defaultOutstanding->index];

                if (!$defaultOutstanding->isPaid() && $installment->isDue()) {
                    $overdueAmount->addOverdueAmount($defaultOutstanding);
                }
            });

            /// refresh current installment amount
            $installmentsCollection
                ->setOverdueDays()
                ->setCurrentInstallment();
        }

        return $this;
    }


    /**
     * @param string $strategyId
     * @return string[]
     * @throws \Exception
     */
    public function getDistributionStrategies(string $strategyId = 'default'): array
    {
        $strategies = [
            'default' => [
                'outstandingLatePenaltyAmount',
                'outstandingLateInterestAmount',
                'outstandingPenaltyAmount',
                'outstandingInterestAmount',
                'outstandingPrincipleAmount',
            ]
        ];

        if (!isset($strategies[$strategyId])) {
            throw new \Exception('Invalid distribution strategy');
        }
        return $strategies[$strategyId];
    }
}
