<script>
import {defineComponent} from 'vue'

export default defineComponent({
    name: "DynamicProductSlider",
    props: [
        'productId',
        'loanId',
        'clientPin',
        'sessionAmount',
        'sessionPeriod',
        'sessionInterest',
        'sessionPenalty',
        'sessionDiscount',
    ],
    data() {
        return {
            amount: (300).toFixed(2),
            period: 12,
            interest: 36,
            penalty: 0,
            discount: 0,
            numberOfInstallments: 0,
            installmentAmount: 0,
            installmentAmountDiscount: 0,
            interestAmount: 0,
            interestAmountDiscount: 0,
            penaltyAmount: 0,
            penaltyAmountDiscount: 0,
            totalReturnAmount: 0,
            totalReturnAmountDiscount: 0,
            totalIncreasedAmount: 0,
            totalIncreasedAmountDiscount: 0,
            refinanceAmount: 0,
        }
    },
    watch: {
        amount() {
            this.calculateLoan();
            if (window.RefinanceSlider.refinanceAmount) {
                this.refinanceAmount = window.RefinanceSlider.refinanceAmount;
            }

            $('input[name="loan[loan_sum]"]').val(parseInt(this.amount).toFixed(2));
        },
        period() {
            this.calculateLoan();
            $('input[name="loan[loan_period]"]').val(this.period);
        },
        interest() {
            this.calculateLoan();
            $('input[name="loan[interest]"]').val(this.interest);
        },
        penalty() {
            this.calculateLoan();
            $('input[name="loan[penalty]"]').val(this.penalty);
        },
        discount() {
            this.calculateLoan();
            $('input[name="loan[discount_percent]"]').val(this.discount);
        },
    },
    mounted() {
        // this.amount = parseInt(this.sessionAmount).toFixed(2);
        // this.period = this.sessionPeriod;
        // this.interest = this.sessionInterest;
        // this.penalty = this.sessionPenalty;
        // this.discount = this.sessionDiscount;

        this.calculateLoan();

        $('input[name="loan[loan_sum]"]').val(this.amount);
        $('input[name="loan[product_id]"]').val(this.productId);
        $('input[name="loan[loan_period]"]').val(this.period);
        $('input[name="loan[interest]"]').val(this.interest);
        $('input[name="loan[penalty]"]').val(this.penalty);
        $('input[name="loan[discount_percent]"]').val(this.discount);

        $(document).on('click', 'button[data-target="#slider-' + this.productId + '"]', () => {
            $('input[name="loan[loan_sum]"]').val(this.amount);
            $('input[name="loan[product_id]"]').val(this.productId);
            $('input[name="loan[loan_period]"]').val(this.period);
            $('input[name="loan[interest]"]').val(this.interest);
            $('input[name="loan[penalty]"]').val(this.penalty);
            $('input[name="loan[discount_percent]"]').val(this.discount);
        });

        window.RefinanceSlider.dynamicAmountEl[this.productId] = this;
    },
    methods: {
        calculateLoan() {
            axios
                    .get(window.calculateLoanRoute, {
                        params: {
                            productId: this.productId,
                            loanId: this.loanId,
                            clientPin: this.clientPin,
                            principle: this.amount,
                            period: this.period,
                            discount: this.discount,
                            interest: (this.interest === '' ? 0 : this.interest),
                            penalty: (this.penalty === '' ? 0 : this.penalty),
                        }
                    })
                    .then($resp => {
                        this.numberOfInstallments = $resp.data.numberOfInstallments;
                        this.installmentAmount = $resp.data.installmentAmount;
                        this.installmentAmountDiscount = $resp.data.installmentAmountDiscount;
                        this.interestAmount = $resp.data.interestAmount;
                        this.interestAmountDiscount = $resp.data.interestAmountDiscount;
                        this.penaltyAmount = $resp.data.penaltyAmount;
                        this.penaltyAmountDiscount = $resp.data.penaltyAmountDiscount;
                        this.totalReturnAmount = $resp.data.totalReturnAmount;
                        this.totalReturnAmountDiscount = $resp.data.totalReturnAmountDiscount;
                        this.totalIncreasedAmount = $resp.data.totalIncreasedAmount;
                        this.totalIncreasedAmountDiscount = $resp.data.totalIncreasedAmountDiscount;
                    });
        },
    }
})
</script>

<template>
    <div class="p-3">
        <div class="form-group">
            <label for="amount">Сума</label>
            <input type="text"
                   class="form-control"
                   name="amount"
                   v-model="amount"
                   data-amount-formatter="true"
            />
        </div>
        <!-- End ./form-group -->

        <div class="form-group">
            <label for="period">Период</label>
            <input type="text"
                   class="form-control"
                   name="period"
                   v-model="period"
            />
        </div>
        <!-- End ./form-group -->

        <div class="form-group">
            <label for="interest">Лихва</label>
            <input type="text"
                   class="form-control"
                   name="interest"
                   v-model="interest"
            />
        </div>
        <!-- End ./form-group -->

        <div class="form-group">
            <label for="penalty">Неустойка</label>
            <input type="text"
                   class="form-control"
                   name="penalty"
                   v-model="penalty"
            />
        </div>
        <!-- End ./form-group -->

        <div class="form-group">
            <label for="discount">Отстъпка</label>
            <input type="text"
                   class="form-control"
                   name="discount"
                   v-model="discount"
            />
        </div>
        <!-- End ./form-group -->

        <hr>
        <div class="text-dark overflow-x-scroll">
            <table class="table table-sm table-bordered">
                <tr v-if="this.refinanceAmount"
                    :class="{'bg-success': (this.amount - this.refinanceAmount) > 0, 'bg-danger': (this.amount - this.refinanceAmount) <= 0}">
                  <td colspan="3">Сума след рефинансиране</td>
                  <td>{{ (this.amount - this.refinanceAmount).toFixed(2) }}</td>
                </tr>
                <tr>
                    <td>Сума</td>
                    <td>{{ this.amount }}</td>
                    <td>Период</td>
                    <td>{{ this.period }}</td>
                </tr>
                <tr>
                    <td>Брой вноски</td>
                    <td>{{ this.numberOfInstallments }}</td>
                    <td>Лихва</td>
                    <td>
                        {{ this.interestAmount }}
                        <span v-if="(this.discount > 0)" class="text-success">
                            -{{ this.interestAmountDiscount }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td>Вноска</td>
                    <td>
                        {{ this.installmentAmount }}
                        <span v-if="(this.discount > 0)" class="text-success">
                            -{{ this.installmentAmountDiscount }}
                        </span>
                    </td>
                    <td>Неустойка</td>
                    <td>
                        {{ this.penaltyAmount }}
                        <span v-if="(this.discount > 0)" class="text-success">
                            -{{ this.penaltyAmountDiscount }}
                        </span>
                    </td>
                </tr>

                <tr>
                    <td>Общо за връщане</td>
                    <td>
                        {{ this.totalReturnAmount }}
                        <span v-if="(this.discount > 0)" class="text-success">
                            -{{ this.totalReturnAmountDiscount }}
                        </span>
                    </td>
                    <td>Общо оскъпяване</td>
                    <td>
                        {{ this.totalIncreasedAmount }}
                        <span v-if="(this.discount > 0)" class="text-success">
                            -{{ this.totalIncreasedAmountDiscount }}
                        </span>
                    </td>
                </tr>
            </table>
        </div>

    </div>
</template>
