@php
    /**
     * @var \Modules\Common\Models\Office $office
     * @var \Modules\Common\Models\Product $product
     */
@endphp
@if($products->first())
    <div class="loan-slider">
        <ul class="nav nav-tabs" id="officeProducts" role="tablist">
            @foreach($products as $product)
                <li class="nav-item" role="presentation">
                    <button class="nav-link @if($loop->first) active @endif"
                            id="{{str($product->name)->slug()}}-tab"
                            data-toggle="tab"
                            data-target="#slider-{{$product->getKey()}}"
                            type="button"
                            role="tab"
                            aria-controls="{{str($product->name)->slug()}}"
                            aria-selected="true"
                    >
                        {{ $product->trade_name }}
                    </button>
                </li>
            @endforeach
        </ul>

        <div class="tab-content " id="vuejs-slider">
            @foreach($products as $product)
                <div class="tab-pane fade @if($loop->first) show active @endif"
                     id="slider-{{$product->getKey()}}"
                     role="tabpanel"
                     aria-labelledby="{{str($product->name)->slug()}}-tab"
                >
                    @if($product->isFixed)
                        <single-product-slider
                                key="{{$product->getKey()}}"
                                productid="{{$product->getKey()}}"
                                loanId="0"
                                client-pin="{{$clientPin}}"
                                phone="{{$phone}}"
                        />
                    @else
                        <dynamic-product-slider
                                key="{{$product->getKey()}}"
                                product-id="{{$product->getKey()}}"
                                loan-id="0"
                                client-pin="{{$clientPin}}"
                                session-amount="{{session('tempSliderData.loan_sum',30000)/100}}"
                                session-period="{{session('tempSliderData.period',12)}}"
                                session-interest="{{session('tempSliderData.interest',36)}}"
                                session-penalty="{{session('tempSliderData.penalty',0)}}"
                                session-discount="{{session('tempSliderData.discount',0)}}"
                        />
                    @endif
                </div>
            @endforeach
        </div>

        <div class="row mb-5">
            <div class="col-lg-6">
                <select name="loan[office_id]" id="officeId" class="form-control" data-live-search="true">
                    @foreach($offices as $office)
                        <option value="{{$office->getKey()}}"
                                @if($office->getKey() === $currentOfficeId) selected="selected" @endif
                        >
                            {{$office->name}}
                        </option>
                    @endforeach
                </select>
            </div>
            <!-- End ./col-lg-6 -->
            <div class="col-lg-6 no-padding">
                <button type="button" class="btn btn-secondary btn-block" id="previewPaymentSchedule">
                    <i class="fa fa-list-alt"></i>&nbsp;
                    {{__('head::clientCard.repaymentPlanInfo')}}
                </button>
            </div>
            <!-- End ./col-lg-6 -->
        </div>
        <!-- End ./row -->

        @if($loansForRefinance?->count())
            @php
                /**
                * @var \Modules\Common\Models\Loan $refinance
                 */
            @endphp
                    <!-- Container for showing ajax errors -->
            <div id="refinance-errors"></div>

            <h3>{{__('table.RefinanceLoans')}}</h3>
            <x-table table-id="refinance-loans">
                <x-slot:head>
                    <tr>
                        <th>{{__('#')}}</th>
                        <th>{{__('table.Id')}}</th>
                        <th>{{__('table.Pin')}}</th>
                        <th>{{__('table.Amount')}}</th>
                        <th>{{__('table.LastInstallmentDate')}}</th>
                        <th>{{__('table.Status')}}</th>
                        <th>{{__('table.OutstandingAmount')}}</th>
                    </tr>
                </x-slot:head>
                @foreach($loansForRefinance as $refinanceLoan)
                    @php
                        $refinanceData = $getRefinanceData($refinanceLoan, ($currentOfficeId == 1));
                        $refLoanStatus = $refinanceLoan->loanStatus;
                    @endphp
                    <tr>
                        <td class="text-center">
                            <input type="checkbox"
                                   class="form-check"
                                   name="refinanced_loans[]"
                                   value="{{$refinanceLoan->getKey()}}"
                                   ref="loanForRefinance"
                                    @checked($currentOfficeId == 1)
                            />
                        </td>
                        <td>{{$refinanceLoan->getKey()}}</td>
                        <td>{{$refinanceLoan->client->pin}}</td>
                        <td>{{intToFloat($refinanceLoan->amount_approved)}}</td>
                        <td>{{$refinanceData['lastInstallmentDate']}}</td>
                        <td class="{{$refLoanStatus->getStatusCssClass()}}">
                            {{$refLoanStatus->label()}}
                        </td>
                        <td>{{$refinanceData['earlyRepaidAmount']}}</td>
                    </tr>
                @endforeach
            </x-table>
        @endif
    </div>

    <button type="button" name="sefPreview" class="btn btn-sm btn-primary">Изтегли СЕФ</button>

    @php
        $productSettings = $productSettings($products->first());
    @endphp

    <input type="hidden" name="loan[isJuridical]" value="{{$productSettings['isJuridical']}}"/>
    <input type="hidden" name="loan[product_id]" value="{{$productSettings['productId']}}"/>
    <input type="hidden" name="loan[loan_sum]" value="{{$productSettings['defAmount']}}"/>
    <input type="hidden" name="loan[loan_period]" value="{{$productSettings['defPeriod']}}"/>
    <input type="hidden" name="loan[discount_percent]" value="0"/>
    <input type="hidden" name="loan[interest]"/>
    <input type="hidden" name="loan[penalty]"/>
@else
    <h3 class="text-center text-danger">
        {{__('No available company products for this office.')}}
    </h3>
    <hr/>
    <div class="col-lg-6">
        @if(!empty($offices))
            <p>{{__('Try to select another office')}}</p>
            <select name="loan[office_id]" id="officeId" class="form-control" data-live-search="true">
                @foreach($offices as $office)
                    <option value="{{$office->getKey()}}"
                            @if($office->getKey() === $currentOfficeId) selected="selected" @endif
                    >
                        {{$office->name}}
                    </option>
                @endforeach
            </select>
        @endif
    </div>
@endif

@push('scripts')
    <script type="text/html" id="js-errors">
        <div class="alert alert-danger alert-dismissible">
            <h5><i class="fa fa-message"></i>&nbsp;{{__('Oops. Something wrong.')}}</h5>
            <p class="mb-0">{message}</p>
            <button type="button"
                    class="close"
                    data-dismiss="alert"
                    aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    </script>

    <script>
        window.officeId = '{{$currentOfficeId}}';
        window.isOnlineOffice = {{intval($isOnlineOffice($currentOfficeId))}};
        window.installmentProductId = {{intval($getInstallmentProductId())}};
        window.dynamicProductId = {{intval($getDynamicProductId())}};
        window.fetchProductSettingsRoute = '{{route('common.slider.fetch-product-settings')}}';
        window.calculateLoanRoute = '{{route('common.slider.calculateLoan')}}';
        window.showPreliminaryPaymentPlan = '{{route('head.loans.showPreliminaryPaymentPlan')}}';

        $(document).on('click', 'button#previewPaymentSchedule', function () {
            let $paymentSchedulePreview = window.showPreliminaryPaymentPlan + '?';
            $paymentSchedulePreview += 'productId=' + $('input[name="loan[product_id]"]').val();
            $paymentSchedulePreview += '&sum=' + $('input[name="loan[loan_sum]"]').val().replace('.', '');
            $paymentSchedulePreview += '&period=' + $('input[name="loan[loan_period]"]').val();
            $paymentSchedulePreview += '&discount=' + $('input[name="loan[discount_percent]"]').val();

            let divPart = '#slider-' + $('input[name="loan[product_id]"]').val() + ' ';
            if ($(divPart + 'input[name="interest"]').length) {
                $paymentSchedulePreview += '&interest=' + ($(divPart + 'input[name="interest"]').val() === '' ? 0 : $(divPart + 'input[name="interest"]').val());
                $paymentSchedulePreview += '&penalty=' + ($(divPart + 'input[name="penalty"]').val() === '' ? 0 : $(divPart + 'input[name="penalty"]').val());
            }

            window.open($paymentSchedulePreview, '_blank');
        });

        let $setOfficeIdUrl = '{{route('sales.setOfficeId')}}';
        let $hasTmpProductId = {{$hasTmpProductId()}};
        $(document).ready(function () {
            $(document).on('change', 'select[name="loan[office_id]"]', function () {
                let $form = $(this).parents('form').first();
                let $tempFormData = $form.serialize();

                $.get($setOfficeIdUrl, $tempFormData, function (resp) {
                    location.reload();
                });
            });

            if ($hasTmpProductId && $('input[name="refinanced_loans[]"]').length === 0) {
                $('button[data-target="#slider-' + $hasTmpProductId + '"]').trigger('click');
            }
        });

        $(document).on('click', 'button[name="sefPreview"]', (event) => {
            let sefPreviewRequest = new URL('{{route('docs.document.sefPreview')}}');
            let params = {
                productId: $('input[name="loan[product_id]"]').val(),
                amount: $('input[name="loan[loan_sum]"]').val(),
                period: $('input[name="loan[loan_period]"]').val(),
                discount: $('input[name="loan[discount_percent]"]').val(),
            };
            for (let key in params) {
                sefPreviewRequest.searchParams.set(key, params[key]);
            }

            window.open(sefPreviewRequest);
        });
    </script>
@endpush
