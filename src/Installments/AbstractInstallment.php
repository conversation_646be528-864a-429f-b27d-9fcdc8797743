<?php

namespace StikCredit\Calculators\Installments;

use StikCredit\Calculators\Abstract\AbstractLoan;
use StikCredit\Calculators\Traits\DatesAwareTrait;
use StikCredit\Calculators\Traits\MathAwareTrait;

/**
 * @property-read string $dueDate;
 * @property-read bool $isPaid;
 * @property-read string $paidDate;
 * @property-read int $index;
 * @property-read bool $isDue;
 * @property int $overdueDays;
 * @property int $installmentDays;
 * @method  DefaultInstallment isDue();
 */
class AbstractInstallment extends AbstractLoan
{
    use DatesAwareTrait, MathAwareTrait;

    /**
     * @param string $paymentDate
     * @return int
     * @throws \Exception
     */
    public function getOverdueDaysToDate(string $paymentDate): int
    {
        $overdueDays = 0;

        /// if payment date more than due date return 0 overdue days
        if ($this->sqlDate($this->dueDate)->getTimestamp() >= $this->sqlDate($paymentDate)->getTimestamp()) {
            return 0;
        }

        if ($this->sqlDate($this->dueDate)->getTimestamp() < $this->sqlDate($paymentDate)->getTimestamp()) {
            $overdueDaysToDate = $this->sqlDate($this->dueDate)->diff($this->sqlDate($paymentDate))->format('%a');
            return intval($overdueDaysToDate);
        }


        return $overdueDays;
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function getDueLabel(): string
    {
        if ($this->isPaid) {
            return 'paidInstallment';
        }

        if ($this->sqlDate($this->dueDate)->getTimestamp() > $this->sqlDate()->getTimestamp()) {
            return 'incomingInstallment';
        }

        if ($this->sqlDate($this->dueDate)->getTimestamp() == $this->sqlDate()->getTimestamp()) {
            return 'pastInstallment';
        }
        /** @phpstan-ignore-next-line */
        if ($this->sqlDate($this->dueDate)->getTimestamp() < $this->sqlDate()->getTimestamp() && !$this->isPaid) {
            return 'overdueInstallment';
        }

        return 'n/a';
    }
}
