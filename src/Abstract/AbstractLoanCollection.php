<?php

namespace StikCredit\Calculators\Abstract;

use StikCredit\Calculators\Config\LoanConfig;
use Illuminate\Support\Collection;
use StikCredit\Calculators\DiContainer;
use StikCredit\Calculators\Services\DateBuilder;
use StikCredit\Calculators\Services\MathService;
use StikCredit\Calculators\Traits\DatesAwareTrait;
use StikCredit\Calculators\Traits\MathAwareTrait;
use StikCredit\Calculators\Traits\UtilsAwareTrait;

abstract class AbstractLoanCollection extends Collection
{
    use DatesAwareTrait, MathAwareTrait, UtilsAwareTrait;

    protected LoanConfig $loanConfig;
    protected DiContainer $diContainer;

    protected readonly DateBuilder $dateBuilder;
    protected readonly MathService $mathService;


    public function __construct($items = [])
    {
        $this->loanConfig = LoanConfig::instance();
        $this->diContainer = DiContainer::instance();

        $this->dateBuilder = new DateBuilder();
        $this->mathService = new MathService();

        parent::__construct($items);
    }

    /**
     * @param int $index
     * @return mixed
     * @throws \Exception
     */
    public function prev(int $index): mixed
    {
        $prevKey = $index - 1;
        if (!$this->has($prevKey)) {
            throw new \Exception('Invalid prev installment');
        }

        return $this->get($prevKey);
    }
}
