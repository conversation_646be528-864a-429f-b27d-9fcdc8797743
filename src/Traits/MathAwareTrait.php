<?php

namespace StikCredit\Calculators\Traits;

trait MathAwareTrait
{
    use UtilsAwareTrait;

    public function calcAccruedLateAmount(int $amount, int $overdueDays): int
    {
        if ($overdueDays <= 0) {
            return 0;
        }
        $loanConfig = $this->getConfig();

        $lateInterest = ($loanConfig->lateInterest / 360);
        $accruedLateAmount = ($overdueDays * $lateInterest * $amount);

        return intval(round($accruedLateAmount, 4));
    }

    public function calcAccruedLatePenaltyAmount(int $amount, int $overdueDays): int
    {
        if ($overdueDays <= 0) {
            return 0;
        }
        $loanConfig = $this->getConfig();

        if ($loanConfig->latePenaltyDays != 0 && $loanConfig->latePenaltyDays <= $overdueDays) {
            $overdueDays = $loanConfig->latePenaltyDays;
        }

        $lateInterestRate = ($loanConfig->latePenalty / 360);
        $accruedLateAmount = ($overdueDays * $lateInterestRate * $amount);

        return intval(round($accruedLateAmount, 4));
    }

    public function getInterest(int $amount, float $interestRate): int
    {
        return intval(round(($amount * $interestRate)));
    }

    public function getDiscountInstallmentAmount(): int
    {
        $loanConfig = $this->getConfig();

        $interestRate = $this->getInterestRate(true);
        $period = $loanConfig->getNumberOfInstallments();

        $installmentAmount = $interestRate * pow(1 + $interestRate, $period) / (pow(1 + $interestRate, $period) - 1) * $loanConfig->amount;

        return intval(round($installmentAmount));
    }

    public function getInstallmentAmount(): int
    {
        $loanConfig = $this->getConfig();

        $interestRate = $this->getInterestRate();
        $period = $loanConfig->getNumberOfInstallments();

        if ($interestRate === 0.0) {
            return intval(round($loanConfig->amount / $period));
        }

        $installmentAmount = $interestRate * pow(1 + $interestRate, $period) / (pow(1 + $interestRate, $period) - 1) * $loanConfig->amount;

        return intval(round($installmentAmount));
    }

    public function getPenaltyInterestRate(): float
    {
        $loanConfig = $this->getConfig();
        $divider = $loanConfig->divider;
        $discountPercent = $loanConfig->discountPercent;
        $numberOfInstallments = $loanConfig->getNumberOfInstallments();
        if ($loanConfig->isProductType('payday')) {
            $numberOfInstallments = $loanConfig->numberOfInstallments;
            return round($loanConfig->apr / $divider * (1 - $discountPercent) * $numberOfInstallments, 4);
        }

        if ($loanConfig->hasDiscount()) {
            return round(($loanConfig->apr * (1 - $discountPercent) / $divider), 4);
        }
        return round(($loanConfig->apr / $divider), 4);
    }

    public function getAirInterestRate(): float
    {
        $loanConfig = $this->getConfig();
        $divider = $loanConfig->divider;
        $discountPercent = $loanConfig->discountPercent;
        $numberOfInstallments = $loanConfig->getNumberOfInstallments();
        if ($loanConfig->isProductType('payday')) {
            $numberOfInstallments = $loanConfig->numberOfInstallments;
            return round($loanConfig->air / $divider * (1 - $discountPercent) * $numberOfInstallments, 4);
        }

        if ($loanConfig->hasDiscount()) {
            return round(($loanConfig->air * (1 - $discountPercent) / $divider), 4);
        }
        return round(($loanConfig->air / $divider), 4);
    }


    /**
     * @param bool $withDiscount
     * @return float
     */
    public function getInterestRate(bool $withDiscount = false): float
    {
        $loanConfig = $this->getConfig();
        $divider = $loanConfig->divider;
        $discountPercent = $loanConfig->discountPercent;
        $numberOfInstallments = $loanConfig->getNumberOfInstallments();
        if ($loanConfig->isProductType('payday')) {
            $numberOfInstallments = $loanConfig->numberOfInstallments;
            return round(($loanConfig->air + $loanConfig->apr) / $divider * (1 - $discountPercent) * $numberOfInstallments, 4);
        }

        if ($loanConfig->hasDiscount()) {
            return round(((($loanConfig->air * (1 - $discountPercent)) + ($loanConfig->apr * (1 - $discountPercent))) / $divider), 4);
        }
        return round((($loanConfig->air + $loanConfig->apr) / $divider), 4);
    }


    public function calculateGpr(): float
    {
        $loanConfig = $this->getConfig();
        if (!$loanConfig->isValid()) {
            return 0.0;
        }
//        $divider = $this->getDivider($loanConfig->divider);
//        $divider = $loanConfig->divider;
        $air = $loanConfig->air;
        if ($loanConfig->hasDiscount()) {
            $air = ($loanConfig->air * (1 - $loanConfig->discountPercent));
        }
        $gpr = round((pow((1 + $air / 12), 12) - 1), 4) * 100;

        return round($gpr, 2);
    }


    /**
     * @param int|float $amount
     * @return int
     */
    public function floatToInt(int|float $amount): int
    {
        if (is_float($amount)) {
            dd($amount);
        }

        return intval(strval(number_format(floatval(trim($amount)), 2, '.', '') * 100));
    }

    /**
     * @param int|float $amount
     * @return float
     */
    public function intToFloat(int|float $amount): float
    {
        return round(number_format(($amount / 100), 2, '.', ''), 2);
    }
}
