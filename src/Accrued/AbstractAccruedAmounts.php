<?php

namespace StikCredit\Calculators\Accrued;

use StikCredit\Calculators\Abstract\AbstractLoan;
use StikCredit\Calculators\Installments\InstallmentsCollection;
use StikCredit\Calculators\Traits\DatesAwareTrait;
use StikCredit\Calculators\Traits\MathAwareTrait;

/**
 * @property-read int $index;
 * @property-read int $installmentOverdueDays;
 * @property-read int $installmentAmount;
 * @property-read int $accruedLateTotalAmount;
 * @property-read int $installmentPrincipleAmount;
 */
class AbstractAccruedAmounts extends AbstractLoan
{
    use DatesAwareTrait, MathAwareTrait;

    public function getAccruedPenaltyAmount(): int
    {
        $loanConfig = $this->getConfig();
        $installments = $this->loanGet(InstallmentsCollection::class);

        /// if already due return already calculated interest
        if ($installments[$this->index]->isDue) {
            return $installments[$this->index]->penaltyAmount;
        }

        $passedDays = $installments[$this->index]->passedDays;
        $installmentDays = $installments[$this->index]->installmentDays;

        /** @phpstan-ignore-next-line */
        if (!$installments[$this->index]->isDue && $loanConfig->isProductType('installment')) {
            $currentPenaltyAmount = $installments[$this->index]->penaltyAmount;

            if ($passedDays != 0 && $installmentDays != 0 && $currentPenaltyAmount != 0) {
                return intval(round(($passedDays / $installmentDays * $currentPenaltyAmount)));
            }
        }


        /// if installment is incoming calculate interest
        /** @phpstan-ignore-next-line */
        if (!$installments[$this->index]->isDue && $loanConfig->isProductType('payday')) {
            $currentPenaltyAmount = $installments[$this->index]->penaltyAmount;

            if ($passedDays != 0 && $installmentDays != 0 && $currentPenaltyAmount != 0) {
                return intval(round(($passedDays / $installmentDays * $currentPenaltyAmount)));
            }
        }

        return 0;
    }

    public function getAccruedInterestAmount(): int
    {
        $loanConfig = $this->getConfig();
        $installments = $this->loanGet(InstallmentsCollection::class);

        /// if already due return already calculated interest
        if ($installments[$this->index]->isDue) {
            return $installments[$this->index]->interest;
        }

        $passedDays = $installments[$this->index]->passedDays;
        $installmentDays = $installments[$this->index]->installmentDays;

        /// if installment is incoming calculate interest
        /** @phpstan-ignore-next-line */
        if (!$installments[$this->index]->isDue && $loanConfig->isProductType('installment')) {
            $currentInterestAmount = $installments[$this->index]->interest;

            if ($passedDays != 0 && $installmentDays != 0 && $currentInterestAmount != 0) {
                return intval(round(($passedDays / $installmentDays * $currentInterestAmount)));
            }
        }

        /// if installment is incoming calculate interest
        /** @phpstan-ignore-next-line */
        if (!$installments[$this->index]->isDue && $loanConfig->isProductType('payday')) {
            $currentInterestAmount = $installments[$this->index]->interest;

            if ($passedDays != 0 && $installmentDays != 0 && $currentInterestAmount != 0) {
                return intval(round(($passedDays / $installmentDays * $currentInterestAmount)));
            }
        }

        return 0;
    }

}
