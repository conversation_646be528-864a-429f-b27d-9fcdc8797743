<?php

namespace StikCredit\Calculators\FreshInstallment;

use StikCredit\Calculators\Abstract\AbstractLoanCollection;
use StikCredit\Calculators\Accrued\AccruedAmountsCollection;
use StikCredit\Calculators\Fees\FeesCollection;
use StikCredit\Calculators\Installments\DefaultInstallment;
use StikCredit\Calculators\Installments\InstallmentsCollection;
use StikCredit\Calculators\Outstanding\OutstandingAmountCollection;
use StikCredit\Calculators\Overdue\OverdueAmountCollection;
use StikCredit\Calculators\Paid\PaidAmountCollection;
use StikCredit\Calculators\Payments\ReceivedPaymentsCollection;

/**
 * @method DefaultFreshInstallment first()
 * @method DefaultFreshInstallment last()
 * @method DefaultFreshInstallment get(int $key, $default = null)
 */
class FreshInstallmentCollection extends AbstractLoanCollection
{

    public function build(): FreshInstallmentCollection
    {
        /**
         * @var InstallmentsCollection $installmentsCollection
         * @var AccruedAmountsCollection $accruedAmountCollection
         * @var OutstandingAmountCollection $outstandingAmountCollection
         * @var PaidAmountCollection $paidAmountCollection
         * @var OverdueAmountCollection $overdueAmountCollection
         * @var FeesCollection $feesCollection
         * @var ReceivedPaymentsCollection $receivedPaymentCollection
         */
        $installmentsCollection = $this->loanGet(InstallmentsCollection::class);
        $accruedAmountCollection = $this->loanGet(AccruedAmountsCollection::class);
        $outstandingAmountCollection = $this->loanGet(OutstandingAmountCollection::class);
        $paidAmountCollection = $this->loanGet(PaidAmountCollection::class);
        $overdueAmountCollection = $this->loanGet(OverdueAmountCollection::class);
        $feesCollection = $this->loanGet(FeesCollection::class);
        $receivedPaymentCollection = $this->loanGet(ReceivedPaymentsCollection::class);

        $installmentsCollection->each(function (DefaultInstallment $defaultInstallment) use (
            $accruedAmountCollection,
            $outstandingAmountCollection,
            $paidAmountCollection,
            $overdueAmountCollection,
            $feesCollection,
            $receivedPaymentCollection
        ) {

            $index = $defaultInstallment->index;

            $defaultAccruedAmount = $accruedAmountCollection->get($index);
            $defaultOutstanding = $outstandingAmountCollection->get($index);
            $defaultPaid = $paidAmountCollection->get($index);
            $defaultOverdue = $overdueAmountCollection->get($index);
            $loanFees = $feesCollection->getDueFeesToInstallment($index);

            $paymentScheduleOutstanding = $defaultOutstanding->getPaymentScheduleOutstanding();
            $outstandingTaxesAmount = $loanFees->sum('outstandingAmount');

            $data = [
                'index' => $index,
                'dueDate' => $defaultInstallment->dueDate,
                'isDue' => $defaultInstallment->isDue,
                'isPaid' => $defaultInstallment->isPaid,
                'isCurrent' => $defaultInstallment->isCurrent,
                'isPartiallyPaid' => $defaultInstallment->partiallyPaid,
                'paidDate' => $defaultInstallment->paidDate,
                'installmentStatus' => $defaultInstallment->getDueLabel(),
                'installmentOverdueDays' => $defaultInstallment->overdueDays,
                'installmentPassedDays' => $defaultInstallment->passedDays,

                'totalInstallmentOverdueAmount' => $defaultOverdue->getTotalInstallmentOverdueAmount(),
                'totalOverdueAmount' => $defaultOverdue->getTotalOverdueAmount(),
                'overduePrincipleAmount' => $defaultOverdue->overduePrincipleAmount,
                'overdueInterestAmount' => $defaultOverdue->overdueInterestAmount,
                'overduePenaltyAmount' => $defaultOverdue->overduePenaltyAmount,

                'totalInstallmentAmount' => $defaultInstallment->installmentAmount,
                'principalAmount' => $defaultInstallment->principal,
                'interestAmount' => $defaultInstallment->interest,
                'penaltyAmount' => $defaultInstallment->penaltyAmount,
                'taxesAmount' => $loanFees->sum('amount'),

                /// accrued amount
                'totalAccruedAmount' => $defaultAccruedAmount->getAccruedAmount(),
                'accruedPrincipalAmount' => $defaultAccruedAmount->accruedPrincipleAmount,
                'accruedInterestAmount' => $defaultAccruedAmount->accruedInterestAmount,
                'accruedPenaltyAmount' => $defaultAccruedAmount->accruedPenaltyAmount,
                'accruedTaxesAmount' => $loanFees->sum('amount'),

                /// outstanding amount
                'totalOutstandingAmount' => $defaultOutstanding->getPaymentScheduleOutstanding(),
                'totalOutstandingAmountAll' => $defaultOutstanding->getTotalOutstandingAmount(),
                'outstandingPrincipalAmount' => $defaultOutstanding->outstandingPrincipleAmount,
                'outstandingInterestAmount' => $defaultOutstanding->outstandingInterestAmount,
                'outstandingPenaltyAmount' => $defaultOutstanding->outstandingPenaltyAmount,
                'outstandingTaxesAmount' => $outstandingTaxesAmount,

                /// paid amount
                'totalPaidAmount' => $defaultPaid->getPaymentSchedulePaidAmount(),
                'totalPaidAmountAll' => $defaultPaid->getTotalPaidAmount(), /// all paid amounts(installment,fees,lates)
                'paidPrincipalAmount' => $defaultPaid->paidPrincipleAmount,
                'paidInterestAmount' => $defaultPaid->paidInterestAmount,
                'paidPenaltyAmount' => $defaultPaid->paidPenaltyAmount,
                'paidTaxesAmount' => $loanFees->sum('paidAmount'),

                /// late amount
                'totalAccruedLateAmount' => $defaultAccruedAmount->getAccruedLateTotalAmount(),
                'accruedLatePenaltyAmount' => $defaultAccruedAmount->getAccruedLatePenaltyAmount(),
                'accruedLateInterestAmount' => $defaultAccruedAmount->getAccruedLateInterestAmount(),

                'totalOutstandingLateAmount' => $defaultOutstanding->getTotalOutstandingLateAmount(),
                'outstandingLatePenaltyAmount' => $defaultOutstanding->getOutstandingLatePenaltyAmount(),
                'outstandingLateInterestAmount' => $defaultOutstanding->getOutstandingLateInterestAmount(),

                'totalPaidLateAmount' => $defaultPaid->paidLateAmount,
                'paidLatePenaltyAmount' => $defaultPaid->paidLatePenaltyAmount,
                'paidLateInterestAmount' => $defaultPaid->paidLateInterestAmount,

                //// others
                'lastPaymentDate' => $receivedPaymentCollection->last()?->createdAt
            ];

            /// set due amounts
            if ($defaultInstallment->isDue()) {
                $data['totalDueAmount'] = $defaultInstallment->installmentAmount;
                $data['duePrincipalAmount'] = $defaultInstallment->principal;
                $data['dueInterestAmount'] = $defaultInstallment->interest;
                $data['duePenaltyAmount'] = $defaultInstallment->penaltyAmount;
                $data['dueTaxesAmount'] = $loanFees->sum('amount');
            }

            $defaultFreshInstallment = new DefaultFreshInstallment();
            $defaultFreshInstallment->build($data);

            $this->put($index, $defaultFreshInstallment);
        });

        return $this;
    }
}
