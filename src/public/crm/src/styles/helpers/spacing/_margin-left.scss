
// Margin Left
.ml {

    // Default
    &-0 {
        margin-left: 0 !important;
    }

    &-1 {
        margin-left: $spacer !important;
    }

    &-2 {
        margin-left: calc(2 * #{$spacer}) !important;
    }

    &-3 {
        margin-left: calc(3 * #{$spacer}) !important;
    }

    &-4 {
        margin-left: calc(4 * #{$spacer}) !important;
    }

    &-5 {
        margin-left: calc(5 * #{$spacer}) !important;
    }

    &-6 {
        margin-left: calc(6 * #{$spacer}) !important;
    }

    &-7 {
        margin-left: calc(7 * #{$spacer}) !important;
    }

    &-8 {
        margin-left: calc(8 * #{$spacer}) !important;
    }

    &-9 {
        margin-left: calc(9 * #{$spacer}) !important;
    }

    &-10 {
        margin-left: calc(10 * #{$spacer}) !important;
    }

    &-11 {
        margin-left: calc(11 * #{$spacer}) !important;
    }

    &-12 {
        margin-left: calc(12 * #{$spacer}) !important;
    }

    &-13 {
        margin-left: calc(13 * #{$spacer}) !important;
    }

    &-14 {
        margin-left: calc(14 * #{$spacer}) !important;
    }

    &-15 {
        margin-left: calc(15 * #{$spacer}) !important;
    }

    &-16 {
        margin-left: calc(16 * #{$spacer}) !important;
    }

    &-17 {
        margin-left: calc(17 * #{$spacer}) !important;
    }

    &-18 {
        margin-left: calc(18 * #{$spacer}) !important;
    }

    &-19 {
        margin-left: calc(19 * #{$spacer}) !important;
    }

    &-20 {
        margin-left: calc(20 * #{$spacer}) !important;
    }

    &-21 {
        margin-left: calc(21 * #{$spacer}) !important;
    }

    &-22 {
        margin-left: calc(22 * #{$spacer}) !important;
    }

    &-23 {
        margin-left: calc(23 * #{$spacer}) !important;
    }

    &-24 {
        margin-left: calc(24 * #{$spacer}) !important;
    }

    &-25 {
        margin-left: calc(25 * #{$spacer}) !important;
    }

    &-26 {
        margin-left: calc(26 * #{$spacer}) !important;
    }

    &-27 {
        margin-left: calc(27 * #{$spacer}) !important;
    }

    &-28 {
        margin-left: calc(28 * #{$spacer}) !important;
    }

    &-29 {
        margin-left: calc(29 * #{$spacer}) !important;
    }

    &-30 {
        margin-left: calc(30 * #{$spacer}) !important;
    }

    &-31 {
        margin-left: calc(31 * #{$spacer}) !important;
    }

    &-32 {
        margin-left: calc(32 * #{$spacer}) !important;
    }

    &-33 {
        margin-left: calc(33 * #{$spacer}) !important;
    }

    &-34 {
        margin-left: calc(34 * #{$spacer}) !important;
    }

    &-35 {
        margin-left: calc(35 * #{$spacer}) !important;
    }

    &-36 {
        margin-left: calc(36 * #{$spacer}) !important;
    }

    &-37 {
        margin-left: calc(37 * #{$spacer}) !important;
    }

    &-38 {
        margin-left: calc(38 * #{$spacer}) !important;
    }

    &-39 {
        margin-left: calc(39 * #{$spacer}) !important;
    }

    &-40 {
        margin-left: calc(40 * #{$spacer}) !important;
    }

    // Small
    @media (min-width: $sm) {
        
        &-sm {

            &-0 {
                margin-left: 0 !important;
            }
        
            &-1 {
                margin-left: $spacer !important;
            }
        
            &-2 {
                margin-left: calc(2 * #{$spacer}) !important;
            }
        
            &-3 {
                margin-left: calc(3 * #{$spacer}) !important;
            }
        
            &-4 {
                margin-left: calc(4 * #{$spacer}) !important;
            }
        
            &-5 {
                margin-left: calc(5 * #{$spacer}) !important;
            }
        
            &-6 {
                margin-left: calc(6 * #{$spacer}) !important;
            }
        
            &-7 {
                margin-left: calc(7 * #{$spacer}) !important;
            }
        
            &-8 {
                margin-left: calc(8 * #{$spacer}) !important;
            }
        
            &-9 {
                margin-left: calc(9 * #{$spacer}) !important;
            }
        
            &-10 {
                margin-left: calc(10 * #{$spacer}) !important;
            }
        
            &-11 {
                margin-left: calc(11 * #{$spacer}) !important;
            }
        
            &-12 {
                margin-left: calc(12 * #{$spacer}) !important;
            }
        
            &-13 {
                margin-left: calc(13 * #{$spacer}) !important;
            }
        
            &-14 {
                margin-left: calc(14 * #{$spacer}) !important;
            }
        
            &-15 {
                margin-left: calc(15 * #{$spacer}) !important;
            }
        
            &-16 {
                margin-left: calc(16 * #{$spacer}) !important;
            }
        
            &-17 {
                margin-left: calc(17 * #{$spacer}) !important;
            }
        
            &-18 {
                margin-left: calc(18 * #{$spacer}) !important;
            }
        
            &-19 {
                margin-left: calc(19 * #{$spacer}) !important;
            }
        
            &-20 {
                margin-left: calc(20 * #{$spacer}) !important;
            }
        
            &-21 {
                margin-left: calc(21 * #{$spacer}) !important;
            }
        
            &-22 {
                margin-left: calc(22 * #{$spacer}) !important;
            }
        
            &-23 {
                margin-left: calc(23 * #{$spacer}) !important;
            }
        
            &-24 {
                margin-left: calc(24 * #{$spacer}) !important;
            }
        
            &-25 {
                margin-left: calc(25 * #{$spacer}) !important;
            }
        
            &-26 {
                margin-left: calc(26 * #{$spacer}) !important;
            }
        
            &-27 {
                margin-left: calc(27 * #{$spacer}) !important;
            }
        
            &-28 {
                margin-left: calc(28 * #{$spacer}) !important;
            }
        
            &-29 {
                margin-left: calc(29 * #{$spacer}) !important;
            }
        
            &-30 {
                margin-left: calc(30 * #{$spacer}) !important;
            }
        
            &-31 {
                margin-left: calc(31 * #{$spacer}) !important;
            }
        
            &-32 {
                margin-left: calc(32 * #{$spacer}) !important;
            }
        
            &-33 {
                margin-left: calc(33 * #{$spacer}) !important;
            }
        
            &-34 {
                margin-left: calc(34 * #{$spacer}) !important;
            }
        
            &-35 {
                margin-left: calc(35 * #{$spacer}) !important;
            }
        
            &-36 {
                margin-left: calc(36 * #{$spacer}) !important;
            }
        
            &-37 {
                margin-left: calc(37 * #{$spacer}) !important;
            }
        
            &-38 {
                margin-left: calc(38 * #{$spacer}) !important;
            }
        
            &-39 {
                margin-left: calc(39 * #{$spacer}) !important;
            }
        
            &-40 {
                margin-left: calc(40 * #{$spacer}) !important;
            }
        }
    }

    // Medium
    @media (min-width: $md) {
        
        &-md {

            &-0 {
                margin-left: 0 !important;
            }
        
            &-1 {
                margin-left: $spacer !important;
            }
        
            &-2 {
                margin-left: calc(2 * #{$spacer}) !important;
            }
        
            &-3 {
                margin-left: calc(3 * #{$spacer}) !important;
            }
        
            &-4 {
                margin-left: calc(4 * #{$spacer}) !important;
            }
        
            &-5 {
                margin-left: calc(5 * #{$spacer}) !important;
            }
        
            &-6 {
                margin-left: calc(6 * #{$spacer}) !important;
            }
        
            &-7 {
                margin-left: calc(7 * #{$spacer}) !important;
            }
        
            &-8 {
                margin-left: calc(8 * #{$spacer}) !important;
            }
        
            &-9 {
                margin-left: calc(9 * #{$spacer}) !important;
            }
        
            &-10 {
                margin-left: calc(10 * #{$spacer}) !important;
            }
        
            &-11 {
                margin-left: calc(11 * #{$spacer}) !important;
            }
        
            &-12 {
                margin-left: calc(12 * #{$spacer}) !important;
            }
        
            &-13 {
                margin-left: calc(13 * #{$spacer}) !important;
            }
        
            &-14 {
                margin-left: calc(14 * #{$spacer}) !important;
            }
        
            &-15 {
                margin-left: calc(15 * #{$spacer}) !important;
            }
        
            &-16 {
                margin-left: calc(16 * #{$spacer}) !important;
            }
        
            &-17 {
                margin-left: calc(17 * #{$spacer}) !important;
            }
        
            &-18 {
                margin-left: calc(18 * #{$spacer}) !important;
            }
        
            &-19 {
                margin-left: calc(19 * #{$spacer}) !important;
            }
        
            &-20 {
                margin-left: calc(20 * #{$spacer}) !important;
            }
        
            &-21 {
                margin-left: calc(21 * #{$spacer}) !important;
            }
        
            &-22 {
                margin-left: calc(22 * #{$spacer}) !important;
            }
        
            &-23 {
                margin-left: calc(23 * #{$spacer}) !important;
            }
        
            &-24 {
                margin-left: calc(24 * #{$spacer}) !important;
            }
        
            &-25 {
                margin-left: calc(25 * #{$spacer}) !important;
            }
        
            &-26 {
                margin-left: calc(26 * #{$spacer}) !important;
            }
        
            &-27 {
                margin-left: calc(27 * #{$spacer}) !important;
            }
        
            &-28 {
                margin-left: calc(28 * #{$spacer}) !important;
            }
        
            &-29 {
                margin-left: calc(29 * #{$spacer}) !important;
            }
        
            &-30 {
                margin-left: calc(30 * #{$spacer}) !important;
            }
        
            &-31 {
                margin-left: calc(31 * #{$spacer}) !important;
            }
        
            &-32 {
                margin-left: calc(32 * #{$spacer}) !important;
            }
        
            &-33 {
                margin-left: calc(33 * #{$spacer}) !important;
            }
        
            &-34 {
                margin-left: calc(34 * #{$spacer}) !important;
            }
        
            &-35 {
                margin-left: calc(35 * #{$spacer}) !important;
            }
        
            &-36 {
                margin-left: calc(36 * #{$spacer}) !important;
            }
        
            &-37 {
                margin-left: calc(37 * #{$spacer}) !important;
            }
        
            &-38 {
                margin-left: calc(38 * #{$spacer}) !important;
            }
        
            &-39 {
                margin-left: calc(39 * #{$spacer}) !important;
            }
        
            &-40 {
                margin-left: calc(40 * #{$spacer}) !important;
            }
        }
    }

    // Large
    @media (min-width: $lg) {
        
        &-lg {

            &-0 {
                margin-left: 0 !important;
            }
        
            &-1 {
                margin-left: $spacer !important;
            }
        
            &-2 {
                margin-left: calc(2 * #{$spacer}) !important;
            }
        
            &-3 {
                margin-left: calc(3 * #{$spacer}) !important;
            }
        
            &-4 {
                margin-left: calc(4 * #{$spacer}) !important;
            }
        
            &-5 {
                margin-left: calc(5 * #{$spacer}) !important;
            }
        
            &-6 {
                margin-left: calc(6 * #{$spacer}) !important;
            }
        
            &-7 {
                margin-left: calc(7 * #{$spacer}) !important;
            }
        
            &-8 {
                margin-left: calc(8 * #{$spacer}) !important;
            }
        
            &-9 {
                margin-left: calc(9 * #{$spacer}) !important;
            }
        
            &-10 {
                margin-left: calc(10 * #{$spacer}) !important;
            }
        
            &-11 {
                margin-left: calc(11 * #{$spacer}) !important;
            }
        
            &-12 {
                margin-left: calc(12 * #{$spacer}) !important;
            }
        
            &-13 {
                margin-left: calc(13 * #{$spacer}) !important;
            }
        
            &-14 {
                margin-left: calc(14 * #{$spacer}) !important;
            }
        
            &-15 {
                margin-left: calc(15 * #{$spacer}) !important;
            }
        
            &-16 {
                margin-left: calc(16 * #{$spacer}) !important;
            }
        
            &-17 {
                margin-left: calc(17 * #{$spacer}) !important;
            }
        
            &-18 {
                margin-left: calc(18 * #{$spacer}) !important;
            }
        
            &-19 {
                margin-left: calc(19 * #{$spacer}) !important;
            }
        
            &-20 {
                margin-left: calc(20 * #{$spacer}) !important;
            }
        
            &-21 {
                margin-left: calc(21 * #{$spacer}) !important;
            }
        
            &-22 {
                margin-left: calc(22 * #{$spacer}) !important;
            }
        
            &-23 {
                margin-left: calc(23 * #{$spacer}) !important;
            }
        
            &-24 {
                margin-left: calc(24 * #{$spacer}) !important;
            }
        
            &-25 {
                margin-left: calc(25 * #{$spacer}) !important;
            }
        
            &-26 {
                margin-left: calc(26 * #{$spacer}) !important;
            }
        
            &-27 {
                margin-left: calc(27 * #{$spacer}) !important;
            }
        
            &-28 {
                margin-left: calc(28 * #{$spacer}) !important;
            }
        
            &-29 {
                margin-left: calc(29 * #{$spacer}) !important;
            }
        
            &-30 {
                margin-left: calc(30 * #{$spacer}) !important;
            }
        
            &-31 {
                margin-left: calc(31 * #{$spacer}) !important;
            }
        
            &-32 {
                margin-left: calc(32 * #{$spacer}) !important;
            }
        
            &-33 {
                margin-left: calc(33 * #{$spacer}) !important;
            }
        
            &-34 {
                margin-left: calc(34 * #{$spacer}) !important;
            }
        
            &-35 {
                margin-left: calc(35 * #{$spacer}) !important;
            }
        
            &-36 {
                margin-left: calc(36 * #{$spacer}) !important;
            }
        
            &-37 {
                margin-left: calc(37 * #{$spacer}) !important;
            }
        
            &-38 {
                margin-left: calc(38 * #{$spacer}) !important;
            }
        
            &-39 {
                margin-left: calc(39 * #{$spacer}) !important;
            }
        
            &-40 {
                margin-left: calc(40 * #{$spacer}) !important;
            }
        }
    }

    // X Large
    @media (min-width: $xl) {
        
        &-xl {

            &-0 {
                margin-left: 0 !important;
            }
        
            &-1 {
                margin-left: $spacer !important;
            }
        
            &-2 {
                margin-left: calc(2 * #{$spacer}) !important;
            }
        
            &-3 {
                margin-left: calc(3 * #{$spacer}) !important;
            }
        
            &-4 {
                margin-left: calc(4 * #{$spacer}) !important;
            }
        
            &-5 {
                margin-left: calc(5 * #{$spacer}) !important;
            }
        
            &-6 {
                margin-left: calc(6 * #{$spacer}) !important;
            }
        
            &-7 {
                margin-left: calc(7 * #{$spacer}) !important;
            }
        
            &-8 {
                margin-left: calc(8 * #{$spacer}) !important;
            }
        
            &-9 {
                margin-left: calc(9 * #{$spacer}) !important;
            }
        
            &-10 {
                margin-left: calc(10 * #{$spacer}) !important;
            }
        
            &-11 {
                margin-left: calc(11 * #{$spacer}) !important;
            }
        
            &-12 {
                margin-left: calc(12 * #{$spacer}) !important;
            }
        
            &-13 {
                margin-left: calc(13 * #{$spacer}) !important;
            }
        
            &-14 {
                margin-left: calc(14 * #{$spacer}) !important;
            }
        
            &-15 {
                margin-left: calc(15 * #{$spacer}) !important;
            }
        
            &-16 {
                margin-left: calc(16 * #{$spacer}) !important;
            }
        
            &-17 {
                margin-left: calc(17 * #{$spacer}) !important;
            }
        
            &-18 {
                margin-left: calc(18 * #{$spacer}) !important;
            }
        
            &-19 {
                margin-left: calc(19 * #{$spacer}) !important;
            }
        
            &-20 {
                margin-left: calc(20 * #{$spacer}) !important;
            }
        
            &-21 {
                margin-left: calc(21 * #{$spacer}) !important;
            }
        
            &-22 {
                margin-left: calc(22 * #{$spacer}) !important;
            }
        
            &-23 {
                margin-left: calc(23 * #{$spacer}) !important;
            }
        
            &-24 {
                margin-left: calc(24 * #{$spacer}) !important;
            }
        
            &-25 {
                margin-left: calc(25 * #{$spacer}) !important;
            }
        
            &-26 {
                margin-left: calc(26 * #{$spacer}) !important;
            }
        
            &-27 {
                margin-left: calc(27 * #{$spacer}) !important;
            }
        
            &-28 {
                margin-left: calc(28 * #{$spacer}) !important;
            }
        
            &-29 {
                margin-left: calc(29 * #{$spacer}) !important;
            }
        
            &-30 {
                margin-left: calc(30 * #{$spacer}) !important;
            }
        
            &-31 {
                margin-left: calc(31 * #{$spacer}) !important;
            }
        
            &-32 {
                margin-left: calc(32 * #{$spacer}) !important;
            }
        
            &-33 {
                margin-left: calc(33 * #{$spacer}) !important;
            }
        
            &-34 {
                margin-left: calc(34 * #{$spacer}) !important;
            }
        
            &-35 {
                margin-left: calc(35 * #{$spacer}) !important;
            }
        
            &-36 {
                margin-left: calc(36 * #{$spacer}) !important;
            }
        
            &-37 {
                margin-left: calc(37 * #{$spacer}) !important;
            }
        
            &-38 {
                margin-left: calc(38 * #{$spacer}) !important;
            }
        
            &-39 {
                margin-left: calc(39 * #{$spacer}) !important;
            }
        
            &-40 {
                margin-left: calc(40 * #{$spacer}) !important;
            }
        }
    }

    // XX Large
    @media (min-width: $xxl) {
        
        &-xxl {

            &-0 {
                margin-left: 0 !important;
            }
        
            &-1 {
                margin-left: $spacer !important;
            }
        
            &-2 {
                margin-left: calc(2 * #{$spacer}) !important;
            }
        
            &-3 {
                margin-left: calc(3 * #{$spacer}) !important;
            }
        
            &-4 {
                margin-left: calc(4 * #{$spacer}) !important;
            }
        
            &-5 {
                margin-left: calc(5 * #{$spacer}) !important;
            }
        
            &-6 {
                margin-left: calc(6 * #{$spacer}) !important;
            }
        
            &-7 {
                margin-left: calc(7 * #{$spacer}) !important;
            }
        
            &-8 {
                margin-left: calc(8 * #{$spacer}) !important;
            }
        
            &-9 {
                margin-left: calc(9 * #{$spacer}) !important;
            }
        
            &-10 {
                margin-left: calc(10 * #{$spacer}) !important;
            }
        
            &-11 {
                margin-left: calc(11 * #{$spacer}) !important;
            }
        
            &-12 {
                margin-left: calc(12 * #{$spacer}) !important;
            }
        
            &-13 {
                margin-left: calc(13 * #{$spacer}) !important;
            }
        
            &-14 {
                margin-left: calc(14 * #{$spacer}) !important;
            }
        
            &-15 {
                margin-left: calc(15 * #{$spacer}) !important;
            }
        
            &-16 {
                margin-left: calc(16 * #{$spacer}) !important;
            }
        
            &-17 {
                margin-left: calc(17 * #{$spacer}) !important;
            }
        
            &-18 {
                margin-left: calc(18 * #{$spacer}) !important;
            }
        
            &-19 {
                margin-left: calc(19 * #{$spacer}) !important;
            }
        
            &-20 {
                margin-left: calc(20 * #{$spacer}) !important;
            }
        
            &-21 {
                margin-left: calc(21 * #{$spacer}) !important;
            }
        
            &-22 {
                margin-left: calc(22 * #{$spacer}) !important;
            }
        
            &-23 {
                margin-left: calc(23 * #{$spacer}) !important;
            }
        
            &-24 {
                margin-left: calc(24 * #{$spacer}) !important;
            }
        
            &-25 {
                margin-left: calc(25 * #{$spacer}) !important;
            }
        
            &-26 {
                margin-left: calc(26 * #{$spacer}) !important;
            }
        
            &-27 {
                margin-left: calc(27 * #{$spacer}) !important;
            }
        
            &-28 {
                margin-left: calc(28 * #{$spacer}) !important;
            }
        
            &-29 {
                margin-left: calc(29 * #{$spacer}) !important;
            }
        
            &-30 {
                margin-left: calc(30 * #{$spacer}) !important;
            }
        
            &-31 {
                margin-left: calc(31 * #{$spacer}) !important;
            }
        
            &-32 {
                margin-left: calc(32 * #{$spacer}) !important;
            }
        
            &-33 {
                margin-left: calc(33 * #{$spacer}) !important;
            }
        
            &-34 {
                margin-left: calc(34 * #{$spacer}) !important;
            }
        
            &-35 {
                margin-left: calc(35 * #{$spacer}) !important;
            }
        
            &-36 {
                margin-left: calc(36 * #{$spacer}) !important;
            }
        
            &-37 {
                margin-left: calc(37 * #{$spacer}) !important;
            }
        
            &-38 {
                margin-left: calc(38 * #{$spacer}) !important;
            }
        
            &-39 {
                margin-left: calc(39 * #{$spacer}) !important;
            }
        
            &-40 {
                margin-left: calc(40 * #{$spacer}) !important;
            }
        }
    }
}