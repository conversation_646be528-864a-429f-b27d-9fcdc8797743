
// Margin Bottom
.mb {

    // Default
    &-0 {
        margin-bottom: 0 !important;
    }

    &-1 {
        margin-bottom: $spacer !important;
    }

    &-2 {
        margin-bottom: calc(2 * #{$spacer}) !important;
    }

    &-3 {
        margin-bottom: calc(3 * #{$spacer}) !important;
    }

    &-4 {
        margin-bottom: calc(4 * #{$spacer}) !important;
    }

    &-5 {
        margin-bottom: calc(5 * #{$spacer}) !important;
    }

    &-6 {
        margin-bottom: calc(6 * #{$spacer}) !important;
    }

    &-7 {
        margin-bottom: calc(7 * #{$spacer}) !important;
    }

    &-8 {
        margin-bottom: calc(8 * #{$spacer}) !important;
    }

    &-9 {
        margin-bottom: calc(9 * #{$spacer}) !important;
    }

    &-10 {
        margin-bottom: calc(10 * #{$spacer}) !important;
    }

    &-11 {
        margin-bottom: calc(11 * #{$spacer}) !important;
    }

    &-12 {
        margin-bottom: calc(12 * #{$spacer}) !important;
    }

    &-13 {
        margin-bottom: calc(13 * #{$spacer}) !important;
    }

    &-14 {
        margin-bottom: calc(14 * #{$spacer}) !important;
    }

    &-15 {
        margin-bottom: calc(15 * #{$spacer}) !important;
    }

    &-16 {
        margin-bottom: calc(16 * #{$spacer}) !important;
    }

    &-17 {
        margin-bottom: calc(17 * #{$spacer}) !important;
    }

    &-18 {
        margin-bottom: calc(18 * #{$spacer}) !important;
    }

    &-19 {
        margin-bottom: calc(19 * #{$spacer}) !important;
    }

    &-20 {
        margin-bottom: calc(20 * #{$spacer}) !important;
    }

    &-21 {
        margin-bottom: calc(21 * #{$spacer}) !important;
    }

    &-22 {
        margin-bottom: calc(22 * #{$spacer}) !important;
    }

    &-23 {
        margin-bottom: calc(23 * #{$spacer}) !important;
    }

    &-24 {
        margin-bottom: calc(24 * #{$spacer}) !important;
    }

    &-25 {
        margin-bottom: calc(25 * #{$spacer}) !important;
    }

    &-26 {
        margin-bottom: calc(26 * #{$spacer}) !important;
    }

    &-27 {
        margin-bottom: calc(27 * #{$spacer}) !important;
    }

    &-28 {
        margin-bottom: calc(28 * #{$spacer}) !important;
    }

    &-29 {
        margin-bottom: calc(29 * #{$spacer}) !important;
    }

    &-30 {
        margin-bottom: calc(30 * #{$spacer}) !important;
    }

    &-31 {
        margin-bottom: calc(31 * #{$spacer}) !important;
    }

    &-32 {
        margin-bottom: calc(32 * #{$spacer}) !important;
    }

    &-33 {
        margin-bottom: calc(33 * #{$spacer}) !important;
    }

    &-34 {
        margin-bottom: calc(34 * #{$spacer}) !important;
    }

    &-35 {
        margin-bottom: calc(35 * #{$spacer}) !important;
    }

    &-36 {
        margin-bottom: calc(36 * #{$spacer}) !important;
    }

    &-37 {
        margin-bottom: calc(37 * #{$spacer}) !important;
    }

    &-38 {
        margin-bottom: calc(38 * #{$spacer}) !important;
    }

    &-39 {
        margin-bottom: calc(39 * #{$spacer}) !important;
    }

    &-40 {
        margin-bottom: calc(40 * #{$spacer}) !important;
    }

    // Small
    @media (min-width: $sm) {
        
        &-sm {

            &-0 {
                margin-bottom: 0 !important;
            }
        
            &-1 {
                margin-bottom: $spacer !important;
            }
        
            &-2 {
                margin-bottom: calc(2 * #{$spacer}) !important;
            }
        
            &-3 {
                margin-bottom: calc(3 * #{$spacer}) !important;
            }
        
            &-4 {
                margin-bottom: calc(4 * #{$spacer}) !important;
            }
        
            &-5 {
                margin-bottom: calc(5 * #{$spacer}) !important;
            }
        
            &-6 {
                margin-bottom: calc(6 * #{$spacer}) !important;
            }
        
            &-7 {
                margin-bottom: calc(7 * #{$spacer}) !important;
            }
        
            &-8 {
                margin-bottom: calc(8 * #{$spacer}) !important;
            }
        
            &-9 {
                margin-bottom: calc(9 * #{$spacer}) !important;
            }
        
            &-10 {
                margin-bottom: calc(10 * #{$spacer}) !important;
            }
        
            &-11 {
                margin-bottom: calc(11 * #{$spacer}) !important;
            }
        
            &-12 {
                margin-bottom: calc(12 * #{$spacer}) !important;
            }
        
            &-13 {
                margin-bottom: calc(13 * #{$spacer}) !important;
            }
        
            &-14 {
                margin-bottom: calc(14 * #{$spacer}) !important;
            }
        
            &-15 {
                margin-bottom: calc(15 * #{$spacer}) !important;
            }
        
            &-16 {
                margin-bottom: calc(16 * #{$spacer}) !important;
            }
        
            &-17 {
                margin-bottom: calc(17 * #{$spacer}) !important;
            }
        
            &-18 {
                margin-bottom: calc(18 * #{$spacer}) !important;
            }
        
            &-19 {
                margin-bottom: calc(19 * #{$spacer}) !important;
            }
        
            &-20 {
                margin-bottom: calc(20 * #{$spacer}) !important;
            }
        
            &-21 {
                margin-bottom: calc(21 * #{$spacer}) !important;
            }
        
            &-22 {
                margin-bottom: calc(22 * #{$spacer}) !important;
            }
        
            &-23 {
                margin-bottom: calc(23 * #{$spacer}) !important;
            }
        
            &-24 {
                margin-bottom: calc(24 * #{$spacer}) !important;
            }
        
            &-25 {
                margin-bottom: calc(25 * #{$spacer}) !important;
            }
        
            &-26 {
                margin-bottom: calc(26 * #{$spacer}) !important;
            }
        
            &-27 {
                margin-bottom: calc(27 * #{$spacer}) !important;
            }
        
            &-28 {
                margin-bottom: calc(28 * #{$spacer}) !important;
            }
        
            &-29 {
                margin-bottom: calc(29 * #{$spacer}) !important;
            }
        
            &-30 {
                margin-bottom: calc(30 * #{$spacer}) !important;
            }
        
            &-31 {
                margin-bottom: calc(31 * #{$spacer}) !important;
            }
        
            &-32 {
                margin-bottom: calc(32 * #{$spacer}) !important;
            }
        
            &-33 {
                margin-bottom: calc(33 * #{$spacer}) !important;
            }
        
            &-34 {
                margin-bottom: calc(34 * #{$spacer}) !important;
            }
        
            &-35 {
                margin-bottom: calc(35 * #{$spacer}) !important;
            }
        
            &-36 {
                margin-bottom: calc(36 * #{$spacer}) !important;
            }
        
            &-37 {
                margin-bottom: calc(37 * #{$spacer}) !important;
            }
        
            &-38 {
                margin-bottom: calc(38 * #{$spacer}) !important;
            }
        
            &-39 {
                margin-bottom: calc(39 * #{$spacer}) !important;
            }
        
            &-40 {
                margin-bottom: calc(40 * #{$spacer}) !important;
            }
        }
    }

    // Medium
    @media (min-width: $md) {
        
        &-md {

            &-0 {
                margin-bottom: 0 !important;
            }
        
            &-1 {
                margin-bottom: $spacer !important;
            }
        
            &-2 {
                margin-bottom: calc(2 * #{$spacer}) !important;
            }
        
            &-3 {
                margin-bottom: calc(3 * #{$spacer}) !important;
            }
        
            &-4 {
                margin-bottom: calc(4 * #{$spacer}) !important;
            }
        
            &-5 {
                margin-bottom: calc(5 * #{$spacer}) !important;
            }
        
            &-6 {
                margin-bottom: calc(6 * #{$spacer}) !important;
            }
        
            &-7 {
                margin-bottom: calc(7 * #{$spacer}) !important;
            }
        
            &-8 {
                margin-bottom: calc(8 * #{$spacer}) !important;
            }
        
            &-9 {
                margin-bottom: calc(9 * #{$spacer}) !important;
            }
        
            &-10 {
                margin-bottom: calc(10 * #{$spacer}) !important;
            }
        
            &-11 {
                margin-bottom: calc(11 * #{$spacer}) !important;
            }
        
            &-12 {
                margin-bottom: calc(12 * #{$spacer}) !important;
            }
        
            &-13 {
                margin-bottom: calc(13 * #{$spacer}) !important;
            }
        
            &-14 {
                margin-bottom: calc(14 * #{$spacer}) !important;
            }
        
            &-15 {
                margin-bottom: calc(15 * #{$spacer}) !important;
            }
        
            &-16 {
                margin-bottom: calc(16 * #{$spacer}) !important;
            }
        
            &-17 {
                margin-bottom: calc(17 * #{$spacer}) !important;
            }
        
            &-18 {
                margin-bottom: calc(18 * #{$spacer}) !important;
            }
        
            &-19 {
                margin-bottom: calc(19 * #{$spacer}) !important;
            }
        
            &-20 {
                margin-bottom: calc(20 * #{$spacer}) !important;
            }
        
            &-21 {
                margin-bottom: calc(21 * #{$spacer}) !important;
            }
        
            &-22 {
                margin-bottom: calc(22 * #{$spacer}) !important;
            }
        
            &-23 {
                margin-bottom: calc(23 * #{$spacer}) !important;
            }
        
            &-24 {
                margin-bottom: calc(24 * #{$spacer}) !important;
            }
        
            &-25 {
                margin-bottom: calc(25 * #{$spacer}) !important;
            }
        
            &-26 {
                margin-bottom: calc(26 * #{$spacer}) !important;
            }
        
            &-27 {
                margin-bottom: calc(27 * #{$spacer}) !important;
            }
        
            &-28 {
                margin-bottom: calc(28 * #{$spacer}) !important;
            }
        
            &-29 {
                margin-bottom: calc(29 * #{$spacer}) !important;
            }
        
            &-30 {
                margin-bottom: calc(30 * #{$spacer}) !important;
            }
        
            &-31 {
                margin-bottom: calc(31 * #{$spacer}) !important;
            }
        
            &-32 {
                margin-bottom: calc(32 * #{$spacer}) !important;
            }
        
            &-33 {
                margin-bottom: calc(33 * #{$spacer}) !important;
            }
        
            &-34 {
                margin-bottom: calc(34 * #{$spacer}) !important;
            }
        
            &-35 {
                margin-bottom: calc(35 * #{$spacer}) !important;
            }
        
            &-36 {
                margin-bottom: calc(36 * #{$spacer}) !important;
            }
        
            &-37 {
                margin-bottom: calc(37 * #{$spacer}) !important;
            }
        
            &-38 {
                margin-bottom: calc(38 * #{$spacer}) !important;
            }
        
            &-39 {
                margin-bottom: calc(39 * #{$spacer}) !important;
            }
        
            &-40 {
                margin-bottom: calc(40 * #{$spacer}) !important;
            }
        }
    }

    // Large
    @media (min-width: $lg) {
        
        &-lg {

            &-0 {
                margin-bottom: 0 !important;
            }
        
            &-1 {
                margin-bottom: $spacer !important;
            }
        
            &-2 {
                margin-bottom: calc(2 * #{$spacer}) !important;
            }
        
            &-3 {
                margin-bottom: calc(3 * #{$spacer}) !important;
            }
        
            &-4 {
                margin-bottom: calc(4 * #{$spacer}) !important;
            }
        
            &-5 {
                margin-bottom: calc(5 * #{$spacer}) !important;
            }
        
            &-6 {
                margin-bottom: calc(6 * #{$spacer}) !important;
            }
        
            &-7 {
                margin-bottom: calc(7 * #{$spacer}) !important;
            }
        
            &-8 {
                margin-bottom: calc(8 * #{$spacer}) !important;
            }
        
            &-9 {
                margin-bottom: calc(9 * #{$spacer}) !important;
            }
        
            &-10 {
                margin-bottom: calc(10 * #{$spacer}) !important;
            }
        
            &-11 {
                margin-bottom: calc(11 * #{$spacer}) !important;
            }
        
            &-12 {
                margin-bottom: calc(12 * #{$spacer}) !important;
            }
        
            &-13 {
                margin-bottom: calc(13 * #{$spacer}) !important;
            }
        
            &-14 {
                margin-bottom: calc(14 * #{$spacer}) !important;
            }
        
            &-15 {
                margin-bottom: calc(15 * #{$spacer}) !important;
            }
        
            &-16 {
                margin-bottom: calc(16 * #{$spacer}) !important;
            }
        
            &-17 {
                margin-bottom: calc(17 * #{$spacer}) !important;
            }
        
            &-18 {
                margin-bottom: calc(18 * #{$spacer}) !important;
            }
        
            &-19 {
                margin-bottom: calc(19 * #{$spacer}) !important;
            }
        
            &-20 {
                margin-bottom: calc(20 * #{$spacer}) !important;
            }
        
            &-21 {
                margin-bottom: calc(21 * #{$spacer}) !important;
            }
        
            &-22 {
                margin-bottom: calc(22 * #{$spacer}) !important;
            }
        
            &-23 {
                margin-bottom: calc(23 * #{$spacer}) !important;
            }
        
            &-24 {
                margin-bottom: calc(24 * #{$spacer}) !important;
            }
        
            &-25 {
                margin-bottom: calc(25 * #{$spacer}) !important;
            }
        
            &-26 {
                margin-bottom: calc(26 * #{$spacer}) !important;
            }
        
            &-27 {
                margin-bottom: calc(27 * #{$spacer}) !important;
            }
        
            &-28 {
                margin-bottom: calc(28 * #{$spacer}) !important;
            }
        
            &-29 {
                margin-bottom: calc(29 * #{$spacer}) !important;
            }
        
            &-30 {
                margin-bottom: calc(30 * #{$spacer}) !important;
            }
        
            &-31 {
                margin-bottom: calc(31 * #{$spacer}) !important;
            }
        
            &-32 {
                margin-bottom: calc(32 * #{$spacer}) !important;
            }
        
            &-33 {
                margin-bottom: calc(33 * #{$spacer}) !important;
            }
        
            &-34 {
                margin-bottom: calc(34 * #{$spacer}) !important;
            }
        
            &-35 {
                margin-bottom: calc(35 * #{$spacer}) !important;
            }
        
            &-36 {
                margin-bottom: calc(36 * #{$spacer}) !important;
            }
        
            &-37 {
                margin-bottom: calc(37 * #{$spacer}) !important;
            }
        
            &-38 {
                margin-bottom: calc(38 * #{$spacer}) !important;
            }
        
            &-39 {
                margin-bottom: calc(39 * #{$spacer}) !important;
            }
        
            &-40 {
                margin-bottom: calc(40 * #{$spacer}) !important;
            }
        }
    }

    // X Large
    @media (min-width: $xl) {
        
        &-xl {

            &-0 {
                margin-bottom: 0 !important;
            }
        
            &-1 {
                margin-bottom: $spacer !important;
            }
        
            &-2 {
                margin-bottom: calc(2 * #{$spacer}) !important;
            }
        
            &-3 {
                margin-bottom: calc(3 * #{$spacer}) !important;
            }
        
            &-4 {
                margin-bottom: calc(4 * #{$spacer}) !important;
            }
        
            &-5 {
                margin-bottom: calc(5 * #{$spacer}) !important;
            }
        
            &-6 {
                margin-bottom: calc(6 * #{$spacer}) !important;
            }
        
            &-7 {
                margin-bottom: calc(7 * #{$spacer}) !important;
            }
        
            &-8 {
                margin-bottom: calc(8 * #{$spacer}) !important;
            }
        
            &-9 {
                margin-bottom: calc(9 * #{$spacer}) !important;
            }
        
            &-10 {
                margin-bottom: calc(10 * #{$spacer}) !important;
            }
        
            &-11 {
                margin-bottom: calc(11 * #{$spacer}) !important;
            }
        
            &-12 {
                margin-bottom: calc(12 * #{$spacer}) !important;
            }
        
            &-13 {
                margin-bottom: calc(13 * #{$spacer}) !important;
            }
        
            &-14 {
                margin-bottom: calc(14 * #{$spacer}) !important;
            }
        
            &-15 {
                margin-bottom: calc(15 * #{$spacer}) !important;
            }
        
            &-16 {
                margin-bottom: calc(16 * #{$spacer}) !important;
            }
        
            &-17 {
                margin-bottom: calc(17 * #{$spacer}) !important;
            }
        
            &-18 {
                margin-bottom: calc(18 * #{$spacer}) !important;
            }
        
            &-19 {
                margin-bottom: calc(19 * #{$spacer}) !important;
            }
        
            &-20 {
                margin-bottom: calc(20 * #{$spacer}) !important;
            }
        
            &-21 {
                margin-bottom: calc(21 * #{$spacer}) !important;
            }
        
            &-22 {
                margin-bottom: calc(22 * #{$spacer}) !important;
            }
        
            &-23 {
                margin-bottom: calc(23 * #{$spacer}) !important;
            }
        
            &-24 {
                margin-bottom: calc(24 * #{$spacer}) !important;
            }
        
            &-25 {
                margin-bottom: calc(25 * #{$spacer}) !important;
            }
        
            &-26 {
                margin-bottom: calc(26 * #{$spacer}) !important;
            }
        
            &-27 {
                margin-bottom: calc(27 * #{$spacer}) !important;
            }
        
            &-28 {
                margin-bottom: calc(28 * #{$spacer}) !important;
            }
        
            &-29 {
                margin-bottom: calc(29 * #{$spacer}) !important;
            }
        
            &-30 {
                margin-bottom: calc(30 * #{$spacer}) !important;
            }
        
            &-31 {
                margin-bottom: calc(31 * #{$spacer}) !important;
            }
        
            &-32 {
                margin-bottom: calc(32 * #{$spacer}) !important;
            }
        
            &-33 {
                margin-bottom: calc(33 * #{$spacer}) !important;
            }
        
            &-34 {
                margin-bottom: calc(34 * #{$spacer}) !important;
            }
        
            &-35 {
                margin-bottom: calc(35 * #{$spacer}) !important;
            }
        
            &-36 {
                margin-bottom: calc(36 * #{$spacer}) !important;
            }
        
            &-37 {
                margin-bottom: calc(37 * #{$spacer}) !important;
            }
        
            &-38 {
                margin-bottom: calc(38 * #{$spacer}) !important;
            }
        
            &-39 {
                margin-bottom: calc(39 * #{$spacer}) !important;
            }
        
            &-40 {
                margin-bottom: calc(40 * #{$spacer}) !important;
            }
        }
    }

    // XX Large
    @media (min-width: $xxl) {
        
        &-xxl {

            &-0 {
                margin-bottom: 0 !important;
            }
        
            &-1 {
                margin-bottom: $spacer !important;
            }
        
            &-2 {
                margin-bottom: calc(2 * #{$spacer}) !important;
            }
        
            &-3 {
                margin-bottom: calc(3 * #{$spacer}) !important;
            }
        
            &-4 {
                margin-bottom: calc(4 * #{$spacer}) !important;
            }
        
            &-5 {
                margin-bottom: calc(5 * #{$spacer}) !important;
            }
        
            &-6 {
                margin-bottom: calc(6 * #{$spacer}) !important;
            }
        
            &-7 {
                margin-bottom: calc(7 * #{$spacer}) !important;
            }
        
            &-8 {
                margin-bottom: calc(8 * #{$spacer}) !important;
            }
        
            &-9 {
                margin-bottom: calc(9 * #{$spacer}) !important;
            }
        
            &-10 {
                margin-bottom: calc(10 * #{$spacer}) !important;
            }
        
            &-11 {
                margin-bottom: calc(11 * #{$spacer}) !important;
            }
        
            &-12 {
                margin-bottom: calc(12 * #{$spacer}) !important;
            }
        
            &-13 {
                margin-bottom: calc(13 * #{$spacer}) !important;
            }
        
            &-14 {
                margin-bottom: calc(14 * #{$spacer}) !important;
            }
        
            &-15 {
                margin-bottom: calc(15 * #{$spacer}) !important;
            }
        
            &-16 {
                margin-bottom: calc(16 * #{$spacer}) !important;
            }
        
            &-17 {
                margin-bottom: calc(17 * #{$spacer}) !important;
            }
        
            &-18 {
                margin-bottom: calc(18 * #{$spacer}) !important;
            }
        
            &-19 {
                margin-bottom: calc(19 * #{$spacer}) !important;
            }
        
            &-20 {
                margin-bottom: calc(20 * #{$spacer}) !important;
            }
        
            &-21 {
                margin-bottom: calc(21 * #{$spacer}) !important;
            }
        
            &-22 {
                margin-bottom: calc(22 * #{$spacer}) !important;
            }
        
            &-23 {
                margin-bottom: calc(23 * #{$spacer}) !important;
            }
        
            &-24 {
                margin-bottom: calc(24 * #{$spacer}) !important;
            }
        
            &-25 {
                margin-bottom: calc(25 * #{$spacer}) !important;
            }
        
            &-26 {
                margin-bottom: calc(26 * #{$spacer}) !important;
            }
        
            &-27 {
                margin-bottom: calc(27 * #{$spacer}) !important;
            }
        
            &-28 {
                margin-bottom: calc(28 * #{$spacer}) !important;
            }
        
            &-29 {
                margin-bottom: calc(29 * #{$spacer}) !important;
            }
        
            &-30 {
                margin-bottom: calc(30 * #{$spacer}) !important;
            }
        
            &-31 {
                margin-bottom: calc(31 * #{$spacer}) !important;
            }
        
            &-32 {
                margin-bottom: calc(32 * #{$spacer}) !important;
            }
        
            &-33 {
                margin-bottom: calc(33 * #{$spacer}) !important;
            }
        
            &-34 {
                margin-bottom: calc(34 * #{$spacer}) !important;
            }
        
            &-35 {
                margin-bottom: calc(35 * #{$spacer}) !important;
            }
        
            &-36 {
                margin-bottom: calc(36 * #{$spacer}) !important;
            }
        
            &-37 {
                margin-bottom: calc(37 * #{$spacer}) !important;
            }
        
            &-38 {
                margin-bottom: calc(38 * #{$spacer}) !important;
            }
        
            &-39 {
                margin-bottom: calc(39 * #{$spacer}) !important;
            }
        
            &-40 {
                margin-bottom: calc(40 * #{$spacer}) !important;
            }
        }
    }
}