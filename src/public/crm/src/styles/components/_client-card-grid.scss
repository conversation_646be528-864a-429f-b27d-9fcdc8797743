$gap: $spacer * 3;
$gapDiff: $gap / 2;

$borderRadius: 3.6px;

.grid-container {

    &-items {

        &-active {
            display: flex;
            flex-wrap: wrap;
            row-gap: $gap;
            column-gap: $gap;
            align-items: normal;
            justify-content: flex-start;
        }

        &-column {
            display: block;
            flex-grow: 1;
            width: 100%;
            max-width: 100%;

            @media (min-width: $md) {
                max-width: calc(50% - #{$gapDiff});
            }

            @media (min-width: $xl) {
                max-width: calc((100% / 3) - (#{$gapDiff} + 3px));
            }

            &-sortable {
                display: block;
                width: 100%;
                height: 100%;

                & > .grid-stack-item {
                    margin-top: $gap;
                    position: relative;
                    left: initial !important;
                    top: initial !important;
                    border-radius: $borderRadius !important;
                    overflow: hidden;
                    max-width: 100% !important;

                    &[draggable="true"] {
                        cursor: grab;

                        .widget-title {
                            display: block !important;
                        }
                    }
                }
            }
        }

        &-placeholder {
            display: block;
            margin-top: $gap;
            background-color: #fafafa !important;
            border: 5px dashed #eeeeee !important;
            box-sizing: border-box;
            width: 100% !important;
            height: 200px;
        }
    }
}