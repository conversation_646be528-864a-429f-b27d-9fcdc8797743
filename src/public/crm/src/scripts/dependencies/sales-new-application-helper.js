class SalesNewApplicationHelper {

    constructor() {

        this.isTest = false;
        this.initted = false;

        this.interval = {
            newGuarant: {
                handler: {},
                method: (data) => {

                    const selector = `[data-field-id="guarant"] [data-seqnum="${data.seq_num}"]`;
                    const newGuarantContainer = jQuery(selector);

                    if (newGuarantContainer.length === 0) { return undefined; }

                    Object.keys(data).forEach((key, index) => {
                            
                        const value = data[key];
                        window.SalesNewApplicationHelper.prefillData(value, key, 'guarantSingle', false, data, false);
                    });

                    if (typeof window.SalesNewApplicationHelper.interval.newGuarant.handler[data.seq_num] !== 'undefined') {

                        clearInterval(window.SalesNewApplicationHelper.interval.newGuarant.handler[data.seq_num]);
                        delete window.SalesNewApplicationHelper.interval.newGuarant.handler[data.seq_num];
                    }
                },
                time: 36,
            },
            newContact: {
                handler: {},
                method: (data) => {

                    const newContactContainer = jQuery(`[data-field-id="contact"] [data-seqnum="${data.orderNumber}"]`);

                    if (newContactContainer.length === 0) { return undefined; }

                    Object.keys(data).forEach((key, index) => {
                            
                        const value = data[key];
                        window.SalesNewApplicationHelper.prefillData(value, key, 'contactSingle', false, data, false);
                    });

                    if (typeof window.SalesNewApplicationHelper.interval.newContact.handler[data.orderNumber] !== 'undefined') {
                    
                        clearInterval(window.SalesNewApplicationHelper.interval.newContact.handler[data.orderNumber]);
                        delete window.SalesNewApplicationHelper.interval.newContact.handler[data.orderNumber];
                    }
                },
                time: 36,
            },
        };

        this.nodes = {
            input: {
                loan: {
                    'amount_requested': (data, element) => {
                        
                        element.val(data);

                        window.SalesNewApplicationHelper.triggerLoanChange();
                    },
                    'period_requested': (data, element) => {
                        
                        element.val(data);

                        window.SalesNewApplicationHelper.triggerLoanChange();
                    },
                    'discount_percent': (data, element) => {
                        
                        element.val(data);

                        window.SalesNewApplicationHelper.triggerLoanChange();
                    },
                },
            },
            select: {
                address: {
                    'city_id': (data) => {

                        const currentAddressBtn = window.SalesNewApplicationHelper.parent.domElements.global.addCurrentAddressButton;
                        const clientCurrentCitySelect = (
                            window.SalesNewApplicationHelper.parent.activeCardType === 'person' ?
                            window.SalesNewApplicationHelper.parent.domElements.global.clientCurrentCityIdSelector :
                            window.SalesNewApplicationHelper.parent.domElements.global.clientCurrentCityIdSelectorCompany
                        );

                        if (!data) {
                          
                            if (window.SalesNewApplicationHelper.parent.activeCardType === 'person') {
                                currentAddressBtn.attr('aria-expanded') === 'true'
                                && currentAddressBtn.click();
                            }

                            clientCurrentCitySelect.empty();
                            return;
                        }

                        clientCurrentCitySelect[0].setAttribute(
                            'data-city-id', data
                        )
    
                        clientCurrentCitySelect[0].dispatchEvent(
                            new Event('appendData')
                        );

                        if (window.SalesNewApplicationHelper.parent.activeCardType === 'person') {
                            if (currentAddressBtn.attr('aria-expanded') !== 'true') {
                                currentAddressBtn.trigger('click');
                            }
                        }
                    },
                },
            },
            textarea: {},
            div: {
                client: {
                    'city_id': (data) => {

                        const clientIdCardCitySelect = window.SalesNewApplicationHelper.parent.domElements.global.clientIdCardCityId;

                        if (clientIdCardCitySelect.length === 0) { return undefined; }

                        clientIdCardCitySelect[0].setAttribute(
                            'data-city-id', data
                        )

                        clientIdCardCitySelect[0].dispatchEvent(
                            new Event('appendData')
                        );
                    },
                },
                contact: {
                    'contact': (data) => {

                        // data.seq_num = data.seq_num > 0 ? data.seq_num : 1;

                        if (data.orderNumber > 2) {
                            
                            const newContactContainer = jQuery(`[data-field-id="contact"] [data-seqnum="${data.orderNumber}"]`);
                            const addNewContactButton = window.SalesNewApplicationHelper.parent.domElements.global.addNewContactButton;
                            
                            if (
                                newContactContainer.length === 0 &&
                                addNewContactButton.length > 0
                            ) {
                                addNewContactButton.trigger('click');
                            }
                        }

                        const handler = setInterval(
                            () => {
                                window.SalesNewApplicationHelper.interval.newContact.method(data);
                            },
                            window.SalesNewApplicationHelper.interval.newContact.time
                        );

                        window.SalesNewApplicationHelper.interval.newContact.handler[data.orderNumber] = handler;
                    },
                },
                guarant: {
                    'guarant': (data) => {
                        // data.pivot.seq_num = data.pivot.seq_num > 0 ? data.pivot.seq_num : 1;

                        if (
                            data.pivot.seq_num > 0 &&
                            window.SalesNewApplicationHelper.metaDataObject.guarant.seqNum !== data.pivot.seq_num
                        ) {

                            const addNewGuarantButton = window.SalesNewApplicationHelper.parent.domElements.global.addNewGuarantButton;

                            if (addNewGuarantButton.length > 0) {
                                addNewGuarantButton.trigger('click');
                            }

                            data.pivot.seq_num = window.SalesNewApplicationHelper.metaDataObject.guarant.seqNum;
                            data.guarant_id = data.pivot.guarant_id;
                            data.client_id = data.pivot.client_id;
                            data.seq_num = data.pivot.seq_num;

                            const handler = setInterval(
                                () => {
                                    window.SalesNewApplicationHelper.interval.newGuarant.method(data);
                                },
                                window.SalesNewApplicationHelper.interval.newGuarant.time
                            );

                            window.SalesNewApplicationHelper.interval.newGuarant.handler[data.seq_num] = handler;
                        }
                    },
                },
            },
        };

        this.selectorMap = {
            address: {
                'city_id': 'clientCurrent\\[city_id\\]',
                'address': 'clientCurrent\\[address\\]',
            },
            contact: {
                'contact': (parentKey, data) => {
                    return `[data-field-id="contact"]`;
                },
            },
            contactSingle: (parentKey, data) => {
                const singleMap = {
                    contact_id: '[data-field-id="contact"] #contactId',
                };

                const selectorWorkable = (
                    typeof singleMap[parentKey] !== 'undefined' ?
                    singleMap[parentKey] :
                    `[data-field-id="contact"] [data-seqnum="${data.orderNumber}"] #contact_${parentKey.replace('contact_', '')}`
                );
                
                return selectorWorkable;
            },
            bank: {
                'iban': (parentKey, data) => {
                    return `[data-field-id="loan"] #iban`;
                },
            },
            loan: {
                'payment_method_id': 'payment_method',
                'comment': 'new_application_comment',
                'amount_requested': (parentKey, data) => {
                    return `#loan-product-id[value="${data.product_id}"] ~ #loan-sliders #loan_sum`;
                },
                'period_requested': (parentKey, data) => {
                    return `#loan-product-id[value="${data.product_id}"] ~ #loan-sliders #loan_period`;
                },
                'discount_percent': (parentKey, data) => {
                    return `#loan-product-id[value="${data.product_id}"] ~ #loan-sliders #loan_discount`;
                },
            },
            guarant: {
                'guarant': (parentKey, data) => {

                    return `[data-field-id="guarant"]`;
                },
            },
            guarantSingle: (parentKey, data) => {

                const singleMap = {
                    client_id: '[data-field-id="guarant"] #guarantIdClientId',
                    guarant_id: '[data-field-id="guarant"] #guarantId',
                };

                const selectorWorkable = (
                    typeof singleMap[parentKey] !== 'undefined' ?
                    singleMap[parentKey] :
                    `[data-field-id="guarant"] [data-seqnum="${data.seq_num}"] #guarant_${parentKey.replace('guarant_', '')}`
                );

                return selectorWorkable;
            },
        };

        this.metaDataObject = {
            loan: {
                setOfficeIdState: false,
                setProductIdState: false,
                changeState: false,
                data: null,
                defaultSettings: null,
                readyFields: 0,
                meta: {
                    loanId: null,
                    newApplicationHref: null,
                },
            },
            pin: {
                changed: true,
                previousData: null,
            },
            contact: {
                seqNum: 2,
            },
            guarant: {
                seqNum: 0,
            },
        };

        this.purgerMap = {
            select: {
                address: {
                    'city_id': function(element) {

                        if (
                            typeof window.SalesNewApplicationHelper.parent.domElements.global.liveSearchCity === 'undefined' ||
                            !window.SalesNewApplicationHelper.parent.domElements.global.liveSearchCity ||
                            window.SalesNewApplicationHelper.parent.domElements.global.liveSearchCity.length === 0
                        ) { return undefined; }

                        window.SalesNewApplicationHelper.parent.domElements.global.liveSearchCity.each(function() {

                            const element = jQuery(this);

                            element
                                .val('Избери опция')
                                .selectpicker('refresh');
                        });
                    },
                },
                loan: {
                },
            },
            input: {
                loan: {
                    'amount_requested': function(element) {
                        element.val(window.SalesNewApplicationHelper.metaDataObject.loan.defaultSettings.default_amount);
                        window.SalesNewApplicationHelper.triggerLoanChange();
                    },
                    'period_requested': function(element) {
                        element.val(window.SalesNewApplicationHelper.metaDataObject.loan.defaultSettings.default_period);
                        window.SalesNewApplicationHelper.triggerLoanChange();
                    },
                    'discount_percent': function(element) {
                        element.val(0);
                        window.SalesNewApplicationHelper.triggerLoanChange();
                    },
                },
            },
        };

        this.elementsToPurge = [];

        this.dataHandlerMap = {
            loan: {
                'discount_percent': () => {
                    return 0;
                },
            },
        };
    }

    init(parent) {

        this.parent = parent;
    }

    getClientFromMvr(data, additional = {}) {

        window.SalesNewApplicationHelper.purgeElements();

        const restUrl = this.parent.config.rest.checkClientMvr;
        
        jQuery.ajax({
            type: 'GET',
            url: restUrl,
            data: data,
            success: (response) => {

                if (Object.keys(response).length === 0) { return undefined; }

                const fillableData = window.SalesNewApplicationHelper.prepareFillableDataMvr(response);
                Object.keys(fillableData).forEach(function(key, index) {

                    const value = fillableData[key];
                    window.SalesNewApplicationHelper.prefillData(value, key, key, true, value, false);
                });
            },
            error: (response) => {
                
                const idCardErrorContainer = window.SalesNewApplicationHelper.parent.domElements.global.idCardErrorContainer;

                const {message} = response.responseJSON;

                showErrorMessage(
                    idCardErrorContainer,
                    message,
                    window.SalesNewApplicationHelper.parent.config.newApplicationDefaultDelayTimeout
                );
            },
            complete: (response) => {

                window.SalesNewApplicationHelper.ajaxComplete(additional);
            }
        });
    }

    getClientAndLoanData(data, additional = {}) {

        window.SalesNewApplicationHelper.purgeElements();

        const restUrl = this.parent.config.rest.getClientLoanData;

        jQuery.ajax({
            type: 'GET',
            url: restUrl,
            data: data,
            success: (response) => {

                if (Object.keys(response).length === 0) { return undefined; }

                if (
                    typeof response.client.client_guarants !== 'undefined' &&
                    response.client.client_guarants.length > 0
                ) {
                    response.guarant = window.SalesNewApplicationHelper.fixGuarant(
                        JSON.parse(JSON.stringify(response.client.client_guarants))
                    );
                } else {

                    delete response.guarant;
                }

                response.contact = window.SalesNewApplicationHelper.fixContact(response.contact);

                window.SalesNewApplicationHelper.metaDataObject.loan.data = response;
                window.SalesNewApplicationHelper.setLoanOfficeId(response.loan.office_id);

                if (
                    !window.SalesNewApplicationHelper.metaDataObject.loan.setOfficeIdState &&
                    !window.SalesNewApplicationHelper.metaDataObject.loan.setProductIdState
                ) {

                    window.SalesNewApplicationHelper.setLoanProductId(response.loan.product_id);

                    if (!window.SalesNewApplicationHelper.metaDataObject.loan.setProductIdState) {

                        window.SalesNewApplicationHelper.initClientLoanDataPrefill(response);
                    }
                }
            },
            error: (response) => {
                
                const pinErrorContainer = (
                    typeof additional.pinErrorContainer !== 'undefined' ?
                    additional.pinErrorContainer :
                    window.SalesNewApplicationHelper.parent.domElements.global.pinErrorContainer
                );

                const {message} = response.responseJSON;

                showErrorMessage(
                    pinErrorContainer,
                    message,
                    window.SalesNewApplicationHelper.parent.config.newApplicationDefaultDelayTimeout
                );
            },
            complete: (response) => {

                window.SalesNewApplicationHelper.ajaxComplete(additional);
            }
        });
    }

    prefillData(data, uniqueKey = null, parentKey = null, dig = true, fullData = null, log = false) {

        if (
            !data &&
            typeof window.SalesNewApplicationHelper.dataHandlerMap[parentKey] !== 'undefined' &&
            typeof window.SalesNewApplicationHelper.dataHandlerMap[parentKey][uniqueKey] !== 'undefined'
        ) {

            data = window.SalesNewApplicationHelper.dataHandlerMap[parentKey][uniqueKey]();
        }

        if (
            (
                typeof data === 'undefined' ||
                !data
            ) &&
            data !== 0 
        ) { return undefined; }

        if (fullData) {

            fullData = JSON.parse(JSON.stringify(fullData));
        }

        if (
            typeof data === 'object' &&
            isNaN(parseInt(uniqueKey))
        ) {

            if (!dig) { return undefined; }
            
            Object.keys(data).forEach((key, index) => {

                const parentKeyWorkable = (
                    parentKey ?
                    parentKey :
                    uniqueKey
                );

                const dataValue = JSON.parse(JSON.stringify(data[key]));

                const uniqueKeyWorkable = key;
                
                window.SalesNewApplicationHelper.prefillData(dataValue, uniqueKeyWorkable, parentKeyWorkable, false, fullData, false);
            });

            return undefined;
        } else if (!isNaN(parseInt(uniqueKey))) {
            
            uniqueKey = parentKey;
        }
        
        const mappedUniqueKey = (
            typeof window.SalesNewApplicationHelper.selectorMap[parentKey] !== 'undefined' &&
            typeof window.SalesNewApplicationHelper.selectorMap[parentKey] !== 'function' &&
            typeof window.SalesNewApplicationHelper.selectorMap[parentKey][uniqueKey] !== 'undefined' ?
            window.SalesNewApplicationHelper.selectorMap[parentKey][uniqueKey] :
            (
                typeof window.SalesNewApplicationHelper.selectorMap[parentKey] === 'function' &&
                (
                    typeof window.SalesNewApplicationHelper.selectorMap[parentKey][uniqueKey] === 'undefined' ||
                    uniqueKey === 'name'
                ) ?
                window.SalesNewApplicationHelper.selectorMap[parentKey] :
                uniqueKey
            )
        );

        const extender = {
            selector: '',
        };

        if (
            parentKey === 'client' ||
            parentKey === 'address'
        ) {

            extender.selector = `[data-card-type="${window.SalesNewApplicationHelper.parent.activeCardType}"] `;
        }

        const selector = (
            extender.selector +
            (
                typeof mappedUniqueKey === 'function' ?
                `${mappedUniqueKey(uniqueKey, fullData)}` :
                `[data-field-id="${parentKey}"] #${mappedUniqueKey}`
            )
        );

        const element = jQuery(selector);

        if (element.length > 0) {

            // Get Node Name
            const nodeName = element[0].nodeName.toLowerCase();

            // Get Conversion Mechanism
            const conversion = (
                typeof window.SalesNewApplicationHelper.nodes[nodeName] !== 'undefined' &&
                typeof window.SalesNewApplicationHelper.nodes[nodeName][parentKey] !== 'undefined' &&
                typeof window.SalesNewApplicationHelper.nodes[nodeName][parentKey][uniqueKey] !== 'undefined' ?
                window.SalesNewApplicationHelper.nodes[nodeName][parentKey][uniqueKey] :
                null
            );

            // Get Purger
            const purger = (
                typeof window.SalesNewApplicationHelper.purgerMap[nodeName] !== 'undefined' &&
                typeof window.SalesNewApplicationHelper.purgerMap[nodeName][parentKey] !== 'undefined' &&
                typeof window.SalesNewApplicationHelper.purgerMap[nodeName][parentKey][uniqueKey] !== 'undefined' ?
                window.SalesNewApplicationHelper.purgerMap[nodeName][parentKey][uniqueKey] :
                function(element) {

                    element.val('').trigger('change');
                }
            );

            // Set Element for purging
            window.SalesNewApplicationHelper.registerElementForPurging(element, purger);

            // Convert
            if (conversion) {
                
                conversion(data, element);
            } else { // Set value without conversion

                element.val(data).trigger('change');
            }
        }

        return undefined;
    }

    wasPinChanged() {

        return this.metaDataObject.pin.changed;
    }

    setPinChange(change = false, value = null) {

        this.metaDataObject.pin.changed = change;

        if (value) {
            this.metaDataObject.pin.previousData = value;
        }
    }

    registerElementForPurging(element, mechanism) {

        const purgeObject = {
            element: element,
            mechanism: mechanism
        };

        this.elementsToPurge.push(purgeObject);
    }

    purgeElements() {

        if (
            typeof this.elementsToPurge === 'undefined' ||
            !this.elementsToPurge ||
            this.elementsToPurge.length === 0
        ) { return undefined; }

        clearRefinancedData();

        this.elementsToPurge.forEach((purgeObject, index) => {

            if (
                typeof purgeObject.element !== 'undefined' &&
                typeof purgeObject.mechanism !== 'undefined'
            ) {

                // Call Purge Mechanism
                purgeObject.mechanism(purgeObject.element);

                // Handle Guarant if needed
                window.SalesNewApplicationHelper.handleGuarantPurging(purgeObject.element);
            }
        });

        return undefined;
    }

    handleGuarantPurging(element) {

        const closestGuarant = jQuery(element.closest(window.SalesNewApplicationHelper.parent.selectorWorkable.global.singleGuarantContainer));                
        if (closestGuarant.length === 0) { return undefined; }

        const deleteButton = jQuery(closestGuarant.find(window.SalesNewApplicationHelper.parent.selectorWorkable.global.guarantAdditionalDelete));
        if (deleteButton.length === 0) { return undefined; }

        deleteButton.trigger('click');
        return undefined;
    }

    ajaxComplete(additional) {

        if (
            typeof additional.button !== 'undefined' &&
            additional.button &&
            additional.button.length > 0
        ) {

            window.SalesNewApplicationHelper.parent.handleButtonState(additional.button, false);
        }
    }

    prepareFillableDataMvr(data) {

        const result = {
            client: {},
        };

        if (Object.keys(data).length === 0) { return result; }

        const map = {
            'issue_by_city_id': 'idcard_issued_id',
        };

        Object.keys(data).forEach((key, index) => {

            const value = data[key];
            if (
                key !== 'client_meta_action' &&
                key !== 'error'
            ) {
            
                result.client = Object.assign(result.client, value);
            } else if (key === 'client_meta_action') {

                window.SalesNewApplicationHelper.parent.domElements.global.clientMetaAction.val(value).trigger('change');
            } else if (key === 'error') {

                const idCardErrorContainer = window.SalesNewApplicationHelper.parent.domElements.global.idCardErrorContainer;

                showErrorMessage(
                    idCardErrorContainer,
                    value,
                    window.SalesNewApplicationHelper.parent.config.newApplicationDefaultDelayTimeout
                );
            }
        });

        Object.keys(result.client).forEach((key, index) => {

            if (typeof map[key] !== 'undefined') {

                const value = result.client[key];
                const newKey = map[key];

                result.client[newKey] = value;

                delete result.client[key];
            }
        });

        return result;
    }

    setLoanProductId(productId) {
        
        const productButton = jQuery(`${window.SalesNewApplicationPerson.selectorWorkable.global.productButton}[data-product-selection="${productId}"]`);

        if (
            window.SalesNewApplicationHelper.initted &&
            (
                productButton.length === 0 ||
                productButton.hasClass('active')
            )
        ) {

            window.SalesNewApplicationHelper.metaDataObject.loan.setProductIdState = false;
            return undefined;
        }
        
        window.SalesNewApplicationHelper.initted = true;
        window.SalesNewApplicationHelper.metaDataObject.loan.setProductIdState = true;
        productButton[0].click();

        return undefined;
    }

    initClientLoanDataPrefill(data) {

        Object.keys(data).forEach(function(key, index) {

            const value = data[key];
            window.SalesNewApplicationHelper.prefillData(value, key, key, true, value, false);
        });
    }

    triggerLoanChange() {

        const loadFields = [
            '#loan_sum',
            '#loan_period',
            '#loan_discount',
        ];

        if (window.SalesNewApplicationHelper.metaDataObject.loan.readyFields < loadFields.length) {
            window.SalesNewApplicationHelper.metaDataObject.loan.readyFields += 1;
 
            if (window.SalesNewApplicationHelper.metaDataObject.loan.readyFields === loadFields.length) {

                window.SalesNewApplicationHelper.triggerLoanChange();
            }

            return undefined;
        }

        window.SalesNewApplicationHelper.metaDataObject.loan.changeState = true;

        const prefix = '#loan-sliders';
        const changeEvent = new Event('change');

        loadFields.forEach((elementSelector, index) => {

            const selector = `${prefix} ${elementSelector}`;
            const element = jQuery(selector);

            if (element.length > 0) {

                if (index === loadFields.length - 1) {
                    window.SalesNewApplicationHelper.metaDataObject.loan.changeState = false;
                }

                element[0].dispatchEvent(changeEvent);
            }
        });

        window.SalesNewApplicationHelper.metaDataObject.loan.readyFields = 0;
        return undefined;
    }

    createNewApplication(data, additional = {}) {

        const restUrl = this.parent.config.rest.saveNewApp;

        jQuery.ajax({
            type: 'POST',
            url: restUrl,
            data: data,
            success: (response) => {

                const modalSelector = response.modalSelector;
                const modal = (
                    modalSelector ?
                    jQuery(modalSelector) :
                    null
                );
                const officeId = response.officeId;
                window.SalesNewApplicationHelper.metaDataObject.loan.meta.newApplicationHref = response.href;

                if (
                    modalSelector && 
                    !window.SalesNewApplicationHelper.metaDataObject.loan.meta.newApplicationHref
                ) {
                    window.SalesNewApplicationHelper.parent.domElements.global.modalInflatedLoanAmount.modal('show');
                    return undefined;
                }

                if (
                    window.SalesNewApplicationHelper.metaDataObject.loan.meta.newApplicationHref && 
                    !modalSelector
                ) {
                    window.location = window.SalesNewApplicationHelper.metaDataObject.loan.meta.newApplicationHref;
                    return undefined;
                }

                if (officeId === window.SalesNewApplicationHelper.parent.config.office.web.id) {
                    
                    if (modal.length > 0) {

                        modal.modal('show');

                        const aTags = jQuery(modal.find('a[href]'));
                        if (aTags.length > 0) {

                            aTags.attr('href', window.SalesNewApplicationHelper.metaDataObject.loan.meta.newApplicationHref);
                        }
                    }
        
                    return undefined;
                }

                if (
                    modalSelector &&
                    window.SalesNewApplicationHelper.metaDataObject.loan.meta.newApplicationHref
                ) {

                    window.SalesNewApplicationHelper.metaDataObject.loan.meta.loanId = response.loanId;

                    if (modal.length > 0) {

                        modal.modal('show');

                        const aTags = jQuery(modal.find('a[href]'));
                        if (aTags.length > 0) {

                            const newHref = window.SalesNewApplicationHelper.parent.config.rest.printDocuments.replace(':loanId', window.SalesNewApplicationHelper.metaDataObject.loan.meta.loanId);
                            aTags.attr('href', newHref);
                        }
                    } 

                    return undefined;
                }
            },
            error: (response) => {

                handleAjaxError(response);
            },
            complete: (response) => {

                window.SalesNewApplicationHelper.ajaxComplete(additional);
            }
        });
    }

    setLoanOfficeId(officeId) {

        const officesSelectElement = $officesSelect;
        if (officesSelectElement.length === 0) { 

            window.SalesNewApplicationHelper.metaDataObject.loan.setOfficeIdState = false;
            return undefined;
        }

        const currentOfficeId = parseInt(officesSelectElement.val().trim());

        if (
            isNaN(currentOfficeId) ||
            currentOfficeId === officeId
        ) {
            
            window.SalesNewApplicationHelper.metaDataObject.loan.setOfficeIdState = false;
            return undefined;
        }

        window.SalesNewApplicationHelper.metaDataObject.loan.setOfficeIdState = true;
        window.SalesNewApplicationHelper.metaDataObject.loan.changeState = true;

        officesSelectElement.val(officeId).trigger('change');

        return undefined;
    }

    fixContact(contacts = []) {

        const fixed = [];

        if (contacts.length === 0) { return fixed; }

        contacts.forEach(function(contact, index) {
            
            const contactFixed = contact;
            contactFixed.orderNumber = index + 1;

            fixed.push(contactFixed);
        });

        return fixed;
    }

    fixGuarant(guarants = []) {

        const fixed = [];

        if (guarants.length === 0) { return fixed; }

        guarants.forEach(function(guarant, index) {

            const guarantFixed = guarant;
            guarantFixed.pivot.seq_num = index + 1;

            fixed.push(guarantFixed);
        });

        return fixed;
    }

}

window.SalesNewApplicationHelper = new SalesNewApplicationHelper();