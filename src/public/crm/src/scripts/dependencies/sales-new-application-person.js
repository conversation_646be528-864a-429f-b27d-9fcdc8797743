class SalesNewApplicationPerson {

    init(parent) {

        this.parent = parent;
        this.selectorWorkable = this.parent.selectorWorkable;
        this.domElements = this.parent.domElements;

        this.rpa = {
            pin: this.rpaPin,
            idCard: this.rpaIdCard,
            none: () => {},
        };

        this.rpaData = {};

        this.initEventHandlers();

        // Parse Current URL
        const currentUrl = new URL(window.location.href);
        const rpa = currentUrl.searchParams.get('rpa');

        // Automated Data Entry
        const activeRpa = (
            rpa ?
            rpa :
            'none'
        );
        this.initRPA(activeRpa);
    }

    initRPA(rpa) {

        const method = (
            typeof this.rpa[rpa] !== 'undefined' ?
            this.rpa[rpa] :
            null
        );

        if (!method) { 
            return undefined; 
        }

        method();
        return undefined;
    }

    rpaPin() {

        const pinElement = window.SalesNewApplicationPerson.domElements.global.personCardInputPin;
        pinElement.val('9112244723').trigger('change');

        jQuery(window.SalesNewApplicationPerson.selectorWorkable.global.searchByPin).trigger('click');
    }

    rpaIdCard() {

        const pinElement = window.SalesNewApplicationPerson.domElements.global.personCardInputPin;
        pinElement.val('9101159949').trigger('change');

        const idCardElement = window.SalesNewApplicationPerson.domElements.global.personCardInputIdCardNumber;
        idCardElement.val('6302902653').trigger('change');
        
        jQuery(window.SalesNewApplicationPerson.selectorWorkable.global.checkFromMvr).trigger('click');
    }

    initEventHandlers() {

        // Click
        jQuery(document).on('click', this.selectorWorkable.person.button, this.handleButtonClick);
    
        // Change
        jQuery(document).on('change', this.selectorWorkable.person.select, this.handleSelectChange);
        jQuery(document).on('change', this.selectorWorkable.person.text, this.handleTextChange);

        // Keyup
        jQuery(document).on('keyup', this.selectorWorkable.person.text, this.handleKeyup);
    }

    handleButtonClick(event) {
        const target = jQuery(event.target).closest(window.SalesNewApplicationPerson.selectorWorkable.person.button);
        const targetClassList = target[0].classList;

        if (targetClassList.length === 0) { return undefined; }
        
        const buttonClassClean = window.SalesNewApplicationPerson.selectorWorkable.person.button.replace('.', '');
        
        targetClassList.forEach(function(className, index) {

            if (className.indexOf(buttonClassClean) > -1) {

                const classNameTrimmed = className.replace(buttonClassClean, '');

                if (classNameTrimmed.length > 0) {

                    const eventHandlerName = window.SalesNewApplication.prepareEventHandlerName(classNameTrimmed);
                    
                    if (typeof window.SalesNewApplicationPerson[eventHandlerName] !== 'undefined') {
                        window.SalesNewApplicationPerson[eventHandlerName](event);
                    }
                }
            }
        });

        return undefined;
    }

    handleSelectChange(event) {
        const target = jQuery(event.target).closest(window.SalesNewApplicationPerson.selectorWorkable.person.select);
        const targetClassList = target[0].classList;

        if (targetClassList.length === 0) { return undefined; }
        
        const selectClassClean = window.SalesNewApplicationPerson.selectorWorkable.person.select.replace('.', '');

        targetClassList.forEach(function(className, index) {

            if (className.indexOf(selectClassClean) > -1) {

                const classNameTrimmed = className.replace(selectClassClean, '');

                if (classNameTrimmed.length > 0) {

                    const eventHandlerName = window.SalesNewApplication.prepareEventHandlerName(classNameTrimmed);
                    
                    if (typeof window.SalesNewApplicationPerson[eventHandlerName] !== 'undefined') {
                        window.SalesNewApplicationPerson[eventHandlerName](event);
                    }
                }
            }
        });

        return undefined;
    }

    handleTextChange(event) {
        const target = jQuery(event.target).closest(window.SalesNewApplicationPerson.selectorWorkable.person.text);
        const targetClassList = target[0].classList;

        if (targetClassList.length === 0) { return event; }
        
        const textClassClean = window.SalesNewApplicationPerson.selectorWorkable.person.text.replace('.', '');

        targetClassList.forEach(function(className, index) {

            if (className.indexOf(textClassClean) > -1) {

                const classNameTrimmed = className.replace(textClassClean, '');

                if (classNameTrimmed.length > 0) {

                    const eventHandlerName = window.SalesNewApplication.prepareEventHandlerName(classNameTrimmed);
                    
                    if (typeof window.SalesNewApplicationPerson[eventHandlerName] !== 'undefined') {
                        window.SalesNewApplicationPerson[eventHandlerName](event);
                    }
                }
            }
        });

        return undefined;
    }

    handleKeyup(event) {
        const target = jQuery(event.target).closest(window.SalesNewApplicationPerson.selectorWorkable.person.text);
        const targetClassList = target[0].classList;

        if (targetClassList.length === 0) { return event; }
        
        const textClassClean = window.SalesNewApplicationPerson.selectorWorkable.person.text.replace('.', '');

        targetClassList.forEach(function(className, index) {

            if (className.indexOf(textClassClean) > -1) {

                const classNameTrimmed = className.replace(textClassClean, '');

                if (classNameTrimmed.length > 0) {

                    const eventHandlerName = window.SalesNewApplication.prepareEventHandlerName(classNameTrimmed);
                    
                    if (typeof window.SalesNewApplicationPerson[eventHandlerName] !== 'undefined') {
                        window.SalesNewApplicationPerson[eventHandlerName](event);
                    }
                }
            }
        });

        return undefined;
    }

    searchByPin(event) {
        event.preventDefault();

        const target = jQuery(event.target);

        if (
            window.SalesNewApplicationPerson.parent.isButtonLocked(target) ||
            !window.SalesNewApplicationPerson.parent.helper.wasPinChanged()
        ) {
            return false;
        }

        window.SalesNewApplicationPerson.parent.handleButtonState(target, true);

        const pinElement = window.SalesNewApplicationPerson.domElements.global.personCardInputPin;
        const pinValue = pinElement.val().trim();

        const pinErrorContainer = window.SalesNewApplicationPerson.domElements.global.pinErrorContainer;
        const validationPinData = window.SalesNewApplicationPerson.parent.validator.validatePin(pinValue);
        
        if (!validationPinData.status) {

            showErrorMessage(
                pinErrorContainer,
                validationPinData.message,
                window.SalesNewApplicationPerson.parent.config.newApplicationDefaultDelayTimeout
            );

            window.SalesNewApplicationPerson.parent.handleButtonState(target, false);
            return undefined;
        }
        
        const loanProductIdElement = window.SalesNewApplicationPerson.domElements.global.loanProductId;
        const loanProductId = loanProductIdElement.val().trim();

        const validationProductIdData = window.SalesNewApplicationPerson.parent.validator.validateProductId(loanProductId);

        if (!validationProductIdData.status) {

            showErrorMessage(
                pinErrorContainer,
                validationProductIdData.message,
                window.SalesNewApplicationPerson.parent.config.newApplicationDefaultDelayTimeout
            );

            window.SalesNewApplicationPerson.parent.handleButtonState(target, false);
            return undefined;
        }

        const data = {
            pin: pinValue,
            selectedProductId: loanProductId,
            getAllContacts: true,
        };

        window.SalesNewApplicationPerson.parent.helper.getClientAndLoanData(data, {
            button: target,
        });
        return undefined;
    }

    checkFromMvr(event) {
        event.preventDefault();

        const target = jQuery(event.target);

        if (window.SalesNewApplicationPerson.parent.isButtonLocked(target)) {
            return false;
        }

        window.SalesNewApplicationPerson.parent.handleButtonState(target, true);
        
        // Collect Pin
        const pinElement = window.SalesNewApplicationPerson.domElements.global.personCardInputPin;
        const pinValue = pinElement.val().trim();

        // Validate Pin
        const validationPinData = window.SalesNewApplicationPerson.parent.validator.validatePin(pinValue);
        const pinErrorContainer = window.SalesNewApplicationPerson.domElements.global.pinErrorContainer;
        
        if (!validationPinData.status) {

            showErrorMessage(
                pinErrorContainer,
                validationPinData.message,
                window.SalesNewApplicationPerson.parent.config.newApplicationDefaultDelayTimeout
            );

            window.SalesNewApplicationPerson.parent.handleButtonState(target, false);
            return undefined;
        }

        // Collect Id Card Number
        const idCardElement = window.SalesNewApplicationPerson.domElements.global.personCardInputIdCardNumber;
        const idCardValue = idCardElement.val().trim();

        // Validate Id Card Number
        const validationIdCardNumber = window.SalesNewApplicationPerson.parent.validator.validateIdCardNumber(idCardValue);
        const idCardErrorContainer = window.SalesNewApplicationPerson.domElements.global.idCardErrorContainer;

        // Length
        if (
            idCardValue.length === 0 || 
            pinValue.length === 0
        ) {

            showErrorMessage(
                idCardErrorContainer,
                window.SalesNewApplicationPerson.parent.config.message.mvr.errorSubmit,
                window.SalesNewApplicationPerson.parent.config.newApplicationDefaultDelayTimeout
            );

            window.SalesNewApplicationPerson.parent.handleButtonState(target, false);
            return undefined;
        }

        // Validator
        if (!validationIdCardNumber.status) {

            showErrorMessage(
                idCardErrorContainer,
                validationIdCardNumber.message,
                window.SalesNewApplicationPerson.parent.config.newApplicationDefaultDelayTimeout
            );

            window.SalesNewApplicationPerson.parent.handleButtonState(target, false);
            return undefined;
        }

        // Call the API and do the trick ;)
        const data = {
            'client_idcard[pin]': pinValue,
            'client_idcard[idcard_number]': idCardValue
        };
       
        window.SalesNewApplicationPerson.parent.helper.getClientFromMvr(data, {
            button: target,
        });
        return undefined;
    }

    paymentMethod(event) {

        const target = jQuery(event.target);

        const paymentMethodId = parseInt(target.val());

        const ibanElement = window.SalesNewApplicationPerson.domElements.global.iban;

        if (paymentMethodId === window.SalesNewApplicationPerson.parent.config.paymentMethod.bank.payment_method_id) {

            ibanElement.show();
            return undefined;
        }

        ibanElement.hide();
        return undefined;
    }

    pin(event) {

        if (event.type === 'change') {
            const target = jQuery(event.target);
            const value = target.val().trim();

            if (value !== window.SalesNewApplicationPerson.parent.helper.metaDataObject.pin.previousData) {
                
                // Reset Meta Action
                window.SalesNewApplicationPerson.domElements.global.clientMetaAction.val('').trigger('change');
            }
        }

        const keyCode = event.keyCode;
        
        if (keyCode === 13) {

            const target = jQuery(event.target);

            const value = target.val().trim();

            if (value.length > 0) {
                event.preventDefault();

                jQuery(window.SalesNewApplicationPerson.selectorWorkable.global.searchByPin).trigger('click');

                return undefined;
            }
        }
    }

    addCurrentAddress(event) {
        event.preventDefault();

        const target = jQuery(event.target);

        if (target.attr('aria-expanded') === 'true') {
            
            target.toggleClass(
                'fa-plus fa-minus btn-circle-blue btn-primary btn-danger'
            );

            return undefined;
        }

        target.toggleClass(
            'fa-minus fa-plus btn-circle-blue btn-primary btn-danger'
        );

        return undefined;
    }

    addCurrentAddressGuarant(event) {

        window.SalesNewApplicationPerson.addCurrentAddress(event);
    }

    applyButton(event) {
        event.preventDefault();

        const target = jQuery(event.target);
        
        if (window.SalesNewApplicationPerson.parent.isButtonLocked(target)) {
            return undefined;
        }

        window.SalesNewApplicationPerson.parent.handleButtonState(target, true);

        const refinanceSumCheck = checkRefinanceSum();
        if (!refinanceSumCheck) {
            window.SalesNewApplicationPerson.parent.handleButtonState(target, false);
            window.SalesNewApplicationPerson.domElements.global.modalRefinance.modal('show');
            return undefined;
        }

        const newApplicationParamsCheck = window.SalesNewApplicationPerson.parent.validator.validateNewApplicationParams({
            loanSum: window.SalesNewApplicationPerson.domElements.global.loanSum.val().trim(),
            paymentMethodId: window.SalesNewApplicationPerson.domElements.global.paymentMethodSelect.val().trim(),
        });

        if (newApplicationParamsCheck) {

            // Prepare Form Data
            window.SalesNewApplicationPerson.parent.prepareFormDataByCardType();

            // Collect the Data
            const dataArr = window.SalesNewApplicationPerson.domElements.global.newAppApplyForm.serializeArray();
            const data = [];
            const restricted = {
                // 'client[first_name_latin]': '',
                // 'client[middle_name_latin]': '',
                // 'client[last_name_latin]': '',
            };

            dataArr.forEach((item, index) => {

                if (typeof restricted[item.name] === 'undefined') {
                    
                    data.push(item);
                } 
            });

            const dataWorkable = jQuery.map(data, function(prop, index) {

                return `${prop.name}=${prop.value}`;
            }).join('&');

            window.SalesNewApplicationPerson.parent.helper.createNewApplication(
                dataWorkable,
                {
                    button: target,
                }
            );
            return undefined;
        }

        window.SalesNewApplicationPerson.parent.handleButtonState(target, false);
        window.SalesNewApplicationPerson.domElements.global.modalInflatedLoanAmount.modal('show');
        return undefined;
    }

    officesSelect(event) {
        
        const target = jQuery(event.target);
        const officeId = target.val().trim();

        if (officeId.length > 0) { 
            
            window.SalesNewApplication.hidePaymentMethodOptions(officeId);
        }
    }
}

window.SalesNewApplicationPerson = new SalesNewApplicationPerson();