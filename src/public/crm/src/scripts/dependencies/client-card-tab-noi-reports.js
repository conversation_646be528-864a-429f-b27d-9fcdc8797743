class ClientCardTabNoiReports {

    constructor() {
        
        this.hash = 'noi_reports';

        this.config = {
            rest: [
                'getNoiReportsRest',
                'hasNoiReportsPermissionRest',
            ],
            restCallbacks: {
                success: (response) => {

                    const endpoint = response.endpoint;
                    const data = response.data;

                    window.ClientCardTabNoiReports.restDataObject[endpoint] = data;

                    window.ClientCardTabNoiReports.restCheckResults();
                },
            },
            restData: {},
        };

        this.resetDefaults();
    }

    init() {

        this.resetDefaults();
        this.collectRestData();
    }

    resetDefaults() {
        this.restDataObject = {};
    }

    collectRestData() {

        const callbacks = window.ClientCardTabNoiReports.config.restCallbacks;

        this.config.rest.forEach((endpoint, index) => {
            
            var rest = window.ClientCard.config.rest[endpoint];

            if (typeof callbacks !== 'undefined') {

                Object.keys(callbacks).forEach(function(key, index) {
                    rest[key] = callbacks[key];
                });
            }

            let data = window.ClientCardTabNoiReports.config.restData[endpoint];
            if (typeof data === 'undefined') {
                data = {};
            }

            data.storeInSession = true;
            data.endpoint = endpoint;

            if (Object.keys(data).length > 0) {

                Object.keys(data).forEach(function(key, index) {
    
                    if (
                        typeof rest.data[key] === 'undefined' || 
                        !rest.data[key]
                    ) {
    
                        const value = data[key];
    
                        rest.data[key] = value;
                    }
                });
            }
    
            jQuery.ajax(rest);
        });
    }

    restCheckResults() {

        const resultsCount = Object.keys(window.ClientCardTabNoiReports.restDataObject).length;
        const allRestCount = window.ClientCardTabNoiReports.config.rest.length;

        if (resultsCount < allRestCount) { return false; }

        var rest = window.ClientCard.config.rest.renderTab;

        const workableData = {
            tabId: window.ClientCardTabNoiReports.hash,
            clientId: window.ClientCardConfigObject.client.id,
            loanId: window.ClientCardConfigObject.loan.id,
        };

        Object.keys(window.ClientCardTabNoiReports.restDataObject).forEach(function(key, index) {

            const workableKey = key.replace('get', '').replace('Rest', '');

            if (typeof workableData[workableKey] === 'undefined') {
                const value = window.ClientCardTabNoiReports.restDataObject[key];
                workableData[workableKey] = value;
            }
        });

        rest.data = Object.assign(
            rest.data,
            workableData
        );

        jQuery.ajax(rest);
    }
}

window.ClientCardTabNoiReports = new ClientCardTabNoiReports();