class ClientCardTabCkrReports {

    constructor() {
        
        this.hash = 'ckr_reports';

        this.config = {
            rest: [
                'getAllCcrReportsByPinRest',
                'hasCcrReportsPermissionRest',
            ],
            restCallbacks: {
                success: (response) => {

                    const endpoint = response.endpoint;
                    const data = response.data;

                    window.ClientCardTabCkrReports.restDataObject[endpoint] = data;

                    window.ClientCardTabCkrReports.restCheckResults();
                },
            },
            restData: {},
        };

        this.resetDefaults();
    }

    init() {

        this.resetDefaults();
        this.collectRestData();
    }

    resetDefaults() {
        this.restDataObject = {};
    }

    collectRestData() {

        const callbacks = window.ClientCardTabCkrReports.config.restCallbacks;

        this.config.rest.forEach((endpoint, index) => {
            
            var rest = window.ClientCard.config.rest[endpoint];

            if (typeof callbacks !== 'undefined') {

                Object.keys(callbacks).forEach(function(key, index) {
                    rest[key] = callbacks[key];
                });
            }

            let data = window.ClientCardTabCkrReports.config.restData[endpoint];
            if (typeof data === 'undefined') {
                data = {};
            }

            data.storeInSession = true;
            data.endpoint = endpoint;

            if (Object.keys(data).length > 0) {

                Object.keys(data).forEach(function(key, index) {
    
                    if (
                        typeof rest.data[key] === 'undefined' || 
                        !rest.data[key]
                    ) {
    
                        const value = data[key];
    
                        rest.data[key] = value;
                    }
                });
            }
    
            jQuery.ajax(rest);
        });
    }

    restCheckResults() {

        const resultsCount = Object.keys(window.ClientCardTabCkrReports.restDataObject).length;
        const allRestCount = window.ClientCardTabCkrReports.config.rest.length;

        if (resultsCount < allRestCount) { return false; }

        var rest = window.ClientCard.config.rest.renderTab;

        const workableData = {
            tabId: window.ClientCardTabCkrReports.hash,
            clientId: window.ClientCardConfigObject.client.id,
        };

        Object.keys(window.ClientCardTabCkrReports.restDataObject).forEach(function(key, index) {

            const workableKey = key.replace('get', '').replace('Rest', '');

            if (typeof workableData[workableKey] === 'undefined') {
                const value = window.ClientCardTabCkrReports.restDataObject[key];
                workableData[workableKey] = value;
            }
        });

        rest.data = Object.assign(
            rest.data,
            workableData
        );

        jQuery.ajax(rest);
    }
}

window.ClientCardTabCkrReports = new ClientCardTabCkrReports();