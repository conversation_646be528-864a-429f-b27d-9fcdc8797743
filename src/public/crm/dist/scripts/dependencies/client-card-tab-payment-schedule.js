"use strict";function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _createClass(e,t,a){return t&&_defineProperties(e.prototype,t),a&&_defineProperties(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}var ClientCardTabPaymentSchedule=function(){function e(){_classCallCheck(this,e),this.hash="payment_schedule",this.config={rest:["getLoanExtensionDataRest","getLoanTaxesRest","calculateExtensionFeeRest","getLoanCommonInformationRest","getLoanDuesRest","getLoanPaidUntilNowRest","getLoanLeftToPayRest","getLoanMaturedAmountsRest","getLoanOverviewRest"],restCallbacks:{success:function(e){var t=e.endpoint,a=e.data;window.ClientCardTabPaymentSchedule.restDataObject[t]=a,window.ClientCardTabPaymentSchedule.restCheckResults()}},restData:{renderTab:null}},this.renderResults=!0,this.resetDefaults()}return _createClass(e,[{key:"init",value:function(){null!==this.config.restData.renderTab&&(window.ClientCard.renderTabFromView(this.config.restData.renderTab),this.renderResults=!1),this.resetDefaults(),this.collectRestData()}},{key:"resetDefaults",value:function(){this.restDataObject={}}},{key:"collectRestData",value:function(){var e=window.ClientCardTabPaymentSchedule.config.restCallbacks;this.config.rest.forEach((function(t,a){var n=window.ClientCard.config.rest[t];void 0!==e&&Object.keys(e).forEach((function(t,a){n[t]=e[t]}));var r=window.ClientCardTabPaymentSchedule.config.restData[t];void 0===r&&(r={}),r.storeInSession=!0,r.endpoint=t,Object.keys(r).length>0&&Object.keys(r).forEach((function(e,t){if(void 0===n.data[e]||!n.data[e]){var a=r[e];n.data[e]=a}})),jQuery.ajax(n)}))}},{key:"restCheckResults",value:function(){if(Object.keys(window.ClientCardTabPaymentSchedule.restDataObject).length<window.ClientCardTabPaymentSchedule.config.rest.length)return!1;var e=window.ClientCard.config.rest.renderTab,t={render:window.ClientCardTabPaymentSchedule.renderResults,tabId:window.ClientCardTabPaymentSchedule.hash,clientId:window.ClientCardConfigObject.client.id,loanId:window.ClientCardConfigObject.loan.id};Object.keys(window.ClientCardTabPaymentSchedule.restDataObject).forEach((function(e,a){var n=e.replace("get","").replace("Rest","");if(void 0===t[n]){var r=window.ClientCardTabPaymentSchedule.restDataObject[e];t[n]=r}})),e.data=Object.assign(e.data,t),jQuery.ajax(e)}}]),e}();window.ClientCardTabPaymentSchedule=new ClientCardTabPaymentSchedule;
//# sourceMappingURL=client-card-tab-payment-schedule.js.map