"use strict";function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _createClass(e,t,a){return t&&_defineProperties(e.prototype,t),a&&_defineProperties(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}var ClientCardTabSystemLog=function(){function e(){_classCallCheck(this,e),this.hash="systemLog",this.config={rest:["getSystemLogsTableDataRest"],restCallbacks:{success:function(e){var t=e.endpoint,a=e.data;window.ClientCardTabSystemLog.restDataObject[t]=a,window.ClientCardTabSystemLog.restCheckResults()}},restData:{}},this.resetDefaults()}return _createClass(e,[{key:"init",value:function(){this.resetDefaults(),this.collectRestData()}},{key:"resetDefaults",value:function(){this.restDataObject={}}},{key:"collectRestData",value:function(){var e=window.ClientCardTabSystemLog.config.restCallbacks;this.config.rest.forEach((function(t,a){var n=window.ClientCard.config.rest[t];void 0!==e&&Object.keys(e).forEach((function(t,a){n[t]=e[t]}));var i=window.ClientCardTabSystemLog.config.restData[t];void 0===i&&(i={}),i.storeInSession=!0,i.endpoint=t,Object.keys(i).length>0&&Object.keys(i).forEach((function(e,t){if(void 0===n.data[e]||!n.data[e]){var a=i[e];n.data[e]=a}})),jQuery.ajax(n)}))}},{key:"restCheckResults",value:function(){if(Object.keys(window.ClientCardTabSystemLog.restDataObject).length<window.ClientCardTabSystemLog.config.rest.length)return!1;var e=window.ClientCard.config.rest.renderTab,t={tabId:window.ClientCardTabSystemLog.hash,clientId:window.ClientCardConfigObject.client.id};Object.keys(window.ClientCardTabSystemLog.restDataObject).forEach((function(e,a){var n=e.replace("get","").replace("Rest","");if(void 0===t[n]){var i=window.ClientCardTabSystemLog.restDataObject[e];t[n]=i}})),e.data=Object.assign(e.data,t),jQuery.ajax(e)}}]),e}();window.ClientCardTabSystemLog=new ClientCardTabSystemLog;
//# sourceMappingURL=client-card-tab-system-log.js.map