<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20090622 at Fri Dec  7 11:37:12 2018
 By deploy user
<PERSON><PERSON>
</metadata>
<defs>
<font id="TofinoPersonal-Regular" horiz-adv-x="0" >
  <font-face 
    font-family="Tofino Personal"
    font-weight="430"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 5 0 0 0 0 0 0 0"
    ascent="760"
    descent="-240"
    x-height="534"
    cap-height="711"
    bbox="-727 -252 1140 1109"
    underline-thickness="50"
    underline-position="-75"
    unicode-range="U+000D-U+2661"
  />
<missing-glyph horiz-adv-x="616" 
d="M501 521q13 -9 15 -30t-11 -32q-10 -7 -24 -7q-15 0 -46 9q60 -63 60 -152q0 -87 -58 -149t-140 -62q-81 0 -139 62t-58 149q0 94 63 155q-22 31 -8 64q13 31 53 51.5t89 20.5q50 0 91.5 -21.5t55.5 -54.5l1 -2q38 12 56 -1zM297 576q-41 0 -75.5 -16.5t-44.5 -40.5
q-9 -19 4 -39q51 40 112 41q0 12 3 20q9 21 25 25q21 5 63 -33l2 -2q8 3 15 3q3 0 9 -2q-17 20 -49 32t-64 12zM319 532q-3 -8 -1 -12q5 -9 43 -14q1 -1 3 -1q1 1 1 3l3 7q-33 30 -42 28q-4 -2 -7 -11zM402 484q11 0 13 9q2 6 -1 10q-3 5 -9 6q-1 1 -4 1q-10 0 -13 -9
q-1 -3 1 -9q4 -6 9 -7zM440 485q43 -13 50 -7q4 2 2 12q0 8 -4 12q-8 6 -45 -5q-1 -1 -4 -1q0 -7 -1 -10q1 -1 2 -1zM228 482q-16 -18 -5 -41q16 -34 79.5 -61t162.5 -22q-17 65 -66 103q-5 0 -8 1q-15 5 -22 18h-1l-10 2q-35 4 -49 14q-4 1 -12 1q-36 0 -69 -15zM202 431
q-7 14 -7 30q-54 -44 -67 -112q29 -1 61 10t52 27q-27 20 -39 45zM447 318q9 12 14 16h-24q-105 0 -175 39q-46 -44 -124 -48l8 -7q16 9 57 9q50 0 71 -10q23 -11 46 0q20 10 71 10q43 0 56 -9zM124 309q0 -77 51 -132.5t122 -55.5q72 0 123 55.5t51 132.5q0 3 -0.5 8.5
t-0.5 8.5q-7 -6 -16 -18v-1q3 -28 -7 -50t-32 -25q-16 -2 -32 -2q-35 0 -47 7q-21 12 -28 45q-1 2 -1 5q-3 10 -11 10q-6 0 -9 -10q-1 -1 -1 -5q-7 -33 -29 -45q-12 -7 -47 -7q-17 0 -31 2q-22 3 -32.5 25.5t-7.5 49.5v1l-15 14v-13zM252 247q17 10 22 37q3 13 -2 20l-2 2
h-1q-18 10 -66 10q-18 0 -30.5 -2t-17 -4.5t-4.5 -3.5q-2 -21 5 -40.5t24 -21.5q16 -2 30 -2q32 0 42 5zM383 242q15 0 31 2q17 2 24 22t5 40q-5 10 -52 10q-48 0 -66 -10q-1 0 -1 -1l-2 -1q-5 -7 -2 -20q3 -26 22 -37q10 -5 41 -5zM226 294q11 -6 11 -21q0 -10 -7 -17
t-17 -7t-17 7t-7 17q0 5 1 8l-11 9q-3 2 0 5q2 2 5 0l9 -8l3 4l-6 11q-1 1 -0.5 2.5t1.5 2.5q3 2 5 -2l6 -10l4 2l-2 12q0 4 4 4q2 0 4 -3l1 -12q1 0 3 -0.5t3 -0.5l3 13q2 3 5 3q4 -2 2 -5zM414 295q1 1 2.5 1t2.5 -1q2 -4 -1 -5l-10 -10q0 -1 0.5 -3t0.5 -4
q0 -10 -7.5 -17t-17.5 -7t-17 7t-7 17q0 14 12 21l-4 14q0 4 3 5q4 0 4 -3l3 -13q2 1 6 1l2 12q0 3 4 3q3 0 3 -4l-2 -12l5 -2l6 10q2 4 4 2q1 -1 2 -2.5t0 -2.5l-7 -11q3 -1 3 -4zM296 143q23 0 38.5 10.5t15.5 26.5q0 15 -16 25.5t-38 10.5t-38 -10.5t-16 -25.5
q0 -16 15.5 -26.5t38.5 -10.5z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="648" 
d="M100 0v454h-73v76h73v42q0 87 36.5 137.5t132.5 50.5h63v-80h-37q-61 0 -83 -28.5t-22 -92.5v-29h224v42q0 87 36 137.5t132 50.5h63v-80h-37q-61 0 -83 -28.5t-22 -92.5v-29h138v-76h-138v-454h-89v454h-224v-454h-90z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="886" 
d="M100 0v454h-73v76h73v42q0 87 36.5 137.5t132.5 50.5h63v-80h-37q-61 0 -83 -28.5t-22 -92.5v-29h224v42q0 87 36 137.5t132 50.5h63v-80h-37q-61 0 -83 -28.5t-22 -92.5v-29h137v-76h-137v-454h-89v454h-224v-454h-90zM721 637v119h90v-119h-90zM721 0v538h90v-538h-90z
" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="891" 
d="M100 0v454h-73v76h73v42q0 87 36.5 137.5t132.5 50.5h63v-80h-37q-61 0 -83 -28.5t-22 -92.5v-29h224v42q0 87 36 137.5t132 50.5h63v-80h-37q-61 0 -83 -28.5t-22 -92.5v-29h137v-76h-137v-454h-89v454h-224v-454h-90zM723 0v753h90v-753h-90z" />
    <glyph glyph-name=".notdef" horiz-adv-x="616" 
d="M501 521q13 -9 15 -30t-11 -32q-10 -7 -24 -7q-15 0 -46 9q60 -63 60 -152q0 -87 -58 -149t-140 -62q-81 0 -139 62t-58 149q0 94 63 155q-22 31 -8 64q13 31 53 51.5t89 20.5q50 0 91.5 -21.5t55.5 -54.5l1 -2q38 12 56 -1zM297 576q-41 0 -75.5 -16.5t-44.5 -40.5
q-9 -19 4 -39q51 40 112 41q0 12 3 20q9 21 25 25q21 5 63 -33l2 -2q8 3 15 3q3 0 9 -2q-17 20 -49 32t-64 12zM319 532q-3 -8 -1 -12q5 -9 43 -14q1 -1 3 -1q1 1 1 3l3 7q-33 30 -42 28q-4 -2 -7 -11zM402 484q11 0 13 9q2 6 -1 10q-3 5 -9 6q-1 1 -4 1q-10 0 -13 -9
q-1 -3 1 -9q4 -6 9 -7zM440 485q43 -13 50 -7q4 2 2 12q0 8 -4 12q-8 6 -45 -5q-1 -1 -4 -1q0 -7 -1 -10q1 -1 2 -1zM228 482q-16 -18 -5 -41q16 -34 79.5 -61t162.5 -22q-17 65 -66 103q-5 0 -8 1q-15 5 -22 18h-1l-10 2q-35 4 -49 14q-4 1 -12 1q-36 0 -69 -15zM202 431
q-7 14 -7 30q-54 -44 -67 -112q29 -1 61 10t52 27q-27 20 -39 45zM447 318q9 12 14 16h-24q-105 0 -175 39q-46 -44 -124 -48l8 -7q16 9 57 9q50 0 71 -10q23 -11 46 0q20 10 71 10q43 0 56 -9zM124 309q0 -77 51 -132.5t122 -55.5q72 0 123 55.5t51 132.5q0 3 -0.5 8.5
t-0.5 8.5q-7 -6 -16 -18v-1q3 -28 -7 -50t-32 -25q-16 -2 -32 -2q-35 0 -47 7q-21 12 -28 45q-1 2 -1 5q-3 10 -11 10q-6 0 -9 -10q-1 -1 -1 -5q-7 -33 -29 -45q-12 -7 -47 -7q-17 0 -31 2q-22 3 -32.5 25.5t-7.5 49.5v1l-15 14v-13zM252 247q17 10 22 37q3 13 -2 20l-2 2
h-1q-18 10 -66 10q-18 0 -30.5 -2t-17 -4.5t-4.5 -3.5q-2 -21 5 -40.5t24 -21.5q16 -2 30 -2q32 0 42 5zM383 242q15 0 31 2q17 2 24 22t5 40q-5 10 -52 10q-48 0 -66 -10q-1 0 -1 -1l-2 -1q-5 -7 -2 -20q3 -26 22 -37q10 -5 41 -5zM226 294q11 -6 11 -21q0 -10 -7 -17
t-17 -7t-17 7t-7 17q0 5 1 8l-11 9q-3 2 0 5q2 2 5 0l9 -8l3 4l-6 11q-1 1 -0.5 2.5t1.5 2.5q3 2 5 -2l6 -10l4 2l-2 12q0 4 4 4q2 0 4 -3l1 -12q1 0 3 -0.5t3 -0.5l3 13q2 3 5 3q4 -2 2 -5zM414 295q1 1 2.5 1t2.5 -1q2 -4 -1 -5l-10 -10q0 -1 0.5 -3t0.5 -4
q0 -10 -7.5 -17t-17.5 -7t-17 7t-7 17q0 14 12 21l-4 14q0 4 3 5q4 0 4 -3l3 -13q2 1 6 1l2 12q0 3 4 3q3 0 3 -4l-2 -12l5 -2l6 10q2 4 4 2q1 -1 2 -2.5t0 -2.5l-7 -11q3 -1 3 -4zM296 143q23 0 38.5 10.5t15.5 26.5q0 15 -16 25.5t-38 10.5t-38 -10.5t-16 -25.5
q0 -16 15.5 -26.5t38.5 -10.5z" />
    <glyph glyph-name=".null" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="NULL" 
 />
    <glyph glyph-name="NULL" 
 />
    <glyph glyph-name="CR" unicode="&#xd;" horiz-adv-x="250" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="210" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="245" 
d="M81 169l-4 542h90l-3 -542h-83zM77 0v117h90v-117h-90z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="318" 
d="M55 553l-15 204h98l-14 -204h-69zM195 553l-14 204h97l-14 -204h-69z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="724" 
d="M123 0l39 196h-100v68h113l39 192h-105v68h118l36 178h72l-36 -178h168l36 178h72l-36 -178h102v-68h-115l-39 -192h107v-68h-120l-39 -196h-72l39 196h-168l-39 -196h-72zM247 264h168l39 192h-168z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="564" 
d="M242 -48v87q-196 19 -196 208h78q0 -118 118 -135v230q-43 11 -71.5 22.5t-58.5 31.5t-45 51.5t-15 74.5q1 75 48 120.5t142 52.5v71h80v-72q99 -9 141.5 -57.5t42.5 -133.5h-80q0 22 -3.5 38.5t-14 34t-32 29t-54.5 16.5v-224q45 -12 73.5 -23t60.5 -31.5t48 -52
t16 -75.5q0 -163 -198 -177v-86h-80zM132 523q0 -41 28.5 -64.5t81.5 -39.5v204q-110 -12 -110 -100zM439 213q0 43 -30 67t-87 41v-211q117 9 117 103z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="797" 
d="M360 539q0 -86 -37 -130.5t-125 -44.5t-126 45t-38 130q0 87 38 133t126 46q87 0 124.5 -46t37.5 -133zM117 0l460 702h86l-460 -702h-86zM101 539q0 -59 23 -87.5t74 -28.5t73 28.5t22 87.5q0 60 -22 90t-73 30q-97 0 -97 -120zM744 159q0 -86 -37.5 -130.5
t-125.5 -44.5q-87 0 -125 45t-38 130q0 87 38 133t125 46q88 0 125.5 -45.5t37.5 -133.5zM485 159q0 -58 22.5 -87t73.5 -29q52 0 74 28.5t22 87.5q0 60 -22.5 90t-73.5 30q-96 0 -96 -120z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="718" 
d="M360 729q195 0 195 -187h-84q0 119 -111 119q-118 0 -118 -111q0 -26 5.5 -47.5t25.5 -49t30 -40t50 -53l54 -55.5l124 -132q27 74 27 198h78q0 -163 -50 -255l92 -95v-24h-84l-56 57q-76 -72 -223 -72q-244 0 -244 220q0 48 14 86.5t37.5 62t44 37t43.5 22.5
q-52 69 -52 147q0 172 202 172zM251 360q-40 -17 -69.5 -55t-29.5 -102q0 -75 41.5 -115.5t137.5 -40.5q109 0 160 57l-167 179z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="178" 
d="M55 553l-15 204h98l-14 -204h-69z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="307" 
d="M77 278q0 82 10.5 162t26 135.5t31 99t25.5 64.5l11 21h83l-11 -23t-25 -65t-32 -102.5t-25 -132.5t-11 -159q0 -126 27 -244.5t53 -174.5l27 -56h-83l-11.5 21t-26 61t-32.5 98.5t-25.5 132t-11.5 162.5z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="307" 
d="M127 760l11 -22.5t25 -62.5t32 -100.5t25 -133.5t11 -163t-11 -161.5t-27 -133.5t-32 -96.5t-27 -62.5l-11 -21h-83l11.5 22.5t26 63t32.5 100.5t25.5 131t11.5 158t-10.5 158t-26 135t-31 100.5t-25.5 66.5l-11 22h84z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="393" 
d="M61 608l18 57l90 -33l-3 94h61l-3 -94l89 33l19 -57l-90 -27l65 -76l-50 -40l-60 80l-61 -80l-50 40l65 76z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="475" 
d="M37 355h161v160h80v-160h161v-80h-161v-161h-80v161h-161v80z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="224" 
d="M26 -106q23 50 38 126q7 35 10.5 62t3.5 37v10h84q0 -39 -20 -115q-10 -36 -22.5 -66t-20.5 -42l-8 -12h-65z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="385" 
d="M74 283v71h238v-71h-238z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="216" 
d="M62 0v129h92v-129h-92z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="473" 
d="M47 -40l310 840h82l-310 -840h-82z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="635" 
d="M584 352q0 -195 -62 -279t-205 -84t-205 84t-62 279t62 279t205 84t205 -84t62 -279zM143 352q0 -156 41 -218t133 -62q93 0 134 62t41 218t-41 218.5t-134 62.5q-92 0 -133 -62.5t-41 -218.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="326" 
d="M134 0v599l-129 -56v92l161 72h57v-707h-89z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="539" 
d="M23 4v84q93 67 193 156l61.5 53.5t48 47.5t41 51.5t22.5 50.5t10 60q0 56 -35.5 90t-103.5 34q-65 0 -105 -39.5t-40 -99.5h-91q0 99 60.5 161t173.5 62q120 0 177 -56.5t57 -143.5q0 -42 -11.5 -80t-25 -64t-45 -61.5t-50 -53l-62.5 -57.5q-27 -24 -138 -116l338 5v-84
h-475z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="563" 
d="M301 449q215 -23 215 -224q0 -237 -253 -237q-127 0 -186.5 60t-59.5 150h91q0 -56 39.5 -92t113.5 -36q159 0 159 161q0 72 -46.5 111.5t-125.5 39.5l-88 -7v67l213 174h-323v84h453v-83z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="565" 
d="M374 0v148h-364v78l314 478h139v-478h82v-78h-82v-148h-89zM374 226v424l-266 -424h266z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="587" 
d="M198 613l-33 -190q56 41 164 41q109 0 165 -67t56 -172q0 -109 -60.5 -173t-192.5 -64t-195 58t-63 140h95q0 -50 42.5 -84t120.5 -34q79 0 119.5 40.5t40.5 116.5q0 78 -41.5 120.5t-111.5 42.5q-34 0 -62.5 -6.5t-45 -16t-28.5 -19.5t-16 -16l-4 -7l-87 1l60 373h391
v-81z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="618" 
d="M575 226q0 -238 -260 -238q-142 -1 -203 77t-61 247q0 222 60.5 313.5t203.5 91.5q64 0 112.5 -18.5t74 -49.5t37.5 -63.5t12 -67.5h-92q0 49 -35 84.5t-108 35.5q-79 0 -118.5 -52.5t-49.5 -190.5q69 75 193 75q120 0 177 -67t57 -177zM322 391q-71 0 -113.5 -32
t-63.5 -72q4 -117 45 -168.5t125 -51.5q164 0 164 162t-157 162z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="482" 
d="M122 0q0 95 26 193t63 170t74 130.5t63 88.5l26 31l-372 2v85h475v-88q-11 -12 -28.5 -33t-62.5 -88.5t-79.5 -136t-63 -166t-28.5 -188.5h-93z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="610" 
d="M305 -11q-262 0 -262 205q0 47 14.5 84t36 56.5t40.5 30t33 11.5q-28 4 -64.5 41.5t-36.5 105.5q0 194 239 194t239 -194q0 -68 -36.5 -105.5t-65.5 -41.5q14 -1 33 -11.5t40.5 -30.5t36.5 -57t15 -83q0 -205 -262 -205zM305 646q-75 0 -111 -31.5t-36 -98.5
q0 -50 37 -78.5t110 -28.5q72 0 109.5 28.5t37.5 78.5q0 130 -147 130zM305 337q-81 0 -122.5 -35t-41.5 -98q0 -135 164 -135q163 0 163 135q0 63 -41.5 98t-121.5 35z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="622" 
d="M570 376q0 -220 -59 -304t-207 -84q-69 0 -120.5 18.5t-79 49.5t-40 63.5t-12.5 66.5h91q0 -47 40.5 -83t120.5 -36q54 0 86 17t53.5 66.5t28.5 139.5q-69 -75 -189 -75q-237 0 -237 263q0 108 65.5 173.5t194.5 65.5q144 0 204 -79.5t60 -261.5zM299 295q123 0 177 106
q-3 130 -43.5 183.5t-126.5 53.5q-76 0 -120.5 -43t-44.5 -121q0 -88 40.5 -133.5t117.5 -45.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="243" 
d="M75 388v129h93v-129h-93zM75 30v133h93v-133h-93z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="247" 
d="M74 358v129h93v-129h-93zM27 -106q22 50 38 126q7 35 10.5 62t3.5 37v10h83q0 -39 -20 -115q-9 -36 -22 -66t-21 -42l-8 -12h-64z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="514" 
d="M441 143l-387 132v95l387 132v-81l-322 -100l322 -99v-79z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="511" 
d="M57 341v79h398v-79h-398zM57 189v80h398v-80h-398z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="514" 
d="M74 141v81l321 99l-321 100v79l387 -132v-95z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="527" 
d="M292 163h-90v36q0 51 17 87.5t41 57.5l47.5 41t40.5 51.5t17 75.5q0 129 -118 129q-121 0 -121 -142h-87q0 221 207 221q111 0 163 -53.5t52 -148.5q0 -52 -17 -88.5t-41 -58.5l-48.5 -42.5t-42.5 -52.5t-20 -77v-36zM202 0v112h90v-112h-90z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="868" 
d="M680 70q23 0 37.5 15.5t23.5 63t9 131.5q0 355 -319 355q-163 0 -238 -95.5t-75 -259.5q0 -60 9.5 -111t32.5 -95.5t58.5 -75.5t89.5 -49t124 -18q116 0 181 32v-66q-76 -28 -182 -28q-381 0 -381 411q0 90 20.5 163.5t64 131.5t119 90.5t177.5 32.5q105 0 181.5 -31
t121 -88.5t65 -131t20.5 -167.5q0 -144 -34.5 -204.5t-114.5 -60.5q-96 0 -118 102q-45 -62 -146 -62q-174 0 -174 223q0 226 174 226q82 0 141 -59l4 51h65v-301q0 -125 64 -125zM419 118q93 0 126 70v184q-44 69 -126 69q-56 0 -84 -43t-28 -120q0 -160 112 -160z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="663" 
d="M10 0l272 711h99l272 -711h-101l-60 168h-324l-59 -168h-99zM198 252h264l-132 373z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="663" 
d="M79 714h241q106 0 167 -22.5t83 -60t22 -97.5q0 -124 -98 -163q118 -39 118 -176q0 -55 -10.5 -88.5t-41.5 -60t-89.5 -38t-152.5 -11.5h-239v717zM171 638v-228h182q82 0 115.5 27.5t33.5 86.5q0 60 -40 87t-137 27h-154zM171 331v-259h154q115 0 156 28t41 101
q0 130 -169 130h-182z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="658" 
d="M612 242q-3 -133 -69 -196.5t-199 -63.5q-157 0 -225.5 91.5t-68.5 281.5t68.5 282t225.5 92q135 0 201.5 -65t68.5 -200h-91q-4 93 -48 137t-131 44q-107 0 -154 -69.5t-47 -220.5t47 -220t154 -69q85 0 129 42.5t48 133.5h91z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="683" 
d="M79 714h222q103 0 169 -21t102 -68.5t49 -110t13 -159.5t-13 -159t-49 -109.5t-102 -68.5t-169 -21h-222v717zM172 630v-550h145q125 -1 173.5 60.5t48.5 214.5t-48 214t-174 61h-145z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="569" 
d="M79 0v711h458v-84h-366v-218h316v-81h-316v-244h371v-84h-463z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="541" 
d="M79 0v711h452v-84h-360v-235h310v-82h-310v-310h-92z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="663" 
d="M605 84q0 -4 -9 -14.5t-30.5 -25.5t-50.5 -29t-75 -23.5t-98 -9.5q-157 0 -224.5 91t-67.5 281q0 191 68 283t224 92q132 0 198 -65.5t66 -179.5h-88q-2 72 -48 116.5t-128 44.5q-106 0 -152.5 -70t-46.5 -221q0 -153 47 -222t154 -69q52 0 94.5 13.5t62 28.5t19.5 25
v140h-145v80h230v-266z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="692" 
d="M79 0v711h92v-300h350v300h92v-711h-92v325h-350v-325h-92z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="250" 
d="M79 0v711h92v-711h-92z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="372" 
d="M304 280q0 -68 -4.5 -109.5t-19 -78t-42.5 -54.5t-72 -28t-111 -10h-53v89h53q96 0 126 39.5t30 151.5l1 431h92v-431z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="636" 
d="M79 0v711h92v-416l337 416h115l-243 -294l246 -417h-105l-202 344l-148 -179v-165h-92z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="502" 
d="M79 0v711h92v-625h318v-86h-410z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="906" 
d="M79 0v711h127l243 -622l246 622h132l-1 -711h-90l6 601l-248 -601h-91l-242 601l6 -601h-88z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="721" 
d="M163 594l3 -594h-87l4 711h99l378 -589l-6 589h88v-711h-99z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="694" 
d="M645 355q0 -190 -69 -281.5t-229 -91.5t-228.5 91t-68.5 282t69 282.5t228 91.5q160 0 229 -92t69 -282zM143 355q0 -152 47.5 -220.5t156.5 -68.5q110 0 157 68.5t47 220.5q0 151 -47.5 220.5t-156.5 69.5t-156.5 -69.5t-47.5 -220.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="647" 
d="M79 0v714h229q167 0 236 -50t69 -184q0 -135 -68.5 -185t-236.5 -50h-137v-245h-92zM171 634v-307h151q50 0 82 5t60.5 20.5t41.5 46.5t13 81t-13 81.5t-41.5 47t-60.5 20.5t-82 5h-151z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="705" 
d="M645 355q0 -218 -91 -305l113 -99l-69 -60l-114 114q-58 -23 -137 -23q-160 0 -228.5 91t-68.5 282t69 282.5t228 91.5q160 0 229 -92t69 -282zM143 355q0 -152 47.5 -220.5t156.5 -68.5q110 0 157 68.5t47 220.5q0 151 -47.5 220.5t-156.5 69.5t-156.5 -69.5
t-47.5 -220.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="665" 
d="M79 0v714h253q154 0 218.5 -47t64.5 -177q0 -95 -33.5 -143.5t-108.5 -66.5l151 -280h-99l-138 269q-18 -1 -57 -1h-159v-268h-92zM171 631v-281h153q52 0 83 4t60 18t41.5 43t12.5 75q0 38 -9.5 64t-24.5 41t-42 23t-53.5 10.5t-67.5 2.5h-153z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="629" 
d="M585 184q0 -101 -68.5 -152t-201.5 -50q-269 2 -269 238h90q0 -154 183 -157q174 -1 174 119q0 45 -32.5 74t-81.5 41.5t-106 29t-106 35.5t-81.5 62t-32.5 107q1 94 65.5 146.5t192.5 51.5q141 -1 199.5 -57t58.5 -162h-90q0 61 -34.5 98.5t-133.5 38.5q-88 1 -127 -31
t-39 -83q0 -39 24.5 -65t63 -40.5t85.5 -26t94 -26t85.5 -36t63 -61t24.5 -94.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="558" 
d="M230 0l2 626h-224v85h542v-85h-226l-2 -626h-92z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="681" 
d="M613 346q0 -75 -6 -127.5t-23 -100t-47 -75.5t-79 -44.5t-118 -16.5t-118 16.5t-78.5 44.5t-46.5 75.5t-23 100t-6 127.5v365h91v-359q0 -153 38 -218t143 -65t142.5 64.5t37.5 218.5v359h93v-365z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="621" 
d="M3 711h103l206 -636l206 636h100l-250 -711h-113z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="978" 
d="M489 614l-155 -614h-111l-208 711h105l161 -626l156 626h104l161 -627l161 627h100l-208 -711h-110z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="632" 
d="M312 293l-194 -293h-105l243 354l-242 357h103l198 -297l198 297h104l-248 -356l241 -355h-103z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="607" 
d="M261 0v228l-251 483h105l191 -404l183 404h108l-245 -481v-230h-91z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="549" 
d="M19 3v79l392 547h-374v79h486v-79l-394 -547h398v-79h-508z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="365" 
d="M100 -197v957h235v-71h-155v-815h155v-71h-235z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="486" 
d="M439 -40h-82l-310 840h82z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="365" 
d="M265 -197h-235v71h155v815h-155v71h235v-957z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="425" 
d="M372 465h-67l-91 212l-91 -212h-66l128 276h59z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="574" 
d="M81 0v68h413v-68h-413z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="335" 
d="M229 631h-80l-92 144h95z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="584" 
d="M439 1l-11 64q-69 -78 -189 -78q-191 0 -191 167q0 96 58.5 136.5t167.5 40.5q81 0 146 -9q-2 88 -25.5 120t-104.5 32q-137 0 -137 -103h-83q0 176 216 176q153 0 197 -77q26 -46 27 -163v-40v-128l11 -138h-82zM257 63q66 0 110.5 30.5t52.5 66.5v90q-61 7 -122 7
q-80 0 -118.5 -22.5t-38.5 -76.5q0 -95 116 -95z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="600" 
d="M68 0l10 144v609h89v-278q62 73 175 73q209 0 209 -282q0 -276 -212 -276q-130 0 -183 81l-11 -71h-77zM320 467q-96 0 -153 -79v-238q48 -78 153 -78q141 0 141 194q0 201 -141 201z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="563" 
d="M520 180q-7 -192 -230 -192q-131 0 -185.5 70t-54.5 209t55 210.5t185 71.5q118 0 173 -53t57 -155h-90q-1 134 -140 134q-80 0 -114 -51t-34 -157q0 -107 33.5 -155.5t114.5 -48.5q66 0 102.5 29.5t37.5 87.5h90z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="596" 
d="M455 0l-12 71q-51 -81 -181 -81q-212 0 -212 276q0 282 209 282q109 0 174 -73v278h90v-609l10 -144h-78zM281 72q101 0 152 78v239q-56 78 -152 78q-141 0 -141 -201q0 -194 141 -194z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="576" 
d="M527 163q0 -28 -10 -55t-34 -56t-73 -47t-118 -18q-131 0 -186.5 71t-55.5 209q0 139 55.5 210.5t186.5 71.5q129 0 184 -71t55 -233h-389q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90zM292 476q-71 0 -106 -38t-42 -118h292q-10 84 -44 120t-100 36z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="335" 
d="M332 761v-81h-37q-61 0 -83 -28.5t-22 -92.5v-29h139v-77h-139v-453h-90v453h-73v77h73v42q0 87 36.5 138.5t132.5 51.5z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="595" 
d="M523 -1q0 -43 -2 -68t-14.5 -62t-36 -57.5t-70 -36t-113.5 -15.5q-65 0 -111.5 19.5t-66.5 47.5t-30 56t-10 47v20l95 1q0 -88 73 -112q22 -7 51 -7q50 0 81 12.5t44 39.5t16.5 51t3.5 66v55q-54 -66 -171 -66q-212 0 -212 276q0 282 209 282q118 0 184 -84l12 74h77
l-9 -140v-399zM281 72q101 0 152 78v239q-56 78 -152 78q-141 0 -141 -201q0 -194 141 -194z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="598" 
d="M77 0v753h90v-289q61 82 189 82q94 0 136 -53.5t41 -178.5v-314h-89v318q0 74 -27 109t-94 35q-101 0 -156 -94v-368h-90z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="241" 
d="M75 637v122h91v-122h-91zM75 0v538h90v-538h-90z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="249" 
d="M83 637v122h91v-122h-91zM173 51q0 -35 -0.5 -54.5t-3 -47t-8.5 -42.5t-15.5 -33.5t-25 -28t-36.5 -19t-50.5 -13t-67.5 -3.5v85q22 0 39 2t29.5 7.5t21 10.5t14 17.5t8.5 20.5t4.5 27.5t1.5 32v38.5v487h89v-487z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="519" 
d="M77 0v753h88v-495l227 280h102l-171 -204l195 -334h-102l-150 266l-101 -120v-146h-88z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="245" 
d="M77 0l1 753h89v-753h-90z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="927" 
d="M78 0v397l-10 141h77l12 -78q59 86 185 86q117 0 152 -95q22 36 69 65.5t125 29.5q90 0 132.5 -54t41.5 -178v-314h-86v318q0 74 -26 109t-90 35q-93 0 -149 -88q3 -28 3 -60v-314h-84v318q0 74 -26 109t-89 35q-93 0 -147 -89v-373h-90z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="597" 
d="M78 0v397l-10 141h77l13 -85q60 93 197 93q94 0 135.5 -54t41.5 -181v-311h-89v314q0 76 -27 112t-94 36q-99 0 -154 -90v-372h-90z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="587" 
d="M538 267q0 -139 -55 -209.5t-189 -70.5q-135 0 -189.5 70t-54.5 210t54.5 211t189.5 72q134 1 189 -71t55 -212zM142 267q0 -107 34 -155.5t118 -48.5t117.5 49t33.5 155q0 108 -34 158t-117 50q-84 0 -118 -50t-34 -158z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="600" 
d="M78 -240v638l-10 140h77l12 -75q12 16 29.5 32t60.5 34.5t95 18.5q209 0 209 -282q0 -276 -212 -276q-118 0 -172 67v-297h-89zM319 467q-97 0 -152 -79v-238q47 -78 153 -78q141 0 141 194q0 201 -142 201z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="596" 
d="M433 -240v296q-54 -66 -171 -66q-212 0 -212 276q0 282 209 282q119 0 182 -83l14 73h78l-10 -124v-654h-90zM281 72q101 0 152 78v239q-56 78 -152 78q-141 0 -141 -201q0 -194 141 -194z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="372" 
d="M78 0v384l-10 154h78l13 -99q8 17 20.5 33.5t34.5 35t58 30t81 11.5v-84q-40 0 -72.5 -12t-51.5 -28t-33 -38.5t-19.5 -36t-8.5 -27.5v-323h-90z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="531" 
d="M494 144q0 -158 -229 -157q-110 2 -169 50.5t-59 138.5h89q0 -54 35.5 -82t102.5 -30q137 -1 139 85q1 38 -35.5 57.5t-88.5 28.5t-104.5 21.5t-89.5 47t-38 93.5q-1 73 54.5 113.5t165.5 39.5q213 -1 213 -177h-88q0 49 -28.5 75.5t-94.5 27.5q-131 1 -131 -83
q0 -34 36.5 -52.5t89 -27.5t105 -22t89 -49.5t36.5 -97.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="333" 
d="M317 -3h-46q-49 0 -83 11.5t-53 28.5t-29.5 47.5t-13 58.5t-2.5 71v247h-70v77h70v156l89 16v-172h135v-77h-135v-244q0 -73 19 -105t79 -32h40v-83z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="593" 
d="M452 0l-14 94q-11 -21 -30 -41t-65 -40.5t-104 -20.5q-94 0 -136 53t-41 178v314h90v-316q0 -74 27 -109.5t93 -35.5q104 0 158 105v357h90v-389l10 -149h-78z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="523" 
d="M8 538h101l153 -455l153 455h100l-207 -538h-94z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="810" 
d="M404 448l-125 -448h-102l-170 538h97l123 -450l125 450h105l131 -452l119 452h96l-164 -538h-104z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="518" 
d="M258 204l-140 -204h-105l192 267l-195 271h108l144 -209l143 209h105l-195 -273l190 -265h-108z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="525" 
d="M171 -225l74 225h-39l-198 538h101l158 -472l155 472h95l-263 -763h-83z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="447" 
d="M19 3v80l302 372h-292v79h395v-78l-302 -373h302v-80h-405z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="409" 
d="M161 595q0 72 15 104.5t60 47t143 14.5v-67q-84 0 -108 -24.5t-24 -100.5v-202q0 -19 -5 -34t-12.5 -24t-16.5 -15t-14 -8t-8 -3q57 -19 57 -84v-204q0 -74 24 -98t107 -24v-67q-97 0 -142.5 15t-60.5 48.5t-15 105.5v221q0 32 -16.5 44t-70.5 12v63q54 0 70.5 11.5
t16.5 43.5v225z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="221" 
d="M77 -194v1014h66v-1014h-66z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="409" 
d="M247 -28q0 -54 -6.5 -83.5t-30.5 -49.5t-65 -26.5t-115 -6.5v67q83 0 107 24.5t24 100.5l1 202q0 62 55 84q-5 0 -16.5 6t-25.5 27t-14 51v204q0 74 -24 98t-107 24v67q73 0 114.5 -7t65.5 -28t30.5 -50t6.5 -84v-221q0 -32 16.5 -44t70.5 -12v-63q-54 0 -70.5 -11.5
t-16.5 -43.5v-225z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="522" 
d="M44 242q0 55 27 92.5t92 37.5q49 0 101.5 -27.5t87.5 -27.5q34 0 51.5 19t17.5 36h57q0 -55 -27 -92.5t-93 -37.5q-49 0 -101 27.5t-87 27.5q-34 0 -51.5 -19t-17.5 -36h-57z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="245" 
d="M77 421v117h90v-117h-90zM77 -173l4 542h83l3 -542h-90z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="563" 
d="M247 -20v87q-107 11 -152 80.5t-45 196.5q0 126 45 197t152 82v87h80v-86q188 -15 193 -207h-90q-1 115 -103 132v-407q102 16 103 114h90q-7 -175 -193 -190v-86h-80zM142 344q0 -92 24 -140t81 -60v403q-57 -12 -81 -62t-24 -141z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="537" 
d="M220 254q3 -24 3 -49q0 -91 -62 -133l337 2v-78h-471v75h1q6 1 13 3t25 11t31 21.5t23.5 36t10.5 52.5q0 23 -4 59h-107v70h92q-3 11 -19 55t-22 73t-6 65q0 87 57 139t164 52q112 0 166.5 -55t54.5 -155h-89q0 61 -35 96t-93 35q-59 0 -94 -34t-35 -95q0 -29 6.5 -56
t19.5 -63t19 -57h160v-70h-146z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="664" 
d="M541 485q37 -62 37 -162q0 -98 -34 -156l71 -72l-55 -54l-68 68q-58 -41 -159 -41t-159 41l-69 -68l-54 54l71 72q-34 58 -34 156q0 97 33 155l-71 71l54 55l67 -68q61 45 162 46q97 0 156 -40l68 68l54 -55zM178 323q0 -176 155 -176t155 176q0 180 -155 180t-155 -180z
" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="575" 
d="M245 0v178h-111v65h111v53h-111v65h86l-210 347h103l176 -322l169 322h107l-207 -347h84v-65h-108v-53h108v-65h-108v-178h-89z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="221" 
d="M77 395v425h66v-425h-66zM143 245v-425h-66v425h66z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="529" 
d="M492 374q0 -76 -58 -121q54 -39 54 -112q0 -156 -227 -156q-120 0 -172 42.5t-52 133.5h81q0 -114 142 -114q141 0 141 89q0 44 -37 67t-89.5 32t-104.5 21t-89 46.5t-37 94.5q0 81 60 123q-56 36 -56 110q0 149 215 149q116 0 165.5 -40.5t49.5 -124.5h-81q0 50 -28 77
t-104 27q-130 0 -130 -87q0 -35 26.5 -56t66 -27.5t86 -17t86 -23t66 -46.5t26.5 -87zM130 398q0 -26 13.5 -43.5t39.5 -27.5t52 -15.5t64.5 -12.5t62.5 -14q44 33 44 84q0 27 -13 46t-39 30t-50.5 17t-64 13.5t-62.5 13.5q-47 -33 -47 -91z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="395" 
d="M61 630v129h84v-129h-84zM229 630v129h85v-129h-85z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="814" 
d="M765 348q0 -81 -18 -146t-58 -118.5t-111.5 -83t-170.5 -29.5q-98 0 -169 29.5t-111 83t-58.5 118.5t-18.5 146t18.5 147t58.5 120.5t111 84.5t169 30q99 0 170.5 -30t111.5 -84.5t58 -120.5t18 -147zM112 348q0 -69 15 -124.5t47.5 -101t91.5 -70.5t141 -25
q295 0 295 321q0 55 -8.5 101.5t-30 89t-54.5 72t-84.5 46.5t-117.5 17q-82 0 -141 -25.5t-91.5 -72t-47.5 -102.5t-15 -126zM567 287q-1 -155 -157 -155q-92 0 -133.5 53.5t-41.5 164.5t41.5 164.5t133.5 53.5q157 0 158 -157h-63q-3 103 -95 103q-58 0 -84 -39t-26 -125
t26 -124.5t84 -38.5q90 0 94 100h63z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="404" 
d="M303 361l-7 39q-49 -49 -129 -49q-134 0 -134 117q0 67 41 95t118 28q46 0 98 -5q-2 57 -18 79.5t-69 22.5q-92 0 -92 -69h-61q0 124 150 124q33 0 58 -5.5t41.5 -13t27.5 -23.5t17 -28.5t9 -37.5t3.5 -41t0.5 -48v-89l8 -96h-62zM180 408q42 0 71.5 17.5t38.5 39.5v67
q-48 5 -81 5q-55 0 -81.5 -15t-26.5 -51q0 -63 79 -63z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="550" 
d="M194 101l-117 154v20l117 161h103l-133 -172l133 -163h-103zM372 101l-117 154v20l117 161h103l-133 -172l133 -163h-103z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="814" 
d="M765 348q0 -81 -18 -146t-58 -118.5t-111.5 -83t-170.5 -29.5q-98 0 -169 29.5t-111 83t-58.5 118.5t-18.5 146t18.5 147t58.5 120.5t111 84.5t169 30q99 0 170.5 -30t111.5 -84.5t58 -120.5t18 -147zM112 348q0 -69 15 -124.5t47.5 -101t91.5 -70.5t141 -25
q295 0 295 321q0 55 -8.5 101.5t-30 89t-54.5 72t-84.5 46.5t-117.5 17q-82 0 -141 -25.5t-91.5 -72t-47.5 -102.5t-15 -126zM261 141v414h159q82 0 118 -28t36 -102q0 -57 -20 -86t-63 -38l79 -160h-66l-72 154h-109v-154h-62zM323 505v-159h92q48 0 71.5 14.5t23.5 64.5
q0 49 -24 64.5t-71 15.5h-92z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="406" 
d="M60 723h253v-66h-253v66z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="335" 
d="M275 609q0 -114 -108 -114q-107 0 -107 114q0 116 107 116q108 0 108 -116zM114 609q0 -66 53 -66q54 0 54 66q0 68 -54 68q-53 0 -53 -68z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="515" 
d="M218 213v115h-154v80h154v114h80v-114h154v-80h-154v-115h-80zM64 96v80h388v-80h-388z" />
    <glyph glyph-name="uni00B2" unicode="&#xb2;" horiz-adv-x="397" 
d="M22 324v65l139 118q49 42 71 63.5t41.5 56.5t19.5 73q0 39 -24.5 62.5t-72.5 23.5q-46 0 -73.5 -27.5t-27.5 -70.5h-75q0 75 44 120.5t131 45.5q91 0 133 -41.5t42 -107.5q0 -31 -8 -59.5t-16.5 -47t-32 -45.5t-34.5 -38l-44 -42l-94 -85l229 4v-68h-348z" />
    <glyph glyph-name="uni00B3" unicode="&#xb3;" horiz-adv-x="439" 
d="M246 648q153 -19 153 -169q0 -76 -45 -122.5t-144 -46.5q-94 0 -138 45t-44 113h75q0 -38 27.5 -63.5t78.5 -25.5q110 0 110 105q0 52 -31.5 81t-89.5 29l-62 -5v55l149 120l-234 -3v69h337v-65z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="335" 
d="M51 631l77 144h95l-91 -144h-81z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="603" 
d="M299 0v298q-149 3 -206.5 51t-57.5 180q0 137 62 184t224 47h204v-760h-66v690h-94v-690h-66zM121 530q0 -95 40 -126.5t138 -32.5v318q-98 -2 -138 -33t-40 -126z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="240" 
d="M75 234v129h93v-129h-93z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="357" 
d="M266 -137q0 -49 -31 -70t-105 -21q-20 0 -37.5 2t-24.5 4l-8 2v54q17 -6 48 -6q38 0 54 8.5t16 33.5q0 45 -85 45h-5l40 88h82l-28 -60q37 -3 60.5 -22.5t23.5 -57.5z" />
    <glyph glyph-name="uni00B9" unicode="&#xb9;" horiz-adv-x="245" 
d="M103 320v428l-95 -43v79l121 53h47v-517h-73z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="399" 
d="M369 548q0 -98 -37.5 -147t-131.5 -49t-132 49t-38 147q0 97 38 146.5t132 49.5t131.5 -49t37.5 -147zM98 548q0 -73 23 -106.5t79 -33.5t79 33.5t23 106.5t-23 106.5t-79 33.5t-79 -33.5t-23 -106.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="550" 
d="M178 101h-102l131 163l-131 172h102l117 -161v-20zM356 101h-102l131 163l-131 172h102l117 -161v-20z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="890" 
d="M86 -40l512 821h83l-513 -821h-82zM103 245v428l-95 -43v79l121 53h47v-517h-73zM736 0v105h-263v65l223 345h113v-345h60v-65h-60v-105h-73zM736 170v294l-179 -294h179z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="912" 
d="M86 -40l512 821h83l-513 -821h-82zM103 245v428l-95 -43v79l121 53h47v-517h-73zM537 4v65l139 118q49 42 71 63.5t41.5 56.5t19.5 73q0 39 -24.5 62.5t-72.5 23.5q-46 0 -73.5 -27.5t-27.5 -70.5h-75q0 75 44 120.5t131 45.5q91 0 133 -41.5t42 -107.5q0 -31 -8 -59.5
t-16.5 -47t-32 -45.5t-34.5 -38l-44 -42l-94 -85l229 4v-68h-348z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1068" 
d="M264 -40l512 821h83l-513 -821h-82zM246 573q153 -19 153 -169q0 -76 -45 -122.5t-144 -46.5q-94 0 -138 45t-44 113h75q0 -38 27.5 -63.5t78.5 -25.5q110 0 110 105q0 52 -31.5 81t-89.5 29l-62 -5v55l149 120l-234 -3v69h337v-65zM914 0v105h-263v65l223 345h113v-345
h60v-65h-60v-105h-73zM914 170v294l-179 -294h179z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="520" 
d="M325 538v-112h-90v112h90zM235 375h90l-1 -37q0 -51 -17 -87.5t-41 -57.5l-47.5 -40.5t-40.5 -51t-17 -75.5q0 -130 119 -130q121 0 121 142h87q0 -221 -207 -221q-111 0 -163 54t-52 148q0 52 17 89t41 58.5l48.5 42t42.5 52.5t19 77z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="663" 
d="M379 767h-94l-91 121h108zM10 0l272 711h99l272 -711h-101l-60 168h-324l-59 -168h-99zM198 252h264l-132 373z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="663" 
d="M283 766l77 121h108l-91 -121h-94zM10 0l272 711h99l272 -711h-101l-60 168h-324l-59 -168h-99zM198 252h264l-132 373z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="663" 
d="M491 766h-90l-68 69l-68 -69h-87l106 121h97zM10 0l272 711h99l272 -711h-101l-60 168h-324l-59 -168h-99zM198 252h264l-132 373z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="663" 
d="M485 880q0 -115 -91 -115q-38 0 -65.5 23t-48.5 23q-39 0 -39 -46h-62q0 116 92 116q37 0 64.5 -23t48.5 -23q39 0 39 45h62zM10 0l272 711h99l272 -711h-101l-60 168h-324l-59 -168h-99zM198 252h264l-132 373z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="663" 
d="M200 766v129h91v-129h-91zM374 766v129h90v-129h-90zM10 0l272 711h99l272 -711h-101l-60 168h-324l-59 -168h-99zM198 252h264l-132 373z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="663" 
d="M434 851q0 -44 -27 -69.5t-74 -25.5t-74 25.5t-27 69.5t27 69.5t74 25.5q46 0 73.5 -25.5t27.5 -69.5zM295 851q0 -18 10.5 -28.5t27.5 -10.5t27.5 10.5t10.5 28.5q0 17 -10.5 27.5t-27.5 10.5t-27.5 -10.5t-10.5 -27.5zM10 0l272 711h99l272 -711h-101l-60 168h-324
l-59 -168h-99zM198 252h264l-132 373z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="929" 
d="M10 0l376 711h511v-84h-365v-218h315v-81h-315v-244h370v-84h-462v179h-235l-92 -179h-103zM440 257v378l-195 -378h195z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="658" 
d="M451 -144q0 -49 -31 -70t-105 -21q-20 0 -37.5 2t-25.5 4l-7 2v54q17 -6 48 -6q38 0 54 8.5t16 33.5q0 45 -85 45h-5l34 76q-137 10 -197 101t-60 270q0 190 68.5 282t225.5 92q135 0 201.5 -65t68.5 -200h-91q-4 93 -48 137t-131 44q-107 0 -154 -69.5t-47 -220.5
t47 -220t154 -69q85 0 129 42.5t48 133.5h91q-3 -122 -58 -184.5t-165 -73.5l-22 -48q37 -3 60.5 -22.5t23.5 -57.5z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="569" 
d="M354 767h-94l-91 121h108zM79 0v711h458v-84h-366v-218h316v-81h-316v-244h371v-84h-463z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="569" 
d="M258 766l77 121h108l-91 -121h-94zM79 0v711h458v-84h-366v-218h316v-81h-316v-244h371v-84h-463z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="569" 
d="M466 766h-90l-68 69l-68 -69h-87l106 121h97zM79 0v711h458v-84h-366v-218h316v-81h-316v-244h371v-84h-463z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="569" 
d="M175 766v129h91v-129h-91zM349 766v129h90v-129h-90zM79 0v711h458v-84h-366v-218h316v-81h-316v-244h371v-84h-463z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="250" 
d="M173 767h-94l-91 121h108zM79 0v711h92v-711h-92z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="250" 
d="M77 766l77 121h108l-91 -121h-94zM79 0v711h92v-711h-92z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="250" 
d="M285 766h-90l-68 69l-68 -69h-87l106 121h97zM79 0v711h92v-711h-92z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="250" 
d="M-6 766v129h91v-129h-91zM168 766v129h90v-129h-90zM79 0v711h92v-711h-92z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="683" 
d="M79 323h-70v64h70v327h222q103 0 169 -21t102 -68.5t49 -110t13 -159.5t-13 -159t-49 -109.5t-102 -68.5t-169 -21h-222v326zM172 323v-243h145q125 -1 173.5 60.5t48.5 214.5t-48 214t-174 61h-145v-243h123v-64h-123z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="721" 
d="M516 880q0 -115 -91 -115q-38 0 -65.5 23t-48.5 23q-39 0 -39 -46h-62q0 116 92 116q37 0 64.5 -23t48.5 -23q39 0 39 45h62zM163 594l3 -594h-87l4 711h99l378 -589l-6 589h88v-711h-99z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="694" 
d="M394 770h-94l-91 121h108zM645 355q0 -190 -69 -281.5t-229 -91.5t-228.5 91t-68.5 282t69 282.5t228 91.5q160 0 229 -92t69 -282zM143 355q0 -152 47.5 -220.5t156.5 -68.5q110 0 157 68.5t47 220.5q0 151 -47.5 220.5t-156.5 69.5t-156.5 -69.5t-47.5 -220.5z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="694" 
d="M298 769l77 121h108l-91 -121h-94zM645 355q0 -190 -69 -281.5t-229 -91.5t-228.5 91t-68.5 282t69 282.5t228 91.5q160 0 229 -92t69 -282zM143 355q0 -152 47.5 -220.5t156.5 -68.5q110 0 157 68.5t47 220.5q0 151 -47.5 220.5t-156.5 69.5t-156.5 -69.5t-47.5 -220.5z
" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="694" 
d="M506 769h-90l-68 69l-68 -69h-87l106 121h97zM645 355q0 -190 -69 -281.5t-229 -91.5t-228.5 91t-68.5 282t69 282.5t228 91.5q160 0 229 -92t69 -282zM143 355q0 -152 47.5 -220.5t156.5 -68.5q110 0 157 68.5t47 220.5q0 151 -47.5 220.5t-156.5 69.5t-156.5 -69.5
t-47.5 -220.5z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="694" 
d="M500 883q0 -115 -91 -115q-38 0 -65.5 23t-48.5 23q-39 0 -39 -46h-62q0 116 92 116q37 0 64.5 -23t48.5 -23q39 0 39 45h62zM645 355q0 -190 -69 -281.5t-229 -91.5t-228.5 91t-68.5 282t69 282.5t228 91.5q160 0 229 -92t69 -282zM143 355q0 -152 47.5 -220.5
t156.5 -68.5q110 0 157 68.5t47 220.5q0 151 -47.5 220.5t-156.5 69.5t-156.5 -69.5t-47.5 -220.5z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="694" 
d="M215 769v129h91v-129h-91zM389 769v129h90v-129h-90zM645 355q0 -190 -69 -281.5t-229 -91.5t-228.5 91t-68.5 282t69 282.5t228 91.5q160 0 229 -92t69 -282zM143 355q0 -152 47.5 -220.5t156.5 -68.5q110 0 157 68.5t47 220.5q0 151 -47.5 220.5t-156.5 69.5
t-156.5 -69.5t-47.5 -220.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="484" 
d="M371 119l-130 134l-129 -134l-52 51l131 135l-131 136l52 50l129 -134l130 134l53 -50l-132 -136l132 -135z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="694" 
d="M44 -9l65 95q-59 89 -59 269q0 191 69 282.5t228 91.5q105 0 172 -41l23 33h101l-62 -91q64 -90 64 -275q0 -190 -69 -281.5t-229 -91.5q-111 0 -178 44l-24 -35h-101zM143 355q0 -116 26 -181l299 438q-46 33 -121 33q-109 0 -156.5 -69.5t-47.5 -220.5zM551 355
q0 121 -29 188l-301 -441q47 -36 126 -36q110 0 157 68.5t47 220.5z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="681" 
d="M388 767h-94l-91 121h108zM613 346q0 -75 -6 -127.5t-23 -100t-47 -75.5t-79 -44.5t-118 -16.5t-118 16.5t-78.5 44.5t-46.5 75.5t-23 100t-6 127.5v365h91v-359q0 -153 38 -218t143 -65t142.5 64.5t37.5 218.5v359h93v-365z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="681" 
d="M292 766l77 121h108l-91 -121h-94zM613 346q0 -75 -6 -127.5t-23 -100t-47 -75.5t-79 -44.5t-118 -16.5t-118 16.5t-78.5 44.5t-46.5 75.5t-23 100t-6 127.5v365h91v-359q0 -153 38 -218t143 -65t142.5 64.5t37.5 218.5v359h93v-365z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="681" 
d="M500 766h-90l-68 69l-68 -69h-87l106 121h97zM613 346q0 -75 -6 -127.5t-23 -100t-47 -75.5t-79 -44.5t-118 -16.5t-118 16.5t-78.5 44.5t-46.5 75.5t-23 100t-6 127.5v365h91v-359q0 -153 38 -218t143 -65t142.5 64.5t37.5 218.5v359h93v-365z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="681" 
d="M209 766v129h91v-129h-91zM383 766v129h90v-129h-90zM613 346q0 -75 -6 -127.5t-23 -100t-47 -75.5t-79 -44.5t-118 -16.5t-118 16.5t-78.5 44.5t-46.5 75.5t-23 100t-6 127.5v365h91v-359q0 -153 38 -218t143 -65t142.5 64.5t37.5 218.5v359h93v-365z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="607" 
d="M253 766l77 121h108l-91 -121h-94zM261 0v228l-251 483h105l191 -404l183 404h108l-245 -481v-230h-91z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="638" 
d="M79 0v711h95l-1 -121h160q146 0 208.5 -49.5t62.5 -182.5q0 -134 -62.5 -183t-210.5 -49h-158v-126h-94zM173 509v-302l162 -1q43 0 71 5t54.5 20t39 46.5t12.5 80.5t-12.5 80t-39 46t-54.5 20t-71 5h-162z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="582" 
d="M77 0v541q0 50 9 87t31.5 68t67 47.5t109.5 17.5q120 0 170.5 -51.5t50.5 -146.5q0 -42 -13.5 -75t-31 -50t-39.5 -28.5t-34 -14t-19 -3.5q8 0 19 -1t38 -11t47.5 -27.5t37 -55.5t16.5 -90q0 -78 -27 -121.5t-94.5 -64.5t-189.5 -21v75q123 0 171.5 29.5t48.5 108.5
q0 44 -15.5 72.5t-47.5 42t-63.5 18t-82.5 5.5v80q187 0 187 120q0 66 -29.5 100t-99.5 34q-69 -1 -98 -35t-29 -99v-551h-90z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="584" 
d="M331 631h-80l-92 144h95zM439 1l-11 64q-69 -78 -189 -78q-191 0 -191 167q0 96 58.5 136.5t167.5 40.5q81 0 146 -9q-2 88 -25.5 120t-104.5 32q-137 0 -137 -103h-83q0 176 216 176q153 0 197 -77q26 -46 27 -163v-40v-128l11 -138h-82zM257 63q66 0 110.5 30.5
t52.5 66.5v90q-61 7 -122 7q-80 0 -118.5 -22.5t-38.5 -76.5q0 -95 116 -95z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="584" 
d="M250 631l77 144h95l-91 -144h-81zM439 1l-11 64q-69 -78 -189 -78q-191 0 -191 167q0 96 58.5 136.5t167.5 40.5q81 0 146 -9q-2 88 -25.5 120t-104.5 32q-137 0 -137 -103h-83q0 176 216 176q153 0 197 -77q26 -46 27 -163v-40v-128l11 -138h-82zM257 63
q66 0 110.5 30.5t52.5 66.5v90q-61 7 -122 7q-80 0 -118.5 -22.5t-38.5 -76.5q0 -95 116 -95z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="584" 
d="M446 630h-86l-70 83l-70 -83h-85l111 143h87zM439 1l-11 64q-69 -78 -189 -78q-191 0 -191 167q0 96 58.5 136.5t167.5 40.5q81 0 146 -9q-2 88 -25.5 120t-104.5 32q-137 0 -137 -103h-83q0 176 216 176q153 0 197 -77q26 -46 27 -163v-40v-128l11 -138h-82zM257 63
q66 0 110.5 30.5t52.5 66.5v90q-61 7 -122 7q-80 0 -118.5 -22.5t-38.5 -76.5q0 -95 116 -95z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="584" 
d="M444 746q0 -115 -90 -115q-36 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM439 1l-11 64q-69 -78 -189 -78q-191 0 -191 167q0 96 58.5 136.5t167.5 40.5q81 0 146 -9q-2 88 -25.5 120t-104.5 32
q-137 0 -137 -103h-83q0 176 216 176q153 0 197 -77q26 -46 27 -163v-40v-128l11 -138h-82zM257 63q66 0 110.5 30.5t52.5 66.5v90q-61 7 -122 7q-80 0 -118.5 -22.5t-38.5 -76.5q0 -95 116 -95z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="584" 
d="M165 630v129h84v-129h-84zM333 630v129h85v-129h-85zM439 1l-11 64q-69 -78 -189 -78q-191 0 -191 167q0 96 58.5 136.5t167.5 40.5q81 0 146 -9q-2 88 -25.5 120t-104.5 32q-137 0 -137 -103h-83q0 176 216 176q153 0 197 -77q26 -46 27 -163v-40v-128l11 -138h-82z
M257 63q66 0 110.5 30.5t52.5 66.5v90q-61 7 -122 7q-80 0 -118.5 -22.5t-38.5 -76.5q0 -95 116 -95z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="584" 
d="M392 711q0 -43 -27 -68t-73 -25t-72.5 25t-26.5 68t26.5 68.5t72.5 25.5t73 -25t27 -69zM254 711q0 -17 10.5 -27.5t27.5 -10.5t27.5 10.5t10.5 27.5q0 18 -10.5 28.5t-27.5 10.5t-27.5 -10.5t-10.5 -28.5zM439 1l-11 64q-69 -78 -189 -78q-191 0 -191 167
q0 96 58.5 136.5t167.5 40.5q81 0 146 -9q-2 88 -25.5 120t-104.5 32q-137 0 -137 -103h-83q0 176 216 176q153 0 197 -77q26 -46 27 -163v-40v-128l11 -138h-82zM257 63q66 0 110.5 30.5t52.5 66.5v90q-61 7 -122 7q-80 0 -118.5 -22.5t-38.5 -76.5q0 -95 116 -95z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="931" 
d="M882 163q0 -28 -10 -55t-34 -56t-73 -47t-118 -18q-147 0 -204 94q-68 -93 -204 -93q-191 0 -191 166q0 96 57.5 133.5t168.5 37.5h132q3 33 9 57q-8 50 -36.5 71t-88.5 21q-137 0 -137 -103h-83q0 176 216 176q133 0 175 -68q59 70 186 70q129 0 184 -71t55 -233h-389
q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90zM647 476q-71 0 -106 -38t-42 -118h292q-10 84 -44 120t-100 36zM414 157q-10 48 -10 95h-106q-82 0 -119.5 -20t-37.5 -74q0 -95 116 -95q64 0 106.5 29.5t50.5 64.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="563" 
d="M400 -138q0 -49 -31 -70t-105 -21q-20 0 -37.5 2t-24.5 4l-8 2v54q17 -6 48 -6q38 0 54 8.5t16 33.5q0 45 -85 45h-5l34 75q-112 9 -159 78.5t-47 199.5q0 139 55 210.5t185 71.5q118 0 173 -53t57 -155h-90q-1 134 -140 134q-80 0 -114 -51t-34 -157q0 -107 33.5 -155.5
t114.5 -48.5q66 0 102.5 29.5t37.5 87.5h90q-7 -171 -182 -189l-22 -49q37 -3 60.5 -22.5t23.5 -57.5z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="576" 
d="M333 631h-80l-92 144h95zM527 163q0 -28 -10 -55t-34 -56t-73 -47t-118 -18q-131 0 -186.5 71t-55.5 209q0 139 55.5 210.5t186.5 71.5q129 0 184 -71t55 -233h-389q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90zM292 476q-71 0 -106 -38t-42 -118h292
q-10 84 -44 120t-100 36z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="576" 
d="M252 631l77 144h95l-91 -144h-81zM527 163q0 -28 -10 -55t-34 -56t-73 -47t-118 -18q-131 0 -186.5 71t-55.5 209q0 139 55.5 210.5t186.5 71.5q129 0 184 -71t55 -233h-389q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90zM292 476q-71 0 -106 -38t-42 -118h292
q-10 84 -44 120t-100 36z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="576" 
d="M448 630h-86l-70 83l-70 -83h-85l111 143h87zM527 163q0 -28 -10 -55t-34 -56t-73 -47t-118 -18q-131 0 -186.5 71t-55.5 209q0 139 55.5 210.5t186.5 71.5q129 0 184 -71t55 -233h-389q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90zM292 476q-71 0 -106 -38
t-42 -118h292q-10 84 -44 120t-100 36z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="576" 
d="M167 630v129h84v-129h-84zM335 630v129h85v-129h-85zM527 163q0 -28 -10 -55t-34 -56t-73 -47t-118 -18q-131 0 -186.5 71t-55.5 209q0 139 55.5 210.5t186.5 71.5q129 0 184 -71t55 -233h-389q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90zM292 476q-71 0 -106 -38
t-42 -118h292q-10 84 -44 120t-100 36z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="241" 
d="M160 631h-80l-92 144h95zM75 0v538h90v-538h-90z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="241" 
d="M82 631l77 144h89l-92 -144h-74zM75 0v538h90v-538h-90z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="241" 
d="M275 630h-86l-70 83l-70 -83h-85l111 143h87zM75 0v538h90v-538h-90z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="241" 
d="M-6 630v129h84v-129h-84zM162 630v129h85v-129h-85zM75 0v538h90v-538h-90z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="587" 
d="M404 657q134 -193 134 -390q0 -140 -54.5 -210t-189.5 -70t-189.5 70t-54.5 210q0 137 54.5 207t189.5 70q50 0 91 -26q-24 52 -55 101l-89 -45l-39 55l89 44l-38 46t-29 31l-10 10h106q18 -18 44 -50l82 41l41 -52zM445 263q0 55 -11 108q-17 42 -52 73t-88 31
q-84 0 -118 -50t-34 -158q0 -107 34 -155.5t118 -48.5q83 0 116.5 47.5t34.5 152.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="597" 
d="M458 746q0 -115 -90 -115q-36 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM78 0v397l-10 141h77l13 -85q60 93 197 93q94 0 135.5 -54t41.5 -181v-311h-89v314q0 76 -27 112t-94 36q-99 0 -154 -90v-372
h-90z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="587" 
d="M334 625h-80l-92 144h95zM538 267q0 -139 -55 -209.5t-189 -70.5q-135 0 -189.5 70t-54.5 210t54.5 211t189.5 72q134 1 189 -71t55 -212zM142 267q0 -107 34 -155.5t118 -48.5t117.5 49t33.5 155q0 108 -34 158t-117 50q-84 0 -118 -50t-34 -158z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="587" 
d="M253 625l77 144h95l-91 -144h-81zM538 267q0 -139 -55 -209.5t-189 -70.5q-135 0 -189.5 70t-54.5 210t54.5 211t189.5 72q134 1 189 -71t55 -212zM142 267q0 -107 34 -155.5t118 -48.5t117.5 49t33.5 155q0 108 -34 158t-117 50q-84 0 -118 -50t-34 -158z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="587" 
d="M449 624h-86l-70 83l-70 -83h-85l111 143h87zM538 267q0 -139 -55 -209.5t-189 -70.5q-135 0 -189.5 70t-54.5 210t54.5 211t189.5 72q134 1 189 -71t55 -212zM142 267q0 -107 34 -155.5t118 -48.5t117.5 49t33.5 155q0 108 -34 158t-117 50q-84 0 -118 -50t-34 -158z
" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="587" 
d="M447 740q0 -115 -90 -115q-37 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM538 267q0 -139 -55 -209.5t-189 -70.5q-135 0 -189.5 70t-54.5 210t54.5 211t189.5 72q134 1 189 -71t55 -212zM142 267
q0 -107 34 -155.5t118 -48.5t117.5 49t33.5 155q0 108 -34 158t-117 50q-84 0 -118 -50t-34 -158z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="587" 
d="M168 624v129h84v-129h-84zM336 624v129h85v-129h-85zM538 267q0 -139 -55 -209.5t-189 -70.5q-135 0 -189.5 70t-54.5 210t54.5 211t189.5 72q134 1 189 -71t55 -212zM142 267q0 -107 34 -155.5t118 -48.5t117.5 49t33.5 155q0 108 -34 158t-117 50q-84 0 -118 -50
t-34 -158z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="482" 
d="M199 413v115h84v-115h-84zM37 275v80h408v-80h-408zM199 102v115h84v-115h-84z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="587" 
d="M41 -9l56 76q-47 66 -47 200q0 140 54.5 211t189.5 72q81 0 134 -28l17 23h100l-55 -76q48 -68 48 -202q0 -139 -55 -209.5t-189 -70.5q-83 0 -137 28l-17 -24h-99zM142 267q0 -75 14 -118l223 306q-32 20 -85 20q-84 0 -118 -50t-34 -158zM445 267q0 78 -15 120
l-223 -304q31 -20 87 -20q84 0 117.5 49t33.5 155z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="593" 
d="M335 631h-80l-92 144h95zM452 0l-14 94q-11 -21 -30 -41t-65 -40.5t-104 -20.5q-94 0 -136 53t-41 178v314h90v-316q0 -74 27 -109.5t93 -35.5q104 0 158 105v357h90v-389l10 -149h-78z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="593" 
d="M254 631l77 144h95l-91 -144h-81zM452 0l-14 94q-11 -21 -30 -41t-65 -40.5t-104 -20.5q-94 0 -136 53t-41 178v314h90v-316q0 -74 27 -109.5t93 -35.5q104 0 158 105v357h90v-389l10 -149h-78z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="593" 
d="M450 630h-86l-70 83l-70 -83h-85l111 143h87zM452 0l-14 94q-11 -21 -30 -41t-65 -40.5t-104 -20.5q-94 0 -136 53t-41 178v314h90v-316q0 -74 27 -109.5t93 -35.5q104 0 158 105v357h90v-389l10 -149h-78z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="593" 
d="M169 630v129h84v-129h-84zM337 630v129h85v-129h-85zM452 0l-14 94q-11 -21 -30 -41t-65 -40.5t-104 -20.5q-94 0 -136 53t-41 178v314h90v-316q0 -74 27 -109.5t93 -35.5q104 0 158 105v357h90v-389l10 -149h-78z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="525" 
d="M223 631l77 144h95l-91 -144h-81zM171 -225l74 225h-39l-198 538h101l158 -472l155 472h95l-263 -763h-83z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="601" 
d="M77 -240v993h90v-279q63 74 176 74q209 0 209 -282q0 -276 -212 -276q-119 0 -173 68v-298h-90zM320 467q-98 0 -153 -80v-235q48 -80 154 -80q141 0 141 194q0 201 -142 201z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="525" 
d="M138 630v129h84v-129h-84zM306 630v129h85v-129h-85zM171 -225l74 225h-39l-198 538h101l158 -472l155 472h95l-263 -763h-83z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="663" 
d="M205 857h253v-71h-253v71zM10 0l272 711h99l272 -711h-101l-60 168h-324l-59 -168h-99zM198 252h264l-132 373z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="584" 
d="M164 723h253v-66h-253v66zM439 1l-11 64q-69 -78 -189 -78q-191 0 -191 167q0 96 58.5 136.5t167.5 40.5q81 0 146 -9q-2 88 -25.5 120t-104.5 32q-137 0 -137 -103h-83q0 176 216 176q153 0 197 -77q26 -46 27 -163v-40v-128l11 -138h-82zM257 63q66 0 110.5 30.5
t52.5 66.5v90q-61 7 -122 7q-80 0 -118.5 -22.5t-38.5 -76.5q0 -95 116 -95z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="663" 
d="M479 874q0 -42 -36.5 -77t-110.5 -35t-110.5 35t-36.5 77h76q0 -20 20.5 -36.5t50.5 -16.5t51 16.5t21 36.5h75zM10 0l272 711h99l272 -711h-101l-60 168h-324l-59 -168h-99zM198 252h264l-132 373z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="584" 
d="M440 752q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76zM439 1l-11 64q-69 -78 -189 -78q-191 0 -191 167q0 96 58.5 136.5t167.5 40.5q81 0 146 -9q-2 88 -25.5 120t-104.5 32q-137 0 -137 -103h-83
q0 176 216 176q153 0 197 -77q26 -46 27 -163v-40v-128l11 -138h-82zM257 63q66 0 110.5 30.5t52.5 66.5v90q-61 7 -122 7q-80 0 -118.5 -22.5t-38.5 -76.5q0 -95 116 -95z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="663" 
d="M463 -115q0 29 19 57t35.5 40.5t32.5 21.5l-58 164h-324l-59 -168h-99l272 711h99l272 -711h-23q-7 -3 -17 -10t-25 -19.5t-25.5 -30t-10.5 -35.5q0 -39 38 -39q10 0 22 3.5t18 6.5l7 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5zM198 252h264l-132 373z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="584" 
d="M346 -115q0 25 12.5 48.5t32 39t30 22.5t17.5 11l-10 59q-69 -78 -189 -78q-191 0 -191 167q0 96 58.5 136.5t167.5 40.5q81 0 146 -9q-2 88 -25.5 120t-104.5 32q-137 0 -137 -103h-83q0 176 216 176q153 0 197 -77q26 -46 27 -163v-40v-128l11 -138q-5 0 -24.5 -12
t-40.5 -35.5t-21 -48.5q0 -39 38 -39q10 0 22 3.5t19 6.5l6 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5zM257 63q66 0 110.5 30.5t52.5 66.5v90q-61 7 -122 7q-80 0 -118.5 -22.5t-38.5 -76.5q0 -95 116 -95z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="658" 
d="M290 769l77 121h108l-91 -121h-94zM612 242q-3 -133 -69 -196.5t-199 -63.5q-157 0 -225.5 91.5t-68.5 281.5t68.5 282t225.5 92q135 0 201.5 -65t68.5 -200h-91q-4 93 -48 137t-131 44q-107 0 -154 -69.5t-47 -220.5t47 -220t154 -69q85 0 129 42.5t48 133.5h91z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="563" 
d="M244 631l77 144h95l-91 -144h-81zM520 180q-7 -192 -230 -192q-131 0 -185.5 70t-54.5 209t55 210.5t185 71.5q118 0 173 -53t57 -155h-90q-1 134 -140 134q-80 0 -114 -51t-34 -157q0 -107 33.5 -155.5t114.5 -48.5q66 0 102.5 29.5t37.5 87.5h90z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="658" 
d="M498 769h-90l-68 69l-68 -69h-87l106 121h97zM612 242q-3 -133 -69 -196.5t-199 -63.5q-157 0 -225.5 91.5t-68.5 281.5t68.5 282t225.5 92q135 0 201.5 -65t68.5 -200h-91q-4 93 -48 137t-131 44q-107 0 -154 -69.5t-47 -220.5t47 -220t154 -69q85 0 129 42.5t48 133.5
h91z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="563" 
d="M440 630h-86l-70 83l-70 -83h-85l111 143h87zM520 180q-7 -192 -230 -192q-131 0 -185.5 70t-54.5 209t55 210.5t185 71.5q118 0 173 -53t57 -155h-90q-1 134 -140 134q-80 0 -114 -51t-34 -157q0 -107 33.5 -155.5t114.5 -48.5q66 0 102.5 29.5t37.5 87.5h90z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="658" 
d="M294 769v129h91v-129h-91zM612 242q-3 -133 -69 -196.5t-199 -63.5q-157 0 -225.5 91.5t-68.5 281.5t68.5 282t225.5 92q135 0 201.5 -65t68.5 -200h-91q-4 93 -48 137t-131 44q-107 0 -154 -69.5t-47 -220.5t47 -220t154 -69q85 0 129 42.5t48 133.5h91z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="563" 
d="M240 637v122h91v-122h-91zM520 180q-7 -192 -230 -192q-131 0 -185.5 70t-54.5 209t55 210.5t185 71.5q118 0 173 -53t57 -155h-90q-1 134 -140 134q-80 0 -114 -51t-34 -157q0 -107 33.5 -155.5t114.5 -48.5q66 0 102.5 29.5t37.5 87.5h90z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="658" 
d="M498 890l-110 -121h-97l-106 121h86l68 -69l68 69h91zM612 242q-3 -133 -69 -196.5t-199 -63.5q-157 0 -225.5 91.5t-68.5 281.5t68.5 282t225.5 92q135 0 201.5 -65t68.5 -200h-91q-4 93 -48 137t-131 44q-107 0 -154 -69.5t-47 -220.5t47 -220t154 -69q85 0 129 42.5
t48 133.5h91z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="563" 
d="M440 773l-113 -143h-87l-109 143h83l69 -81l71 81h86zM520 180q-7 -192 -230 -192q-131 0 -185.5 70t-54.5 209t55 210.5t185 71.5q118 0 173 -53t57 -155h-90q-1 134 -140 134q-80 0 -114 -51t-34 -157q0 -107 33.5 -155.5t114.5 -48.5q66 0 102.5 29.5t37.5 87.5h90z
" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="683" 
d="M479 887l-110 -121h-97l-106 121h86l68 -69l68 69h91zM79 714h222q103 0 169 -21t102 -68.5t49 -110t13 -159.5t-13 -159t-49 -109.5t-102 -68.5t-169 -21h-222v717zM172 630v-550h145q125 -1 173.5 60.5t48.5 214.5t-48 214t-174 61h-145z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="596" 
d="M549 606l50 154h76l-71 -154h-55zM455 0l-12 71q-51 -81 -181 -81q-212 0 -212 276q0 282 209 282q109 0 174 -73v278h90v-609l10 -144h-78zM281 72q101 0 152 78v239q-56 78 -152 78q-141 0 -141 -201q0 -194 141 -194z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="683" 
d="M79 323h-70v64h70v327h222q103 0 169 -21t102 -68.5t49 -110t13 -159.5t-13 -159t-49 -109.5t-102 -68.5t-169 -21h-222v326zM172 323v-243h145q125 -1 173.5 60.5t48.5 214.5t-48 214t-174 61h-145v-243h123v-64h-123z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="596" 
d="M523 586v-442l10 -144h-78l-12 71q-51 -81 -181 -81q-212 0 -212 276q0 282 209 282q109 0 174 -73v111h-130v64h130v103h90v-103h66v-64h-66zM281 72q101 0 152 78v239q-56 78 -152 78q-141 0 -141 -201q0 -194 141 -194z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="569" 
d="M180 857h253v-71h-253v71zM79 0v711h458v-84h-366v-218h316v-81h-316v-244h371v-84h-463z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="576" 
d="M166 723h253v-66h-253v66zM527 163q0 -28 -10 -55t-34 -56t-73 -47t-118 -18q-131 0 -186.5 71t-55.5 209q0 139 55.5 210.5t186.5 71.5q129 0 184 -71t55 -233h-389q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90zM292 476q-71 0 -106 -38t-42 -118h292
q-10 84 -44 120t-100 36z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="569" 
d="M454 874q0 -42 -36.5 -77t-110.5 -35t-110.5 35t-36.5 77h76q0 -20 20.5 -36.5t50.5 -16.5t51 16.5t21 36.5h75zM79 0v711h458v-84h-366v-218h316v-81h-316v-244h371v-84h-463z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="576" 
d="M442 752q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76zM527 163q0 -28 -10 -55t-34 -56t-73 -47t-118 -18q-131 0 -186.5 71t-55.5 209q0 139 55.5 210.5t186.5 71.5q129 0 184 -71t55 -233h-389
q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90zM292 476q-71 0 -106 -38t-42 -118h292q-10 84 -44 120t-100 36z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="569" 
d="M262 766v129h91v-129h-91zM79 0v711h458v-84h-366v-218h316v-81h-316v-244h371v-84h-463z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="576" 
d="M248 637v122h91v-122h-91zM527 163q0 -28 -10 -55t-34 -56t-73 -47t-118 -18q-131 0 -186.5 71t-55.5 209q0 139 55.5 210.5t186.5 71.5q129 0 184 -71t55 -233h-389q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90zM292 476q-71 0 -106 -38t-42 -118h292
q-10 84 -44 120t-100 36z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="569" 
d="M372 -116q0 65 82 116h-375v711h458v-84h-366v-218h316v-81h-316v-244h371v-84q-3 -2 -8.5 -5t-19.5 -13t-24.5 -20.5t-19.5 -26.5t-9 -31q0 -39 38 -39q10 0 22 3.5t19 6.5l6 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="576" 
d="M278 -91q0 42 35 79q-7 -1 -21 -1q-131 0 -186.5 71t-55.5 209q0 139 55.5 210.5t186.5 71.5q129 0 184 -71t55 -233h-389q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90q-1 -43 -23.5 -84.5t-76.5 -66.5q-60 -40 -60 -83q0 -39 38 -39q10 0 22 3t19 7l6 3v-54
q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5zM292 476q-71 0 -106 -38t-42 -118h292q-10 84 -44 120t-100 36z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="569" 
d="M466 887l-110 -121h-97l-106 121h86l68 -69l68 69h91zM79 0v711h458v-84h-366v-218h316v-81h-316v-244h371v-84h-463z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="576" 
d="M448 773l-113 -143h-87l-109 143h83l69 -81l71 81h86zM527 163q0 -28 -10 -55t-34 -56t-73 -47t-118 -18q-131 0 -186.5 71t-55.5 209q0 139 55.5 210.5t186.5 71.5q129 0 184 -71t55 -233h-389q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90zM292 476q-71 0 -106 -38
t-42 -118h292q-10 84 -44 120t-100 36z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="663" 
d="M498 769h-90l-68 69l-68 -69h-87l106 121h97zM605 84q0 -4 -9 -14.5t-30.5 -25.5t-50.5 -29t-75 -23.5t-98 -9.5q-157 0 -224.5 91t-67.5 281q0 191 68 283t224 92q132 0 198 -65.5t66 -179.5h-88q-2 72 -48 116.5t-128 44.5q-106 0 -152.5 -70t-46.5 -221
q0 -153 47 -222t154 -69q52 0 94.5 13.5t62 28.5t19.5 25v140h-145v80h230v-266z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="595" 
d="M450 630h-86l-70 83l-70 -83h-85l111 143h87zM523 -1q0 -43 -2 -68t-14.5 -62t-36 -57.5t-70 -36t-113.5 -15.5q-65 0 -111.5 19.5t-66.5 47.5t-30 56t-10 47v20l95 1q0 -88 73 -112q22 -7 51 -7q50 0 81 12.5t44 39.5t16.5 51t3.5 66v55q-54 -66 -171 -66
q-212 0 -212 276q0 282 209 282q118 0 184 -84l12 74h77l-9 -140v-399zM281 72q101 0 152 78v239q-56 78 -152 78q-141 0 -141 -201q0 -194 141 -194z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="663" 
d="M486 877q0 -42 -36.5 -77t-110.5 -35t-110.5 35t-36.5 77h76q0 -20 20.5 -36.5t50.5 -16.5t51 16.5t21 36.5h75zM605 84q0 -4 -9 -14.5t-30.5 -25.5t-50.5 -29t-75 -23.5t-98 -9.5q-157 0 -224.5 91t-67.5 281q0 191 68 283t224 92q132 0 198 -65.5t66 -179.5h-88
q-2 72 -48 116.5t-128 44.5q-106 0 -152.5 -70t-46.5 -221q0 -153 47 -222t154 -69q52 0 94.5 13.5t62 28.5t19.5 25v140h-145v80h230v-266z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="595" 
d="M444 752q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76zM523 -1q0 -43 -2 -68t-14.5 -62t-36 -57.5t-70 -36t-113.5 -15.5q-65 0 -111.5 19.5t-66.5 47.5t-30 56t-10 47v20l95 1q0 -88 73 -112q22 -7 51 -7
q50 0 81 12.5t44 39.5t16.5 51t3.5 66v55q-54 -66 -171 -66q-212 0 -212 276q0 282 209 282q118 0 184 -84l12 74h77l-9 -140v-399zM281 72q101 0 152 78v239q-56 78 -152 78q-141 0 -141 -201q0 -194 141 -194z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="663" 
d="M294 769v129h91v-129h-91zM605 84q0 -4 -9 -14.5t-30.5 -25.5t-50.5 -29t-75 -23.5t-98 -9.5q-157 0 -224.5 91t-67.5 281q0 191 68 283t224 92q132 0 198 -65.5t66 -179.5h-88q-2 72 -48 116.5t-128 44.5q-106 0 -152.5 -70t-46.5 -221q0 -153 47 -222t154 -69
q52 0 94.5 13.5t62 28.5t19.5 25v140h-145v80h230v-266z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="595" 
d="M250 637v122h91v-122h-91zM523 -1q0 -43 -2 -68t-14.5 -62t-36 -57.5t-70 -36t-113.5 -15.5q-65 0 -111.5 19.5t-66.5 47.5t-30 56t-10 47v20l95 1q0 -88 73 -112q22 -7 51 -7q50 0 81 12.5t44 39.5t16.5 51t3.5 66v55q-54 -66 -171 -66q-212 0 -212 276q0 282 209 282
q118 0 184 -84l12 74h77l-9 -140v-399zM281 72q101 0 152 78v239q-56 78 -152 78q-141 0 -141 -201q0 -194 141 -194z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="663" 
d="M605 84q0 -4 -9 -14.5t-30.5 -25.5t-50.5 -29t-75 -23.5t-98 -9.5q-157 0 -224.5 91t-67.5 281q0 191 68 283t224 92q132 0 198 -65.5t66 -179.5h-88q-2 72 -48 116.5t-128 44.5q-106 0 -152.5 -70t-46.5 -221q0 -153 47 -222t154 -69q52 0 94.5 13.5t62 28.5t19.5 25
v140h-145v80h230v-266zM281 -252q16 39 29 98q6 24 9.5 48t4.5 36v13h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="595" 
d="M372 798q-19 -44 -35 -113q-7 -31 -10.5 -57.5t-3.5 -37.5v-11h-80q1 46 20 112q9 32 21 59t20 38l7 10h61zM523 -1q0 -43 -2 -68t-14.5 -62t-36 -57.5t-70 -36t-113.5 -15.5q-65 0 -111.5 19.5t-66.5 47.5t-30 56t-10 47v20l95 1q0 -88 73 -112q22 -7 51 -7
q50 0 81 12.5t44 39.5t16.5 51t3.5 66v55q-54 -66 -171 -66q-212 0 -212 276q0 282 209 282q118 0 184 -84l12 74h77l-9 -140v-399zM281 72q101 0 152 78v239q-56 78 -152 78q-141 0 -141 -201q0 -194 141 -194z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="692" 
d="M505 766h-90l-68 69l-68 -69h-87l106 121h97zM79 0v711h92v-300h350v300h92v-711h-92v325h-350v-325h-92z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="598" 
d="M283 784h-90l-68 69l-68 -69h-87l106 121h97zM77 0v753h90v-289q61 82 189 82q94 0 136 -53.5t41 -178.5v-314h-89v318q0 74 -27 109t-94 35q-101 0 -156 -94v-368h-90z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="692" 
d="M613 522v-522h-92v325h-350v-325h-92v522h-58v71h58v118h92v-118h350v118h92v-118h58v-71h-58zM521 411v111h-350v-111h350z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="598" 
d="M167 588v-124q61 82 189 82q94 0 136 -53.5t41 -178.5v-314h-89v318q0 74 -27 109t-94 35q-101 0 -156 -94v-368h-90v588h-71v64h71v101h90v-101h125v-64h-125z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="250" 
d="M279 880q0 -115 -91 -115q-38 0 -65.5 23t-48.5 23q-39 0 -39 -46h-62q0 116 92 116q37 0 64.5 -23t48.5 -23q39 0 39 45h62zM79 0v711h92v-711h-92z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="241" 
d="M273 746q0 -115 -90 -115q-29 0 -48.5 12t-35 24t-32.5 12q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM75 0v538h90v-538h-90z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="250" 
d="M-1 857h253v-71h-253v71zM79 0v711h92v-711h-92z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="241" 
d="M-7 723h253v-66h-253v66zM75 0v538h90v-538h-90z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="250" 
d="M273 874q0 -42 -36.5 -77t-110.5 -35t-110.5 35t-36.5 77h76q0 -20 20.5 -36.5t50.5 -16.5t51 16.5t21 36.5h75zM79 0v711h92v-711h-92z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="241" 
d="M269 752q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76zM75 0v538h90v-538h-90z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="250" 
d="M-6 -115q0 28 18 55t34 40t33 22v709h92v-711h-10q-7 -3 -17 -10t-25 -19.5t-25.5 -30t-10.5 -35.5q0 -39 38 -39q10 0 22 3.5t19 6.5l6 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="241" 
d="M75 637v122h91v-122h-91zM-15 -115q0 31 20.5 59.5t38.5 41.5t31 19v533h90v-538h-13q-7 -3 -17 -10t-25 -19.5t-25.5 -30t-10.5 -35.5q0 -39 38 -39q10 0 22 3.5t18 6.5l7 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="250" 
d="M81 766v129h91v-129h-91zM79 0v711h92v-711h-92z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="241" 
d="M75 0v538h90v-538h-90z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="622" 
d="M79 0v711h92v-711h-92zM554 280q0 -68 -4.5 -109.5t-19 -78t-42.5 -54.5t-72 -28t-111 -10h-53v89h53q96 0 126 39.5t30 151.5l1 431h92v-431z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="496" 
d="M75 637v122h91v-122h-91zM330 637v122h91v-122h-91zM75 0v538h90v-538h-90zM420 51q0 -35 -0.5 -54.5t-3 -47t-8.5 -42.5t-15.5 -33.5t-25 -28t-36.5 -19t-50.5 -13t-67.5 -3.5v85q22 0 39 2t29.5 7.5t21 10.5t14 17.5t8.5 20.5t4.5 27.5t1.5 32v38.5v487h89v-487z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="372" 
d="M416 766h-90l-68 69l-68 -69h-87l106 121h97zM304 280q0 -68 -4.5 -109.5t-19 -78t-42.5 -54.5t-72 -28t-111 -10h-53v89h53q96 0 126 39.5t30 151.5l1 431h92v-431z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="249" 
d="M283 630h-86l-70 83l-70 -83h-85l111 143h87zM173 51q0 -35 -0.5 -54.5t-3 -47t-8.5 -42.5t-15.5 -33.5t-25 -28t-36.5 -19t-50.5 -13t-67.5 -3.5v85q22 0 39 2t29.5 7.5t21 10.5t14 17.5t8.5 20.5t4.5 27.5t1.5 32v38.5v487h89v-487z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="636" 
d="M79 0v711h92v-416l337 416h115l-243 -294l246 -417h-105l-202 344l-148 -179v-165h-92zM245 -235q16 39 29 98q6 24 9.5 48t4.5 37v12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="519" 
d="M77 0v753h88v-495l227 280h102l-171 -204l195 -334h-102l-150 266l-101 -120v-146h-88zM208 -235q16 39 29 98q6 24 9.5 48t3.5 37l1 12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="519" 
d="M77 0v538h88v-280l227 280h102l-171 -204l195 -334h-102l-150 266l-101 -120v-146h-88z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="502" 
d="M85 766l77 121h108l-91 -121h-94zM79 0v711h92v-625h318v-86h-410z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="245" 
d="M73 812l77 121h108l-91 -121h-94zM77 0l1 753h89v-753h-90z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="502" 
d="M79 0v711h92v-625h318v-86h-410zM202 -235q16 39 29 98q6 24 9.5 48t3.5 37l1 12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="245" 
d="M77 0l1 753h89v-753h-90zM41 -235q16 39 29 98q6 24 9.5 48t4.5 37v12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="502" 
d="M262 612l54 151h79l-68 -151h-65zM79 0v711h92v-625h318v-86h-410z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="245" 
d="M204 606l50 154h76l-71 -154h-55zM77 0l1 753h89v-753h-90z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="500" 
d="M79 0v711h94v-623h313v-88h-407zM321 302v129h90v-129h-90z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="284" 
d="M81 0v760h89v-760h-89zM211 313v129h90v-129h-90z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="502" 
d="M171 357v-271h318v-86h-410v305l-75 -43l-29 58l104 59v332h92v-279l80 46l30 -58z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="245" 
d="M77 0v316l-74 -42l-29 58l103 59l1 362h89v-311l83 48l30 -58l-113 -65v-367h-90z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="721" 
d="M314 766l77 121h108l-91 -121h-94zM163 594l3 -594h-87l4 711h99l378 -589l-6 589h88v-711h-99z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="597" 
d="M264 631l77 144h95l-91 -144h-81zM78 0v397l-10 141h77l13 -85q60 93 197 93q94 0 135.5 -54t41.5 -181v-311h-89v314q0 76 -27 112t-94 36q-99 0 -154 -90v-372h-90z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="721" 
d="M163 594l3 -594h-87l4 711h99l378 -589l-6 589h88v-711h-99zM297 -235q16 39 29 98q6 24 9.5 48t4.5 37v12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="597" 
d="M78 0v397l-10 141h77l13 -85q60 93 197 93q94 0 135.5 -54t41.5 -181v-311h-89v314q0 76 -27 112t-94 36q-99 0 -154 -90v-372h-90zM231 -235q16 39 29 98q6 24 9.5 48t4.5 37v12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="721" 
d="M522 887l-110 -121h-97l-106 121h86l68 -69l68 69h91zM163 594l3 -594h-87l4 711h99l378 -589l-6 589h88v-711h-99z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="597" 
d="M460 773l-113 -143h-87l-109 143h83l69 -81l71 81h86zM78 0v397l-10 141h77l13 -85q60 93 197 93q94 0 135.5 -54t41.5 -181v-311h-89v314q0 76 -27 112t-94 36q-99 0 -154 -90v-372h-90z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="721" 
d="M642 711v-711q0 -113 -42 -168.5t-161 -55.5h-51v72h51q69 0 96 35t27 117l-399 588l3 -588h-87l4 711h96l382 -569l-5 569h86z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="598" 
d="M77 0v397l-9 141h76l13 -86q22 36 70 65t128 29q94 0 135.5 -54t41.5 -181l1 -249q0 -40 -1 -62t-5 -52t-12 -46t-23 -34t-37 -26.5t-54.5 -14.5t-75.5 -6v85q29 0 49.5 3t33.5 13.5t20 19t10 32t3.5 38t0.5 50.5l1 252q0 76 -27 112t-94 36q-99 0 -155 -92v-370h-90z
" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="694" 
d="M220 860h253v-71h-253v71zM645 355q0 -190 -69 -281.5t-229 -91.5t-228.5 91t-68.5 282t69 282.5t228 91.5q160 0 229 -92t69 -282zM143 355q0 -152 47.5 -220.5t156.5 -68.5q110 0 157 68.5t47 220.5q0 151 -47.5 220.5t-156.5 69.5t-156.5 -69.5t-47.5 -220.5z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="587" 
d="M167 717h253v-66h-253v66zM538 267q0 -139 -55 -209.5t-189 -70.5q-135 0 -189.5 70t-54.5 210t54.5 211t189.5 72q134 1 189 -71t55 -212zM142 267q0 -107 34 -155.5t118 -48.5t117.5 49t33.5 155q0 108 -34 158t-117 50q-84 0 -118 -50t-34 -158z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="694" 
d="M494 877q0 -42 -36.5 -77t-110.5 -35t-110.5 35t-36.5 77h76q0 -20 20.5 -36.5t50.5 -16.5t51 16.5t21 36.5h75zM645 355q0 -190 -69 -281.5t-229 -91.5t-228.5 91t-68.5 282t69 282.5t228 91.5q160 0 229 -92t69 -282zM143 355q0 -152 47.5 -220.5t156.5 -68.5
q110 0 157 68.5t47 220.5q0 151 -47.5 220.5t-156.5 69.5t-156.5 -69.5t-47.5 -220.5z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="587" 
d="M443 746q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76zM538 267q0 -139 -55 -209.5t-189 -70.5q-135 0 -189.5 70t-54.5 210t54.5 211t189.5 72q134 1 189 -71t55 -212zM142 267q0 -107 34 -155.5t118 -48.5
t117.5 49t33.5 155q0 108 -34 158t-117 50q-84 0 -118 -50t-34 -158z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="694" 
d="M215 770l72 121h95l-85 -121h-82zM368 770l86 121h95l-100 -121h-81zM645 355q0 -190 -69 -281.5t-229 -91.5t-228.5 91t-68.5 282t69 282.5t228 91.5q160 0 229 -92t69 -282zM143 355q0 -152 47.5 -220.5t156.5 -68.5q110 0 157 68.5t47 220.5q0 151 -47.5 220.5
t-156.5 69.5t-156.5 -69.5t-47.5 -220.5z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="587" 
d="M161 625l71 144h95l-86 -144h-80zM313 625l86 144h95l-101 -144h-80zM538 267q0 -139 -55 -209.5t-189 -70.5q-135 0 -189.5 70t-54.5 210t54.5 211t189.5 72q134 1 189 -71t55 -212zM142 267q0 -107 34 -155.5t118 -48.5t117.5 49t33.5 155q0 108 -34 158t-117 50
q-84 0 -118 -50t-34 -158z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="960" 
d="M346 0q-160 0 -228 87t-68 268t68 268.5t228 87.5h582v-84h-366v-218h316v-81h-316v-244h371v-84h-587zM144 355q0 -143 47 -210.5t156 -67.5h123v557h-123q-109 0 -156 -68t-47 -211z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="972" 
d="M924 163q-1 -28 -11 -55t-34 -56t-73 -47t-118 -18q-149 0 -196 103q-45 -103 -198 -103q-135 0 -189.5 70t-54.5 210t54.5 211t189.5 72q153 0 198 -105q47 104 196 104q129 0 184 -71t55 -233h-389q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h91zM688 476
q-71 0 -105.5 -37.5t-42.5 -118.5h292q-10 84 -44 120t-100 36zM142 267q0 -108 33.5 -156t118.5 -48q84 0 117.5 48t33.5 156q0 109 -33.5 158.5t-117.5 49.5t-118 -49.5t-34 -158.5z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="665" 
d="M273 766l77 121h108l-91 -121h-94zM79 0v714h253q154 0 218.5 -47t64.5 -177q0 -95 -33.5 -143.5t-108.5 -66.5l151 -280h-99l-138 269q-18 -1 -57 -1h-159v-268h-92zM171 631v-281h153q52 0 83 4t60 18t41.5 43t12.5 75q0 38 -9.5 64t-24.5 41t-42 23t-53.5 10.5
t-67.5 2.5h-153z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="372" 
d="M157 631l77 144h95l-91 -144h-81zM78 0v384l-10 154h78l13 -99q8 17 20.5 33.5t34.5 35t58 30t81 11.5v-84q-40 0 -72.5 -12t-51.5 -28t-33 -38.5t-19.5 -36t-8.5 -27.5v-323h-90z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="665" 
d="M79 0v714h253q154 0 218.5 -47t64.5 -177q0 -95 -33.5 -143.5t-108.5 -66.5l151 -280h-99l-138 269q-18 -1 -57 -1h-159v-268h-92zM171 631v-281h153q52 0 83 4t60 18t41.5 43t12.5 75q0 38 -9.5 64t-24.5 41t-42 23t-53.5 10.5t-67.5 2.5h-153zM254 -235q16 39 29 98
q6 24 9.5 48t3.5 37l1 12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="372" 
d="M78 0v384l-10 154h78l13 -99q8 17 20.5 33.5t34.5 35t58 30t81 11.5v-84q-40 0 -72.5 -12t-51.5 -28t-33 -38.5t-19.5 -36t-8.5 -27.5v-323h-90zM39 -235q16 39 29 98q6 24 9.5 48t4.5 37v12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="665" 
d="M481 887l-110 -121h-97l-106 121h86l68 -69l68 69h91zM79 0v714h253q154 0 218.5 -47t64.5 -177q0 -95 -33.5 -143.5t-108.5 -66.5l151 -280h-99l-138 269q-18 -1 -57 -1h-159v-268h-92zM171 631v-281h153q52 0 83 4t60 18t41.5 43t12.5 75q0 38 -9.5 64t-24.5 41t-42 23
t-53.5 10.5t-67.5 2.5h-153z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="372" 
d="M353 773l-113 -143h-87l-109 143h83l69 -81l71 81h86zM78 0v384l-10 154h78l13 -99q8 17 20.5 33.5t34.5 35t58 30t81 11.5v-84q-40 0 -72.5 -12t-51.5 -28t-33 -38.5t-19.5 -36t-8.5 -27.5v-323h-90z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="629" 
d="M263 769l77 121h108l-91 -121h-94zM585 184q0 -101 -68.5 -152t-201.5 -50q-269 2 -269 238h90q0 -154 183 -157q174 -1 174 119q0 45 -32.5 74t-81.5 41.5t-106 29t-106 35.5t-81.5 62t-32.5 107q1 94 65.5 146.5t192.5 51.5q141 -1 199.5 -57t58.5 -162h-90
q0 61 -34.5 98.5t-133.5 38.5q-88 1 -127 -31t-39 -83q0 -39 24.5 -65t63 -40.5t85.5 -26t94 -26t85.5 -36t63 -61t24.5 -94.5z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="531" 
d="M225 631l77 144h95l-91 -144h-81zM494 144q0 -158 -229 -157q-110 2 -169 50.5t-59 138.5h89q0 -54 35.5 -82t102.5 -30q137 -1 139 85q1 38 -35.5 57.5t-88.5 28.5t-104.5 21.5t-89.5 47t-38 93.5q-1 73 54.5 113.5t165.5 39.5q213 -1 213 -177h-88q0 49 -28.5 75.5
t-94.5 27.5q-131 1 -131 -83q0 -34 36.5 -52.5t89 -27.5t105 -22t89 -49.5t36.5 -97.5z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="629" 
d="M471 769h-90l-68 69l-68 -69h-87l106 121h97zM585 184q0 -101 -68.5 -152t-201.5 -50q-269 2 -269 238h90q0 -154 183 -157q174 -1 174 119q0 45 -32.5 74t-81.5 41.5t-106 29t-106 35.5t-81.5 62t-32.5 107q1 94 65.5 146.5t192.5 51.5q141 -1 199.5 -57t58.5 -162h-90
q0 61 -34.5 98.5t-133.5 38.5q-88 1 -127 -31t-39 -83q0 -39 24.5 -65t63 -40.5t85.5 -26t94 -26t85.5 -36t63 -61t24.5 -94.5z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="531" 
d="M421 630h-86l-70 83l-70 -83h-85l111 143h87zM494 144q0 -158 -229 -157q-110 2 -169 50.5t-59 138.5h89q0 -54 35.5 -82t102.5 -30q137 -1 139 85q1 38 -35.5 57.5t-88.5 28.5t-104.5 21.5t-89.5 47t-38 93.5q-1 73 54.5 113.5t165.5 39.5q213 -1 213 -177h-88
q0 49 -28.5 75.5t-94.5 27.5q-131 1 -131 -83q0 -34 36.5 -52.5t89 -27.5t105 -22t89 -49.5t36.5 -97.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="629" 
d="M421 -152q0 -49 -31 -70t-105 -21q-20 0 -37.5 2t-25.5 4l-7 2v54q17 -6 48 -6q38 0 54 8.5t16 33.5q0 45 -85 45h-5l38 83q-235 17 -235 237h90q0 -154 183 -157q174 -1 174 119q0 45 -32.5 74t-81.5 41.5t-106 29t-106 35.5t-81.5 62t-32.5 107q1 93 65 146t193 52
q141 -1 199.5 -57t58.5 -162h-90q0 61 -34.5 98.5t-133.5 38.5q-88 1 -127 -31t-39 -83q0 -39 24.5 -65t63 -40.5t85.5 -26t94 -26t85.5 -36t63 -61t24.5 -94.5q0 -183 -222 -200l-26 -56q37 -3 60.5 -22.5t23.5 -57.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="531" 
d="M373 -137q0 -49 -31 -70t-105 -21q-20 0 -37.5 2t-25.5 4l-7 2v54q17 -6 48 -6q38 0 54 8.5t16 33.5q0 45 -85 45h-5l33 74q-93 9 -142 57t-49 130h89q0 -54 35.5 -82t102.5 -30q137 -1 139 85q1 38 -35.5 57.5t-88.5 28.5t-104.5 21.5t-89.5 47t-38 93.5
q-1 73 54.5 113.5t165.5 39.5q213 -1 213 -177h-88q0 102 -123 103q-131 3 -131 -83q0 -34 36.5 -52.5t89 -27.5t105 -22t89 -49.5t36.5 -97.5q0 -142 -184 -155l-21 -46q37 -3 60.5 -22.5t23.5 -57.5z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="629" 
d="M471 890l-110 -121h-97l-106 121h86l68 -69l68 69h91zM585 184q0 -101 -68.5 -152t-201.5 -50q-269 2 -269 238h90q0 -154 183 -157q174 -1 174 119q0 45 -32.5 74t-81.5 41.5t-106 29t-106 35.5t-81.5 62t-32.5 107q1 94 65.5 146.5t192.5 51.5q141 -1 199.5 -57
t58.5 -162h-90q0 61 -34.5 98.5t-133.5 38.5q-88 1 -127 -31t-39 -83q0 -39 24.5 -65t63 -40.5t85.5 -26t94 -26t85.5 -36t63 -61t24.5 -94.5z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="531" 
d="M421 773l-113 -143h-87l-109 143h83l69 -81l71 81h86zM494 144q0 -158 -229 -157q-110 2 -169 50.5t-59 138.5h89q0 -54 35.5 -82t102.5 -30q137 -1 139 85q1 38 -35.5 57.5t-88.5 28.5t-104.5 21.5t-89.5 47t-38 93.5q-1 73 54.5 113.5t165.5 39.5q213 -1 213 -177h-88
q0 49 -28.5 75.5t-94.5 27.5q-131 1 -131 -83q0 -34 36.5 -52.5t89 -27.5t105 -22t89 -49.5t36.5 -97.5z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="558" 
d="M367 -139q0 -49 -31 -70t-105 -21q-20 0 -37.5 2t-25.5 4l-7 2v54q17 -6 48 -6q38 0 54 8.5t16 33.5q0 45 -85 45h-5l41 88l2 625h-224v85h542v-85h-226l-2 -626h-11l-28 -59q37 -3 60.5 -22.5t23.5 -57.5z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="333" 
d="M313 -126q0 -49 -31 -70t-105 -21q-20 0 -37.5 2t-25.5 4l-7 2v54q17 -6 48 -6q38 0 54 8.5t16 33.5q0 45 -85 45h-5l39 86q-53 21 -68.5 69t-15.5 133v247h-70v77h70v156l89 16v-172h135v-77h-135v-244q0 -73 19 -105t79 -32h40v-83h-46q-15 0 -22 1l-20 -44
q37 -3 60.5 -22.5t23.5 -57.5z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="558" 
d="M438 887l-110 -121h-97l-106 121h86l68 -69l68 69h91zM230 0l2 626h-224v85h542v-85h-226l-2 -626h-92z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="333" 
d="M230 599l50 154h76l-71 -154h-55zM317 -3h-46q-49 0 -83 11.5t-53 28.5t-29.5 47.5t-13 58.5t-2.5 71v247h-70v77h70v156l89 16v-172h135v-77h-135v-244q0 -73 19 -105t79 -32h40v-83z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="558" 
d="M230 0l1 300h-96v64h96l1 262h-224v85h542v-85h-226l-1 -262h98v-64h-98l-1 -300h-92z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="333" 
d="M179 250v-33q0 -73 19 -105t79 -32h40v-83h-46q-49 0 -83 11.5t-53 28.5t-29.5 47.5t-13 58.5t-2.5 71v36h-67v64h67v147h-70v77h70v156l89 16v-172h135v-77h-135v-147h130v-64h-130z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="681" 
d="M494 880q0 -115 -91 -115q-38 0 -65.5 23t-48.5 23q-39 0 -39 -46h-62q0 116 92 116q37 0 64.5 -23t48.5 -23q39 0 39 45h62zM613 346q0 -75 -6 -127.5t-23 -100t-47 -75.5t-79 -44.5t-118 -16.5t-118 16.5t-78.5 44.5t-46.5 75.5t-23 100t-6 127.5v365h91v-359
q0 -153 38 -218t143 -65t142.5 64.5t37.5 218.5v359h93v-365z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="593" 
d="M448 746q0 -115 -90 -115q-36 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM452 0l-14 94q-11 -21 -30 -41t-65 -40.5t-104 -20.5q-94 0 -136 53t-41 178v314h90v-316q0 -74 27 -109.5t93 -35.5
q104 0 158 105v357h90v-389l10 -149h-78z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="681" 
d="M214 857h253v-71h-253v71zM613 346q0 -75 -6 -127.5t-23 -100t-47 -75.5t-79 -44.5t-118 -16.5t-118 16.5t-78.5 44.5t-46.5 75.5t-23 100t-6 127.5v365h91v-359q0 -153 38 -218t143 -65t142.5 64.5t37.5 218.5v359h93v-365z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="593" 
d="M168 723h253v-66h-253v66zM452 0l-14 94q-11 -21 -30 -41t-65 -40.5t-104 -20.5q-94 0 -136 53t-41 178v314h90v-316q0 -74 27 -109.5t93 -35.5q104 0 158 105v357h90v-389l10 -149h-78z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="681" 
d="M488 874q0 -42 -36.5 -77t-110.5 -35t-110.5 35t-36.5 77h76q0 -20 20.5 -36.5t50.5 -16.5t51 16.5t21 36.5h75zM613 346q0 -75 -6 -127.5t-23 -100t-47 -75.5t-79 -44.5t-118 -16.5t-118 16.5t-78.5 44.5t-46.5 75.5t-23 100t-6 127.5v365h91v-359q0 -153 38 -218
t143 -65t142.5 64.5t37.5 218.5v359h93v-365z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="593" 
d="M444 752q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76zM452 0l-14 94q-11 -21 -30 -41t-65 -40.5t-104 -20.5q-94 0 -136 53t-41 178v314h90v-316q0 -74 27 -109.5t93 -35.5q104 0 158 105v357h90v-389
l10 -149h-78z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="681" 
d="M443 851q0 -44 -27 -69.5t-74 -25.5t-74 25.5t-27 69.5t27 69.5t74 25.5q46 0 73.5 -25.5t27.5 -69.5zM304 851q0 -18 10.5 -28.5t27.5 -10.5t27.5 10.5t10.5 28.5q0 17 -10.5 27.5t-27.5 10.5t-27.5 -10.5t-10.5 -27.5zM613 346q0 -75 -6 -127.5t-23 -100t-47 -75.5
t-79 -44.5t-118 -16.5t-118 16.5t-78.5 44.5t-46.5 75.5t-23 100t-6 127.5v365h91v-359q0 -153 38 -218t143 -65t142.5 64.5t37.5 218.5v359h93v-365z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="593" 
d="M396 711q0 -43 -27 -68t-73 -25t-72.5 25t-26.5 68t26.5 68.5t72.5 25.5t73 -25t27 -69zM258 711q0 -17 10.5 -27.5t27.5 -10.5t27.5 10.5t10.5 27.5q0 18 -10.5 28.5t-27.5 10.5t-27.5 -10.5t-10.5 -28.5zM452 0l-14 94q-11 -21 -30 -41t-65 -40.5t-104 -20.5
q-94 0 -136 53t-41 178v314h90v-316q0 -74 27 -109.5t93 -35.5q104 0 158 105v357h90v-389l10 -149h-78z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="681" 
d="M209 767l72 121h95l-85 -121h-82zM362 767l86 121h95l-100 -121h-81zM613 346q0 -75 -6 -127.5t-23 -100t-47 -75.5t-79 -44.5t-118 -16.5t-118 16.5t-78.5 44.5t-46.5 75.5t-23 100t-6 127.5v365h91v-359q0 -153 38 -218t143 -65t142.5 64.5t37.5 218.5v359h93v-365z
" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="593" 
d="M162 631l71 144h95l-86 -144h-80zM314 631l86 144h95l-101 -144h-80zM452 0l-14 94q-11 -21 -30 -41t-65 -40.5t-104 -20.5q-94 0 -136 53t-41 178v314h90v-316q0 -74 27 -109.5t93 -35.5q104 0 158 105v357h90v-389l10 -149h-78z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="681" 
d="M343 -91q0 39 31 74q-5 0 -16.5 -0.5t-17.5 -0.5q-69 0 -118 16.5t-78.5 44.5t-46.5 75.5t-23 100t-6 127.5v365h91v-359q0 -153 38 -218t143 -65t142.5 64.5t37.5 218.5v359h93v-365q0 -140 -26 -221t-99 -116q-56 -37 -56 -80q0 -39 38 -39q10 0 22 3t18 7l7 3v-54
q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="593" 
d="M363 -113q0 29 19.5 57t36.5 41t32 21l-13 88q-11 -21 -30 -41t-65 -40.5t-104 -20.5q-94 0 -135.5 53t-41.5 178v314h90v-316q0 -74 27 -109.5t93 -35.5q104 0 158 105v357h90v-389l10 -146q-78 -45 -78 -96q0 -39 38 -39q10 0 22 3.5t18 6.5l7 3v-54q-24 -20 -83 -20
q-47 0 -69 22.5t-22 57.5z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="978" 
d="M650 766h-90l-68 69l-68 -69h-87l106 121h97zM489 614l-155 -614h-111l-208 711h105l161 -626l156 626h104l161 -627l161 627h100l-208 -711h-110z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="810" 
d="M562 630h-86l-70 83l-70 -83h-85l111 143h87zM404 448l-125 -448h-102l-170 538h97l123 -450l125 450h105l131 -452l119 452h96l-164 -538h-104z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="607" 
d="M461 766h-90l-68 69l-68 -69h-87l106 121h97zM261 0v228l-251 483h105l191 -404l183 404h108l-245 -481v-230h-91z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="525" 
d="M419 630h-86l-70 83l-70 -83h-85l111 143h87zM171 -225l74 225h-39l-198 538h101l158 -472l155 472h95l-263 -763h-83z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="607" 
d="M170 766v129h91v-129h-91zM344 766v129h90v-129h-90zM261 0v228l-251 483h105l191 -404l183 404h108l-245 -481v-230h-91z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="549" 
d="M231 766l77 121h108l-91 -121h-94zM19 3v79l392 547h-374v79h486v-79l-394 -547h398v-79h-508z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="447" 
d="M188 631l77 144h95l-91 -144h-81zM19 3v80l302 372h-292v79h395v-78l-302 -373h302v-80h-405z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="549" 
d="M235 766v129h91v-129h-91zM19 3v79l392 547h-374v79h486v-79l-394 -547h398v-79h-508z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="447" 
d="M184 637v122h91v-122h-91zM19 3v80l302 372h-292v79h395v-78l-302 -373h302v-80h-405z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="549" 
d="M439 887l-110 -121h-97l-106 121h86l68 -69l68 69h91zM19 3v79l392 547h-374v79h486v-79l-394 -547h398v-79h-508z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="447" 
d="M384 773l-113 -143h-87l-109 143h83l69 -81l71 81h86zM19 3v80l302 372h-292v79h395v-78l-302 -373h302v-80h-405z" />
    <glyph glyph-name="uni018F" unicode="&#x18f;" horiz-adv-x="692" 
d="M643 355q0 -191 -68 -282t-229 -91q-160 0 -228 93.5t-68 291.5h499q-2 145 -48.5 212t-154.5 67q-162 0 -193 -157h-95q18 122 87.5 181t200.5 59q161 0 229 -91.5t68 -282.5zM346 68q96 0 143 52.5t57 165.5h-399q11 -113 58 -165.5t141 -52.5z" />
    <glyph glyph-name="uni019D" unicode="&#x19d;" horiz-adv-x="720" 
d="M163 594v-594q0 -59 -7.5 -96.5t-28 -68t-60 -44.5t-101.5 -14h-50v72h50q67 0 89.5 32t23.5 119l4 711h98l377 -587l-5 587h88v-711h-99z" />
    <glyph glyph-name="Gcaron" unicode="&#x1e6;" horiz-adv-x="663" 
d="M498 890l-110 -121h-97l-106 121h86l68 -69l68 69h91zM605 84q0 -4 -9 -14.5t-30.5 -25.5t-50.5 -29t-75 -23.5t-98 -9.5q-157 0 -224.5 91t-67.5 281q0 191 68 283t224 92q132 0 198 -65.5t66 -179.5h-88q-2 72 -48 116.5t-128 44.5q-106 0 -152.5 -70t-46.5 -221
q0 -153 47 -222t154 -69q52 0 94.5 13.5t62 28.5t19.5 25v140h-145v80h230v-266z" />
    <glyph glyph-name="gcaron" unicode="&#x1e7;" horiz-adv-x="595" 
d="M450 773l-113 -143h-87l-109 143h83l69 -81l71 81h86zM523 -1q0 -43 -2 -68t-14.5 -62t-36 -57.5t-70 -36t-113.5 -15.5q-65 0 -111.5 19.5t-66.5 47.5t-30 56t-10 47v20l95 1q0 -88 73 -112q22 -7 51 -7q50 0 81 12.5t44 39.5t16.5 51t3.5 66v55q-54 -66 -171 -66
q-212 0 -212 276q0 282 209 282q118 0 184 -84l12 74h77l-9 -140v-399zM281 72q101 0 152 78v239q-56 78 -152 78q-141 0 -141 -201q0 -194 141 -194z" />
    <glyph glyph-name="uni01EA" unicode="&#x1ea;" horiz-adv-x="694" 
d="M356 -88q0 40 29 72q-26 -2 -38 -2q-160 0 -228.5 91t-68.5 282t69 282.5t228 91.5q160 0 229 -92t69 -282q0 -271 -143 -342q-57 -38 -57 -81q0 -39 38 -39q10 0 22 3.5t19 6.5l6 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5zM143 355q0 -152 47.5 -220.5
t156.5 -68.5q110 0 157 68.5t47 220.5q0 151 -47.5 220.5t-156.5 69.5t-156.5 -69.5t-47.5 -220.5z" />
    <glyph glyph-name="uni01EB" unicode="&#x1eb;" horiz-adv-x="587" 
d="M266 -97q0 45 41 84h-13q-135 0 -189.5 70t-54.5 210t54.5 211t189.5 72q134 1 189 -71t55 -212q0 -100 -27.5 -164t-90.5 -93q-65 -40 -65 -87q0 -39 38 -39q10 0 22 3t19 7l6 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5zM142 267q0 -107 34 -155.5t118 -48.5
t117.5 49t33.5 155q0 108 -34 158t-117 50q-84 0 -118 -50t-34 -158z" />
    <glyph glyph-name="Aringacute" unicode="&#x1fa;" horiz-adv-x="663" 
d="M283 988l77 121h108l-91 -121h-94zM434 851q0 -44 -27 -69.5t-74 -25.5t-74 25.5t-27 69.5t27 69.5t74 25.5q46 0 73.5 -25.5t27.5 -69.5zM295 851q0 -18 10.5 -28.5t27.5 -10.5t27.5 10.5t10.5 28.5q0 17 -10.5 27.5t-27.5 10.5t-27.5 -10.5t-10.5 -27.5zM10 0l272 711
h99l272 -711h-101l-60 168h-324l-59 -168h-99zM198 252h264l-132 373z" />
    <glyph glyph-name="aringacute" unicode="&#x1fb;" horiz-adv-x="584" 
d="M257 839l77 144h95l-91 -144h-81zM392 711q0 -43 -27 -68t-73 -25t-72.5 25t-26.5 68t26.5 68.5t72.5 25.5t73 -25t27 -69zM254 711q0 -17 10.5 -27.5t27.5 -10.5t27.5 10.5t10.5 27.5q0 18 -10.5 28.5t-27.5 10.5t-27.5 -10.5t-10.5 -28.5zM439 1l-11 64
q-69 -78 -189 -78q-191 0 -191 167q0 96 58.5 136.5t167.5 40.5q81 0 146 -9q-2 88 -25.5 120t-104.5 32q-137 0 -137 -103h-83q0 176 216 176q153 0 197 -77q26 -46 27 -163v-40v-128l11 -138h-82zM257 63q66 0 110.5 30.5t52.5 66.5v90q-61 7 -122 7q-80 0 -118.5 -22.5
t-38.5 -76.5q0 -95 116 -95z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="929" 
d="M409 766l77 121h108l-91 -121h-94zM10 0l376 711h511v-84h-365v-218h315v-81h-315v-244h370v-84h-462v179h-235l-92 -179h-103zM440 257v378l-195 -378h195z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="931" 
d="M448 631l77 144h95l-91 -144h-81zM882 163q0 -28 -10 -55t-34 -56t-73 -47t-118 -18q-147 0 -204 94q-68 -93 -204 -93q-191 0 -191 166q0 96 57.5 133.5t168.5 37.5h132q3 33 9 57q-8 50 -36.5 71t-88.5 21q-137 0 -137 -103h-83q0 176 216 176q133 0 175 -68
q59 70 186 70q129 0 184 -71t55 -233h-389q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90zM647 476q-71 0 -106 -38t-42 -118h292q-10 84 -44 120t-100 36zM414 157q-10 48 -10 95h-106q-82 0 -119.5 -20t-37.5 -74q0 -95 116 -95q64 0 106.5 29.5t50.5 64.5z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="694" 
d="M292 767l77 121h108l-91 -121h-94zM44 -9l65 95q-59 89 -59 269q0 191 69 282.5t228 91.5q105 0 172 -41l23 33h101l-62 -91q64 -90 64 -275q0 -190 -69 -281.5t-229 -91.5q-111 0 -178 44l-24 -35h-101zM143 355q0 -116 26 -181l299 438q-46 33 -121 33
q-109 0 -156.5 -69.5t-47.5 -220.5zM551 355q0 121 -29 188l-301 -441q47 -36 126 -36q110 0 157 68.5t47 220.5z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="587" 
d="M253 625l77 144h95l-91 -144h-81zM41 -9l56 76q-47 66 -47 200q0 140 54.5 211t189.5 72q81 0 134 -28l17 23h100l-55 -76q48 -68 48 -202q0 -139 -55 -209.5t-189 -70.5q-83 0 -137 28l-17 -24h-99zM142 267q0 -75 14 -118l223 306q-32 20 -85 20q-84 0 -118 -50
t-34 -158zM445 267q0 78 -15 120l-223 -304q31 -20 87 -20q84 0 117.5 49t33.5 155z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="629" 
d="M585 184q0 -101 -68.5 -152t-201.5 -50q-269 2 -269 238h90q0 -154 183 -157q174 -1 174 119q0 45 -32.5 74t-81.5 41.5t-106 29t-106 35.5t-81.5 62t-32.5 107q1 94 65.5 146.5t192.5 51.5q141 -1 199.5 -57t58.5 -162h-90q0 61 -34.5 98.5t-133.5 38.5q-88 1 -127 -31
t-39 -83q0 -39 24.5 -65t63 -40.5t85.5 -26t94 -26t85.5 -36t63 -61t24.5 -94.5zM258 -250q16 39 29 98q6 24 9.5 48t3.5 36l1 13h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="531" 
d="M494 144q0 -158 -229 -157q-110 2 -169 50.5t-59 138.5h89q0 -54 35.5 -82t102.5 -30q137 -1 139 85q1 38 -35.5 57.5t-88.5 28.5t-104.5 21.5t-89.5 47t-38 93.5q-1 73 54.5 113.5t165.5 39.5q213 -1 213 -177h-88q0 49 -28.5 75.5t-94.5 27.5q-131 1 -131 -83
q0 -34 36.5 -52.5t89 -27.5t105 -22t89 -49.5t36.5 -97.5zM210 -235q16 39 29 98q6 24 9.5 48t3.5 37l1 12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="568" 
d="M234 0l2 625h-221v86h538v-86h-223l-2 -625h-94zM194 -237q16 39 30 98q5 24 8.5 48t4.5 36l1 12h79q-4 -50 -20 -102q-9 -28 -19.5 -51t-16.5 -32l-6 -9h-61z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="333" 
d="M317 -3h-46q-49 0 -83 11.5t-53 28.5t-29.5 47.5t-13 58.5t-2.5 71v247h-70v77h70v156l89 16v-172h135v-77h-135v-244q0 -73 19 -105t79 -32h40v-83zM150 -224q16 39 29 98q6 24 9.5 48t3.5 36l1 13h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="uni0232" unicode="&#x232;" horiz-adv-x="607" 
d="M175 857h253v-71h-253v71zM261 0v228l-251 483h105l191 -404l183 404h108l-245 -481v-230h-91z" />
    <glyph glyph-name="uni0233" unicode="&#x233;" horiz-adv-x="525" 
d="M137 723h253v-66h-253v66zM171 -225l74 225h-39l-198 538h101l158 -472l155 472h95l-263 -763h-83z" />
    <glyph glyph-name="uni0237" unicode="&#x237;" horiz-adv-x="249" 
d="M173 51q0 -35 -0.5 -54.5t-3 -47t-8.5 -42.5t-15.5 -33.5t-25 -28t-36.5 -19t-50.5 -13t-67.5 -3.5v85q22 0 39 2t29.5 7.5t21 10.5t14 17.5t8.5 20.5t4.5 27.5t1.5 32v38.5v487h89v-487z" />
    <glyph glyph-name="uni0259" unicode="&#x259;" horiz-adv-x="580" 
d="M531 270q0 -139 -56 -211t-187 -72q-128 0 -183 70.5t-55 229.5h389q-2 98 -37 142t-113 44q-76 0 -110 -31.5t-35 -67.5h-91q1 28 11 55t33.5 56t72.5 47t118 18q131 0 187 -71t56 -209zM289 62q70 0 104.5 36t42.5 114h-291q10 -80 44 -115t100 -35z" />
    <glyph glyph-name="uni0272" unicode="&#x272;" horiz-adv-x="603" 
d="M173 62q0 -35 -0.5 -54.5t-3 -47t-8.5 -42.5t-15.5 -33.5t-25 -28t-36.5 -19t-50.5 -13t-67.5 -3.5v79q22 0 39 1.5t29 8l20.5 11t14 17.5t8.5 21t4.5 29t1.5 33v41v335l-10 141h78l13 -85q22 35 70 64t127 29q94 0 135.5 -54t41.5 -181v-311h-90v314q0 77 -26.5 112.5
t-93.5 35.5q-99 0 -155 -92v-308z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="426" 
d="M378 630h-86l-70 83l-70 -83h-85l111 143h87z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="427" 
d="M379 773l-113 -143h-87l-109 143h83l69 -81l71 81h86z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="419" 
d="M348 752q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="286" 
d="M55 637v122h91v-122h-91z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="379" 
d="M262 711q0 -43 -27 -68t-73 -25t-72.5 25t-26.5 68t26.5 68.5t72.5 25.5t73 -25t27 -69zM124 711q0 -17 10.5 -27.5t27.5 -10.5t27.5 10.5t10.5 27.5q0 18 -10.5 28.5t-27.5 10.5t-27.5 -10.5t-10.5 -28.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="368" 
d="M33 -112q0 33 23.5 63.5t46.5 44.5l23 14l77 -6q-3 -2 -8.5 -5t-19.5 -13t-24.5 -20.5t-19.5 -26.5t-9 -31q0 -39 38 -39q10 0 22 3.5t18 6.5l7 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="430" 
d="M371 746q0 -115 -90 -115q-37 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="405" 
d="M61 631l71 144h95l-86 -144h-80zM213 631l86 144h95l-101 -144h-80z" />
    <glyph glyph-name="gravecomb" unicode="&#x300;" 
d="M-51 631h-80l-92 144h95z" />
    <glyph glyph-name="acutecomb" unicode="&#x301;" 
d="M-229 631l77 144h95l-91 -144h-81z" />
    <glyph glyph-name="uni0302" unicode="&#x302;" 
d="M-70 630h-86l-70 83l-70 -83h-85l111 143h87z" />
    <glyph glyph-name="tildecomb" unicode="&#x303;" 
d="M-65 746q0 -115 -90 -115q-37 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61z" />
    <glyph glyph-name="uni0304" unicode="&#x304;" 
d="M-313 723h253v-66h-253v66z" />
    <glyph glyph-name="uni0306" unicode="&#x306;" 
d="M-51 752q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76z" />
    <glyph glyph-name="uni0307" unicode="&#x307;" 
d="M-145 637v122h91v-122h-91z" />
    <glyph glyph-name="uni0308" unicode="&#x308;" 
d="M-314 630v129h84v-129h-84zM-146 630v129h85v-129h-85z" />
    <glyph glyph-name="uni030A" unicode="&#x30a;" 
d="M-59 711q0 -43 -27 -68t-73 -25t-72.5 25t-26.5 68t26.5 68.5t72.5 25.5t73 -25t27 -69zM-197 711q0 -17 10.5 -27.5t27.5 -10.5t27.5 10.5t10.5 27.5q0 18 -10.5 28.5t-27.5 10.5t-27.5 -10.5t-10.5 -28.5z" />
    <glyph glyph-name="uni030B" unicode="&#x30b;" 
d="M-387 631l71 144h95l-86 -144h-80zM-235 631l86 144h95l-101 -144h-80z" />
    <glyph glyph-name="uni030C" unicode="&#x30c;" 
d="M-70 773l-113 -143h-87l-109 143h83l69 -81l71 81h86z" />
    <glyph glyph-name="uni0312" unicode="&#x312;" 
d="M-52 798q-19 -44 -35 -113q-7 -31 -10.5 -57.5t-3.5 -37.5v-11h-80q1 46 20 112q9 32 21 59t20 38l7 10h61z" />
    <glyph glyph-name="dotbelowcomb" unicode="&#x323;" 
d="M-145 -182v114h90v-114h-90z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" 
d="M-198 -235q16 39 29 98q6 24 9.5 48t3.5 37l1 12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="uni0327" unicode="&#x327;" 
d="M-51 -137q0 -49 -31 -70t-105 -21q-20 0 -37.5 2t-25.5 4l-7 2v54q17 -6 48 -6q38 0 54 8.5t16 33.5q0 45 -85 45h-5l40 88h82l-28 -60q37 -3 60.5 -22.5t23.5 -57.5z" />
    <glyph glyph-name="uni0328" unicode="&#x328;" 
d="M-272 -112q0 33 23 63.5t47 44.5l23 14l77 -6q-3 -2 -8.5 -5t-19.5 -13t-24.5 -20.5t-19.5 -26.5t-9 -31q0 -39 38 -39q10 0 22 3.5t19 6.5l6 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5z" />
    <glyph glyph-name="uni0335" unicode="&#x335;" 
d="M-333 304v64h286v-64h-286z" />
    <glyph glyph-name="uni0336" unicode="&#x336;" 
d="M-727 300v71h650v-71h-650z" />
    <glyph glyph-name="uni0337" unicode="&#x337;" 
d="M-325 209l-29 58l276 158l30 -58z" />
    <glyph glyph-name="uni0338" unicode="&#x338;" 
d="M-467 102l404 554h100l-405 -554h-99z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="642" 
d="M493 473v-286q0 -58 12.5 -87t54.5 -29h17v-71h-20q-37 0 -63 9.5t-40.5 24t-22.5 40.5t-10 50.5t-2 62.5v286h-210v-473h-74v473h-85v75h528v-75h-85z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="978" 
d="M538 767h-94l-91 121h108zM489 614l-155 -614h-111l-208 711h105l161 -626l156 626h104l161 -627l161 627h100l-208 -711h-110z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="810" 
d="M447 631h-80l-92 144h95zM404 448l-125 -448h-102l-170 538h97l123 -450l125 450h105l131 -452l119 452h96l-164 -538h-104z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="978" 
d="M442 766l77 121h108l-91 -121h-94zM489 614l-155 -614h-111l-208 711h105l161 -626l156 626h104l161 -627l161 627h100l-208 -711h-110z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="810" 
d="M366 631l77 144h95l-91 -144h-81zM404 448l-125 -448h-102l-170 538h97l123 -450l125 450h105l131 -452l119 452h96l-164 -538h-104z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="978" 
d="M359 766v129h91v-129h-91zM533 766v129h90v-129h-90zM489 614l-155 -614h-111l-208 711h105l161 -626l156 626h104l161 -627l161 627h100l-208 -711h-110z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="810" 
d="M281 630v129h84v-129h-84zM449 630v129h85v-129h-85zM404 448l-125 -448h-102l-170 538h97l123 -450l125 450h105l131 -452l119 452h96l-164 -538h-104z" />
    <glyph glyph-name="uni1E9E" unicode="&#x1e9e;" horiz-adv-x="676" 
d="M644 225q0 -101 -60.5 -163t-192.5 -62h-150v78h148q159 0 159 160q0 73 -45 113t-123 40q-20 0 -41 -2.5t-33 -4.5l-11 -3v63l213 189h-204q-73 0 -104 -38t-31 -110v-485h-90v491q0 105 46 162t172 58h322v-78l-199 -181q224 -19 224 -227z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="663" 
d="M10 0l272 711h99l272 -711h-101l-60 168h-324l-59 -168h-99zM198 252h264l-132 373zM285 -182v114h90v-114h-90z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="584" 
d="M439 1l-11 64q-69 -78 -189 -78q-191 0 -191 167q0 96 58.5 136.5t167.5 40.5q81 0 146 -9q-2 88 -25.5 120t-104.5 32q-137 0 -137 -103h-83q0 176 216 176q153 0 197 -77q26 -46 27 -163v-40v-128l11 -138h-82zM257 63q66 0 110.5 30.5t52.5 66.5v90q-61 7 -122 7
q-80 0 -118.5 -22.5t-38.5 -76.5q0 -95 116 -95zM242 -188v114h90v-114h-90z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="569" 
d="M79 0v711h458v-84h-366v-218h316v-81h-316v-244h371v-84h-463zM266 -182v114h90v-114h-90z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="576" 
d="M527 163q0 -28 -10 -55t-34 -56t-73 -47t-118 -18q-131 0 -186.5 71t-55.5 209q0 139 55.5 210.5t186.5 71.5q129 0 184 -71t55 -233h-389q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90zM292 476q-71 0 -106 -38t-42 -118h292q-10 84 -44 120t-100 36zM250 -187v114
h90v-114h-90z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="569" 
d="M460 880q0 -115 -91 -115q-38 0 -65.5 23t-48.5 23q-39 0 -39 -46h-62q0 116 92 116q37 0 64.5 -23t48.5 -23q39 0 39 45h62zM79 0v711h458v-84h-366v-218h316v-81h-316v-244h371v-84h-463z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="576" 
d="M446 746q0 -115 -90 -115q-36 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM527 163q0 -28 -10 -55t-34 -56t-73 -47t-118 -18q-131 0 -186.5 71t-55.5 209q0 139 55.5 210.5t186.5 71.5q129 0 184 -71
t55 -233h-389q3 -95 38 -138t112 -43q76 0 110 31.5t35 67.5h90zM292 476q-71 0 -106 -38t-42 -118h292q-10 84 -44 120t-100 36z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="250" 
d="M79 0v711h92v-711h-92zM80 -185v114h90v-114h-90z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="241" 
d="M75 637v122h91v-122h-91zM75 0v538h90v-538h-90zM75 -182v114h90v-114h-90z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="694" 
d="M645 355q0 -190 -69 -281.5t-229 -91.5t-228.5 91t-68.5 282t69 282.5t228 91.5q160 0 229 -92t69 -282zM143 355q0 -152 47.5 -220.5t156.5 -68.5q110 0 157 68.5t47 220.5q0 151 -47.5 220.5t-156.5 69.5t-156.5 -69.5t-47.5 -220.5zM302 -188v114h90v-114h-90z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" horiz-adv-x="587" 
d="M538 267q0 -139 -55 -209.5t-189 -70.5q-135 0 -189.5 70t-54.5 210t54.5 211t189.5 72q134 1 189 -71t55 -212zM142 267q0 -107 34 -155.5t118 -48.5t117.5 49t33.5 155q0 108 -34 158t-117 50q-84 0 -118 -50t-34 -158zM249 -188v114h90v-114h-90z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="681" 
d="M613 346q0 -75 -6 -127.5t-23 -100t-47 -75.5t-79 -44.5t-118 -16.5t-118 16.5t-78.5 44.5t-46.5 75.5t-23 100t-6 127.5v365h91v-359q0 -153 38 -218t143 -65t142.5 64.5t37.5 218.5v359h93v-365zM296 -188v114h90v-114h-90z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" horiz-adv-x="593" 
d="M452 0l-14 94q-11 -21 -30 -41t-65 -40.5t-104 -20.5q-94 0 -136 53t-41 178v314h90v-316q0 -74 27 -109.5t93 -35.5q104 0 158 105v357h90v-389l10 -149h-78zM250 -188v114h90v-114h-90z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="607" 
d="M349 767h-94l-91 121h108zM261 0v228l-251 483h105l191 -404l183 404h108l-245 -481v-230h-91z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="525" 
d="M304 631h-80l-92 144h95zM171 -225l74 225h-39l-198 538h101l158 -472l155 472h95l-263 -763h-83z" />
    <glyph glyph-name="uni1EF8" unicode="&#x1ef8;" horiz-adv-x="607" 
d="M455 880q0 -115 -91 -115q-38 0 -65.5 23t-48.5 23q-39 0 -39 -46h-62q0 116 92 116q37 0 64.5 -23t48.5 -23q39 0 39 45h62zM261 0v228l-251 483h105l191 -404l183 404h108l-245 -481v-230h-91z" />
    <glyph glyph-name="uni1EF9" unicode="&#x1ef9;" horiz-adv-x="525" 
d="M417 746q0 -115 -90 -115q-37 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM171 -225l74 225h-39l-198 538h101l158 -472l155 472h95l-263 -763h-83z" />
    <glyph glyph-name="uni2009" unicode="&#x2009;" horiz-adv-x="70" 
 />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="587" 
d="M64 286v68h459v-68h-459z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="930" 
d="M81 286v68h768v-68h-768z" />
    <glyph glyph-name="uni2016" unicode="&#x2016;" horiz-adv-x="401" 
d="M77 -180v1000h66v-1000h-66zM257 -180v1000h66v-1000h-66z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="201" 
d="M50 525q0 39 20 115q10 36 22.5 66t20.5 42l8 12h65q-23 -50 -38 -126q-7 -35 -10.5 -62t-3.5 -37v-10h-84z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="201" 
d="M30 525q23 50 38 126q7 35 10.5 62t3.5 37v10h84q0 -40 -20 -115q-10 -36 -22.5 -66t-20.5 -42l-8 -12h-65z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="230" 
d="M44 -106q23 50 38 126q7 35 10.5 62t3.5 37v10h84q0 -39 -20 -115q-10 -36 -22.5 -66t-20.5 -42l-8 -12h-65z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="342" 
d="M50 525q0 39 20 115q10 36 22.5 66t20.5 42l8 12h65q-23 -50 -38 -126q-7 -35 -10.5 -62t-3.5 -37v-10h-84zM191 525q0 39 20 115q10 36 22.5 66t20.5 42l8 12h65q-23 -50 -38 -126q-7 -35 -10.5 -62t-3.5 -37v-10h-84z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="342" 
d="M30 525q23 50 38 126q7 35 10.5 62t3.5 37v10h84q0 -40 -20 -115q-10 -36 -22.5 -66t-20.5 -42l-8 -12h-65zM171 525q23 50 38 126q7 35 10.5 62t3.5 37v10h84q0 -40 -20 -115q-10 -36 -23 -66t-21 -42l-8 -12h-64z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="353" 
d="M30 -121q23 50 38 126q7 35 10.5 62t3.5 37v10h84q0 -39 -20 -115q-10 -36 -22.5 -66t-20.5 -42l-8 -12h-65zM168 -121q22 50 38 126q7 35 10.5 62t3.5 37v10h83q0 -39 -20 -115q-9 -36 -22 -66t-21 -42l-8 -12h-64z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="543" 
d="M77 574h161v199h66v-199h162v-57h-162v-517h-66v517h-161v57z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="565" 
d="M77 574h161v199h66v-199h162v-57h-162v-261h162v-58h-162v-198h-66v198h-161v58h161v261h-161v57z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="457" 
d="M169 226v144h119v-144h-119z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="615" 
d="M88 0v132h92v-132h-92zM261 0v132h93v-132h-93zM435 0v132h93v-132h-93z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1143" 
d="M360 539q0 -86 -37 -130.5t-125 -44.5t-126 45t-38 130q0 87 38 133t126 46q87 0 124.5 -46t37.5 -133zM117 0l460 702h86l-460 -702h-86zM101 539q0 -59 23 -87.5t74 -28.5t73 28.5t22 87.5q0 60 -22 90t-73 30q-97 0 -97 -120zM744 159q0 -86 -37.5 -130.5
t-125.5 -44.5q-87 0 -125 45t-38 130q0 87 38 133t125 46q88 0 125.5 -45.5t37.5 -133.5zM1104 159q0 -86 -37.5 -130.5t-125.5 -44.5q-87 0 -125.5 45t-38.5 130q0 87 38.5 133t125.5 46q88 0 125.5 -46t37.5 -133zM485 159q0 -58 22.5 -87t73.5 -29q52 0 74 28.5t22 87.5
q0 60 -22.5 90t-73.5 30q-96 0 -96 -120zM845 159q0 -58 22.5 -87t73.5 -29t73.5 28.5t22.5 87.5q0 120 -96 120t-96 -120z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="372" 
d="M194 101l-117 154v20l117 161h103l-133 -172l133 -163h-103z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="372" 
d="M178 101h-102l131 163l-131 172h102l117 -161v-20z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="270" 
d="M-159 -40l512 821h83l-513 -821h-82z" />
    <glyph glyph-name="uni2070" unicode="&#x2070;" horiz-adv-x="500" 
d="M449 577q0 -144 -46 -205t-153 -61q-108 0 -154 61t-46 205t46 205t154 61q107 0 153 -61t46 -205zM126 577q0 -110 29 -154t95 -44t94.5 43.5t28.5 154.5t-28.5 155t-94.5 44t-95 -44t-29 -155z" />
    <glyph glyph-name="uni2074" unicode="&#x2074;" horiz-adv-x="442" 
d="M288 320v105h-263v65l223 345h113v-345h60v-65h-60v-105h-73zM288 490v294l-179 -294h179z" />
    <glyph glyph-name="uni2075" unicode="&#x2075;" horiz-adv-x="457" 
d="M164 766l-23 -134q43 28 113 28q83 0 124.5 -49t41.5 -126q0 -80 -45 -127.5t-143 -47.5t-144.5 43.5t-46.5 104.5h79q0 -34 29 -59t83 -25q111 0 111 111q0 53 -29 83t-81 30q-23 0 -42 -4.5t-30 -11t-19 -13.5t-11 -11l-3 -5h-71l43 277h292v-66z" />
    <glyph glyph-name="uni2076" unicode="&#x2076;" horiz-adv-x="484" 
d="M439 485q0 -174 -193 -175q-106 0 -151 57.5t-45 180.5q0 163 45 230t151 67q92 0 133.5 -45t41.5 -103h-75q0 33 -24.5 57.5t-74.5 24.5q-55 0 -83 -36t-34 -129q46 50 137 50q89 0 130.5 -49.5t41.5 -129.5zM251 600q-84 0 -123 -69q2 -83 30.5 -119.5t87.5 -36.5
q114 0 114 112q0 113 -109 113z" />
    <glyph glyph-name="uni2077" unicode="&#x2077;" horiz-adv-x="375" 
d="M100 320q0 68 18.5 139t45 123t53.5 94t45 64l19 22l-267 -2v72h351v-72l-20.5 -24t-44.5 -63.5t-57 -98t-45 -119.5t-20 -135h-78z" />
    <glyph glyph-name="uni2078" unicode="&#x2078;" horiz-adv-x="482" 
d="M244 311q-104 0 -149.5 39t-45.5 112q0 64 29.5 97.5t57.5 35.5q-21 2 -46 30t-25 78q0 142 179 142q177 0 177 -142q0 -50 -25 -78t-47 -30q28 -2 58.5 -35.5t30.5 -97.5q0 -151 -194 -151zM244 786q-103 0 -103 -90q0 -74 103 -74q101 0 101 74q0 90 -101 90zM244 563
q-114 0 -114 -92q0 -95 114 -95q113 0 113 95q0 46 -27 69t-86 23z" />
    <glyph glyph-name="uni2079" unicode="&#x2079;" horiz-adv-x="484" 
d="M439 595q0 -161 -43.5 -223t-153.5 -62q-98 0 -142.5 45t-44.5 102h75q0 -32 27.5 -56.5t84.5 -24.5q55 0 82.5 31.5t34.5 119.5q-48 -50 -134 -50q-175 0 -175 192q0 175 193 176q107 0 151.5 -58.5t44.5 -191.5zM238 542q85 0 124 69q-2 92 -30.5 130t-88.5 38
q-114 0 -114 -113q0 -124 109 -124z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="617" 
d="M195 235q13 -84 51 -123.5t110 -39.5q60 0 96 29t40 85h78q-9 -188 -217 -188q-119 0 -177 58.5t-73 178.5h-83v63h78q-1 16 -1 52v41h-77v64h82q13 126 71 187.5t180 61.5q107 0 162 -52t58 -149h-79q-3 61 -38.5 94t-99.5 33q-73 0 -112 -42.5t-50 -132.5h222v-64h-228
v-41q0 -36 1 -52h227v-63h-221z" />
    <glyph glyph-name="uni20BD" unicode="&#x20bd;" horiz-adv-x="640" 
d="M114 0v173h-94v67h94v78h-94v72l94 -1v323h211q151 0 216 -43t65 -155q0 -111 -65 -153.5t-216 -42.5h-119v-78h180v-67h-180v-173h-92zM206 641v-252h129q41 0 70 4.5t54.5 17.5t38.5 39t13 65t-13 65t-38.5 39t-54.5 17.5t-70 4.5h-129z" />
    <glyph glyph-name="uni2116" unicode="&#x2116;" horiz-adv-x="1239" 
d="M1138 479q0 -118 -46.5 -178.5t-163.5 -60.5q-115 0 -161.5 61t-46.5 178q0 118 46.5 179t161.5 62q116 1 163 -61t47 -180zM79 0l4 711h94l380 -580l-2 580h83v-711h-94l-382 583l-1 -583h-82zM805 479q0 -88 28 -129t96 -41t96 41t28 129q0 87 -28.5 129.5t-96.5 42.5
q-67 0 -95 -42.5t-28 -129.5zM717 101v77h423v-77h-423z" />
    <glyph glyph-name="uni2117" unicode="&#x2117;" horiz-adv-x="814" 
d="M765 348q0 -81 -18 -146t-58 -118.5t-111.5 -83t-170.5 -29.5q-98 0 -169 29.5t-111 83t-58.5 118.5t-18.5 146t18.5 147t58.5 120.5t111 84.5t169 30q99 0 170.5 -30t111.5 -84.5t58 -120.5t18 -147zM112 348q0 -69 15 -124.5t47.5 -101t91.5 -70.5t141 -25
q295 0 295 321q0 55 -8.5 101.5t-30 89t-54.5 72t-84.5 46.5t-117.5 17q-82 0 -141 -25.5t-91.5 -72t-47.5 -102.5t-15 -126zM271 141v414h159q81 0 117.5 -30t36.5 -109q0 -82 -35.5 -110t-119.5 -28h-97v-137h-61zM332 505v-177h93q48 0 71.5 16.5t23.5 71.5t-24 72
t-71 17h-93z" />
    <glyph glyph-name="uni2120" unicode="&#x2120;" horiz-adv-x="785" 
d="M312 468q0 -98 -130 -98q-67 1 -98.5 28.5t-31.5 85.5h50q0 -72 78 -72q79 -1 79 54q0 26 -21 40.5t-50.5 21.5t-59.5 16t-51 32.5t-21 62.5q0 94 124 93q70 -1 97.5 -27t27.5 -76h-50q0 61 -73 62q-74 1 -74 -54q0 -29 32 -45t70 -22.5t70 -31.5t32 -70zM368 379v347h75
l100 -301l102 301h75l-1 -347h-49l5 294l-106 -294h-53l-104 298l5 -298h-49z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="748" 
d="M136 379v300h-103v47h259v-47h-103v-300h-53zM324 379v347h74l100 -302l103 302h74l-1 -347h-48l4 295l-106 -295h-53l-104 298l5 -298h-48z" />
    <glyph glyph-name="oneeighth" unicode="&#x215b;" horiz-adv-x="977" 
d="M86 -40l512 821h83l-513 -821h-82zM103 245v428l-95 -43v79l121 53h47v-517h-73zM739 -9q-104 0 -149.5 39t-45.5 112q0 64 29.5 97.5t57.5 35.5q-21 2 -46 30t-25 78q0 142 179 142q177 0 177 -142q0 -50 -25 -78t-47 -30q28 -2 58.5 -35.5t30.5 -97.5q0 -151 -194 -151
zM739 466q-103 0 -103 -90q0 -74 103 -74q101 0 101 74q0 90 -101 90zM739 243q-114 0 -114 -92q0 -95 114 -95q113 0 113 95q0 46 -27 69t-86 23z" />
    <glyph glyph-name="threeeighths" unicode="&#x215c;" horiz-adv-x="1155" 
d="M264 -40l512 821h83l-513 -821h-82zM246 573q153 -19 153 -169q0 -76 -45 -122.5t-144 -46.5q-94 0 -138 45t-44 113h75q0 -38 27.5 -63.5t78.5 -25.5q110 0 110 105q0 52 -31.5 81t-89.5 29l-62 -5v55l149 120l-234 -3v69h337v-65zM917 -9q-104 0 -149.5 39t-45.5 112
q0 64 29.5 97.5t57.5 35.5q-21 2 -46 30t-25 78q0 142 179 142q177 0 177 -142q0 -50 -25 -78t-47 -30q28 -2 58.5 -35.5t30.5 -97.5q0 -151 -194 -151zM917 466q-103 0 -103 -90q0 -74 103 -74q101 0 101 74q0 90 -101 90zM917 243q-114 0 -114 -92q0 -95 114 -95
q113 0 113 95q0 46 -27 69t-86 23z" />
    <glyph glyph-name="fiveeighths" unicode="&#x215d;" horiz-adv-x="1176" 
d="M285 -40l512 821h83l-513 -821h-82zM164 691l-23 -134q43 28 113 28q83 0 124.5 -49t41.5 -126q0 -80 -45 -127.5t-143 -47.5t-144.5 43.5t-46.5 104.5h79q0 -35 29 -59.5t83 -24.5q111 0 111 111q0 53 -29 83t-81 30q-36 0 -62 -11.5t-35 -22.5l-8 -11h-71l43 277h292
v-66zM938 -9q-104 0 -149.5 39t-45.5 112q0 64 29.5 97.5t57.5 35.5q-21 2 -46 30t-25 78q0 142 179 142q177 0 177 -142q0 -50 -25 -78t-47 -30q28 -2 58.5 -35.5t30.5 -97.5q0 -151 -194 -151zM938 466q-103 0 -103 -90q0 -74 103 -74q101 0 101 74q0 90 -101 90zM938 243
q-114 0 -114 -92q0 -95 114 -95q113 0 113 95q0 46 -27 69t-86 23z" />
    <glyph glyph-name="seveneighths" unicode="&#x215e;" horiz-adv-x="1044" 
d="M153 -40l512 821h83l-513 -821h-82zM100 245q0 68 18.5 139t45 123t53.5 94t45 64l19 22l-267 -2v72h351v-72l-20.5 -24t-44.5 -63.5t-57 -98t-45 -119.5t-20 -135h-78zM806 -9q-104 0 -149.5 39t-45.5 112q0 64 29.5 97.5t57.5 35.5q-21 2 -46 30t-25 78q0 142 179 142
q177 0 177 -142q0 -50 -25 -78t-47 -30q28 -2 58.5 -35.5t30.5 -97.5q0 -151 -194 -151zM806 466q-103 0 -103 -90q0 -74 103 -74q101 0 101 74q0 90 -101 90zM806 243q-114 0 -114 -92q0 -95 114 -95q113 0 113 95q0 46 -27 69t-86 23z" />
    <glyph glyph-name="arrowleft" unicode="&#x2190;" horiz-adv-x="680" 
d="M644 245l-474 13l201 -160l-49 -54l-262 217v59l262 217l49 -55l-201 -159l474 12v-90z" />
    <glyph glyph-name="arrowup" unicode="&#x2191;" horiz-adv-x="574" 
d="M242 1l13 475l-160 -202l-55 49l218 263h58l218 -263l-55 -49l-160 200l13 -473h-90z" />
    <glyph glyph-name="arrowright" unicode="&#x2192;" horiz-adv-x="680" 
d="M36 333l474 -12l-200 159l49 55l262 -217v-59l-262 -218l-49 55l201 160l-475 -13v90z" />
    <glyph glyph-name="arrowdown" unicode="&#x2193;" horiz-adv-x="561" 
d="M326 578l-13 -474l160 201l54 -49l-217 -262h-59l-217 262l55 49l159 -201l-12 474h90z" />
    <glyph glyph-name="arrowboth" unicode="&#x2194;" horiz-adv-x="943" 
d="M623 517l260 -218v-59l-260 -217l-49 55l194 155l-594 -1l197 -156l-49 -55l-262 217v59l262 217l49 -54l-200 -158l601 2l-197 158z" />
    <glyph glyph-name="arrowupdn" unicode="&#x2195;" horiz-adv-x="577" 
d="M319 -148h-59l-217 260l55 49l155 -194l-2 606l-155 -196l-56 49l218 262h59l217 -262l-54 -49l-156 196l1 -609l157 196l54 -48z" />
    <glyph glyph-name="uni2196" unicode="&#x2196;" horiz-adv-x="558" 
d="M459 55l-326 344l29 -255l-73 -4l-32 339l41 42l340 -32l-5 -73l-255 29l344 -327z" />
    <glyph glyph-name="uni2197" unicode="&#x2197;" horiz-adv-x="558" 
d="M35 119l345 326l-256 -29l-3 73l339 32l41 -41l-31 -340l-74 5l30 255l-327 -345z" />
    <glyph glyph-name="uni2198" unicode="&#x2198;" horiz-adv-x="558" 
d="M100 527l326 -346l-30 257l74 3l31 -339l-41 -41l-339 31l3 74l255 -29l-344 326z" />
    <glyph glyph-name="uni2199" unicode="&#x2199;" horiz-adv-x="558" 
d="M523 463l-344 -326l255 29l4 -74l-339 -31l-42 41l32 339l73 -3l-29 -257l327 345z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="435" 
d="M40 275v80h355v-80h-355z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="776" 
d="M204 134q-85 0 -126 45t-41 125t41.5 125t126.5 45q61 -1 102 -32.5t81 -88.5q40 57 81.5 88.5t102.5 32.5q85 0 126.5 -45t41.5 -125t-41.5 -125t-126.5 -45q-61 0 -102 31.5t-82 88.5q-40 -57 -82 -88.5t-102 -31.5zM106 304q0 -104 103 -104q45 0 77.5 26.5t67.5 77.5
q-36 50 -68 76.5t-76 27.5q-104 0 -104 -104zM671 304q0 48 -25.5 75.5t-77.5 28.5q-46 0 -78.5 -26.5t-67.5 -77.5q35 -51 67.5 -77.5t78.5 -26.5q52 0 77.5 27.5t25.5 76.5z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="548" 
d="M57 327q0 55 27 93t93 38q49 0 101 -27.5t87 -27.5q34 0 51.5 19t17.5 36h57q0 -55 -27 -93t-93 -38q-49 0 -101 27.5t-87 27.5q-34 0 -51.5 -19t-17.5 -36h-57zM57 163q0 55 27 92.5t93 37.5q49 0 101 -27.5t87 -27.5q34 0 51.5 19t17.5 36h57q0 -55 -27 -92.5
t-93 -37.5q-49 0 -101 27.5t-87 27.5q-34 0 -51.5 -19t-17.5 -36h-57z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="516" 
d="M100 108l49 85h-86v79h131l41 70h-172v80h218l46 79h71l-46 -79h105v-80h-151l-40 -70h191v-79h-236l-49 -85h-72z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="539" 
d="M459 182l-387 139v93l387 125v-79l-321 -94l321 -105v-79zM70 89v79h389v-79h-389z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="539" 
d="M80 182v79l319 105l-319 94v79l387 -125v-93zM80 89v79h389v-79h-389z" />
    <glyph glyph-name="uni2661" unicode="&#x2661;" horiz-adv-x="734" 
d="M514 601q67 0 113.5 -46.5t46.5 -130.5q0 -63 -31.5 -129t-77 -116t-90.5 -90.5t-77 -62.5l-31 -21l-17 11q-16 11 -43.5 33l-60 50.5t-66 66t-60 77.5t-43.5 87.5t-16 93.5q1 84 47 130.5t113 46.5q32 0 60.5 -9t47 -23t26 -21t13.5 -14q0 1 5.5 7.5t17.5 16.5
t28.5 19.5t41.5 16.5t53 7zM367 113l1 1l29.5 21.5t51 44t61 64t46.5 74.5t21 82q0 53 -28.5 82t-67.5 29q-20 0 -38.5 -7t-31 -17.5t-22 -21t-13.5 -17.5l-5 -7h-6q-5 8 -13.5 19.5t-37.5 31t-61 19.5q-39 0 -67 -29t-29 -82q0 -23 7 -48t19 -47t27.5 -44t33 -40.5
l35 -35.5t33.5 -30l28 -22.5t20 -14.5z" />
    <glyph glyph-name="Euro.sc" horiz-adv-x="529" 
d="M176 195q11 -67 42 -99t89 -32q49 0 78.5 23.5t31.5 68.5h74q-8 -160 -187 -160q-103 0 -153 49t-62 150h-69v57h65q-1 13 -1 41v33h-64v57h68q12 107 61.5 158t154.5 51q184 0 189 -170h-74q-2 49 -31 75t-81 26q-59 0 -90.5 -34t-41.5 -106h187v-57h-192v-33
q0 -28 1 -41h191v-57h-186z" />
    <glyph glyph-name="a.sc" horiz-adv-x="565" 
d="M8 0l227 592h96l226 -592h-96l-47 135h-266l-46 -135h-94zM174 210h215l-107 311z" />
    <glyph glyph-name="aacute.sc" horiz-adv-x="565" 
d="M242 685l77 144h95l-91 -144h-81zM8 0l227 592h96l226 -592h-96l-47 135h-266l-46 -135h-94zM174 210h215l-107 311z" />
    <glyph glyph-name="abreve.sc" horiz-adv-x="565" 
d="M432 806q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76zM8 0l227 592h96l226 -592h-96l-47 135h-266l-46 -135h-94zM174 210h215l-107 311z" />
    <glyph glyph-name="acircumflex.sc" horiz-adv-x="565" 
d="M438 684h-86l-70 83l-70 -83h-85l111 143h87zM8 0l227 592h96l226 -592h-96l-47 135h-266l-46 -135h-94zM174 210h215l-107 311z" />
    <glyph glyph-name="acutecomb.case" 
d="M-230 766l77 121h108l-91 -121h-94z" />
    <glyph glyph-name="adieresis.sc" horiz-adv-x="565" 
d="M157 684v129h84v-129h-84zM325 684v129h85v-129h-85zM8 0l227 592h96l226 -592h-96l-47 135h-266l-46 -135h-94zM174 210h215l-107 311z" />
    <glyph glyph-name="ae.sc" horiz-adv-x="790" 
d="M8 0l314 592h441v-76h-303v-175h261v-71h-261v-194h308v-76h-395v144h-194l-74 -144h-97zM373 216v306l-157 -306h157z" />
    <glyph glyph-name="aeacute.sc" horiz-adv-x="790" 
d="M346 685l77 144h95l-91 -144h-81zM8 0l314 592h441v-76h-303v-175h261v-71h-261v-194h308v-76h-395v144h-194l-74 -144h-97zM373 216v306l-157 -306h157z" />
    <glyph glyph-name="agrave.sc" horiz-adv-x="565" 
d="M323 685h-80l-92 144h95zM8 0l227 592h96l226 -592h-96l-47 135h-266l-46 -135h-94zM174 210h215l-107 311z" />
    <glyph glyph-name="amacron.sc" horiz-adv-x="565" 
d="M156 777h253v-66h-253v66zM8 0l227 592h96l226 -592h-96l-47 135h-266l-46 -135h-94zM174 210h215l-107 311z" />
    <glyph glyph-name="ampersand.sc" horiz-adv-x="609" 
d="M306 607q168 0 168 -159h-78q0 96 -90 96q-97 0 -97 -88q0 -22 5 -39.5t22 -40t26.5 -33.5t42 -44l45.5 -46l97 -101q22 60 22 160h73q0 -132 -43 -212l79 -79v-24h-79l-45 46q-68 -59 -189 -59q-207 0 -207 185q0 73 35.5 114t79.5 58q-43 57 -43 121q0 145 176 145z
M212 295q-78 -38 -78 -126q0 -124 147 -124q85 0 128 44l-136 143z" />
    <glyph glyph-name="aogonek.sc" horiz-adv-x="565" 
d="M372 -115q0 29 19 57t35.5 40.5t32.5 21.5l-45 131h-266l-46 -135h-94l227 592h96l226 -592h-18q-7 -3 -17 -10t-25 -19.5t-25.5 -30t-10.5 -35.5q0 -39 38 -39q10 0 22 3.5t19 6.5l6 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5zM174 210h215l-107 311z" />
    <glyph glyph-name="aring.sc" horiz-adv-x="565" 
d="M384 765q0 -43 -27 -68t-73 -25t-72.5 25t-26.5 68t26.5 68.5t72.5 25.5t73 -25t27 -69zM246 765q0 -17 10.5 -27.5t27.5 -10.5t27.5 10.5t10.5 27.5q0 18 -10.5 28.5t-27.5 10.5t-27.5 -10.5t-10.5 -28.5zM8 0l227 592h96l226 -592h-96l-47 135h-266l-46 -135h-94z
M174 210h215l-107 311z" />
    <glyph glyph-name="aringacute.sc" horiz-adv-x="565" 
d="M249 893l77 144h95l-91 -144h-81zM384 765q0 -43 -27 -68t-73 -25t-72.5 25t-26.5 68t26.5 68.5t72.5 25.5t73 -25t27 -69zM246 765q0 -17 10.5 -27.5t27.5 -10.5t27.5 10.5t10.5 27.5q0 18 -10.5 28.5t-27.5 10.5t-27.5 -10.5t-10.5 -28.5zM8 0l227 592h96l226 -592h-96
l-47 135h-266l-46 -135h-94zM174 210h215l-107 311z" />
    <glyph glyph-name="asterisk.sc" horiz-adv-x="393" 
d="M61 543l18 58l90 -34l-3 94h61l-3 -94l89 34l19 -58l-90 -26l65 -76l-50 -41l-60 81l-61 -81l-50 41l65 76z" />
    <glyph glyph-name="at.case" horiz-adv-x="868" 
d="M750 347q0 355 -319 355q-163 0 -238 -95.5t-75 -259.5q0 -60 9.5 -111t32.5 -95.5t58.5 -75.5t89.5 -49t124 -18q116 0 181 32v-67q-76 -28 -182 -28q-381 0 -381 412q0 71 12.5 132t41 114t72 90.5t108.5 59t147 21.5q105 0 181.5 -31t121 -88t65 -131t20.5 -167
q0 -145 -34.5 -205.5t-115.5 -60.5q-97 0 -117 102q-45 -62 -146 -62q-174 0 -174 224q0 226 174 226q83 0 141 -60l4 51h65v-301q0 -125 63 -125q24 0 38.5 15.5t23.5 63t9 132.5zM419 185q93 0 126 70v183q-44 69 -126 69q-56 0 -84 -42.5t-28 -119.5q0 -160 112 -160z
" />
    <glyph glyph-name="atilde.sc" horiz-adv-x="565" 
d="M436 800q0 -115 -90 -115q-36 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM8 0l227 592h96l226 -592h-96l-47 135h-266l-46 -135h-94zM174 210h215l-107 311z" />
    <glyph glyph-name="b.sc" horiz-adv-x="556" 
d="M65 594h205q133 0 183 -36.5t50 -113.5q0 -103 -81 -136q98 -33 98 -145q0 -62 -17.5 -95t-72.5 -51.5t-161 -18.5h-204v596zM152 526v-185h144q67 0 94.5 22t27.5 71t-32.5 70.5t-110.5 21.5h-123zM152 274v-209h123q93 0 126.5 22.5t33.5 81.5q0 54 -33.5 79.5
t-105.5 25.5h-144z" />
    <glyph glyph-name="backslash.sc" horiz-adv-x="432" 
d="M391 -57h-82l-268 718h82z" />
    <glyph glyph-name="bar.sc" horiz-adv-x="221" 
d="M77 -77v754h66v-754h-66z" />
    <glyph glyph-name="bitcoin" horiz-adv-x="590" 
d="M197 -48v93h-118v645h118v89h55v-89h57v89h54v-92q87 -9 123.5 -49t36.5 -110q0 -57 -22 -93.5t-77 -52.5q64 -18 89.5 -58t25.5 -101q0 -44 -6.5 -72.5t-25 -52t-54 -36t-90.5 -16.5v-94h-54v93h-57v-93h-55zM163 619v-201h143q77 0 106 23.5t29 76.5t-32.5 77
t-102.5 24h-143zM163 344v-228h143q88 0 119 24.5t31 89.5q0 58 -34.5 86t-115.5 28h-143z" />
    <glyph glyph-name="bitcoin.sc" horiz-adv-x="496" 
d="M165 -40v77h-100v541h100v74h50v-74h47v74h51v-77q73 -9 103.5 -42t30.5 -92q0 -96 -82 -122q95 -27 95 -131q0 -75 -29 -109t-118 -40v-79h-51v77h-47v-77h-50zM144 513v-161h115q63 0 87 18.5t24 61.5t-26.5 62t-84.5 19h-115zM144 286v-184h115q72 -1 98 18.5t26 72.5
q0 48 -29 70.5t-95 22.5h-115z" />
    <glyph glyph-name="braceleft.case" horiz-adv-x="412" 
d="M161 595q0 72 15 104.5t60 47t143 14.5v-67q-83 0 -107 -22t-25 -87v-155q0 -30 -14 -50.5t-24.5 -26.5t-17.5 -7q57 -19 57 -84v-157q0 -63 24 -84t107 -21v-68q-97 0 -142.5 15.5t-60.5 49t-15 105.5v157q0 32 -16.5 44t-70.5 12v64q54 0 70.5 11.5t16.5 43.5v161z" />
    <glyph glyph-name="braceleft.sc" horiz-adv-x="402" 
d="M161 511q0 72 15 105t58.5 47.5t137.5 14.5v-68q-57 0 -85 -10.5t-36.5 -32t-9.5 -65.5v-121q0 -19 -5.5 -34.5t-13 -24t-16 -14.5t-13.5 -8t-8 -3q56 -19 56 -84v-123q0 -63 24.5 -84.5t106.5 -21.5v-67q-93 0 -137 15t-59 48.5t-15 105.5v124q0 32 -16.5 44t-70.5 12
v63q54 0 70.5 11.5t16.5 43.5v127z" />
    <glyph glyph-name="braceright.case" horiz-adv-x="412" 
d="M247 99q0 -54 -6.5 -83.5t-30.5 -50t-65 -27t-115 -6.5v68q83 0 107 21.5t24 86.5l1 155q1 62 55 84q-56 19 -56 84v157q0 63 -24.5 84.5t-106.5 21.5v67q73 0 114.5 -7t65.5 -28t30.5 -50t6.5 -84v-158q0 -32 16.5 -44t70.5 -12v-63q-54 0 -70.5 -11.5t-16.5 -43.5v-161
z" />
    <glyph glyph-name="braceright.sc" horiz-adv-x="402" 
d="M241 83q0 -72 -14.5 -104.5t-58.5 -47t-138 -14.5v67q83 0 107 22t24 87l1 120q1 63 55 85q-5 0 -16.5 6t-25.5 27t-14 51v123q0 62 -24.5 83.5t-106.5 21.5v68q94 0 138 -15.5t58.5 -49t14.5 -105.5v-123q0 -32 16.5 -44t70.5 -12v-64q-54 0 -70.5 -11.5t-16.5 -43.5
v-127z" />
    <glyph glyph-name="bracketleft.case" horiz-adv-x="362" 
d="M100 -57v817h229v-65h-149v-687h149v-65h-229z" />
    <glyph glyph-name="bracketleft.sc" horiz-adv-x="359" 
d="M100 -79v749h222v-65h-148v-620h148v-64h-222z" />
    <glyph glyph-name="bracketright.case" horiz-adv-x="359" 
d="M262 -57h-229v65h149v687h-149v65h229v-817z" />
    <glyph glyph-name="bracketright.sc" horiz-adv-x="359" 
d="M256 -79h-223v64h149v620h-149v65h223v-749z" />
    <glyph glyph-name="brokenbar.sc" horiz-adv-x="221" 
d="M77 389v288h66v-288h-66zM143 251v-328h-66v328h66z" />
    <glyph glyph-name="c.sc" horiz-adv-x="558" 
d="M521 205q-3 -115 -59.5 -168t-170.5 -53q-133 0 -192 75.5t-59 235.5t59 236t192 76q115 0 172.5 -54.5t58.5 -168.5h-86q-4 75 -39.5 109.5t-105.5 34.5q-86 0 -124 -55t-38 -178t37.5 -177t124.5 -54q69 0 104.5 33.5t39.5 107.5h86z" />
    <glyph glyph-name="cacute.sc" horiz-adv-x="558" 
d="M248 688l77 144h95l-91 -144h-81zM521 205q-3 -115 -59.5 -168t-170.5 -53q-133 0 -192 75.5t-59 235.5t59 236t192 76q115 0 172.5 -54.5t58.5 -168.5h-86q-4 75 -39.5 109.5t-105.5 34.5q-86 0 -124 -55t-38 -178t37.5 -177t124.5 -54q69 0 104.5 33.5t39.5 107.5h86z
" />
    <glyph glyph-name="caroncomb.alt" 
d="M-208 606l50 154h76l-71 -154h-55z" />
    <glyph glyph-name="caroncomb.alt.case" 
d="M-218 766l54 151h79l-68 -151h-65z" />
    <glyph glyph-name="ccaron.sc" horiz-adv-x="558" 
d="M444 830l-113 -143h-87l-109 143h83l69 -81l71 81h86zM521 205q-3 -115 -59.5 -168t-170.5 -53q-133 0 -192 75.5t-59 235.5t59 236t192 76q115 0 172.5 -54.5t58.5 -168.5h-86q-4 75 -39.5 109.5t-105.5 34.5q-86 0 -124 -55t-38 -178t37.5 -177t124.5 -54
q69 0 104.5 33.5t39.5 107.5h86z" />
    <glyph glyph-name="ccedilla.sc" horiz-adv-x="558" 
d="M396 -142q0 -49 -31 -70t-105 -21q-20 0 -37.5 2t-24.5 4l-8 2v54q17 -6 48 -6q38 0 54 8.5t16 33.5q0 45 -85 45h-5l34 76q-112 10 -162 85.5t-50 223.5q0 160 59 236t192 76q115 0 172.5 -54.5t58.5 -168.5h-86q-4 75 -39.5 109.5t-105.5 34.5q-86 0 -124 -55t-38 -178
t37.5 -177t124.5 -54q69 0 104.5 33.5t39.5 107.5h86q-3 -103 -49 -156t-138 -63l-22 -48q37 -3 60.5 -22.5t23.5 -57.5z" />
    <glyph glyph-name="ccircumflex.sc" horiz-adv-x="558" 
d="M444 687h-86l-70 83l-70 -83h-85l111 143h87zM521 205q-3 -115 -59.5 -168t-170.5 -53q-133 0 -192 75.5t-59 235.5t59 236t192 76q115 0 172.5 -54.5t58.5 -168.5h-86q-4 75 -39.5 109.5t-105.5 34.5q-86 0 -124 -55t-38 -178t37.5 -177t124.5 -54q69 0 104.5 33.5
t39.5 107.5h86z" />
    <glyph glyph-name="cdotaccent.sc" horiz-adv-x="558" 
d="M244 694v122h91v-122h-91zM521 205q-3 -115 -59.5 -168t-170.5 -53q-133 0 -192 75.5t-59 235.5t59 236t192 76q115 0 172.5 -54.5t58.5 -168.5h-86q-4 75 -39.5 109.5t-105.5 34.5q-86 0 -124 -55t-38 -178t37.5 -177t124.5 -54q69 0 104.5 33.5t39.5 107.5h86z" />
    <glyph glyph-name="cent.sc" horiz-adv-x="482" 
d="M209 -17v72q-91 9 -130 67.5t-39 165.5q0 106 39 165.5t130 69.5v71h73v-71q84 -7 123 -51.5t41 -124.5h-84q-3 91 -80 106v-328q78 14 79 93h85q-7 -150 -164 -164v-71h-73zM128 288q0 -73 19 -112t62 -49v324q-43 -11 -62 -51t-19 -112z" />
    <glyph glyph-name="d.sc" horiz-adv-x="573" 
d="M65 594h188q87 0 142.5 -17.5t85.5 -57t41 -91t11 -132.5t-11 -133t-41 -91.5t-85.5 -56.5t-142.5 -17h-188v596zM152 517v-444h117q98 0 136 49.5t38 173.5q0 123 -38 172t-136 49h-117z" />
    <glyph glyph-name="dagger.sups" horiz-adv-x="314" 
d="M60 656h67v79h60v-79h67v-53h-67v-230h-60v230h-67v53z" />
    <glyph glyph-name="daggerdbl.sups" horiz-adv-x="314" 
d="M60 656h67v79h60v-79h67v-53h-67v-98h67v-53h-67v-79h-60v79h-67v53h67v98h-67v53z" />
    <glyph glyph-name="dcaron.sc" horiz-adv-x="573" 
d="M426 827l-113 -143h-87l-109 143h83l69 -81l71 81h86zM65 594h188q87 0 142.5 -17.5t85.5 -57t41 -91t11 -132.5t-11 -133t-41 -91.5t-85.5 -56.5t-142.5 -17h-188v596zM152 517v-444h117q98 0 136 49.5t38 173.5q0 123 -38 172t-136 49h-117z" />
    <glyph glyph-name="dcroat.sc" horiz-adv-x="573" 
d="M65 264h-72v64h72v266h188q87 0 142.5 -17.5t85.5 -57t41 -91t11 -132.5t-11 -133t-41 -91.5t-85.5 -56.5t-142.5 -17h-188v266zM152 264v-191h117q98 0 136 49.5t38 173.5q0 123 -38 172t-136 49h-117v-189h108v-64h-108z" />
    <glyph glyph-name="degree.sc" horiz-adv-x="335" 
d="M275 526q0 -114 -108 -114q-107 0 -107 114q0 116 107 116q108 0 108 -116zM114 526q0 -66 53 -66q54 0 54 66q0 68 -54 68q-53 0 -53 -68z" />
    <glyph glyph-name="dollar.sc" horiz-adv-x="480" 
d="M203 -38v71q-165 17 -165 176h73q0 -94 92 -110v184q-36 9 -60.5 19t-49.5 26.5t-37.5 43.5t-12.5 63q1 63 40.5 101.5t119.5 45.5v59h74v-60q84 -9 120 -49.5t36 -112.5h-76q0 38 -16 63t-64 33v-180q37 -9 62 -18.5t51.5 -26.5t40 -44t13.5 -63q0 -68 -42.5 -106.5
t-124.5 -44.5v-70h-74zM118 436q0 -31 22 -49.5t63 -31.5v162q-85 -10 -85 -81zM367 180q0 33 -23 51.5t-67 32.5v-167q90 11 90 83z" />
    <glyph glyph-name="e.sc" horiz-adv-x="482" 
d="M65 0v592h390v-76h-303v-172h261v-77h-261v-191h308v-76h-395z" />
    <glyph glyph-name="eacute.sc" horiz-adv-x="482" 
d="M217 685l77 144h95l-91 -144h-81zM65 0v592h390v-76h-303v-172h261v-77h-261v-191h308v-76h-395z" />
    <glyph glyph-name="ebreve.sc" horiz-adv-x="482" 
d="M407 806q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76zM65 0v592h390v-76h-303v-172h261v-77h-261v-191h308v-76h-395z" />
    <glyph glyph-name="ecaron.sc" horiz-adv-x="482" 
d="M413 827l-113 -143h-87l-109 143h83l69 -81l71 81h86zM65 0v592h390v-76h-303v-172h261v-77h-261v-191h308v-76h-395z" />
    <glyph glyph-name="ecircumflex.sc" horiz-adv-x="482" 
d="M413 684h-86l-70 83l-70 -83h-85l111 143h87zM65 0v592h390v-76h-303v-172h261v-77h-261v-191h308v-76h-395z" />
    <glyph glyph-name="edieresis.sc" horiz-adv-x="482" 
d="M132 684v129h84v-129h-84zM300 684v129h85v-129h-85zM65 0v592h390v-76h-303v-172h261v-77h-261v-191h308v-76h-395z" />
    <glyph glyph-name="edotaccent.sc" horiz-adv-x="482" 
d="M213 691v122h91v-122h-91zM65 0v592h390v-76h-303v-172h261v-77h-261v-191h308v-76h-395z" />
    <glyph glyph-name="egrave.sc" horiz-adv-x="482" 
d="M298 685h-80l-92 144h95zM65 0v592h390v-76h-303v-172h261v-77h-261v-191h308v-76h-395z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="482" 
d="M244 -9q-104 0 -149.5 39t-45.5 112q0 64 29.5 97.5t57.5 35.5q-21 2 -46 30t-25 78q0 142 179 142q177 0 177 -142q0 -50 -25 -78t-47 -30q28 -2 58.5 -35.5t30.5 -97.5q0 -151 -194 -151zM244 466q-103 0 -103 -90q0 -74 103 -74q101 0 101 74q0 90 -101 90zM244 243
q-114 0 -114 -92q0 -95 114 -95q113 0 113 95q0 46 -27 69t-86 23z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="482" 
d="M244 236q-104 0 -149.5 39t-45.5 112q0 64 29.5 97.5t57.5 35.5q-21 2 -46 30t-25 78q0 142 179 142q177 0 177 -142q0 -50 -25 -78t-47 -30q28 -2 58.5 -35.5t30.5 -97.5q0 -151 -194 -151zM244 711q-103 0 -103 -90q0 -74 103 -74q101 0 101 74q0 90 -101 90zM244 488
q-114 0 -114 -92q0 -95 114 -95q113 0 113 95q0 46 -27 69t-86 23z" />
    <glyph glyph-name="eight.osf" horiz-adv-x="596" 
d="M298 -11q-255 0 -255 205q0 47 14.5 84t36 56.5t40.5 30t33 11.5q-28 4 -64.5 41.5t-36.5 105.5q0 194 232 194t232 -194q0 -68 -36.5 -105.5t-65.5 -41.5q14 -1 33 -11.5t40.5 -30t36.5 -57t15 -83.5q0 -205 -255 -205zM298 646q-140 0 -140 -130q0 -50 36 -78.5
t104 -28.5q67 0 103.5 28.5t36.5 78.5q0 130 -140 130zM298 337q-76 0 -116.5 -35t-40.5 -98q0 -135 157 -135t157 135q0 63 -40.5 98t-116.5 35z" />
    <glyph glyph-name="eight.sc" horiz-adv-x="520" 
d="M259 -9q-224 0 -224 171q0 72 37 111t67 41q-22 1 -53 33t-31 88q0 162 204 162q206 0 206 -162q0 -55 -30 -87t-53 -34q30 -4 66 -42.5t36 -109.5q0 -82 -52.5 -126.5t-172.5 -44.5zM259 531q-119 0 -119 -104q0 -39 30.5 -62t88.5 -23q59 0 90 23t31 62q0 104 -121 104
zM259 278q-65 0 -98.5 -28t-33.5 -78q0 -108 132 -108q134 0 134 108q0 50 -34 78t-100 28z" />
    <glyph glyph-name="eight.subs" horiz-adv-x="482" 
d="M244 -203q-104 0 -149.5 39t-45.5 112q0 64 29.5 97.5t57.5 35.5q-21 2 -46 30t-25 78q0 142 179 142q177 0 177 -142q0 -50 -25 -78t-47 -30q28 -2 58.5 -35.5t30.5 -97.5q0 -151 -194 -151zM244 272q-103 0 -103 -90q0 -74 103 -74q101 0 101 74q0 90 -101 90zM244 49
q-114 0 -114 -92q0 -95 114 -95q113 0 113 95q0 46 -27 69t-86 23z" />
    <glyph glyph-name="eight.tf" horiz-adv-x="660" 
d="M323 -11q-262 0 -262 205q0 47 14.5 84t36 56.5t40.5 30t33 11.5q-28 4 -64.5 41.5t-36.5 105.5q0 194 239 194t239 -194q0 -68 -36.5 -105.5t-65.5 -41.5q14 -1 33 -11.5t40.5 -30.5t36.5 -57t15 -83q0 -205 -262 -205zM323 646q-75 0 -111 -31.5t-36 -98.5
q0 -50 37 -78.5t110 -28.5q72 0 109.5 28.5t37.5 78.5q0 130 -147 130zM323 337q-81 0 -122.5 -35t-41.5 -98q0 -135 164 -135q163 0 163 135q0 63 -41.5 98t-121.5 35z" />
    <glyph glyph-name="emacron.sc" horiz-adv-x="482" 
d="M131 777h253v-66h-253v66zM65 0v592h390v-76h-303v-172h261v-77h-261v-191h308v-76h-395z" />
    <glyph glyph-name="eng.sc" horiz-adv-x="611" 
d="M546 592v-592q0 -94 -37 -140.5t-141 -46.5h-43v67h43q57 0 80 27.5t23 92.5l-327 476l3 -476h-82l2 592h91l311 -459l-4 459h81z" />
    <glyph glyph-name="eogonek.sc" horiz-adv-x="482" 
d="M290 -115q0 38 26 68t55 47h-306v592h390v-76h-303v-172h261v-77h-261v-191h308v-76q-21 -8 -51 -35t-30 -60q0 -39 38 -39q10 0 22 3.5t19 6.5l6 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5z" />
    <glyph glyph-name="eth.sc" horiz-adv-x="573" 
d="M65 264h-72v64h72v266h188q87 0 142.5 -17.5t85.5 -57t41 -91t11 -132.5t-11 -133t-41 -91.5t-85.5 -56.5t-142.5 -17h-188v266zM152 264v-191h117q98 0 136 49.5t38 173.5q0 123 -38 172t-136 49h-117v-189h108v-64h-108z" />
    <glyph glyph-name="exclam.sc" horiz-adv-x="245" 
d="M81 169l-4 424h90l-3 -424h-83zM77 0v117h90v-117h-90z" />
    <glyph glyph-name="exclamdown.case" horiz-adv-x="245" 
d="M77 594v117h90v-117h-90zM77 0l4 542h83l3 -542h-90z" />
    <glyph glyph-name="exclamdown.sc" horiz-adv-x="245" 
d="M77 476v117h90v-117h-90zM77 0l4 424h83l3 -424h-90z" />
    <glyph glyph-name="f.sc" horiz-adv-x="458" 
d="M65 0v592h385v-76h-298v-186h255v-79h-255v-251h-87z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="457" 
d="M164 446l-23 -134q43 28 113 28q83 0 124.5 -49t41.5 -126q0 -80 -45 -127.5t-143 -47.5t-144.5 43.5t-46.5 104.5h79q0 -34 29 -59t83 -25q111 0 111 111q0 53 -29 83t-81 30q-36 0 -62 -11t-35 -23l-8 -11h-71l43 277h292v-66z" />
    <glyph glyph-name="five.numr" horiz-adv-x="457" 
d="M164 691l-23 -134q43 28 113 28q83 0 124.5 -49t41.5 -126q0 -80 -45 -127.5t-143 -47.5t-144.5 43.5t-46.5 104.5h79q0 -35 29 -59.5t83 -24.5q111 0 111 111q0 53 -29 83t-81 30q-36 0 -62 -11.5t-35 -22.5l-8 -11h-71l43 277h292v-66z" />
    <glyph glyph-name="five.osf" horiz-adv-x="561" 
d="M192 476l-32 -193q53 41 157 41t159 -67t55 -172q0 -108 -59.5 -172.5t-186.5 -64.5q-126 0 -188 58.5t-62 139.5h94q0 -49 41 -83.5t115 -34.5t113.5 40.5t39.5 116.5q0 77 -40 120t-106 43q-32 0 -59.5 -6.5t-43 -16t-27 -19.5t-15.5 -16l-4 -7l-86 1l60 376h375v-81z
" />
    <glyph glyph-name="five.sc" horiz-adv-x="499" 
d="M173 505l-26 -153q46 34 136 34q93 0 139.5 -56t46.5 -143q0 -90 -51.5 -144t-164.5 -54q-114 0 -167.5 49t-53.5 118h91q0 -38 33.5 -65.5t96.5 -27.5q64 0 97 32t33 93q0 62 -33 96t-90 34q-44 0 -76 -13t-42 -27l-10 -13h-82l50 315h336v-74z" />
    <glyph glyph-name="five.subs" horiz-adv-x="457" 
d="M164 252l-23 -134q43 28 113 28q83 0 124.5 -49t41.5 -126q0 -80 -45 -127.5t-143 -47.5t-144.5 43.5t-46.5 104.5h79q0 -34 29 -59t83 -25q111 0 111 111q0 53 -29 83t-81 30q-23 0 -42 -4.5t-30 -11t-19 -13.5t-11 -11l-3 -5h-71l43 277h292v-66z" />
    <glyph glyph-name="five.tf" horiz-adv-x="660" 
d="M217 613l-33 -190q56 41 164 41q109 0 165 -67t56 -172q0 -109 -60.5 -173t-192.5 -64t-195 58t-63 140h95q0 -50 42.5 -84t120.5 -34q79 0 119.5 40.5t40.5 116.5q0 78 -41.5 120.5t-111.5 42.5q-34 0 -62.5 -6.5t-45 -16t-28.5 -19.5t-16 -16l-4 -7l-87 1l60 373h391
v-81z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="442" 
d="M288 0v105h-263v65l223 345h113v-345h60v-65h-60v-105h-73zM288 170v294l-179 -294h179z" />
    <glyph glyph-name="four.numr" horiz-adv-x="442" 
d="M288 245v105h-263v65l223 345h113v-345h60v-65h-60v-105h-73zM288 415v294l-179 -294h179z" />
    <glyph glyph-name="four.osf" horiz-adv-x="569" 
d="M378 -168v168h-361v75l314 485h134v-485h82v-75h-82v-168h-87zM378 75v430l-267 -430h267z" />
    <glyph glyph-name="four.sc" horiz-adv-x="482" 
d="M312 0v119h-304v72l261 394h127v-394h69v-72h-69v-119h-84zM312 191v342l-214 -342h214z" />
    <glyph glyph-name="four.subs" horiz-adv-x="442" 
d="M288 -194v105h-263v65l223 345h113v-345h60v-65h-60v-105h-73zM288 -24v294l-179 -294h179z" />
    <glyph glyph-name="four.tf" horiz-adv-x="660" 
d="M396 0v148h-364v78l314 478h139v-478h82v-78h-82v-148h-89zM396 226v424l-266 -424h266z" />
    <glyph glyph-name="g.sc" horiz-adv-x="562" 
d="M514 71q0 -3 -7.5 -12t-26 -22t-43 -24.5t-64 -20t-84.5 -8.5q-132 0 -190.5 75.5t-58.5 235.5t58 236t191 76q114 0 170 -54.5t56 -152.5h-83q-1 59 -38.5 93.5t-104.5 34.5q-85 0 -122.5 -55.5t-37.5 -177.5q0 -124 38.5 -179t124.5 -55q42 0 76.5 10.5t50 22.5
t15.5 21v110h-117v67h197v-221z" />
    <glyph glyph-name="gbreve.sc" horiz-adv-x="562" 
d="M437 809q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76zM514 71q0 -3 -7.5 -12t-26 -22t-43 -24.5t-64 -20t-84.5 -8.5q-132 0 -190.5 75.5t-58.5 235.5t58 236t191 76q114 0 170 -54.5t56 -152.5h-83
q-1 59 -38.5 93.5t-104.5 34.5q-85 0 -122.5 -55.5t-37.5 -177.5q0 -124 38.5 -179t124.5 -55q42 0 76.5 10.5t50 22.5t15.5 21v110h-117v67h197v-221z" />
    <glyph glyph-name="gcaron.sc" horiz-adv-x="562" 
d="M443 830l-113 -143h-87l-109 143h83l69 -81l71 81h86zM514 71q0 -3 -7.5 -12t-26 -22t-43 -24.5t-64 -20t-84.5 -8.5q-132 0 -190.5 75.5t-58.5 235.5t58 236t191 76q114 0 170 -54.5t56 -152.5h-83q-1 59 -38.5 93.5t-104.5 34.5q-85 0 -122.5 -55.5t-37.5 -177.5
q0 -124 38.5 -179t124.5 -55q42 0 76.5 10.5t50 22.5t15.5 21v110h-117v67h197v-221z" />
    <glyph glyph-name="gcircumflex.sc" horiz-adv-x="562" 
d="M443 687h-86l-70 83l-70 -83h-85l111 143h87zM514 71q0 -3 -7.5 -12t-26 -22t-43 -24.5t-64 -20t-84.5 -8.5q-132 0 -190.5 75.5t-58.5 235.5t58 236t191 76q114 0 170 -54.5t56 -152.5h-83q-1 59 -38.5 93.5t-104.5 34.5q-85 0 -122.5 -55.5t-37.5 -177.5
q0 -124 38.5 -179t124.5 -55q42 0 76.5 10.5t50 22.5t15.5 21v110h-117v67h197v-221z" />
    <glyph glyph-name="gcommaaccent.sc" horiz-adv-x="562" 
d="M514 71q0 -3 -7.5 -12t-26 -22t-43 -24.5t-64 -20t-84.5 -8.5q-132 0 -190.5 75.5t-58.5 235.5t58 236t191 76q114 0 170 -54.5t56 -152.5h-83q-1 59 -38.5 93.5t-104.5 34.5q-85 0 -122.5 -55.5t-37.5 -177.5q0 -124 38.5 -179t124.5 -55q42 0 76.5 10.5t50 22.5
t15.5 21v110h-117v67h197v-221zM230 -251q16 39 29 98q6 24 9.5 48.5t3.5 36.5l1 12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="gdotaccent.sc" horiz-adv-x="562" 
d="M243 694v122h91v-122h-91zM514 71q0 -3 -7.5 -12t-26 -22t-43 -24.5t-64 -20t-84.5 -8.5q-132 0 -190.5 75.5t-58.5 235.5t58 236t191 76q114 0 170 -54.5t56 -152.5h-83q-1 59 -38.5 93.5t-104.5 34.5q-85 0 -122.5 -55.5t-37.5 -177.5q0 -124 38.5 -179t124.5 -55
q42 0 76.5 10.5t50 22.5t15.5 21v110h-117v67h197v-221z" />
    <glyph glyph-name="germandbls.sc" horiz-adv-x="584" 
d="M548 187q0 -83 -51 -135t-165 -52h-122v73h117q130 0 130 126q0 58 -36 90t-100 32q-16 0 -33 -2t-26 -4l-9 -2v57l172 152h-166q-109 -1 -109 -120v-402h-85v405q0 90 39 138t150 49h273v-70l-161 -146q182 -17 182 -189z" />
    <glyph glyph-name="gravecomb.case" 
d="M-50 767h-94l-91 121h108z" />
    <glyph glyph-name="guillemotleft.case" horiz-adv-x="536" 
d="M187 158l-117 154v20l117 161h103l-133 -172l133 -163h-103zM365 158l-117 154v20l117 161h103l-133 -172l133 -163h-103z" />
    <glyph glyph-name="guillemotright.case" horiz-adv-x="536" 
d="M171 158h-102l131 163l-131 172h102l117 -161v-20zM349 158h-102l131 163l-131 172h102l117 -161v-20z" />
    <glyph glyph-name="guilsinglleft.case" horiz-adv-x="358" 
d="M187 158l-117 154v20l117 161h103l-133 -172l133 -163h-103z" />
    <glyph glyph-name="guilsinglright.case" horiz-adv-x="358" 
d="M171 158h-102l131 163l-131 172h102l117 -161v-20z" />
    <glyph glyph-name="h.sc" horiz-adv-x="585" 
d="M65 0v592h87v-248h282v248h86v-592h-86v267h-282v-267h-87z" />
    <glyph glyph-name="hbar.sc" horiz-adv-x="585" 
d="M520 409v-409h-86v267h-282v-267h-87v409h-71v71h71v112h87v-112h282v112h86v-112h71v-71h-71zM434 344v65h-282v-65h282z" />
    <glyph glyph-name="hcircumflex.sc" horiz-adv-x="585" 
d="M448 684h-86l-70 83l-70 -83h-85l111 143h87zM65 0v592h87v-248h282v248h86v-592h-86v267h-282v-267h-87z" />
    <glyph glyph-name="i.loclTRK" horiz-adv-x="241" 
d="M75 637v122h91v-122h-91zM75 0v538h90v-538h-90z" />
    <glyph glyph-name="i.sc" horiz-adv-x="217" 
d="M65 0v592h87v-592h-87z" />
    <glyph glyph-name="i.sc.loclTRK" horiz-adv-x="217" 
d="M65 691v122h91v-122h-91zM65 0v592h87v-592h-87z" />
    <glyph glyph-name="iacute.sc" horiz-adv-x="217" 
d="M69 685l77 144h95l-91 -144h-81zM65 0v592h87v-592h-87z" />
    <glyph glyph-name="ibreve.sc" horiz-adv-x="217" 
d="M259 806q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76zM65 0v592h87v-592h-87z" />
    <glyph glyph-name="icircumflex.sc" horiz-adv-x="217" 
d="M265 684h-86l-70 83l-70 -83h-85l111 143h87zM65 0v592h87v-592h-87z" />
    <glyph glyph-name="idieresis.sc" horiz-adv-x="217" 
d="M-16 684v129h84v-129h-84zM152 684v129h85v-129h-85zM65 0v592h87v-592h-87z" />
    <glyph glyph-name="igrave.sc" horiz-adv-x="217" 
d="M150 685h-80l-92 144h95zM65 0v592h87v-592h-87z" />
    <glyph glyph-name="ij.sc" horiz-adv-x="536" 
d="M65 0v592h87v-592h-87zM480 237q0 -72 -6.5 -112t-29 -71t-65 -42.5t-114.5 -11.5h-46v76h46q78 0 103 33t25 128v355h87v-355z" />
    <glyph glyph-name="imacron.sc" horiz-adv-x="217" 
d="M-17 777h253v-66h-253v66zM65 0v592h87v-592h-87z" />
    <glyph glyph-name="iogonek.sc" horiz-adv-x="217" 
d="M-27 -115q0 25 12.5 48.5t32 39t30 22.5t17.5 11v586h87v-592h-12q-7 -3 -17 -10t-25 -19.5t-25.5 -30t-10.5 -35.5q0 -39 38 -39q10 0 22 3.5t18 6.5l7 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5z" />
    <glyph glyph-name="itilde.sc" horiz-adv-x="217" 
d="M263 800q0 -115 -90 -115q-37 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM65 0v592h87v-592h-87z" />
    <glyph glyph-name="j.sc" horiz-adv-x="319" 
d="M263 237q0 -72 -6.5 -112t-29 -71t-65 -42.5t-114.5 -11.5h-46v76h46q78 0 103 33t25 128v355h87v-355z" />
    <glyph glyph-name="jcircumflex.sc" horiz-adv-x="319" 
d="M375 684h-86l-70 83l-70 -83h-85l111 143h87zM263 237q0 -72 -6.5 -112t-29 -71t-65 -42.5t-114.5 -11.5h-46v76h46q78 0 103 33t25 128v355h87v-355z" />
    <glyph glyph-name="k.sc" horiz-adv-x="542" 
d="M65 0v592h87v-338l272 338h108l-203 -244l205 -348h-98l-164 279l-120 -145v-134h-87z" />
    <glyph glyph-name="kcommaaccent.sc" horiz-adv-x="542" 
d="M65 0v592h87v-338l272 338h108l-203 -244l205 -348h-98l-164 279l-120 -145v-134h-87zM198 -235q16 39 29 98q6 24 9.5 48t3.5 37l1 12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="l.sc" horiz-adv-x="424" 
d="M65 0v592h87v-511h262v-81h-349z" />
    <glyph glyph-name="lacute.sc" horiz-adv-x="424" 
d="M74 685l77 144h95l-91 -144h-81zM65 0v592h87v-511h262v-81h-349z" />
    <glyph glyph-name="lcaron.sc" horiz-adv-x="424" 
d="M228 463l50 154h76l-71 -154h-55zM65 0v592h87v-511h262v-81h-349z" />
    <glyph glyph-name="lcommaaccent.sc" horiz-adv-x="424" 
d="M65 0v592h87v-511h262v-81h-349zM160 -234q16 39 29 98q6 24 9.5 48t3.5 36l1 13h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="ldot.sc" horiz-adv-x="424" 
d="M65 0v592h87v-511h262v-81h-349zM270 255v130h93v-130h-93z" />
    <glyph glyph-name="lslash.sc" horiz-adv-x="424" 
d="M152 289v-208h262v-81h-349v239l-78 -44l-29 58l107 61v278h87v-228l82 47l30 -58z" />
    <glyph glyph-name="m.sc" horiz-adv-x="771" 
d="M65 0v592h119l198 -512l200 512h124l-1 -592h-85l6 494l-202 -494h-86l-197 493l7 -493h-83z" />
    <glyph glyph-name="n.sc" horiz-adv-x="611" 
d="M142 487l4 -487h-81l2 592h94l307 -483l-5 483h83v-592h-93z" />
    <glyph glyph-name="nacute.sc" horiz-adv-x="611" 
d="M266 685l77 144h95l-91 -144h-81zM142 487l4 -487h-81l2 592h94l307 -483l-5 483h83v-592h-93z" />
    <glyph glyph-name="ncaron.sc" horiz-adv-x="611" 
d="M462 827l-113 -143h-87l-109 143h83l69 -81l71 81h86zM142 487l4 -487h-81l2 592h94l307 -483l-5 483h83v-592h-93z" />
    <glyph glyph-name="ncommaaccent.sc" horiz-adv-x="611" 
d="M142 487l4 -487h-81l2 592h94l307 -483l-5 483h83v-592h-93zM243 -235q16 39 29 98q6 24 9.5 48t4.5 37v12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="484" 
d="M439 275q0 -161 -43.5 -223t-153.5 -62q-98 0 -142.5 45t-44.5 102h75q0 -32 27.5 -56.5t84.5 -24.5q55 0 82.5 31.5t34.5 119.5q-48 -50 -134 -50q-175 0 -175 192q0 175 193 176q107 0 151.5 -58.5t44.5 -191.5zM238 222q85 0 124 69q-2 92 -30.5 130t-88.5 38
q-114 0 -114 -113q0 -124 109 -124z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="484" 
d="M439 520q0 -161 -43.5 -223t-153.5 -62q-98 0 -142.5 45t-44.5 102h75q0 -32 27.5 -56.5t84.5 -24.5q55 0 82.5 31.5t34.5 119.5q-48 -50 -134 -50q-175 0 -175 192q0 175 193 176q107 0 151.5 -58.5t44.5 -191.5zM238 467q85 0 124 69q-2 92 -30.5 130t-88.5 38
q-114 0 -114 -113q0 -124 109 -124z" />
    <glyph glyph-name="nine.osf" horiz-adv-x="604" 
d="M550 228q0 -219 -58 -304t-198 -85q-65 0 -114.5 18.5t-76 49t-39 63t-12.5 66.5h88q0 -47 39 -83t115 -36t114.5 47.5t48.5 178.5q-66 -76 -184 -76q-227 0 -227 263q0 108 63.5 173.5t186.5 66.5q136 0 195 -80t59 -262zM289 148q117 0 170 106q-4 129 -43 182.5
t-120 53.5q-71 0 -114.5 -43t-43.5 -120q0 -87 39 -133t112 -46z" />
    <glyph glyph-name="nine.sc" horiz-adv-x="528" 
d="M486 313q0 -183 -50.5 -253.5t-177.5 -70.5q-113 0 -164.5 51t-51.5 116h86q0 -38 32 -66.5t97 -28.5t97.5 37.5t40.5 140.5q-55 -59 -157 -59q-201 0 -201 217q0 89 55.5 144t167.5 56q123 0 174.5 -66t51.5 -218zM254 251q99 0 144 84q-3 103 -36.5 146t-102.5 43
q-62 0 -97.5 -34.5t-35.5 -96.5q0 -69 32.5 -105.5t95.5 -36.5z" />
    <glyph glyph-name="nine.subs" horiz-adv-x="484" 
d="M439 81q0 -161 -43.5 -223t-153.5 -62q-98 0 -142.5 45t-44.5 102h75q0 -32 27.5 -56.5t84.5 -24.5q55 0 82.5 31.5t34.5 119.5q-48 -50 -134 -50q-175 0 -175 192q0 175 193 176q107 0 151.5 -58.5t44.5 -191.5zM238 28q85 0 124 69q-2 92 -30.5 130t-88.5 38
q-114 0 -114 -113q0 -124 109 -124z" />
    <glyph glyph-name="nine.tf" horiz-adv-x="661" 
d="M586 376q0 -220 -59 -304t-207 -84q-69 0 -120.5 18.5t-79 49.5t-40 63.5t-12.5 66.5h91q0 -47 40.5 -83t120.5 -36q54 0 86 17t53.5 66.5t28.5 139.5q-69 -75 -189 -75q-237 0 -237 263q0 108 65.5 173.5t194.5 65.5q144 0 204 -79.5t60 -261.5zM315 295q123 0 177 106
q-3 130 -43.5 183.5t-126.5 53.5q-76 0 -120.5 -43t-44.5 -121q0 -88 40.5 -133.5t117.5 -45.5z" />
    <glyph glyph-name="ntilde.sc" horiz-adv-x="611" 
d="M460 800q0 -115 -90 -115q-36 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM142 487l4 -487h-81l2 592h94l307 -483l-5 483h83v-592h-93z" />
    <glyph glyph-name="numbersign.sc" horiz-adv-x="625" 
d="M103 0l32 160h-83v63h96l31 152h-87v64h100l29 144h64l-29 -144h141l29 144h65l-29 -144h84v-64h-97l-31 -152h88v-63h-101l-32 -160h-64l32 160h-142l-32 -160h-64zM212 223h141l31 152h-141z" />
    <glyph glyph-name="o.sc" horiz-adv-x="587" 
d="M547 295q0 -160 -59 -235.5t-194 -75.5t-194.5 75.5t-59.5 235.5t59.5 236t194.5 76t194 -76t59 -236zM129 295q0 -126 38 -181t127 -55q88 0 126 55t38 181q0 127 -38 182.5t-126 55.5q-89 0 -127 -55.5t-38 -182.5z" />
    <glyph glyph-name="oacute.sc" horiz-adv-x="587" 
d="M253 688l77 144h95l-91 -144h-81zM547 295q0 -160 -59 -235.5t-194 -75.5t-194.5 75.5t-59.5 235.5t59.5 236t194.5 76t194 -76t59 -236zM129 295q0 -126 38 -181t127 -55q88 0 126 55t38 181q0 127 -38 182.5t-126 55.5q-89 0 -127 -55.5t-38 -182.5z" />
    <glyph glyph-name="obreve.sc" horiz-adv-x="587" 
d="M443 809q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76zM547 295q0 -160 -59 -235.5t-194 -75.5t-194.5 75.5t-59.5 235.5t59.5 236t194.5 76t194 -76t59 -236zM129 295q0 -126 38 -181t127 -55q88 0 126 55
t38 181q0 127 -38 182.5t-126 55.5q-89 0 -127 -55.5t-38 -182.5z" />
    <glyph glyph-name="ocircumflex.sc" horiz-adv-x="587" 
d="M449 687h-86l-70 83l-70 -83h-85l111 143h87zM547 295q0 -160 -59 -235.5t-194 -75.5t-194.5 75.5t-59.5 235.5t59.5 236t194.5 76t194 -76t59 -236zM129 295q0 -126 38 -181t127 -55q88 0 126 55t38 181q0 127 -38 182.5t-126 55.5q-89 0 -127 -55.5t-38 -182.5z" />
    <glyph glyph-name="odieresis.sc" horiz-adv-x="587" 
d="M168 687v129h84v-129h-84zM336 687v129h85v-129h-85zM547 295q0 -160 -59 -235.5t-194 -75.5t-194.5 75.5t-59.5 235.5t59.5 236t194.5 76t194 -76t59 -236zM129 295q0 -126 38 -181t127 -55q88 0 126 55t38 181q0 127 -38 182.5t-126 55.5q-89 0 -127 -55.5t-38 -182.5z
" />
    <glyph glyph-name="oe.sc" horiz-adv-x="810" 
d="M293 0q-136 0 -194.5 71.5t-58.5 223.5q0 153 58.5 225t194.5 72h490v-76h-303v-175h260v-71h-260v-194h308v-76h-495zM130 295q0 -116 38 -169.5t126 -53.5h98v448h-98q-88 0 -126 -54t-38 -171z" />
    <glyph glyph-name="ograve.sc" horiz-adv-x="587" 
d="M334 688h-80l-92 144h95zM547 295q0 -160 -59 -235.5t-194 -75.5t-194.5 75.5t-59.5 235.5t59.5 236t194.5 76t194 -76t59 -236zM129 295q0 -126 38 -181t127 -55q88 0 126 55t38 181q0 127 -38 182.5t-126 55.5q-89 0 -127 -55.5t-38 -182.5z" />
    <glyph glyph-name="ohungarumlaut.sc" horiz-adv-x="587" 
d="M161 688l71 144h95l-86 -144h-80zM313 688l86 144h95l-101 -144h-80zM547 295q0 -160 -59 -235.5t-194 -75.5t-194.5 75.5t-59.5 235.5t59.5 236t194.5 76t194 -76t59 -236zM129 295q0 -126 38 -181t127 -55q88 0 126 55t38 181q0 127 -38 182.5t-126 55.5
q-89 0 -127 -55.5t-38 -182.5z" />
    <glyph glyph-name="omacron.sc" horiz-adv-x="587" 
d="M167 780h253v-66h-253v66zM547 295q0 -160 -59 -235.5t-194 -75.5t-194.5 75.5t-59.5 235.5t59.5 236t194.5 76t194 -76t59 -236zM129 295q0 -126 38 -181t127 -55q88 0 126 55t38 181q0 127 -38 182.5t-126 55.5q-89 0 -127 -55.5t-38 -182.5z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="245" 
d="M103 0v428l-95 -43v79l121 53h47v-517h-73z" />
    <glyph glyph-name="one.numr" horiz-adv-x="245" 
d="M103 245v428l-95 -43v79l121 53h47v-517h-73z" />
    <glyph glyph-name="one.osf" horiz-adv-x="346" 
d="M31 0v76h102v377l-94 -38v92l124 53h57v-484h105v-76h-294z" />
    <glyph glyph-name="one.sc" horiz-adv-x="280" 
d="M113 0v488l-109 -43v83l139 60h53v-588h-83z" />
    <glyph glyph-name="one.subs" horiz-adv-x="245" 
d="M103 -194v428l-95 -43v79l121 53h47v-517h-73z" />
    <glyph glyph-name="one.tf" horiz-adv-x="662" 
d="M171 0v72h124v527l-129 -56v92l161 72h57v-635h121v-72h-334z" />
    <glyph glyph-name="ordfeminine.sc" horiz-adv-x="363" 
d="M268 293l-6 33q-46 -42 -113 -42q-121 0 -121 105q0 111 142 111q54 0 86 -5q-2 48 -16 66.5t-58 18.5q-78 0 -78 -58h-61q0 112 136 112q42 0 69.5 -9.5t42 -21.5t22 -39t8.5 -46.5t1 -59.5v-78l7 -87h-61zM162 339q34 0 59.5 14t34.5 33v58q-48 3 -69 3
q-47 0 -69 -12.5t-22 -42.5q0 -53 66 -53z" />
    <glyph glyph-name="ordmasculine.sc" horiz-adv-x="359" 
d="M333 460q0 -88 -34 -131.5t-119 -43.5t-119.5 43.5t-34.5 131.5q0 87 34.5 131t119.5 44t119 -43.5t34 -131.5zM93 460q0 -63 19.5 -91.5t67.5 -28.5t67 28.5t19 91.5t-19.5 92t-66.5 29q-48 0 -67.5 -29t-19.5 -92z" />
    <glyph glyph-name="oslash.sc" horiz-adv-x="587" 
d="M20 -17l67 93q-47 73 -47 219q0 160 59.5 236t194.5 76q94 0 153 -38l24 33h99l-68 -93q45 -74 45 -214q0 -160 -59 -235.5t-194 -75.5q-91 0 -149 35l-26 -36h-99zM129 295q0 -88 17 -139l252 345q-38 32 -104 32q-89 0 -127 -55.5t-38 -182.5zM458 295q0 81 -15 132
l-249 -341q36 -27 100 -27q88 0 126 55t38 181z" />
    <glyph glyph-name="oslashacute.sc" horiz-adv-x="587" 
d="M253 688l77 144h95l-91 -144h-81zM20 -17l67 93q-47 73 -47 219q0 160 59.5 236t194.5 76q94 0 153 -38l24 33h99l-68 -93q45 -74 45 -214q0 -160 -59 -235.5t-194 -75.5q-91 0 -149 35l-26 -36h-99zM129 295q0 -88 17 -139l252 345q-38 32 -104 32q-89 0 -127 -55.5
t-38 -182.5zM458 295q0 81 -15 132l-249 -341q36 -27 100 -27q88 0 126 55t38 181z" />
    <glyph glyph-name="otilde.sc" horiz-adv-x="587" 
d="M447 803q0 -115 -90 -115q-37 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM547 295q0 -160 -59 -235.5t-194 -75.5t-194.5 75.5t-59.5 235.5t59.5 236t194.5 76t194 -76t59 -236zM129 295
q0 -126 38 -181t127 -55q88 0 126 55t38 181q0 127 -38 182.5t-126 55.5q-89 0 -127 -55.5t-38 -182.5z" />
    <glyph glyph-name="p.sc" horiz-adv-x="551" 
d="M65 0v594h194q145 0 204 -42t59 -155q0 -114 -59 -156t-205 -42h-106v-199h-87zM152 521v-247h120q84 0 122.5 23.5t38.5 99.5t-38.5 100t-122.5 24h-120z" />
    <glyph glyph-name="paragraph.alt" horiz-adv-x="602" 
d="M298 0v298q-148 3 -205.5 51t-57.5 180q0 133 58 182t205 49h226v-760h-66v696h-94v-696h-66z" />
    <glyph glyph-name="parenleft.case" horiz-adv-x="317" 
d="M104 345q0 82 8 155t19 118.5t22.5 79.5t19.5 48l8 14h93l-8 -16q-7 -15 -18.5 -49.5t-22.5 -80.5t-19 -118t-8 -151t8 -150t20 -115.5t24 -77.5t20 -48l8 -14h-94l-8.5 14t-19.5 44t-24.5 77.5t-19 115t-8.5 154.5z" />
    <glyph glyph-name="parenleft.sc" horiz-adv-x="298" 
d="M104 275q0 75 8 142.5t19 110t22.5 75t19.5 46.5l8 14h83q-3 -5 -8 -15.5t-18.5 -47t-23.5 -78t-18 -108.5t-8 -139q0 -114 20 -208.5t40 -132.5l19 -37h-84l-8.5 14t-19 42.5t-24 73t-19 106.5t-8.5 142z" />
    <glyph glyph-name="parenright.case" horiz-adv-x="317" 
d="M136 760q3 -5 8.5 -15t18.5 -46.5t23 -80t18.5 -117.5t8.5 -156t-8 -154t-20 -116t-24 -76.5t-20 -45.5l-8 -13h-93l8 14q7 15 19 48t23 77.5t19.5 115.5t8.5 150t-8 151t-19 118t-22.5 80.5t-19.5 49.5l-8 16h95z" />
    <glyph glyph-name="parenright.sc" horiz-adv-x="298" 
d="M127 663l8.5 -14.5t18.5 -45t23 -76.5t18.5 -109t8.5 -143t-8.5 -141.5t-21 -107.5t-24.5 -72t-20 -44l-9 -13h-83l8.5 14.5t19.5 44.5t24.5 75t19 106t8.5 138t-8 138t-19 109.5t-22.5 77t-19.5 48.5l-8 15h86z" />
    <glyph glyph-name="percent.sc" horiz-adv-x="688" 
d="M310 451q0 -73 -32.5 -111t-108.5 -38t-108.5 38t-32.5 111q0 74 33 112.5t108 38.5t108 -38.5t33 -112.5zM102 0l390 588h81l-390 -588h-81zM90 451q0 -94 79 -94q42 0 60.5 23t18.5 71q0 98 -79 98t-79 -98zM646 133q0 -72 -32.5 -110t-107.5 -38q-142 0 -142 148
q0 74 33 113t109 39q75 0 107.5 -38.5t32.5 -113.5zM427 133q0 -48 18.5 -71t60.5 -23t60 23t18 71q0 98 -78 98q-79 0 -79 -98z" />
    <glyph glyph-name="perthousand.sc" horiz-adv-x="988" 
d="M310 451q0 -73 -32.5 -111t-108.5 -38t-108.5 38t-32.5 111q0 74 33 112.5t108 38.5t108 -38.5t33 -112.5zM102 0l390 588h81l-390 -588h-81zM90 451q0 -94 79 -94q42 0 60.5 23t18.5 71q0 98 -79 98t-79 -98zM646 133q0 -72 -32.5 -110t-107.5 -38q-142 0 -142 148
q0 74 33 113t109 39q75 0 107.5 -38.5t32.5 -113.5zM956 133q0 -72 -32.5 -110t-108.5 -38q-142 0 -142 148q0 152 142 152q76 0 108.5 -38.5t32.5 -113.5zM427 133q0 -48 18.5 -71t60.5 -23t60 23t18 71q0 98 -78 98q-79 0 -79 -98zM736 133q0 -48 18.5 -71t60.5 -23t60 23
t18 71q0 98 -78 98q-79 0 -79 -98z" />
    <glyph glyph-name="q.sc" horiz-adv-x="596" 
d="M547 295q0 -182 -76 -254l93 -81l-64 -54l-95 95q-48 -17 -111 -17q-135 0 -194.5 75.5t-59.5 235.5t59.5 236t194.5 76t194 -76t59 -236zM129 295q0 -126 38 -181t127 -55q88 0 126 55t38 181q0 127 -38 182.5t-126 55.5q-89 0 -127 -55.5t-38 -182.5z" />
    <glyph glyph-name="question.sc" horiz-adv-x="487" 
d="M271 155h-86l1 34q1 36 16 65.5t37 49.5l43.5 39t37 45.5t15.5 58.5q0 40 -28.5 67.5t-78.5 27.5q-51 0 -80 -29.5t-29 -77.5h-82q0 80 47 130.5t143 50.5q98 0 148 -47t50 -116q0 -39 -15.5 -70.5t-37.5 -52.5l-44.5 -40.5t-39 -45t-17.5 -55.5v-34zM185 0v105h86v-105
h-86z" />
    <glyph glyph-name="questiondown.case" horiz-adv-x="536" 
d="M335 711v-112h-90v112h90zM245 548h90l-1 -36q0 -51 -17 -87.5t-41 -57.5l-47.5 -41t-40.5 -51.5t-17 -75.5q0 -129 118 -129q121 0 121 142h88q0 -222 -207 -222q-111 0 -163 54t-52 149q0 52 17 88.5t41 58l48.5 42t42.5 53t19 77.5z" />
    <glyph glyph-name="questiondown.sc" horiz-adv-x="494" 
d="M309 594v-105h-85v105h85zM224 439h85l-1 -35q-1 -36 -16.5 -65t-37 -49l-43.5 -39.5t-37 -45.5t-15 -58q0 -40 28.5 -67.5t78.5 -27.5q52 0 81 29.5t29 77.5h82q0 -80 -47 -130.5t-143 -50.5q-99 0 -148.5 46.5t-49.5 116.5q0 39 15.5 70.5t37.5 52.5t44.5 40t39 44.5
t17.5 55.5v35z" />
    <glyph glyph-name="quotedbl.sc" horiz-adv-x="318" 
d="M55 456l-15 204h98l-14 -204h-69zM195 456l-14 204h97l-14 -204h-69z" />
    <glyph glyph-name="quotedblleft.sc" horiz-adv-x="342" 
d="M50 428q0 40 20 115q10 36 22.5 66t20.5 42l8 12h65q-23 -50 -38 -126q-7 -35 -10.5 -62t-3.5 -37v-10h-84zM191 428q0 40 20 115q10 36 22.5 66t20.5 42l8 12h65q-23 -50 -38 -126q-7 -35 -10.5 -62t-3.5 -37v-10h-84z" />
    <glyph glyph-name="quotedblright.sc" horiz-adv-x="342" 
d="M30 428q23 50 38 126q7 35 10.5 62t3.5 37v10h84q0 -39 -20 -115q-10 -36 -22.5 -66t-20.5 -42l-8 -12h-65zM171 428q23 50 38 126q7 35 10.5 62t3.5 37v10h84q0 -39 -20 -115q-10 -36 -23 -66t-21 -42l-8 -12h-64z" />
    <glyph glyph-name="quoteleft.sc" horiz-adv-x="201" 
d="M50 428q0 40 20 115q10 36 22.5 66t20.5 42l8 12h65q-23 -50 -38 -126q-7 -35 -10.5 -62t-3.5 -37v-10h-84z" />
    <glyph glyph-name="quoteright.sc" horiz-adv-x="201" 
d="M30 428q23 50 38 126q7 35 10.5 62t3.5 37v10h84q0 -39 -20 -115q-10 -36 -22.5 -66t-20.5 -42l-8 -12h-65z" />
    <glyph glyph-name="quotesingle.sc" horiz-adv-x="178" 
d="M55 456l-15 204h98l-14 -204h-69z" />
    <glyph glyph-name="r.sc" horiz-adv-x="565" 
d="M65 0v594h217q131 0 186.5 -40t55.5 -150q0 -79 -27 -120t-88 -56l122 -228h-94l-110 218q-15 -1 -47 -1h-128v-217h-87zM152 519v-228h122q86 0 123 21t37 93q0 71 -37.5 92.5t-122.5 21.5h-122z" />
    <glyph glyph-name="racute.sc" horiz-adv-x="565" 
d="M231 685l77 144h95l-91 -144h-81zM65 0v594h217q131 0 186.5 -40t55.5 -150q0 -79 -27 -120t-88 -56l122 -228h-94l-110 218q-15 -1 -47 -1h-128v-217h-87zM152 519v-228h122q86 0 123 21t37 93q0 71 -37.5 92.5t-122.5 21.5h-122z" />
    <glyph glyph-name="rcaron.sc" horiz-adv-x="565" 
d="M427 827l-113 -143h-87l-109 143h83l69 -81l71 81h86zM65 0v594h217q131 0 186.5 -40t55.5 -150q0 -79 -27 -120t-88 -56l122 -228h-94l-110 218q-15 -1 -47 -1h-128v-217h-87zM152 519v-228h122q86 0 123 21t37 93q0 71 -37.5 92.5t-122.5 21.5h-122z" />
    <glyph glyph-name="rcommaaccent.sc" horiz-adv-x="565" 
d="M65 0v594h217q131 0 186.5 -40t55.5 -150q0 -79 -27 -120t-88 -56l122 -228h-94l-110 218q-15 -1 -47 -1h-128v-217h-87zM152 519v-228h122q86 0 123 21t37 93q0 71 -37.5 92.5t-122.5 21.5h-122zM207 -235q16 39 29 98q6 24 9.5 48t4.5 37v12h80q-5 -51 -21 -103
q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="registered.alt" horiz-adv-x="499" 
d="M454 531q0 -97 -48 -156t-157 -59q-108 0 -156.5 59.5t-48.5 155.5q0 97 49 157t156 60q109 0 157 -60t48 -157zM87 531q0 -177 162 -177t162 177q0 180 -162 180q-87 0 -124.5 -50t-37.5 -130zM166 417v229h89q47 0 67.5 -16t20.5 -58q0 -57 -44 -69l42 -86h-41l-38 82
h-56v-82h-40zM206 614v-83h46q26 0 38.5 7.5t12.5 33.5t-12.5 34t-38.5 8h-46z" />
    <glyph glyph-name="s.sc" horiz-adv-x="534" 
d="M498 156q0 -85 -59 -129t-172 -43q-229 3 -229 199h84q0 -123 148 -126q142 -1 142 95q0 43 -38 67.5t-92.5 36.5t-108.5 28t-92 54t-38 101q1 80 56 125t165 44q120 -1 170.5 -48t50.5 -136h-85q0 48 -28 78.5t-108 31.5q-72 1 -103.5 -25t-31.5 -67q0 -31 20.5 -52
t53 -32.5t71.5 -20.5t78.5 -21.5t72 -30t53 -50.5t20.5 -79z" />
    <glyph glyph-name="sacute.sc" horiz-adv-x="534" 
d="M224 688l77 144h95l-91 -144h-81zM498 156q0 -85 -59 -129t-172 -43q-229 3 -229 199h84q0 -123 148 -126q142 -1 142 95q0 43 -38 67.5t-92.5 36.5t-108.5 28t-92 54t-38 101q1 80 56 125t165 44q120 -1 170.5 -48t50.5 -136h-85q0 48 -28 78.5t-108 31.5
q-72 1 -103.5 -25t-31.5 -67q0 -31 20.5 -52t53 -32.5t71.5 -20.5t78.5 -21.5t72 -30t53 -50.5t20.5 -79z" />
    <glyph glyph-name="scaron.sc" horiz-adv-x="534" 
d="M420 830l-113 -143h-87l-109 143h83l69 -81l71 81h86zM498 156q0 -85 -59 -129t-172 -43q-229 3 -229 199h84q0 -123 148 -126q142 -1 142 95q0 43 -38 67.5t-92.5 36.5t-108.5 28t-92 54t-38 101q1 80 56 125t165 44q120 -1 170.5 -48t50.5 -136h-85q0 48 -28 78.5
t-108 31.5q-72 1 -103.5 -25t-31.5 -67q0 -31 20.5 -52t53 -32.5t71.5 -20.5t78.5 -21.5t72 -30t53 -50.5t20.5 -79z" />
    <glyph glyph-name="scedilla.sc" horiz-adv-x="534" 
d="M374 -149q0 -49 -31 -70t-105 -21q-20 0 -37.5 2t-24.5 4l-8 2v54q17 -6 48 -6q38 0 54 8.5t16 33.5q0 45 -85 45h-5l37 83q-195 17 -195 197h84q0 -123 148 -126q142 -1 142 95q0 43 -38 67.5t-92.5 36.5t-108.5 28t-92 54t-38 101q1 80 56 125t165 44q120 -1 170.5 -48
t50.5 -136h-85q0 48 -28 78.5t-108 31.5q-72 1 -103.5 -25t-31.5 -67q0 -31 20.5 -52t53 -32.5t71.5 -20.5t78.5 -21.5t72 -30t53 -50.5t20.5 -79q0 -153 -183 -170l-25 -55q37 -3 60.5 -22.5t23.5 -57.5z" />
    <glyph glyph-name="scircumflex.sc" horiz-adv-x="534" 
d="M420 687h-86l-70 83l-70 -83h-85l111 143h87zM498 156q0 -85 -59 -129t-172 -43q-229 3 -229 199h84q0 -123 148 -126q142 -1 142 95q0 43 -38 67.5t-92.5 36.5t-108.5 28t-92 54t-38 101q1 80 56 125t165 44q120 -1 170.5 -48t50.5 -136h-85q0 48 -28 78.5t-108 31.5
q-72 1 -103.5 -25t-31.5 -67q0 -31 20.5 -52t53 -32.5t71.5 -20.5t78.5 -21.5t72 -30t53 -50.5t20.5 -79z" />
    <glyph glyph-name="scommaaccent.sc" horiz-adv-x="534" 
d="M498 156q0 -85 -59 -129t-172 -43q-229 3 -229 199h84q0 -123 148 -126q142 -1 142 95q0 43 -38 67.5t-92.5 36.5t-108.5 28t-92 54t-38 101q1 80 56 125t165 44q120 -1 170.5 -48t50.5 -136h-85q0 48 -28 78.5t-108 31.5q-72 1 -103.5 -25t-31.5 -67q0 -31 20.5 -52
t53 -32.5t71.5 -20.5t78.5 -21.5t72 -30t53 -50.5t20.5 -79zM211 -247q16 39 29 98q6 24 9.5 48t4.5 37v12h80q-5 -51 -21 -103q-9 -28 -19 -51t-16 -32l-6 -9h-61z" />
    <glyph glyph-name="section.sups" horiz-adv-x="335" 
d="M275 551q0 -32 -24 -56q22 -19 22 -51q0 -75 -107 -75q-106 0 -106 84h50q0 -46 55 -46q26 0 40.5 9.5t14.5 25.5q0 22 -24.5 32.5t-54 14t-54 21t-24.5 51.5q0 32 24 56q-22 19 -22 50q0 72 102 72q55 0 78 -20t23 -59h-50q0 42 -50 42q-51 0 -51 -35q0 -20 24.5 -30
t54.5 -12.5t54.5 -20.5t24.5 -53zM116 561q0 -10 5 -16.5t15.5 -11t20.5 -7t26 -5.5t26 -6q13 13 13 33q0 10 -5 17.5t-15.5 12.5t-20 7.5t-26 6.5t-25.5 6q-14 -16 -14 -37z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="375" 
d="M100 0q0 68 18.5 139t45 123t53.5 94t45 64l19 22l-267 -2v72h351v-72l-20.5 -24t-44.5 -63.5t-57 -98t-45 -119.5t-20 -135h-78z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="375" 
d="M100 245q0 68 18.5 139t45 123t53.5 94t45 64l19 22l-267 -2v72h351v-72l-20.5 -24t-44.5 -63.5t-57 -98t-45 -119.5t-20 -135h-78z" />
    <glyph glyph-name="seven.osf" horiz-adv-x="469" 
d="M117 -145q0 97 26 196t63 172t74 131.5t63 89.5l26 30l-367 2v85h466v-88q-11 -12 -28.5 -33t-62.5 -88.5t-79.5 -136.5t-63 -168.5t-28.5 -191.5h-89z" />
    <glyph glyph-name="seven.sc" horiz-adv-x="412" 
d="M103 0q0 77 21.5 157.5t52.5 139.5t61.5 107.5t51.5 73.5l22 25h-310v79h406v-82q-9 -10 -23.5 -27.5t-52 -72.5t-66 -111t-52 -136t-23.5 -153h-88z" />
    <glyph glyph-name="seven.subs" horiz-adv-x="375" 
d="M100 -194q0 68 18.5 139t45 123t53.5 94t45 64l19 22l-267 -2v72h351v-72l-20.5 -24t-44.5 -63.5t-57 -98t-45 -119.5t-20 -135h-78z" />
    <glyph glyph-name="seven.tf" horiz-adv-x="660" 
d="M211 0q0 95 26 193t63 170t74 130.5t63 88.5l26 31l-372 2v85h475v-88q-11 -12 -28.5 -33t-62.5 -88.5t-79.5 -136t-63 -166t-28.5 -188.5h-93z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="484" 
d="M439 165q0 -174 -193 -175q-106 0 -151 57.5t-45 180.5q0 163 45 230t151 67q92 0 133.5 -45t41.5 -103h-75q0 33 -24.5 57.5t-74.5 24.5q-55 0 -83 -36t-34 -129q46 50 137 50q89 0 130.5 -49.5t41.5 -129.5zM251 280q-84 0 -123 -69q2 -83 30.5 -119.5t87.5 -36.5
q114 0 114 112q0 113 -109 113z" />
    <glyph glyph-name="six.numr" horiz-adv-x="484" 
d="M439 410q0 -174 -193 -175q-106 0 -151 57.5t-45 180.5q0 163 45 230t151 67q92 0 133.5 -45t41.5 -103h-75q0 33 -24.5 57.5t-74.5 24.5q-55 0 -83 -36t-34 -129q46 50 137 50q89 0 130.5 -49.5t41.5 -129.5zM251 525q-84 0 -123 -69q2 -83 30.5 -119.5t87.5 -36.5
q114 0 114 112q0 113 -109 113z" />
    <glyph glyph-name="six.osf" horiz-adv-x="602" 
d="M562 226q0 -110 -61 -174t-196 -64q-134 0 -194 78.5t-60 245.5q0 221 59.5 313t194.5 92q63 0 110.5 -18.5t72.5 -49.5t37 -64t12 -67h-88q0 49 -35 84.5t-108 35.5q-75 0 -114.5 -53.5t-48.5 -190.5q66 76 188 76q118 0 174.5 -67t56.5 -177zM312 391q-118 0 -171 -106
q4 -116 44 -167t120 -51q164 0 164 162t-157 162z" />
    <glyph glyph-name="six.sc" horiz-adv-x="525" 
d="M490 188q0 -91 -52 -145t-170 -54q-122 0 -174 65.5t-52 205.5q0 185 51.5 261t174.5 76q56 0 97 -15.5t63 -41.5t32 -54t10 -57h-85q0 39 -28.5 67.5t-87.5 28.5q-64 0 -96.5 -42t-39.5 -151q54 58 159 58q102 0 150 -55.5t48 -146.5zM273 320q-96 0 -142 -82
q4 -94 37 -135.5t100 -41.5q133 0 133 130q0 129 -128 129z" />
    <glyph glyph-name="six.subs" horiz-adv-x="484" 
d="M439 -29q0 -174 -193 -175q-106 0 -151 57.5t-45 180.5q0 163 45 230t151 67q92 0 133.5 -45t41.5 -103h-75q0 33 -24.5 57.5t-74.5 24.5q-55 0 -83 -36t-34 -129q46 50 137 50q89 0 130.5 -49.5t41.5 -129.5zM251 86q-84 0 -123 -69q2 -83 30.5 -119.5t87.5 -36.5
q114 0 114 112q0 113 -109 113z" />
    <glyph glyph-name="six.tf" horiz-adv-x="661" 
d="M591 226q0 -238 -260 -238q-142 -1 -203 77t-61 247q0 222 60.5 313.5t203.5 91.5q64 0 112.5 -18.5t74 -49.5t37.5 -63.5t12 -67.5h-92q0 49 -35 84.5t-108 35.5q-79 0 -118.5 -52.5t-49.5 -190.5q69 75 193 75q120 0 177 -67t57 -177zM338 391q-71 0 -113.5 -32
t-63.5 -72q4 -117 45 -168.5t125 -51.5q164 0 164 162t-157 162z" />
    <glyph glyph-name="slash.sc" horiz-adv-x="424" 
d="M41 -69l260 717h82l-260 -717h-82z" />
    <glyph glyph-name="sterling.sc" horiz-adv-x="465" 
d="M198 212q2 -24 2 -36q0 -74 -50 -110l281 4v-73h-405v68q71 18 84 76q3 12 3 25q0 25 -3 46h-90v61h76q-3 10 -16.5 45.5t-19 60t-5.5 54.5q0 72 49 116t143 44q192 0 192 -180h-84q0 51 -28.5 79t-76.5 28q-49 0 -77 -27t-28 -76q0 -23 6 -45.5t17.5 -52t16.5 -46.5h135
v-61h-122z" />
    <glyph glyph-name="t.sc" horiz-adv-x="473" 
d="M191 0l2 512h-186v80h460v-80h-187l-3 -512h-86z" />
    <glyph glyph-name="tbar.sc" horiz-adv-x="473" 
d="M191 0l1 242h-96v64h96l1 206h-186v80h460v-80h-187l-1 -206h98v-64h-99l-1 -242h-86z" />
    <glyph glyph-name="tcaron.sc" horiz-adv-x="473" 
d="M392 827l-113 -143h-87l-109 143h83l69 -81l71 81h86zM191 0l2 512h-186v80h460v-80h-187l-3 -512h-86z" />
    <glyph glyph-name="thorn.sc" horiz-adv-x="543" 
d="M65 0v592h89l-1 -98h129q126 0 179 -41.5t53 -154.5t-53 -154.5t-181 -41.5h-127v-102h-88zM153 419v-243h130q73 0 108.5 23.5t35.5 98.5q0 74 -36 97.5t-109 23.5h-129z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="439" 
d="M246 328q153 -19 153 -169q0 -76 -45 -122.5t-144 -46.5q-94 0 -138 45t-44 113h75q0 -38 27.5 -63.5t78.5 -25.5q110 0 110 105q0 52 -31.5 81t-89.5 29l-62 -5v55l149 120l-234 -3v69h337v-65z" />
    <glyph glyph-name="three.numr" horiz-adv-x="439" 
d="M246 573q153 -19 153 -169q0 -76 -45 -122.5t-144 -46.5q-94 0 -138 45t-44 113h75q0 -38 27.5 -63.5t78.5 -25.5q110 0 110 105q0 52 -31.5 81t-89.5 29l-62 -5v55l149 120l-234 -3v69h337v-65z" />
    <glyph glyph-name="three.osf" horiz-adv-x="539" 
d="M292 308q209 -23 209 -224q0 -109 -58.5 -173t-186.5 -64q-121 0 -179.5 60.5t-58.5 150.5h90q0 -56 38.5 -92.5t107.5 -36.5q152 0 152 162q0 72 -45 111t-120 39l-88 -7v67l208 176h-309v84h435v-83z" />
    <glyph glyph-name="three.sc" horiz-adv-x="480" 
d="M266 372q176 -21 176 -185q0 -91 -51.5 -144.5t-165.5 -53.5q-110 0 -160.5 51t-50.5 129h85q0 -45 32 -74t92 -29q130 0 130 128q0 57 -38.5 88.5t-104.5 31.5l-73 -6v60l178 140l-274 -5v79h389v-72z" />
    <glyph glyph-name="three.subs" horiz-adv-x="439" 
d="M246 134q153 -19 153 -169q0 -76 -45 -122.5t-144 -46.5q-94 0 -138 45t-44 113h75q0 -38 27.5 -63.5t78.5 -25.5q110 0 110 105q0 52 -31.5 81t-89.5 29l-62 -5v55l149 120l-234 -3v69h337v-65z" />
    <glyph glyph-name="three.tf" horiz-adv-x="662" 
d="M343 449q215 -23 215 -224q0 -237 -253 -237q-127 0 -186.5 60t-59.5 150h91q0 -56 39.5 -92t113.5 -36q159 0 159 161q0 72 -46.5 111.5t-125.5 39.5l-88 -7v67l213 174h-323v84h453v-83z" />
    <glyph glyph-name="tildecomb.case" 
d="M-65 880q0 -115 -91 -115q-38 0 -65.5 23t-48.5 23q-39 0 -39 -46h-62q0 116 92 116q37 0 64.5 -23t48.5 -23q39 0 39 45h62z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="397" 
d="M22 4v65l139 118q49 42 71 63.5t41.5 56.5t19.5 73q0 39 -24.5 62.5t-72.5 23.5q-46 0 -73.5 -27.5t-27.5 -70.5h-75q0 75 44 120.5t131 45.5q91 0 133 -41.5t42 -107.5q0 -31 -8 -59.5t-16.5 -47t-32 -45.5t-34.5 -38l-44 -42l-94 -85l229 4v-68h-348z" />
    <glyph glyph-name="two.numr" horiz-adv-x="397" 
d="M22 249v65l139 118q49 42 71 63.5t41.5 56.5t19.5 73q0 39 -24.5 62.5t-72.5 23.5q-46 0 -73.5 -27.5t-27.5 -70.5h-75q0 75 44 120.5t131 45.5q91 0 133 -41.5t42 -107.5q0 -31 -8 -59.5t-16.5 -47t-32 -45.5t-34.5 -38l-44 -42l-94 -85l229 4v-68h-348z" />
    <glyph glyph-name="two.osf" horiz-adv-x="507" 
d="M32 0v75q86 55 170 121q96 75 118 97q47 50 47 108q0 42 -30 67.5t-93 25.5q-61 0 -94 -29t-33 -77h-85q0 83 51.5 132t159.5 49q113 0 162 -44.5t49 -117.5q0 -37 -9 -66.5t-32.5 -59.5t-44.5 -50t-64 -58l-16 -14q-34 -30 -113 -88l282 4v-75h-425z" />
    <glyph glyph-name="two.sc" horiz-adv-x="462" 
d="M19 3v78q79 53 162 124q44 36 63.5 54t45.5 47t36 55t10 57q0 44 -28.5 71t-85.5 27q-53 0 -85 -32t-32 -80h-85q0 86 51.5 138.5t149.5 52.5q105 0 153.5 -48t48.5 -121q0 -43 -11 -78.5t-38.5 -70.5t-47 -54.5t-63.5 -59.5l-103 -87l267 4v-77h-408z" />
    <glyph glyph-name="two.subs" horiz-adv-x="397" 
d="M22 -190v65l139 118q49 42 71 63.5t41.5 56.5t19.5 73q0 39 -24.5 62.5t-72.5 23.5q-46 0 -73.5 -27.5t-27.5 -70.5h-75q0 75 44 120.5t131 45.5q91 0 133 -41.5t42 -107.5q0 -31 -8 -59.5t-16.5 -47t-32 -45.5t-34.5 -38l-44 -42l-94 -84l229 3v-68h-348z" />
    <glyph glyph-name="two.tf" horiz-adv-x="661" 
d="M76 4v84q93 67 193 156l61.5 53.5t48 47.5t41 51.5t22.5 50.5t10 60q0 56 -35.5 90t-103.5 34q-65 0 -105 -39.5t-40 -99.5h-91q0 99 60.5 161t173.5 62q120 0 177 -56.5t57 -143.5q0 -42 -11.5 -80t-25 -64t-45 -61.5t-50 -53l-62.5 -57.5q-27 -24 -138 -116l338 5v-84
h-475z" />
    <glyph glyph-name="u.sc" horiz-adv-x="577" 
d="M521 287q0 -63 -5 -106.5t-19.5 -83t-40 -63t-67.5 -37t-101 -13.5t-100.5 13.5t-67 37t-40 63t-19.5 83t-5 106.5v305h87v-307q0 -117 30 -168t115 -51t115 51t30 168v307h88v-305z" />
    <glyph glyph-name="uacute.sc" horiz-adv-x="577" 
d="M248 685l77 144h95l-91 -144h-81zM521 287q0 -63 -5 -106.5t-19.5 -83t-40 -63t-67.5 -37t-101 -13.5t-100.5 13.5t-67 37t-40 63t-19.5 83t-5 106.5v305h87v-307q0 -117 30 -168t115 -51t115 51t30 168v307h88v-305z" />
    <glyph glyph-name="ubreve.sc" horiz-adv-x="577" 
d="M438 806q0 -44 -37.5 -83.5t-111.5 -39.5t-111.5 39.5t-37.5 83.5h78q0 -21 20.5 -42t50.5 -21q31 0 52 21t21 42h76zM521 287q0 -63 -5 -106.5t-19.5 -83t-40 -63t-67.5 -37t-101 -13.5t-100.5 13.5t-67 37t-40 63t-19.5 83t-5 106.5v305h87v-307q0 -117 30 -168
t115 -51t115 51t30 168v307h88v-305z" />
    <glyph glyph-name="ucircumflex.sc" horiz-adv-x="577" 
d="M444 684h-86l-70 83l-70 -83h-85l111 143h87zM521 287q0 -63 -5 -106.5t-19.5 -83t-40 -63t-67.5 -37t-101 -13.5t-100.5 13.5t-67 37t-40 63t-19.5 83t-5 106.5v305h87v-307q0 -117 30 -168t115 -51t115 51t30 168v307h88v-305z" />
    <glyph glyph-name="udieresis.sc" horiz-adv-x="577" 
d="M163 684v129h84v-129h-84zM331 684v129h85v-129h-85zM521 287q0 -63 -5 -106.5t-19.5 -83t-40 -63t-67.5 -37t-101 -13.5t-100.5 13.5t-67 37t-40 63t-19.5 83t-5 106.5v305h87v-307q0 -117 30 -168t115 -51t115 51t30 168v307h88v-305z" />
    <glyph glyph-name="ugrave.sc" horiz-adv-x="577" 
d="M329 685h-80l-92 144h95zM521 287q0 -63 -5 -106.5t-19.5 -83t-40 -63t-67.5 -37t-101 -13.5t-100.5 13.5t-67 37t-40 63t-19.5 83t-5 106.5v305h87v-307q0 -117 30 -168t115 -51t115 51t30 168v307h88v-305z" />
    <glyph glyph-name="uhungarumlaut.sc" horiz-adv-x="577" 
d="M156 685l71 144h95l-86 -144h-80zM308 685l86 144h95l-101 -144h-80zM521 287q0 -63 -5 -106.5t-19.5 -83t-40 -63t-67.5 -37t-101 -13.5t-100.5 13.5t-67 37t-40 63t-19.5 83t-5 106.5v305h87v-307q0 -117 30 -168t115 -51t115 51t30 168v307h88v-305z" />
    <glyph glyph-name="umacron.sc" horiz-adv-x="577" 
d="M162 777h253v-66h-253v66zM521 287q0 -63 -5 -106.5t-19.5 -83t-40 -63t-67.5 -37t-101 -13.5t-100.5 13.5t-67 37t-40 63t-19.5 83t-5 106.5v305h87v-307q0 -117 30 -168t115 -51t115 51t30 168v307h88v-305z" />
    <glyph glyph-name="uni0163.sc" horiz-adv-x="473" 
d="M329 -138q0 -49 -31 -70t-105 -21q-20 0 -37.5 2t-25.5 4l-7 2v54q17 -6 48 -6q38 0 54 8.5t16 33.5q0 45 -85 45h-5l40 88l2 510h-186v80h460v-80h-187l-3 -512h-5l-27 -58q37 -3 60.5 -22.5t23.5 -57.5z" />
    <glyph glyph-name="uni01EB.sc" horiz-adv-x="587" 
d="M278 -92q0 39 33 76h-17q-135 0 -194.5 75.5t-59.5 235.5t59.5 236t194.5 76t194 -76t59 -236q0 -229 -125 -287q-55 -37 -55 -80q0 -39 38 -39q10 0 22 3.5t19 6.5l6 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5zM129 295q0 -126 38 -181t127 -55q88 0 126 55
t38 181q0 127 -38 182.5t-126 55.5q-89 0 -127 -55.5t-38 -182.5z" />
    <glyph glyph-name="uni021B.sc" horiz-adv-x="473" 
d="M191 0l2 512h-186v80h460v-80h-187l-3 -512h-86zM151 -236q15 39 29 98q6 24 9.5 48t4.5 36v12h80q-4 -50 -21 -103q-8 -28 -18.5 -50.5t-16.5 -31.5l-6 -9h-61z" />
    <glyph glyph-name="uni0233.sc" horiz-adv-x="518" 
d="M131 777h253v-66h-253v66zM217 0v186l-209 406h99l153 -336l148 336h102l-206 -404v-188h-87z" />
    <glyph glyph-name="uni0259.sc" horiz-adv-x="604" 
d="M555 295q0 -160 -59 -235.5t-194 -75.5q-136 0 -194 77.5t-58 244.5h416q-2 116 -40 169.5t-124 53.5q-132 0 -156 -124h-89q14 103 73.5 152.5t171.5 49.5q135 0 194 -76t59 -236zM302 65q76 0 114.5 41.5t46.5 130.5h-321q8 -91 46 -131.5t114 -40.5z" />
    <glyph glyph-name="uni0272.sc" horiz-adv-x="610" 
d="M143 485l1 -485q0 -97 -34.5 -141.5t-137.5 -44.5h-43v67h43q55 0 74.5 25.5t19.5 93.5l2 592h94l305 -478l-5 478h83v-592h-94z" />
    <glyph glyph-name="uni0302.case" 
d="M-69 766h-90l-68 69l-68 -69h-87l106 121h97z" />
    <glyph glyph-name="uni0304.case" 
d="M-313 857h253v-71h-253v71z" />
    <glyph glyph-name="uni0306.case" 
d="M-54 874q0 -42 -36.5 -77t-110.5 -35t-110.5 35t-36.5 77h76q0 -20 20.5 -36.5t50.5 -16.5t51 16.5t21 36.5h75z" />
    <glyph glyph-name="uni0307.case" 
d="M-145 766v129h91v-129h-91z" />
    <glyph glyph-name="uni0308.case" 
d="M-313 766v129h91v-129h-91zM-139 766v129h90v-129h-90z" />
    <glyph glyph-name="uni030A.case" 
d="M-56 851q0 -44 -27 -69.5t-74 -25.5t-74 25.5t-27 69.5t27 69.5t74 25.5q46 0 73.5 -25.5t27.5 -69.5zM-195 851q0 -18 10.5 -28.5t27.5 -10.5t27.5 10.5t10.5 28.5q0 17 -10.5 27.5t-27.5 10.5t-27.5 -10.5t-10.5 -27.5z" />
    <glyph glyph-name="uni030B.case" 
d="M-386 767l72 121h95l-85 -121h-82zM-233 767l86 121h95l-100 -121h-81z" />
    <glyph glyph-name="uni030C.case" 
d="M-47 887l-110 -121h-97l-106 121h86l68 -69l68 69h91z" />
    <glyph glyph-name="uni0335.sc" horiz-adv-x="356" 
d="M45 304v64h267v-64h-267z" />
    <glyph glyph-name="uni0336.sc" horiz-adv-x="797" 
d="M97 300v71h597v-71h-597z" />
    <glyph glyph-name="uni0338.case" 
d="M-516 17l498 730h101l-498 -730h-101z" />
    <glyph glyph-name="uni0338.sc" horiz-adv-x="428" 
d="M-59 80l451 619h99l-451 -619h-99z" />
    <glyph glyph-name="uni1EA1.sc" horiz-adv-x="565" 
d="M8 0l227 592h96l226 -592h-96l-47 135h-266l-46 -135h-94zM174 210h215l-107 311zM237 -182v114h90v-114h-90z" />
    <glyph glyph-name="uni1EB9.sc" horiz-adv-x="482" 
d="M65 0v592h390v-76h-303v-172h261v-77h-261v-191h308v-76h-395zM217 -182v114h90v-114h-90z" />
    <glyph glyph-name="uni1EBD.sc" horiz-adv-x="482" 
d="M411 800q0 -115 -90 -115q-37 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM65 0v592h390v-76h-303v-172h261v-77h-261v-191h308v-76h-395z" />
    <glyph glyph-name="uni1ECB.sc" horiz-adv-x="217" 
d="M65 0v592h87v-592h-87zM62 -185v114h90v-114h-90z" />
    <glyph glyph-name="uni1ECD.sc" horiz-adv-x="587" 
d="M547 295q0 -160 -59 -235.5t-194 -75.5t-194.5 75.5t-59.5 235.5t59.5 236t194.5 76t194 -76t59 -236zM129 295q0 -126 38 -181t127 -55q88 0 126 55t38 181q0 127 -38 182.5t-126 55.5q-89 0 -127 -55.5t-38 -182.5zM249 -186v114h90v-114h-90z" />
    <glyph glyph-name="uni1EE5.sc" horiz-adv-x="577" 
d="M521 287q0 -63 -5 -106.5t-19.5 -83t-40 -63t-67.5 -37t-101 -13.5t-100.5 13.5t-67 37t-40 63t-19.5 83t-5 106.5v305h87v-307q0 -117 30 -168t115 -51t115 51t30 168v307h88v-305zM244 -188v114h90v-114h-90z" />
    <glyph glyph-name="uni1EF9.sc" horiz-adv-x="518" 
d="M411 800q0 -115 -90 -115q-37 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM217 0v186l-209 406h99l153 -336l148 336h102l-206 -404v-188h-87z" />
    <glyph glyph-name="uni2016.sc" horiz-adv-x="401" 
d="M77 -77v754h66v-754h-66zM257 -77v754h66v-754h-66z" />
    <glyph glyph-name="uni20BD.sc" horiz-adv-x="553" 
d="M98 0v139h-78v61h78v64h-78v66l78 -1v268h181q133 0 189 -36t56 -132q0 -95 -56 -130t-189 -35h-94v-64h151v-61h-151v-139h-87zM185 531v-202h104q72 0 109 20.5t37 80.5t-37 80.5t-109 20.5h-104z" />
    <glyph glyph-name="uogonek.sc" horiz-adv-x="577" 
d="M268 -95q0 43 36 79h-16q-59 0 -100.5 13.5t-67 37t-40 63t-19.5 83t-5 106.5v305h87v-307q0 -117 30 -168t115 -51t115 51t30 168v307h88v-305q0 -117 -22 -184t-84 -96q-58 -39 -58 -82q0 -39 38 -39q10 0 22 3t19 7l6 3v-54q-24 -20 -83 -20q-47 0 -69 22.5t-22 57.5z
" />
    <glyph glyph-name="uring.sc" horiz-adv-x="577" 
d="M390 765q0 -43 -27 -68t-73 -25t-72.5 25t-26.5 68t26.5 68.5t72.5 25.5t73 -25t27 -69zM252 765q0 -17 10.5 -27.5t27.5 -10.5t27.5 10.5t10.5 27.5q0 18 -10.5 28.5t-27.5 10.5t-27.5 -10.5t-10.5 -28.5zM521 287q0 -63 -5 -106.5t-19.5 -83t-40 -63t-67.5 -37
t-101 -13.5t-100.5 13.5t-67 37t-40 63t-19.5 83t-5 106.5v305h87v-307q0 -117 30 -168t115 -51t115 51t30 168v307h88v-305z" />
    <glyph glyph-name="utilde.sc" horiz-adv-x="577" 
d="M442 800q0 -115 -90 -115q-36 0 -67 24t-49 24q-22 0 -30.5 -15t-8.5 -31h-61q0 115 90 115q37 0 67.5 -24t49.5 -24q21 0 29.5 15t8.5 31h61zM521 287q0 -63 -5 -106.5t-19.5 -83t-40 -63t-67.5 -37t-101 -13.5t-100.5 13.5t-67 37t-40 63t-19.5 83t-5 106.5v305h87
v-307q0 -117 30 -168t115 -51t115 51t30 168v307h88v-305z" />
    <glyph glyph-name="v.sc" horiz-adv-x="530" 
d="M3 592h97l167 -523l166 523h94l-209 -592h-105z" />
    <glyph glyph-name="w.sc" horiz-adv-x="835" 
d="M418 504l-127 -504h-105l-174 592h99l130 -518l127 518h100l129 -515l131 515h94l-174 -592h-104z" />
    <glyph glyph-name="wacute.sc" horiz-adv-x="835" 
d="M378 685l77 144h95l-91 -144h-81zM418 504l-127 -504h-105l-174 592h99l130 -518l127 518h100l129 -515l131 515h94l-174 -592h-104z" />
    <glyph glyph-name="wcircumflex.sc" horiz-adv-x="835" 
d="M574 684h-86l-70 83l-70 -83h-85l111 143h87zM418 504l-127 -504h-105l-174 592h99l130 -518l127 518h100l129 -515l131 515h94l-174 -592h-104z" />
    <glyph glyph-name="wdieresis.sc" horiz-adv-x="835" 
d="M293 684v129h84v-129h-84zM461 684v129h85v-129h-85zM418 504l-127 -504h-105l-174 592h99l130 -518l127 518h100l129 -515l131 515h94l-174 -592h-104z" />
    <glyph glyph-name="wgrave.sc" horiz-adv-x="835" 
d="M459 685h-80l-92 144h95zM418 504l-127 -504h-105l-174 592h99l130 -518l127 518h100l129 -515l131 515h94l-174 -592h-104z" />
    <glyph glyph-name="x.sc" horiz-adv-x="538" 
d="M266 239l-157 -239h-99l202 293l-201 299h97l160 -243l160 243h99l-207 -297l199 -295h-96z" />
    <glyph glyph-name="y.sc" horiz-adv-x="518" 
d="M217 0v186l-209 406h99l153 -336l148 336h102l-206 -404v-188h-87z" />
    <glyph glyph-name="yacute.sc" horiz-adv-x="518" 
d="M217 685l77 144h95l-91 -144h-81zM217 0v186l-209 406h99l153 -336l148 336h102l-206 -404v-188h-87z" />
    <glyph glyph-name="ycircumflex.sc" horiz-adv-x="518" 
d="M413 684h-86l-70 83l-70 -83h-85l111 143h87zM217 0v186l-209 406h99l153 -336l148 336h102l-206 -404v-188h-87z" />
    <glyph glyph-name="ydieresis.sc" horiz-adv-x="518" 
d="M132 684v129h84v-129h-84zM300 684v129h85v-129h-85zM217 0v186l-209 406h99l153 -336l148 336h102l-206 -404v-188h-87z" />
    <glyph glyph-name="yen.sc" horiz-adv-x="495" 
d="M207 0v144h-95v58h95v43h-95v59h73l-177 289h97l143 -264l137 264h102l-175 -289h71v-59h-93v-43h93v-58h-93v-144h-83z" />
    <glyph glyph-name="ygrave.sc" horiz-adv-x="518" 
d="M298 685h-80l-92 144h95zM217 0v186l-209 406h99l153 -336l148 336h102l-206 -404v-188h-87z" />
    <glyph glyph-name="z.sc" horiz-adv-x="468" 
d="M16 2v74l329 439h-315v74h416v-74l-332 -439h335v-74h-433z" />
    <glyph glyph-name="zacute.sc" horiz-adv-x="468" 
d="M197 685l77 144h95l-91 -144h-81zM16 2v74l329 439h-315v74h416v-74l-332 -439h335v-74h-433z" />
    <glyph glyph-name="zcaron.sc" horiz-adv-x="468" 
d="M393 827l-113 -143h-87l-109 143h83l69 -81l71 81h86zM16 2v74l329 439h-315v74h416v-74l-332 -439h335v-74h-433z" />
    <glyph glyph-name="zdotaccent.sc" horiz-adv-x="468" 
d="M193 691v122h91v-122h-91zM16 2v74l329 439h-315v74h416v-74l-332 -439h335v-74h-433z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="500" 
d="M449 257q0 -144 -46 -205t-153 -61q-108 0 -154 61t-46 205t46 205t154 61q107 0 153 -61t46 -205zM126 257q0 -110 29 -154t95 -44t94.5 43.5t28.5 154.5t-28.5 155t-94.5 44t-95 -44t-29 -155z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="500" 
d="M449 502q0 -144 -46 -205t-153 -61q-108 0 -154 61t-46 205t46 205t154 61q107 0 153 -61t46 -205zM126 502q0 -110 29 -154t95 -44t94.5 43.5t28.5 154.5t-28.5 155t-94.5 44t-95 -44t-29 -155z" />
    <glyph glyph-name="zero.osf" horiz-adv-x="601" 
d="M551 277q0 -147 -55.5 -218.5t-195.5 -71.5q-139 0 -194.5 71.5t-55.5 218.5q0 148 55.5 220t194.5 73q140 1 195.5 -72t55.5 -221zM142 277q0 -114 34.5 -164t123.5 -50q90 0 124.5 50t34.5 164q0 115 -35 166.5t-124 51.5t-123.5 -51.5t-34.5 -166.5z" />
    <glyph glyph-name="zero.sc" horiz-adv-x="539" 
d="M497 292q0 -163 -52.5 -232t-174.5 -69t-175.5 69t-53.5 232t53.5 233t175.5 70q121 0 174 -70t53 -233zM129 292q0 -126 33 -175t108 -49q74 0 107 49.5t33 174.5q0 126 -33 176t-107 50q-75 0 -108 -50t-33 -176z" />
    <glyph glyph-name="zero.subs" horiz-adv-x="500" 
d="M449 63q0 -144 -46 -205t-153 -61q-108 0 -154 61t-46 205t46 205t154 61q107 0 153 -61t46 -205zM126 63q0 -110 29 -154t95 -44t94.5 43.5t28.5 154.5t-28.5 155t-94.5 44t-95 -44t-29 -155z" />
    <glyph glyph-name="zero.tf" horiz-adv-x="660" 
d="M594 352q0 -195 -62 -279t-205 -84t-205 84t-62 279t62 279t205 84t205 -84t62 -279zM153 352q0 -156 41 -218t133 -62q93 0 134 62t41 218t-41 218.5t-134 62.5q-92 0 -133 -62.5t-41 -218.5z" />
    <hkern u1="&#x2f;" u2="&#x129;" k="-25" />
    <hkern u1="A" u2="Y" k="61" />
    <hkern u1="C" u2="&#x129;" k="-13" />
    <hkern u1="D" u2="V" k="5" />
    <hkern u1="D" u2="A" k="11" />
    <hkern u1="E" u2="&#x129;" k="-22" />
    <hkern u1="F" u2="&#x129;" k="-36" />
    <hkern u1="F" u2="&#xee;" k="-29" />
    <hkern u1="F" u2="&#xec;" k="-10" />
    <hkern u1="G" u2="&#x129;" k="-6" />
    <hkern u1="H" u2="&#x129;" k="-16" />
    <hkern u1="I" u2="&#x129;" k="-16" />
    <hkern u1="J" u2="&#x129;" k="-13" />
    <hkern u1="K" u2="&#x12b;" k="-16" />
    <hkern u1="K" u2="&#x129;" k="-19" />
    <hkern u1="L" u2="&#x201d;" k="13" />
    <hkern u1="L" u2="&#x22;" k="19" />
    <hkern u1="M" u2="&#x129;" k="-16" />
    <hkern u1="N" u2="&#x129;" k="-16" />
    <hkern u1="O" u2="V" k="5" />
    <hkern u1="O" u2="A" k="11" />
    <hkern u1="P" u2="&#x129;" k="-13" />
    <hkern u1="P" u2="&#xef;" k="-25" />
    <hkern u1="P" u2="&#xee;" k="-22" />
    <hkern u1="Q" u2="V" k="5" />
    <hkern u1="Q" u2="A" k="11" />
    <hkern u1="R" u2="&#x129;" k="-10" />
    <hkern u1="S" u2="&#x129;" k="-6" />
    <hkern u1="T" u2="&#x12b;" k="-22" />
    <hkern u1="T" u2="&#x129;" k="-46" />
    <hkern u1="T" u2="y" k="41" />
    <hkern u1="T" u2="o" k="68" />
    <hkern u1="U" u2="&#x129;" k="-16" />
    <hkern u1="V" u2="&#x1ecc;" k="5" />
    <hkern u1="V" u2="&#x1fe;" k="5" />
    <hkern u1="V" u2="&#x1ea;" k="5" />
    <hkern u1="V" u2="&#x1e6;" k="5" />
    <hkern u1="V" u2="&#x18f;" k="5" />
    <hkern u1="V" u2="&#x152;" k="5" />
    <hkern u1="V" u2="&#x150;" k="5" />
    <hkern u1="V" u2="&#x14e;" k="5" />
    <hkern u1="V" u2="&#x14c;" k="5" />
    <hkern u1="V" u2="&#x12b;" k="-16" />
    <hkern u1="V" u2="&#x129;" k="-46" />
    <hkern u1="V" u2="&#x122;" k="5" />
    <hkern u1="V" u2="&#x120;" k="5" />
    <hkern u1="V" u2="&#x11e;" k="5" />
    <hkern u1="V" u2="&#x11c;" k="5" />
    <hkern u1="V" u2="&#x10c;" k="5" />
    <hkern u1="V" u2="&#x10a;" k="5" />
    <hkern u1="V" u2="&#x108;" k="5" />
    <hkern u1="V" u2="&#x106;" k="5" />
    <hkern u1="V" u2="&#x103;" k="19" />
    <hkern u1="V" u2="&#xf6;" k="19" />
    <hkern u1="V" u2="&#xee;" k="-19" />
    <hkern u1="V" u2="&#xeb;" k="19" />
    <hkern u1="V" u2="&#xe4;" k="13" />
    <hkern u1="V" u2="&#xe3;" k="19" />
    <hkern u1="V" u2="&#xd8;" k="5" />
    <hkern u1="V" u2="&#xd6;" k="5" />
    <hkern u1="V" u2="&#xd5;" k="5" />
    <hkern u1="V" u2="&#xd4;" k="5" />
    <hkern u1="V" u2="&#xd3;" k="5" />
    <hkern u1="V" u2="&#xd2;" k="5" />
    <hkern u1="V" u2="&#xc7;" k="5" />
    <hkern u1="V" u2="Q" k="5" />
    <hkern u1="V" u2="O" k="10" />
    <hkern u1="V" u2="G" k="5" />
    <hkern u1="V" u2="C" k="5" />
    <hkern u1="W" u2="&#x129;" k="-29" />
    <hkern u1="W" u2="&#xf6;" k="19" />
    <hkern u1="W" u2="&#xe4;" k="13" />
    <hkern u1="X" u2="&#x129;" k="-19" />
    <hkern u1="X" u2="O" k="17" />
    <hkern u1="Y" u2="&#x129;" k="-42" />
    <hkern u1="Y" u2="&#xe3;" k="22" />
    <hkern u1="Y" u2="O" k="19" />
    <hkern u1="Z" u2="&#x129;" k="-25" />
    <hkern u1="f" u2="&#xee;" k="-22" />
    <hkern u1="f" u2="&#xec;" k="-33" />
    <hkern u1="i" u2="j" k="-6" />
    <hkern u1="o" u2="y" k="10" />
    <hkern u1="o" u2="w" k="10" />
    <hkern u1="v" u2="o" k="10" />
    <hkern u1="v" u2="e" k="10" />
    <hkern u1="w" u2="o" k="10" />
    <hkern u1="w" u2="a" k="10" />
    <hkern u1="x" u2="o" k="18" />
    <hkern u1="y" u2="o" k="10" />
    <hkern u1="&#xc6;" u2="&#x129;" k="-22" />
    <hkern u1="&#xc7;" u2="&#x129;" k="-13" />
    <hkern u1="&#xc8;" u2="&#x129;" k="-22" />
    <hkern u1="&#xc9;" u2="&#x129;" k="-22" />
    <hkern u1="&#xca;" u2="&#x129;" k="-22" />
    <hkern u1="&#xcb;" u2="&#x129;" k="-22" />
    <hkern u1="&#xcc;" u2="&#x129;" k="-16" />
    <hkern u1="&#xcd;" u2="&#x129;" k="-16" />
    <hkern u1="&#xce;" u2="&#x129;" k="-16" />
    <hkern u1="&#xcf;" u2="&#x129;" k="-16" />
    <hkern u1="&#xd0;" u2="V" k="5" />
    <hkern u1="&#xd0;" u2="A" k="11" />
    <hkern u1="&#xd1;" u2="&#x129;" k="-16" />
    <hkern u1="&#xd2;" u2="V" k="5" />
    <hkern u1="&#xd2;" u2="A" k="11" />
    <hkern u1="&#xd3;" u2="V" k="5" />
    <hkern u1="&#xd3;" u2="A" k="11" />
    <hkern u1="&#xd4;" u2="V" k="5" />
    <hkern u1="&#xd4;" u2="A" k="11" />
    <hkern u1="&#xd5;" u2="V" k="5" />
    <hkern u1="&#xd5;" u2="A" k="11" />
    <hkern u1="&#xd6;" u2="V" k="5" />
    <hkern u1="&#xd6;" u2="A" k="11" />
    <hkern u1="&#xd8;" u2="V" k="5" />
    <hkern u1="&#xd8;" u2="A" k="11" />
    <hkern u1="&#xd9;" u2="&#x129;" k="-16" />
    <hkern u1="&#xda;" u2="&#x129;" k="-16" />
    <hkern u1="&#xdb;" u2="&#x129;" k="-16" />
    <hkern u1="&#xdc;" u2="&#x129;" k="-16" />
    <hkern u1="&#xdd;" u2="&#x129;" k="-42" />
    <hkern u1="&#xdd;" u2="&#xe3;" k="35" />
    <hkern u1="&#xdd;" u2="O" k="19" />
    <hkern u1="&#xec;" u2="j" k="-6" />
    <hkern u1="&#xed;" u2="&#x17e;" k="-10" />
    <hkern u1="&#xed;" u2="&#x161;" k="-6" />
    <hkern u1="&#xed;" u2="&#x159;" k="-19" />
    <hkern u1="&#xed;" u2="j" k="-6" />
    <hkern u1="&#xee;" u2="t" k="-13" />
    <hkern u1="&#xee;" u2="j" k="-6" />
    <hkern u1="&#xef;" u2="j" k="-6" />
    <hkern u1="&#xef;" u2="f" k="-13" />
    <hkern u1="&#x105;" u2="&#x237;" k="-19" />
    <hkern u1="&#x105;" u2="&#x135;" k="-19" />
    <hkern u1="&#x105;" u2="j" k="-19" />
    <hkern u1="&#x106;" u2="&#x129;" k="-13" />
    <hkern u1="&#x108;" u2="&#x129;" k="-13" />
    <hkern u1="&#x10a;" u2="&#x129;" k="-13" />
    <hkern u1="&#x10c;" u2="&#x129;" k="-13" />
    <hkern u1="&#x10e;" u2="V" k="5" />
    <hkern u1="&#x10e;" u2="A" k="11" />
    <hkern u1="&#x10f;" u2="&#x201d;" k="-57" />
    <hkern u1="&#x10f;" u2="&#x201c;" k="-48" />
    <hkern u1="&#x10f;" u2="&#x7d;" k="-73" />
    <hkern u1="&#x10f;" u2="u" k="-32" />
    <hkern u1="&#x10f;" u2="o" k="-10" />
    <hkern u1="&#x10f;" u2="k" k="-64" />
    <hkern u1="&#x10f;" u2="i" k="-64" />
    <hkern u1="&#x10f;" u2="]" k="-76" />
    <hkern u1="&#x10f;" u2="U" k="-57" />
    <hkern u1="&#x10f;" u2="L" k="-64" />
    <hkern u1="&#x10f;" u2="I" k="-54" />
    <hkern u1="&#x10f;" u2="&#x3f;" k="-45" />
    <hkern u1="&#x10f;" u2="&#x2a;" k="-35" />
    <hkern u1="&#x10f;" u2="&#x29;" k="-70" />
    <hkern u1="&#x10f;" u2="&#x27;" k="-73" />
    <hkern u1="&#x10f;" u2="&#x22;" k="-70" />
    <hkern u1="&#x10f;" u2="&#x21;" k="-60" />
    <hkern u1="&#x110;" u2="V" k="5" />
    <hkern u1="&#x110;" u2="A" k="11" />
    <hkern u1="&#x112;" u2="&#x129;" k="-22" />
    <hkern u1="&#x114;" u2="&#x129;" k="-22" />
    <hkern u1="&#x116;" u2="&#x129;" k="-22" />
    <hkern u1="&#x118;" u2="&#x129;" k="-22" />
    <hkern u1="&#x11a;" u2="&#x129;" k="-22" />
    <hkern u1="&#x11c;" u2="&#x129;" k="-6" />
    <hkern u1="&#x11e;" u2="&#x129;" k="-6" />
    <hkern u1="&#x120;" u2="&#x129;" k="-6" />
    <hkern u1="&#x122;" u2="&#x129;" k="-6" />
    <hkern u1="&#x124;" u2="&#x129;" k="-16" />
    <hkern u1="&#x126;" u2="&#x129;" k="-16" />
    <hkern u1="&#x128;" u2="&#x129;" k="-16" />
    <hkern u1="&#x129;" u2="&#x1ef8;" k="-36" />
    <hkern u1="&#x129;" u2="&#x1ef2;" k="-36" />
    <hkern u1="&#x129;" u2="&#x1ee4;" k="-10" />
    <hkern u1="&#x129;" u2="&#x1eca;" k="-10" />
    <hkern u1="&#x129;" u2="&#x1ebc;" k="-10" />
    <hkern u1="&#x129;" u2="&#x1eb8;" k="-10" />
    <hkern u1="&#x129;" u2="&#x1e84;" k="-29" />
    <hkern u1="&#x129;" u2="&#x1e82;" k="-29" />
    <hkern u1="&#x129;" u2="&#x1e80;" k="-29" />
    <hkern u1="&#x129;" u2="&#x232;" k="-36" />
    <hkern u1="&#x129;" u2="&#x21a;" k="-46" />
    <hkern u1="&#x129;" u2="&#x17d;" k="-16" />
    <hkern u1="&#x129;" u2="&#x17b;" k="-16" />
    <hkern u1="&#x129;" u2="&#x179;" k="-16" />
    <hkern u1="&#x129;" u2="&#x178;" k="-36" />
    <hkern u1="&#x129;" u2="&#x176;" k="-36" />
    <hkern u1="&#x129;" u2="&#x174;" k="-29" />
    <hkern u1="&#x129;" u2="&#x172;" k="-10" />
    <hkern u1="&#x129;" u2="&#x170;" k="-10" />
    <hkern u1="&#x129;" u2="&#x16e;" k="-10" />
    <hkern u1="&#x129;" u2="&#x16c;" k="-10" />
    <hkern u1="&#x129;" u2="&#x16a;" k="-10" />
    <hkern u1="&#x129;" u2="&#x168;" k="-10" />
    <hkern u1="&#x129;" u2="&#x166;" k="-46" />
    <hkern u1="&#x129;" u2="&#x164;" k="-46" />
    <hkern u1="&#x129;" u2="&#x162;" k="-46" />
    <hkern u1="&#x129;" u2="&#x158;" k="-10" />
    <hkern u1="&#x129;" u2="&#x156;" k="-10" />
    <hkern u1="&#x129;" u2="&#x154;" k="-10" />
    <hkern u1="&#x129;" u2="&#x14a;" k="-10" />
    <hkern u1="&#x129;" u2="&#x147;" k="-10" />
    <hkern u1="&#x129;" u2="&#x145;" k="-10" />
    <hkern u1="&#x129;" u2="&#x143;" k="-10" />
    <hkern u1="&#x129;" u2="&#x141;" k="-10" />
    <hkern u1="&#x129;" u2="&#x13f;" k="-10" />
    <hkern u1="&#x129;" u2="&#x13d;" k="-10" />
    <hkern u1="&#x129;" u2="&#x13b;" k="-10" />
    <hkern u1="&#x129;" u2="&#x139;" k="-10" />
    <hkern u1="&#x129;" u2="&#x136;" k="-10" />
    <hkern u1="&#x129;" u2="&#x132;" k="-10" />
    <hkern u1="&#x129;" u2="&#x130;" k="-10" />
    <hkern u1="&#x129;" u2="&#x12e;" k="-10" />
    <hkern u1="&#x129;" u2="&#x12c;" k="-10" />
    <hkern u1="&#x129;" u2="&#x12a;" k="-10" />
    <hkern u1="&#x129;" u2="&#x128;" k="-10" />
    <hkern u1="&#x129;" u2="&#x126;" k="-10" />
    <hkern u1="&#x129;" u2="&#x124;" k="-10" />
    <hkern u1="&#x129;" u2="&#x11a;" k="-10" />
    <hkern u1="&#x129;" u2="&#x118;" k="-10" />
    <hkern u1="&#x129;" u2="&#x116;" k="-10" />
    <hkern u1="&#x129;" u2="&#x114;" k="-10" />
    <hkern u1="&#x129;" u2="&#x112;" k="-10" />
    <hkern u1="&#x129;" u2="&#x110;" k="-10" />
    <hkern u1="&#x129;" u2="&#x10e;" k="-10" />
    <hkern u1="&#x129;" u2="&#xde;" k="-10" />
    <hkern u1="&#x129;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x129;" u2="&#xdc;" k="-10" />
    <hkern u1="&#x129;" u2="&#xdb;" k="-10" />
    <hkern u1="&#x129;" u2="&#xda;" k="-10" />
    <hkern u1="&#x129;" u2="&#xd9;" k="-10" />
    <hkern u1="&#x129;" u2="&#xd1;" k="-10" />
    <hkern u1="&#x129;" u2="&#xd0;" k="-10" />
    <hkern u1="&#x129;" u2="&#xcf;" k="-10" />
    <hkern u1="&#x129;" u2="&#xce;" k="-10" />
    <hkern u1="&#x129;" u2="&#xcd;" k="-10" />
    <hkern u1="&#x129;" u2="&#xcc;" k="-10" />
    <hkern u1="&#x129;" u2="&#xcb;" k="-10" />
    <hkern u1="&#x129;" u2="&#xca;" k="-10" />
    <hkern u1="&#x129;" u2="&#xc9;" k="-10" />
    <hkern u1="&#x129;" u2="&#xc8;" k="-10" />
    <hkern u1="&#x129;" u2="j" k="-6" />
    <hkern u1="&#x129;" u2="Z" k="-16" />
    <hkern u1="&#x129;" u2="Y" k="-36" />
    <hkern u1="&#x129;" u2="X" k="-25" />
    <hkern u1="&#x129;" u2="W" k="-29" />
    <hkern u1="&#x129;" u2="V" k="-17" />
    <hkern u1="&#x129;" u2="U" k="-10" />
    <hkern u1="&#x129;" u2="T" k="-46" />
    <hkern u1="&#x129;" u2="R" k="-10" />
    <hkern u1="&#x129;" u2="P" k="-10" />
    <hkern u1="&#x129;" u2="N" k="-10" />
    <hkern u1="&#x129;" u2="M" k="-10" />
    <hkern u1="&#x129;" u2="L" k="-10" />
    <hkern u1="&#x129;" u2="K" k="-10" />
    <hkern u1="&#x129;" u2="I" k="-10" />
    <hkern u1="&#x129;" u2="H" k="-10" />
    <hkern u1="&#x129;" u2="F" k="-10" />
    <hkern u1="&#x129;" u2="E" k="-10" />
    <hkern u1="&#x129;" u2="D" k="-10" />
    <hkern u1="&#x129;" u2="B" k="-10" />
    <hkern u1="&#x12a;" u2="&#x129;" k="-16" />
    <hkern u1="&#x12b;" u2="j" k="-6" />
    <hkern u1="&#x12c;" u2="&#x129;" k="-16" />
    <hkern u1="&#x12d;" u2="j" k="-6" />
    <hkern u1="&#x12e;" u2="&#x129;" k="-16" />
    <hkern u1="&#x12f;" u2="j" k="-6" />
    <hkern u1="&#x130;" u2="&#x129;" k="-16" />
    <hkern u1="&#x131;" u2="j" k="-6" />
    <hkern u1="&#x132;" u2="&#x129;" k="-13" />
    <hkern u1="&#x134;" u2="&#x129;" k="-13" />
    <hkern u1="&#x136;" u2="&#x129;" k="-19" />
    <hkern u1="&#x139;" u2="&#x201d;" k="13" />
    <hkern u1="&#x139;" u2="&#x22;" k="19" />
    <hkern u1="&#x13b;" u2="&#x201d;" k="13" />
    <hkern u1="&#x13b;" u2="&#x22;" k="19" />
    <hkern u1="&#x13d;" u2="&#x201d;" k="-6" />
    <hkern u1="&#x13d;" u2="&#x201c;" k="3" />
    <hkern u1="&#x13d;" u2="&#x2018;" k="13" />
    <hkern u1="&#x13d;" u2="&#x7d;" k="-22" />
    <hkern u1="&#x13d;" u2="k" k="-16" />
    <hkern u1="&#x13d;" u2="i" k="-16" />
    <hkern u1="&#x13d;" u2="]" k="-22" />
    <hkern u1="&#x13d;" u2="U" k="-6" />
    <hkern u1="&#x13d;" u2="I" k="-6" />
    <hkern u1="&#x13d;" u2="&#x3f;" k="-3" />
    <hkern u1="&#x13d;" u2="&#x29;" k="-16" />
    <hkern u1="&#x13d;" u2="&#x27;" k="-10" />
    <hkern u1="&#x13d;" u2="&#x22;" k="-16" />
    <hkern u1="&#x13d;" u2="&#x21;" k="-10" />
    <hkern u1="&#x13f;" u2="&#x201d;" k="13" />
    <hkern u1="&#x13f;" u2="&#x22;" k="19" />
    <hkern u1="&#x141;" u2="&#x201d;" k="13" />
    <hkern u1="&#x141;" u2="&#x22;" k="19" />
    <hkern u1="&#x143;" u2="&#x129;" k="-16" />
    <hkern u1="&#x145;" u2="&#x129;" k="-16" />
    <hkern u1="&#x147;" u2="&#x129;" k="-16" />
    <hkern u1="&#x14a;" u2="&#x129;" k="-16" />
    <hkern u1="&#x14c;" u2="V" k="5" />
    <hkern u1="&#x14c;" u2="A" k="11" />
    <hkern u1="&#x14e;" u2="V" k="5" />
    <hkern u1="&#x14e;" u2="A" k="11" />
    <hkern u1="&#x150;" u2="V" k="5" />
    <hkern u1="&#x150;" u2="A" k="11" />
    <hkern u1="&#x152;" u2="&#x129;" k="-22" />
    <hkern u1="&#x154;" u2="&#x129;" k="-10" />
    <hkern u1="&#x156;" u2="&#x129;" k="-10" />
    <hkern u1="&#x158;" u2="&#x129;" k="-10" />
    <hkern u1="&#x15a;" u2="&#x129;" k="-6" />
    <hkern u1="&#x15c;" u2="&#x129;" k="-6" />
    <hkern u1="&#x15e;" u2="&#x129;" k="-6" />
    <hkern u1="&#x160;" u2="&#x129;" k="-6" />
    <hkern u1="&#x162;" u2="&#x129;" k="-46" />
    <hkern u1="&#x164;" u2="&#x129;" k="-46" />
    <hkern u1="&#x165;" u2="&#x201d;" k="-22" />
    <hkern u1="&#x165;" u2="&#x201c;" k="-13" />
    <hkern u1="&#x165;" u2="&#x7d;" k="-41" />
    <hkern u1="&#x165;" u2="k" k="-32" />
    <hkern u1="&#x165;" u2="i" k="-38" />
    <hkern u1="&#x165;" u2="]" k="-41" />
    <hkern u1="&#x165;" u2="U" k="-19" />
    <hkern u1="&#x165;" u2="I" k="-32" />
    <hkern u1="&#x165;" u2="&#x3f;" k="-16" />
    <hkern u1="&#x165;" u2="&#x29;" k="-35" />
    <hkern u1="&#x165;" u2="&#x27;" k="-41" />
    <hkern u1="&#x165;" u2="&#x22;" k="-41" />
    <hkern u1="&#x165;" u2="&#x21;" k="-28" />
    <hkern u1="&#x166;" u2="&#x129;" k="-46" />
    <hkern u1="&#x168;" u2="&#x129;" k="-16" />
    <hkern u1="&#x16a;" u2="&#x129;" k="-16" />
    <hkern u1="&#x16c;" u2="&#x129;" k="-16" />
    <hkern u1="&#x16e;" u2="&#x129;" k="-16" />
    <hkern u1="&#x170;" u2="&#x129;" k="-16" />
    <hkern u1="&#x172;" u2="&#x129;" k="-16" />
    <hkern u1="&#x173;" u2="j" k="-22" />
    <hkern u1="&#x174;" u2="&#x129;" k="-29" />
    <hkern u1="&#x174;" u2="&#xf6;" k="25" />
    <hkern u1="&#x174;" u2="&#xe4;" k="25" />
    <hkern u1="&#x175;" u2="a" k="10" />
    <hkern u1="&#x176;" u2="&#x129;" k="-42" />
    <hkern u1="&#x176;" u2="&#xe3;" k="35" />
    <hkern u1="&#x176;" u2="O" k="19" />
    <hkern u1="&#x178;" u2="&#x129;" k="-42" />
    <hkern u1="&#x178;" u2="&#xe3;" k="35" />
    <hkern u1="&#x178;" u2="O" k="19" />
    <hkern u1="&#x179;" u2="&#x129;" k="-25" />
    <hkern u1="&#x17b;" u2="&#x129;" k="-25" />
    <hkern u1="&#x17d;" u2="&#x129;" k="-25" />
    <hkern u1="&#x18f;" u2="V" k="5" />
    <hkern u1="&#x18f;" u2="A" k="11" />
    <hkern u1="&#x19d;" u2="&#x129;" k="-16" />
    <hkern u1="&#x1e6;" u2="&#x129;" k="-6" />
    <hkern u1="&#x1ea;" u2="V" k="5" />
    <hkern u1="&#x1ea;" u2="A" k="11" />
    <hkern u1="&#x1fc;" u2="&#x129;" k="-22" />
    <hkern u1="&#x1fe;" u2="V" k="5" />
    <hkern u1="&#x1fe;" u2="A" k="11" />
    <hkern u1="&#x218;" u2="&#x129;" k="-6" />
    <hkern u1="&#x21a;" u2="&#x129;" k="-46" />
    <hkern u1="&#x232;" u2="&#x129;" k="-42" />
    <hkern u1="&#x232;" u2="&#xe3;" k="35" />
    <hkern u1="&#x232;" u2="O" k="19" />
    <hkern u1="&#x1e80;" u2="&#x129;" k="-29" />
    <hkern u1="&#x1e80;" u2="&#xf6;" k="25" />
    <hkern u1="&#x1e80;" u2="&#xe4;" k="25" />
    <hkern u1="&#x1e81;" u2="a" k="10" />
    <hkern u1="&#x1e82;" u2="&#x129;" k="-29" />
    <hkern u1="&#x1e82;" u2="&#xf6;" k="25" />
    <hkern u1="&#x1e82;" u2="&#xe4;" k="25" />
    <hkern u1="&#x1e83;" u2="a" k="10" />
    <hkern u1="&#x1e84;" u2="&#x129;" k="-29" />
    <hkern u1="&#x1e84;" u2="&#xf6;" k="25" />
    <hkern u1="&#x1e84;" u2="&#xe4;" k="25" />
    <hkern u1="&#x1e85;" u2="a" k="10" />
    <hkern u1="&#x1e9e;" u2="&#xc6;" k="13" />
    <hkern u1="&#x1eb8;" u2="&#x129;" k="-22" />
    <hkern u1="&#x1ebc;" u2="&#x129;" k="-22" />
    <hkern u1="&#x1eca;" u2="&#x129;" k="-16" />
    <hkern u1="&#x1ecb;" u2="j" k="-6" />
    <hkern u1="&#x1ecc;" u2="V" k="5" />
    <hkern u1="&#x1ecc;" u2="A" k="11" />
    <hkern u1="&#x1ee4;" u2="&#x129;" k="-16" />
    <hkern u1="&#x1ef2;" u2="&#x129;" k="-42" />
    <hkern u1="&#x1ef2;" u2="&#xe3;" k="35" />
    <hkern u1="&#x1ef2;" u2="O" k="19" />
    <hkern u1="&#x1ef8;" u2="&#x129;" k="-42" />
    <hkern u1="&#x1ef8;" u2="&#xe3;" k="35" />
    <hkern u1="&#x1ef8;" u2="O" k="19" />
    <hkern u1="&#x2019;" u2="&#x129;" k="-29" />
    <hkern g1="f_f" u2="&#xec;" k="-33" />
    <hkern g1="i.loclTRK" u2="j" k="-6" />
    <hkern g1="slash.sc" u2="&#x129;" k="-25" />
    <hkern g1="asciitilde"
	g2="six"
	k="-20" />
    <hkern g1="asciitilde"
	g2="zero"
	k="33" />
    <hkern g1="divide"
	g2="eight"
	k="-7" />
    <hkern g1="divide"
	g2="five"
	k="30" />
    <hkern g1="equal"
	g2="two"
	k="-7" />
    <hkern g1="equal"
	g2="quotedbl,quotesingle"
	k="33" />
    <hkern g1="equal"
	g2="eight"
	k="14" />
    <hkern g1="equal"
	g2="five"
	k="27" />
    <hkern g1="greater"
	g2="seven"
	k="-14" />
    <hkern g1="greater"
	g2="one"
	k="44" />
    <hkern g1="greaterequal"
	g2="zero"
	k="-14" />
    <hkern g1="less"
	g2="five"
	k="-17" />
    <hkern g1="less"
	g2="nine"
	k="40" />
    <hkern g1="lessequal"
	g2="quotedbl,quotesingle"
	k="-14" />
    <hkern g1="lessequal"
	g2="quoteleft,quotedblleft"
	k="44" />
    <hkern g1="lessequal"
	g2="eight"
	k="-20" />
    <hkern g1="minus"
	g2="one"
	k="-7" />
    <hkern g1="minus"
	g2="three"
	k="36" />
    <hkern g1="multiply"
	g2="two"
	k="13" />
    <hkern g1="multiply"
	g2="quotedbl,quotesingle"
	k="23" />
    <hkern g1="notequal"
	g2="nine"
	k="-14" />
    <hkern g1="notequal"
	g2="six"
	k="44" />
    <hkern g1="notequal"
	g2="zero"
	k="-14" />
    <hkern g1="percent,perthousand,percent.sc,perthousand.sc"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="plus"
	g2="three"
	k="40" />
    <hkern g1="plus"
	g2="eight"
	k="16" />
    <hkern g1="plus"
	g2="six"
	k="33" />
    <hkern g1="plus"
	g2="zero"
	k="13" />
    <hkern g1="plusminus"
	g2="three"
	k="-145" />
    <hkern g1="plusminus"
	g2="eight"
	k="21584" />
    <hkern g1="plusminus"
	g2="five"
	k="-513" />
    <hkern g1="plusminus"
	g2="zero"
	k="-14672" />
    <hkern g1="sterling,sterling.sc"
	g2="four"
	k="-513" />
    <hkern g1="sterling,sterling.sc"
	g2="three"
	k="-14736" />
    <hkern g1="sterling,sterling.sc"
	g2="two"
	k="-513" />
    <hkern g1="sterling,sterling.sc"
	g2="eight"
	k="-14768" />
    <hkern g1="sterling,sterling.sc"
	g2="five"
	k="-513" />
    <hkern g1="sterling,sterling.sc"
	g2="zero"
	k="-14800" />
    <hkern g1="yen,yen.sc"
	g2="four"
	k="-513" />
    <hkern g1="yen,yen.sc"
	g2="three"
	k="-14864" />
    <hkern g1="yen,yen.sc"
	g2="two"
	k="-513" />
    <hkern g1="yen,yen.sc"
	g2="eight"
	k="-14896" />
    <hkern g1="yen,yen.sc"
	g2="five"
	k="-513" />
    <hkern g1="yen,yen.sc"
	g2="zero"
	k="29632" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="two"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="guillemotright,guilsinglright"
	k="-9" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="quotedbl,quotesingle"
	k="23" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="comma,period,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="sterling,sterling.sc"
	k="14" />
    <hkern g1="guillemotright,guilsinglright"
	g2="four"
	k="20" />
    <hkern g1="guillemotright,guilsinglright"
	g2="question"
	k="14" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="nine"
	k="16" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="guillemotright,guilsinglright"
	k="23" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="quotedbl,quotesingle"
	k="6" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="quoteleft,quotedblleft"
	k="6" />
    <hkern g1="hyphen,endash,emdash"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="numbersign"
	g2="one"
	k="14" />
    <hkern g1="parenleft,parenleft.sc"
	g2="one"
	k="23" />
    <hkern g1="parenleft,parenleft.sc"
	g2="six"
	k="10" />
    <hkern g1="parenleft,parenleft.sc"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="comma,period,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="questiondown,questiondown.sc"
	k="-27" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="quotesinglbase"
	k="7" />
    <hkern g1="question"
	g2="four"
	k="20" />
    <hkern g1="question"
	g2="zero"
	k="-27" />
    <hkern g1="question"
	g2="parenright,parenright.sc"
	k="7" />
    <hkern g1="question"
	g2="one"
	k="14" />
    <hkern g1="question"
	g2="nine"
	k="20" />
    <hkern g1="questiondown,questiondown.sc"
	g2="comma,period,quotedblbase,ellipsis"
	k="-9" />
    <hkern g1="questiondown.case"
	g2="four"
	k="-9" />
    <hkern g1="questiondown.case"
	g2="guillemotright,guilsinglright"
	k="-20" />
    <hkern g1="questiondown.case"
	g2="quotedbl,quotesingle"
	k="19" />
    <hkern g1="questiondown.case"
	g2="quoteleft,quotedblleft"
	k="-20" />
    <hkern g1="questiondown.case"
	g2="guillemotleft,guilsinglleft"
	k="3" />
    <hkern g1="questiondown.case"
	g2="five"
	k="100" />
    <hkern g1="questiondown.case"
	g2="hyphen,endash,emdash"
	k="13" />
    <hkern g1="questiondown.case"
	g2="quotesinglbase"
	k="-34" />
    <hkern g1="quotedbl,quotesingle"
	g2="seven"
	k="100" />
    <hkern g1="quotedbl,quotesingle"
	g2="three"
	k="13" />
    <hkern g1="quotedbl,quotesingle"
	g2="parenright,parenright.sc"
	k="-34" />
    <hkern g1="quotedbl,quotesingle"
	g2="two"
	k="52" />
    <hkern g1="quotedbl,quotesingle"
	g2="eight"
	k="46" />
    <hkern g1="quoteleft,quotedblleft"
	g2="guillemotleft,guilsinglleft"
	k="16" />
    <hkern g1="quoteleft,quotedblleft"
	g2="hyphen,endash,emdash"
	k="16" />
    <hkern g1="quoteleft,quotedblleft"
	g2="questiondown,questiondown.sc"
	k="126" />
    <hkern g1="slash,slash.sc"
	g2="three"
	k="16" />
    <hkern g1="slash,slash.sc"
	g2="zero"
	k="126" />
    <hkern g1="slash,slash.sc"
	g2="six"
	k="34" />
    <hkern g1="slash,slash.sc"
	g2="five"
	k="20" />
    <hkern g1="slash,slash.sc"
	g2="quotesinglbase"
	k="14" />
    <hkern g1="slash,slash.sc"
	g2="sterling,sterling.sc"
	k="14" />
    <hkern g1="eight.dnom"
	g2="notequal"
	k="14" />
    <hkern g1="eight.sc"
	g2="nine.sc"
	k="17" />
    <hkern g1="five"
	g2="four.dnom"
	k="17" />
    <hkern g1="four"
	g2="guillemotleft,guilsinglleft"
	k="23" />
    <hkern g1="four"
	g2="guillemotright,guilsinglright"
	k="7" />
    <hkern g1="four"
	g2="guillemotright.case,guilsinglright.case"
	k="3" />
    <hkern g1="fraction"
	g2="comma,period,quotedblbase,ellipsis"
	k="7" />
    <hkern g1="fraction"
	g2="quotedbl,quotesingle"
	k="3" />
    <hkern g1="nine"
	g2="zero.dnom"
	k="20" />
    <hkern g1="nine"
	g2="divide"
	k="10" />
    <hkern g1="nine"
	g2="equal"
	k="-6" />
    <hkern g1="nine"
	g2="notequal"
	k="-7" />
    <hkern g1="nine"
	g2="asciitilde"
	k="14" />
    <hkern g1="nine"
	g2="eight"
	k="-14" />
    <hkern g1="nine"
	g2="four"
	k="-14" />
    <hkern g1="nine"
	g2="guillemotleft,guilsinglleft"
	k="-3" />
    <hkern g1="nine"
	g2="guillemotleft.case,guilsinglleft.case"
	k="-14" />
    <hkern g1="nine.dnom"
	g2="eight.sc"
	k="-19" />
    <hkern g1="nine.sc"
	g2="plusminus"
	k="-19" />
    <hkern g1="nine.sc"
	g2="zero.dnom"
	k="20" />
    <hkern g1="nine.sc"
	g2="divide"
	k="14" />
    <hkern g1="nine.sc"
	g2="equal"
	k="67" />
    <hkern g1="nine.sc"
	g2="greater"
	k="20" />
    <hkern g1="nine.sc"
	g2="multiply"
	k="-36" />
    <hkern g1="nine.sc"
	g2="notequal"
	k="20" />
    <hkern g1="nine.sc"
	g2="asciitilde"
	k="-13" />
    <hkern g1="nine.sc"
	g2="eight"
	k="20" />
    <hkern g1="one"
	g2="eight"
	k="30" />
    <hkern g1="one"
	g2="four"
	k="20" />
    <hkern g1="one"
	g2="guillemotleft,guilsinglleft"
	k="14" />
    <hkern g1="one"
	g2="guillemotleft.case,guilsinglleft.case"
	k="20" />
    <hkern g1="one"
	g2="guillemotright,guilsinglright"
	k="27" />
    <hkern g1="seven"
	g2="comma,period,quotedblbase,ellipsis"
	k="27" />
    <hkern g1="seven.dnom"
	g2="divide"
	k="14" />
    <hkern g1="seveneighths"
	g2="seveneighths"
	k="17" />
    <hkern g1="seven.sc"
	g2="plus"
	k="17" />
    <hkern g1="six"
	g2="eight"
	k="10" />
    <hkern g1="six"
	g2="guillemotleft,guilsinglleft"
	k="-6" />
    <hkern g1="six"
	g2="nine"
	k="-7" />
    <hkern g1="six"
	g2="one"
	k="-14" />
    <hkern g1="six"
	g2="nine.sc"
	k="-7" />
    <hkern g1="six"
	g2="one.sc"
	k="-7" />
    <hkern g1="six"
	g2="six.sc"
	k="-14" />
    <hkern g1="six"
	g2="zero.sc"
	k="-7" />
    <hkern g1="six.dnom"
	g2="seven.dnom"
	k="-7" />
    <hkern g1="six.dnom"
	g2="seven.sc"
	k="-14" />
    <hkern g1="six.dnom"
	g2="four.dnom"
	k="-7" />
    <hkern g1="six.dnom"
	g2="nine.dnom"
	k="-7" />
    <hkern g1="six.dnom"
	g2="one.dnom"
	k="-14" />
    <hkern g1="six.dnom"
	g2="six.dnom"
	k="-7" />
    <hkern g1="six.dnom"
	g2="three.dnom"
	k="-7" />
    <hkern g1="six.dnom"
	g2="four.sc"
	k="90" />
    <hkern g1="six.dnom"
	g2="nine.sc"
	k="-30" />
    <hkern g1="six.dnom"
	g2="one.sc"
	k="-27" />
    <hkern g1="six.dnom"
	g2="six.sc"
	k="-23" />
    <hkern g1="six.dnom"
	g2="zero.sc"
	k="64" />
    <hkern g1="six.sc"
	g2="five.dnom"
	k="90" />
    <hkern g1="six.sc"
	g2="four.dnom"
	k="-30" />
    <hkern g1="six.sc"
	g2="nine.dnom"
	k="-27" />
    <hkern g1="six.sc"
	g2="one.dnom"
	k="-23" />
    <hkern g1="six.sc"
	g2="six.dnom"
	k="64" />
    <hkern g1="six.sc"
	g2="divide"
	k="64" />
    <hkern g1="six.sc"
	g2="equal"
	k="41" />
    <hkern g1="six.sc"
	g2="greater"
	k="41" />
    <hkern g1="six.sc"
	g2="multiply"
	k="68" />
    <hkern g1="six.sc"
	g2="notequal"
	k="61" />
    <hkern g1="six.sc"
	g2="asciitilde"
	k="54" />
    <hkern g1="six.sc"
	g2="minus"
	k="44" />
    <hkern g1="six.sc"
	g2="nine"
	k="41" />
    <hkern g1="six.sc"
	g2="one"
	k="34" />
    <hkern g1="six.sc"
	g2="six"
	k="61" />
    <hkern g1="six.sc"
	g2="zero"
	k="54" />
    <hkern g1="six.sc"
	g2="fraction"
	k="67" />
    <hkern g1="six.sc"
	g2="onequarter,onehalf,oneeighth"
	k="14" />
    <hkern g1="six.sc"
	g2="seveneighths"
	k="84" />
    <hkern g1="six.sc"
	g2="eight.sc"
	k="54" />
    <hkern g1="six.sc"
	g2="five.sc"
	k="41" />
    <hkern g1="six.sc"
	g2="four.sc"
	k="44" />
    <hkern g1="six.sc"
	g2="nine.sc"
	k="41" />
    <hkern g1="six.sc"
	g2="one.sc"
	k="37" />
    <hkern g1="six.sc"
	g2="six.sc"
	k="61" />
    <hkern g1="six.sc"
	g2="zero.sc"
	k="58" />
    <hkern g1="three"
	g2="slash,slash.sc"
	k="44" />
    <hkern g1="three"
	g2="seven.dnom"
	k="41" />
    <hkern g1="three"
	g2="seven.sc"
	k="34" />
    <hkern g1="three"
	g2="approxequal"
	k="61" />
    <hkern g1="three"
	g2="colon,semicolon"
	k="54" />
    <hkern g1="three"
	g2="greaterequal"
	k="67" />
    <hkern g1="three"
	g2="lessequal"
	k="14" />
    <hkern g1="three"
	g2="plus"
	k="84" />
    <hkern g1="three"
	g2="plusminus"
	k="54" />
    <hkern g1="three"
	g2="eight.dnom"
	k="41" />
    <hkern g1="three"
	g2="five.dnom"
	k="44" />
    <hkern g1="three"
	g2="four.dnom"
	k="41" />
    <hkern g1="three"
	g2="nine.dnom"
	k="37" />
    <hkern g1="three"
	g2="one.dnom"
	k="61" />
    <hkern g1="three"
	g2="six.dnom"
	k="58" />
    <hkern g1="three"
	g2="three.dnom"
	k="14" />
    <hkern g1="three"
	g2="zero.dnom"
	k="-30" />
    <hkern g1="three"
	g2="divide"
	k="14" />
    <hkern g1="three"
	g2="equal"
	k="14" />
    <hkern g1="three"
	g2="zero"
	k="-17" />
    <hkern g1="three"
	g2="one.sc"
	k="10" />
    <hkern g1="three"
	g2="zero.sc"
	k="60" />
    <hkern g1="two"
	g2="colon,semicolon"
	k="-17" />
    <hkern g1="two"
	g2="nine.dnom"
	k="10" />
    <hkern g1="two"
	g2="six.dnom"
	k="60" />
    <hkern g1="two"
	g2="three.dnom"
	k="10" />
    <hkern g1="two"
	g2="zero.dnom"
	k="-22" />
    <hkern g1="two"
	g2="divide"
	k="10" />
    <hkern g1="two"
	g2="greater"
	k="10" />
    <hkern g1="two.sc"
	g2="guillemotright.case,guilsinglright.case"
	k="27" />
    <hkern g1="two.sc"
	g2="hyphen,endash,emdash"
	k="-22" />
    <hkern g1="two.sc"
	g2="less"
	k="-17" />
    <hkern g1="two.sc"
	g2="eight.sc"
	k="34" />
    <hkern g1="two.sc"
	g2="zero.sc"
	k="-20" />
    <hkern g1="zero"
	g2="quotedbl,quotesingle"
	k="27" />
    <hkern g1="zero"
	g2="quoteleft,quotedblleft"
	k="-22" />
    <hkern g1="zero"
	g2="seven"
	k="-17" />
    <hkern g1="zero"
	g2="plusminus"
	k="34" />
    <hkern g1="zero"
	g2="six.dnom"
	k="-20" />
    <hkern g1="zero"
	g2="onequarter,onehalf,oneeighth"
	k="10" />
    <hkern g1="zero.dnom"
	g2="lessequal"
	k="10" />
    <hkern g1="zero.dnom"
	g2="multiply"
	k="12" />
    <hkern g1="zero.dnom"
	g2="notequal"
	k="20" />
    <hkern g1="zero.dnom"
	g2="asciitilde"
	k="71" />
    <hkern g1="zero.dnom"
	g2="eight"
	k="12" />
    <hkern g1="zero.dnom"
	g2="four"
	k="-25" />
    <hkern g1="zero.dnom"
	g2="guillemotleft,guilsinglleft"
	k="12" />
    <hkern g1="zero.dnom"
	g2="guillemotleft.case,guilsinglleft.case"
	k="12" />
    <hkern g1="zero.dnom"
	g2="guillemotright.case,guilsinglright.case"
	k="17" />
    <hkern g1="zero.dnom"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="zero.dnom"
	g2="less"
	k="14" />
    <hkern g1="zero.dnom"
	g2="minus"
	k="27" />
    <hkern g1="zero.dnom"
	g2="nine"
	k="20" />
    <hkern g1="zero.sc"
	g2="quotedbl,quotesingle"
	k="17" />
    <hkern g1="zero.sc"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="zero.sc"
	g2="seven"
	k="14" />
    <hkern g1="zero.sc"
	g2="slash,slash.sc"
	k="27" />
    <hkern g1="zero.sc"
	g2="seven.dnom"
	k="20" />
    <hkern g1="zero.sc"
	g2="guillemotleft.case,guilsinglleft.case"
	k="6" />
    <hkern g1="eight.numr"
	g2="asciitilde"
	k="19" />
    <hkern g1="five.numr"
	g2="one.sc"
	k="22" />
    <hkern g1="nine.numr"
	g2="nine.dnom"
	k="22" />
    <hkern g1="seven.numr"
	g2="guillemotleft.case,guilsinglleft.case"
	k="6" />
    <hkern g1="seven.numr"
	g2="guillemotright.case,guilsinglright.case"
	k="4" />
    <hkern g1="seven.numr"
	g2="six"
	k="14" />
    <hkern g1="six.numr"
	g2="quotedbl,quotesingle"
	k="4" />
    <hkern g1="six.numr"
	g2="approxequal"
	k="14" />
    <hkern g1="three.numr"
	g2="three.dnom"
	k="-3" />
    <hkern g1="three.numr"
	g2="zero.dnom"
	k="14" />
    <hkern g1="three.numr"
	g2="seveneighths"
	k="-7" />
    <hkern g1="three.numr"
	g2="one.sc"
	k="14" />
    <hkern g1="zero.numr"
	g2="plus"
	k="-7" />
    <hkern g1="zero.numr"
	g2="nine.dnom"
	k="14" />
    <hkern g1="B"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="19" />
    <hkern g1="B"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="B"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="14" />
    <hkern g1="B"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="49" />
    <hkern g1="B"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="37" />
    <hkern g1="B"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,uni1EE4"
	k="33" />
    <hkern g1="B"
	g2="V"
	k="27" />
    <hkern g1="B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="53" />
    <hkern g1="B"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="30" />
    <hkern g1="B"
	g2="a.sc"
	k="-7" />
    <hkern g1="B"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,uni1ECB,i.loclTRK"
	k="6" />
    <hkern g1="B"
	g2="x"
	k="14" />
    <hkern g1="B"
	g2="gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	k="28" />
    <hkern g1="B"
	g2="guillemotright.case,guilsinglright.case"
	k="23" />
    <hkern g1="B"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="B"
	g2="quoteright,quotedblright"
	k="37" />
    <hkern g1="B"
	g2="uni1E9E"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="at.case"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="f,f_f,f_f_i,f_f_l"
	k="14" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="28" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="question"
	k="23" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="quoteleft,quotedblleft"
	k="37" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="w.sc"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="X"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="guillemotleft.case,guilsinglleft.case"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="colon,semicolon"
	k="13" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="j,jcircumflex,uni0237"
	k="-13" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="b,m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="13" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="13" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="V"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="at"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,uni01EB,oslashacute,uni1EB9,uni1EBD,uni1ECD"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="comma,period,quotedblbase,ellipsis"
	k="3" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="quotedbl,quotesingle"
	k="13" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="t.sc"
	k="-13" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="13" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="u.sc"
	k="13" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="j,jcircumflex,uni0237"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="b,m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="uni1E9E"
	k="15" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="ae.sc"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,uni1EB8,uni1EBC"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="33" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="-13" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="13" />
    <hkern g1="F"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="13" />
    <hkern g1="F"
	g2="t.sc"
	k="15" />
    <hkern g1="F"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="14" />
    <hkern g1="F"
	g2="v.sc"
	k="14" />
    <hkern g1="F"
	g2="w.sc"
	k="15" />
    <hkern g1="F"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="6" />
    <hkern g1="F"
	g2="y.sc"
	k="33" />
    <hkern g1="F"
	g2="AE"
	k="14" />
    <hkern g1="F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="7" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="15" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="J,Jcircumflex"
	k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="V"
	k="15" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="33" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="hyphen,endash,emdash"
	k="14" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,uni01EB,oslashacute,uni1EB9,uni1EBD,uni1ECD"
	k="7" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="X"
	k="23" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="x"
	k="6" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="AE"
	k="13" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="7" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	k="7" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="guillemotleft.case,guilsinglleft.case"
	k="10" />
    <hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Gcaron"
	g2="guillemotright.case,guilsinglright.case"
	k="-3" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="at"
	k="23" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="f,f_f,f_f_i,f_f_l"
	k="6" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="hyphen,endash,emdash"
	k="13" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,uni01EB,oslashacute,uni1EB9,uni1EBD,uni1ECD"
	k="7" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="7" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="comma,period,quotedblbase,ellipsis"
	k="10" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="question"
	k="-3" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="x"
	k="-17" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	k="-17" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="guillemotright.case,guilsinglright.case"
	k="-7" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="quoteright,quotedblright"
	k="-7" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="slash,slash.sc"
	k="7" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="j.sc"
	k="-14" />
    <hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,uni019D,uni1ECA"
	g2="ae.sc"
	k="-20" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="f,f_f,f_f_i,f_f_l"
	k="-17" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="-17" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="question"
	k="-7" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="quoteleft,quotedblleft"
	k="-7" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="u.sc"
	k="10" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="v"
	k="7" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="-14" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="-20" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="3" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="guillemotleft.case,guilsinglleft.case"
	k="3" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="colon,semicolon"
	k="3" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="guillemotleft,guilsinglleft"
	k="-14" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="slash,slash.sc"
	k="7" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="ae.sc"
	k="-14" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="-14" />
    <hkern g1="K,Kcommaaccent"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="10" />
    <hkern g1="K,Kcommaaccent"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="7" />
    <hkern g1="K,Kcommaaccent"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,uni1EE4"
	k="-14" />
    <hkern g1="K,Kcommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-20" />
    <hkern g1="K,Kcommaaccent"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,uni01EB,oslashacute,uni1EB9,uni1EBD,uni1ECD"
	k="3" />
    <hkern g1="K,Kcommaaccent"
	g2="comma,period,quotedblbase,ellipsis"
	k="3" />
    <hkern g1="K,Kcommaaccent"
	g2="quotedbl,quotesingle"
	k="3" />
    <hkern g1="K,Kcommaaccent"
	g2="dollar.sc,s.sc"
	k="-14" />
    <hkern g1="K,Kcommaaccent"
	g2="v"
	k="7" />
    <hkern g1="K,Kcommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="-14" />
    <hkern g1="K,Kcommaaccent"
	g2="y.sc"
	k="-14" />
    <hkern g1="K,Kcommaaccent"
	g2="j,jcircumflex,uni0237"
	k="83" />
    <hkern g1="K,Kcommaaccent"
	g2="b,m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="74" />
    <hkern g1="K,Kcommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="17" />
    <hkern g1="K,Kcommaaccent"
	g2="slash,slash.sc"
	k="20" />
    <hkern g1="K,Kcommaaccent"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="-25" />
    <hkern g1="K,Kcommaaccent"
	g2="uni1E9E"
	k="-14" />
    <hkern g1="K,Kcommaaccent"
	g2="ae.sc"
	k="-7" />
    <hkern g1="K,Kcommaaccent"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="-7" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="7" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-14" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="-14" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="t.sc"
	k="83" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="74" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="u.sc"
	k="17" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="v"
	k="20" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="v.sc"
	k="-25" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="w.sc"
	k="-14" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="-7" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="y.sc"
	k="-7" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="37" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="guillemotleft.case,guilsinglleft.case"
	k="56" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="colon,semicolon"
	k="-27" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="quoteright,quotedblright"
	k="-10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="b,m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="27" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="ae.sc"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="83" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="J,Jcircumflex"
	k="74" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="17" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-25" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="V"
	k="-14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-7" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="-7" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,uni01EB,oslashacute,uni1EB9,uni1EBD,uni1ECD"
	k="37" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="comma,period,quotedblbase,ellipsis"
	k="56" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="quotedbl,quotesingle"
	k="-27" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="quoteleft,quotedblleft"
	k="-10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="27" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,uni1ECB,i.loclTRK"
	k="-14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="x"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="AE"
	k="114" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="48" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="guillemotright.case,guilsinglright.case"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="colon,semicolon"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="quoteright,quotedblright"
	k="-20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="guillemotleft,guilsinglleft"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="guillemotright,guilsinglright"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="j,jcircumflex,uni0237"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="b,m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="34" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,uni018F,uni01EA,Oslashacute,uni1ECC"
	g2="slash,slash.sc"
	k="14" />
    <hkern g1="P"
	g2="J,Jcircumflex"
	k="27" />
    <hkern g1="P"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="14" />
    <hkern g1="P"
	g2="at.case"
	k="-14" />
    <hkern g1="P"
	g2="f,f_f,f_f_i,f_f_l"
	k="14" />
    <hkern g1="P"
	g2="hyphen,endash,emdash"
	k="114" />
    <hkern g1="P"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,uni01EB,oslashacute,uni1EB9,uni1EBD,uni1ECD"
	k="48" />
    <hkern g1="P"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="20" />
    <hkern g1="P"
	g2="question"
	k="14" />
    <hkern g1="P"
	g2="quotedbl,quotesingle"
	k="14" />
    <hkern g1="P"
	g2="quoteleft,quotedblleft"
	k="-20" />
    <hkern g1="P"
	g2="dollar.sc,s.sc"
	k="20" />
    <hkern g1="P"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="14" />
    <hkern g1="P"
	g2="t.sc"
	k="14" />
    <hkern g1="P"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="20" />
    <hkern g1="P"
	g2="u.sc"
	k="34" />
    <hkern g1="P"
	g2="v"
	k="14" />
    <hkern g1="P"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,uni1ECB,i.loclTRK"
	k="8" />
    <hkern g1="P"
	g2="x"
	k="7" />
    <hkern g1="P"
	g2="gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	k="20" />
    <hkern g1="P"
	g2="guillemotright.case,guilsinglright.case"
	k="13" />
    <hkern g1="P"
	g2="colon,semicolon"
	k="10" />
    <hkern g1="P"
	g2="quoteright,quotedblright"
	k="35" />
    <hkern g1="P"
	g2="ae.sc"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="J,Jcircumflex"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="34" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="14" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="at.case"
	k="8" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="f,f_f,f_f_i,f_f_l"
	k="7" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="question"
	k="13" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quoteleft,quotedblleft"
	k="35" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="20" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="guillemotright,guilsinglright"
	k="26" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="b,m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="10" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="slash,slash.sc"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="20" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="26" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="u.sc"
	k="6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	g2="v"
	k="7" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="J,Jcircumflex"
	k="10" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="6" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="7" />
    <hkern g1="T,uni0162,Tcaron,Tbar,uni021A"
	g2="guillemotleft.case,guilsinglleft.case"
	k="14" />
    <hkern g1="Thorn"
	g2="comma,period,quotedblbase,ellipsis"
	k="14" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,uni1EE4"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,uni1ECB,i.loclTRK"
	k="40" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,uni1EE4"
	g2="quoteright,quotedblright"
	k="6" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,uni1EE4"
	g2="j.sc"
	k="33" />
    <hkern g1="V"
	g2="at.case"
	k="40" />
    <hkern g1="V"
	g2="quoteleft,quotedblleft"
	k="6" />
    <hkern g1="V"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="33" />
    <hkern g1="V"
	g2="x"
	k="6" />
    <hkern g1="V"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="6" />
    <hkern g1="V"
	g2="guillemotleft.case,guilsinglleft.case"
	k="6" />
    <hkern g1="V"
	g2="colon,semicolon"
	k="6" />
    <hkern g1="V"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="V"
	g2="b,m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="6" />
    <hkern g1="V"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="19" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,uni1EE4"
	k="33" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="f,f_f,f_f_i,f_f_l"
	k="6" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,uni01EB,oslashacute,uni1EB9,uni1EBD,uni1ECD"
	k="6" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="comma,period,quotedblbase,ellipsis"
	k="6" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="quotedbl,quotesingle"
	k="6" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="6" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="u.sc"
	k="19" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="j,jcircumflex,uni0237"
	k="-3" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="b,m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="-7" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="33" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="slash,slash.sc"
	k="26" />
    <hkern g1="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="j.sc"
	k="16" />
    <hkern g1="X"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="X"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="19" />
    <hkern g1="X"
	g2="t.sc"
	k="-3" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="-7" />
    <hkern g1="X"
	g2="u.sc"
	k="33" />
    <hkern g1="X"
	g2="v"
	k="26" />
    <hkern g1="X"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="16" />
    <hkern g1="X"
	g2="X"
	k="13" />
    <hkern g1="X"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,uni1ECB,i.loclTRK"
	k="27" />
    <hkern g1="X"
	g2="x"
	k="13" />
    <hkern g1="X"
	g2="AE"
	k="66" />
    <hkern g1="X"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="33" />
    <hkern g1="X"
	g2="guillemotright.case,guilsinglright.case"
	k="10" />
    <hkern g1="X"
	g2="quoteright,quotedblright"
	k="3" />
    <hkern g1="X"
	g2="guillemotright,guilsinglright"
	k="16" />
    <hkern g1="X"
	g2="b,m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="26" />
    <hkern g1="X"
	g2="slash,slash.sc"
	k="42" />
    <hkern g1="X"
	g2="j.sc"
	k="25" />
    <hkern g1="X"
	g2="ae.sc"
	k="29" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="-3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="J,Jcircumflex"
	k="-7" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="33" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="26" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,uni1EE4"
	k="16" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="at"
	k="13" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="at.case"
	k="27" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="f,f_f,f_f_i,f_f_l"
	k="13" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="hyphen,endash,emdash"
	k="66" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,uni01EB,oslashacute,uni1EB9,uni1EBD,uni1ECD"
	k="33" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="question"
	k="10" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="quoteleft,quotedblleft"
	k="3" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="16" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="26" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="v"
	k="42" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="25" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="29" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,uni1ECB,i.loclTRK"
	k="-14" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="20" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	k="17" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="guillemotleft.case,guilsinglleft.case"
	k="36" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="guillemotright.case,guilsinglright.case"
	k="26" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="colon,semicolon"
	k="-14" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="quoteright,quotedblright"
	k="-14" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="guillemotleft,guilsinglleft"
	k="13" />
    <hkern g1="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="J,Jcircumflex"
	k="26" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="42" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,uni1EE4"
	k="25" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="29" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="at.case"
	k="-14" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,uni01EB,oslashacute,uni1EB9,uni1EBD,uni1ECD"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="17" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="comma,period,quotedblbase,ellipsis"
	k="36" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="question"
	k="26" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="quotedbl,quotesingle"
	k="-14" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="quoteleft,quotedblleft"
	k="-14" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="dollar.sc,s.sc"
	k="13" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="u.sc"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="x"
	k="-20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="AE"
	k="27" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	k="94" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="guillemotright.case,guilsinglright.case"
	k="80" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="colon,semicolon"
	k="60" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="quoteright,quotedblright"
	k="94" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="j,jcircumflex,uni0237"
	k="34" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="b,m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="54" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="slash,slash.sc"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="uni1E9E"
	k="64" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="ae.sc"
	k="46" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="13" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="hyphen,endash,emdash"
	k="6" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,uni01EB,oslashacute,uni1EB9,uni1EBD,uni1ECD"
	k="3" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="quoteright,quotedblright"
	k="14" />
    <hkern g1="c,cent,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="j,jcircumflex,uni0237"
	k="13" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBD"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="23" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBD"
	g2="bracketright,bracketright.sc"
	k="16" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBD"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="10" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBD"
	g2="h,k,l,germandbls,thorn,hcircumflex,hbar,kgreenlandic,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBD"
	g2="question"
	k="23" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBD"
	g2="colon,semicolon"
	k="6" />
    <hkern g1="eth"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="eth"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="eth"
	g2="v"
	k="23" />
    <hkern g1="eth"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="f,f_f"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="16" />
    <hkern g1="f,f_f"
	g2="gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	k="6" />
    <hkern g1="f,f_f"
	g2="hyphen,endash,emdash"
	k="6" />
    <hkern g1="f,f_f"
	g2="quoteright,quotedblright"
	k="13" />
    <hkern g1="f,f_f"
	g2="j,jcircumflex,uni0237"
	k="20" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	g2="V"
	k="-20" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-34" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="-7" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="-14" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	g2="question"
	k="60" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	g2="colon,semicolon"
	k="23" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="14" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="6" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	g2="at"
	k="20" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	g2="f,f_f,f_f_i,f_f_l"
	k="20" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	g2="guillemotright,guilsinglright"
	k="-48" />
    <hkern g1="germandbls"
	g2="v"
	k="60" />
    <hkern g1="germandbls"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="23" />
    <hkern g1="germandbls"
	g2="x"
	k="14" />
    <hkern g1="germandbls"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="6" />
    <hkern g1="germandbls"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="20" />
    <hkern g1="germandbls"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="20" />
    <hkern g1="germandbls"
	g2="comma,period,quotedblbase,ellipsis"
	k="-48" />
    <hkern g1="germandbls"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="14" />
    <hkern g1="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,uni1ECB,i.loclTRK"
	g2="x"
	k="14" />
    <hkern g1="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,uni1ECB,i.loclTRK"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="-16" />
    <hkern g1="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,uni1ECB,i.loclTRK"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="7" />
    <hkern g1="j,jcircumflex,uni0237"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="guillemotright,guilsinglright"
	k="-10" />
    <hkern g1="l,dcaron,dcroat,lacute,lcommaaccent,lcaron,ldot,lslash,f_f_l"
	g2="comma,period,quotedblbase,ellipsis"
	k="-10" />
    <hkern g1="l,dcaron,dcroat,lacute,lcommaaccent,lcaron,ldot,lslash,f_f_l"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-10" />
    <hkern g1="l,dcaron,dcroat,lacute,lcommaaccent,lcaron,ldot,lslash,f_f_l"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="-14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng,uni0272"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,uni01EB,oslashacute,uni1ECD"
	g2="question"
	k="-6" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,uni01EB,oslashacute,uni1ECD"
	g2="at"
	k="-20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,uni01EB,oslashacute,uni1ECD"
	g2="f,f_f,f_f_i,f_f_l"
	k="3" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn,omacron,obreve,ohungarumlaut,uni01EB,oslashacute,uni1ECD"
	g2="guillemotright,guilsinglright"
	k="14" />
    <hkern g1="q"
	g2="v"
	k="-6" />
    <hkern g1="q"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="-20" />
    <hkern g1="q"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="3" />
    <hkern g1="q"
	g2="comma,period,quotedblbase,ellipsis"
	k="14" />
    <hkern g1="q"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="6" />
    <hkern g1="q"
	g2="gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	k="36" />
    <hkern g1="q"
	g2="guillemotleft,guilsinglleft"
	k="16" />
    <hkern g1="q"
	g2="quoteright,quotedblright"
	k="-13" />
    <hkern g1="q"
	g2="V"
	k="6" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="h,k,l,germandbls,thorn,hcircumflex,hbar,kgreenlandic,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="14" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-41" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="at"
	k="-20" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="f,f_f,f_f_i,f_f_l"
	k="-20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="quoteleft,quotedblleft"
	k="14" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="-41" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="-20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="37" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="27" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="33" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="braceright,braceright.sc"
	k="23" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="bracketright,bracketright.sc"
	k="17" />
    <hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="30" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="quotedbl,quotesingle"
	k="30" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="7" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="z,zacute,zdotaccent,zcaron"
	k="37" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="at"
	k="27" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="f,f_f,f_f_i,f_f_l"
	k="13" />
    <hkern g1="t,uni0163,tcaron,tbar,uni021B"
	g2="guillemotright,guilsinglright"
	k="8" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="37" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="27" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="13" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	g2="comma,period,quotedblbase,ellipsis"
	k="8" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="24" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	g2="gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	k="20" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,uni01EB,oslashacute,uni1EB9,uni1EBD,uni1ECD"
	k="10" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="2" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	g2="at"
	k="3" />
    <hkern g1="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	g2="f,f_f,f_f_i,f_f_l"
	k="14" />
    <hkern g1="v"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="3" />
    <hkern g1="v"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="14" />
    <hkern g1="v"
	g2="f,f_f,f_f_i,f_f_l"
	k="-19" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-19" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-14" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="z,zacute,zdotaccent,zcaron"
	k="-20" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="at"
	k="-14" />
    <hkern g1="w,wcircumflex,wgrave,wacute,wdieresis"
	g2="f,f_f,f_f_i,f_f_l"
	k="-20" />
    <hkern g1="x"
	g2="x"
	k="-14" />
    <hkern g1="x"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="-20" />
    <hkern g1="x"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="-14" />
    <hkern g1="x"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-20" />
    <hkern g1="x"
	g2="gcircumflex,gbreve,gdotaccent,gcommaaccent,gcaron"
	k="50" />
    <hkern g1="x"
	g2="guillemotleft,guilsinglleft"
	k="22" />
    <hkern g1="x"
	g2="hyphen,endash,emdash"
	k="20" />
    <hkern g1="x"
	g2="quoteright,quotedblright"
	k="20" />
    <hkern g1="x"
	g2="j,jcircumflex,uni0237"
	k="20" />
    <hkern g1="x"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="14" />
    <hkern g1="x"
	g2="braceright,braceright.sc"
	k="14" />
    <hkern g1="x"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="-14" />
    <hkern g1="x"
	g2="z,zacute,zdotaccent,zcaron"
	k="7" />
    <hkern g1="x"
	g2="at"
	k="-10" />
    <hkern g1="x"
	g2="f,f_f,f_f_i,f_f_l"
	k="-14" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	g2="x"
	k="-14" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="7" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="-10" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="-14" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	g2="quoteright,quotedblright"
	k="16" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	g2="j,jcircumflex,uni0237"
	k="13" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	g2="slash,slash.sc"
	k="12" />
    <hkern g1="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	g2="V"
	k="13" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="-7" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="f,f_f,f_f_i,f_f_l"
	k="-27" />
    <hkern g1="b.sc"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="5" />
    <hkern g1="b.sc"
	g2="question.sc"
	k="11" />
    <hkern g1="b.sc"
	g2="dollar.sc,s.sc"
	k="8" />
    <hkern g1="b.sc"
	g2="t.sc"
	k="19" />
    <hkern g1="b.sc"
	g2="u.sc"
	k="5" />
    <hkern g1="b.sc"
	g2="v.sc"
	k="12" />
    <hkern g1="b.sc"
	g2="x.sc"
	k="16" />
    <hkern g1="b.sc"
	g2="quotedblright.sc,quoteright.sc"
	k="20" />
    <hkern g1="b.sc"
	g2="comma,period,quotedblbase,ellipsis"
	k="8" />
    <hkern g1="b.sc"
	g2="z.sc"
	k="33" />
    <hkern g1="b.sc"
	g2="slash,slash.sc"
	k="14" />
    <hkern g1="b.sc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="7" />
    <hkern g1="b.sc"
	g2="b.sc,d.sc,e.sc,eng.sc,f.sc,h.sc,i.sc,k.sc,l.sc,m.sc,n.sc,p.sc,r.sc,thorn.sc"
	k="11" />
    <hkern g1="c.sc,cent.sc"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="11" />
    <hkern g1="c.sc,cent.sc"
	g2="w.sc"
	k="8" />
    <hkern g1="c.sc,cent.sc"
	g2="y.sc"
	k="8" />
    <hkern g1="c.sc,cent.sc"
	g2="X"
	k="5" />
    <hkern g1="c.sc,cent.sc"
	g2="x.sc"
	k="14" />
    <hkern g1="c.sc,cent.sc"
	g2="ae.sc"
	k="13" />
    <hkern g1="c.sc,cent.sc"
	g2="quotedblright.sc,quoteright.sc"
	k="12" />
    <hkern g1="c.sc,cent.sc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="12" />
    <hkern g1="c.sc,cent.sc"
	g2="braceright,braceright.sc"
	k="13" />
    <hkern g1="c.sc,cent.sc"
	g2="bracketright,bracketright.sc"
	k="5" />
    <hkern g1="c.sc,cent.sc"
	g2="parenright,parenright.sc"
	k="28" />
    <hkern g1="c.sc,cent.sc"
	g2="quotedbl,quotesingle"
	k="14" />
    <hkern g1="ae.sc,e.sc,oe.sc"
	g2="V"
	k="13" />
    <hkern g1="ae.sc,e.sc,oe.sc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="ae.sc,e.sc,oe.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="28" />
    <hkern g1="ae.sc,e.sc,oe.sc"
	g2="a.sc"
	k="14" />
    <hkern g1="ae.sc,e.sc,oe.sc"
	g2="j.sc"
	k="16" />
    <hkern g1="ae.sc,e.sc,oe.sc"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="11" />
    <hkern g1="ae.sc,e.sc,oe.sc"
	g2="braceright,braceright.sc"
	k="-14" />
    <hkern g1="f.sc"
	g2="V"
	k="-14" />
    <hkern g1="f.sc"
	g2="j.sc"
	k="-14" />
    <hkern g1="f.sc"
	g2="question.sc"
	k="-5" />
    <hkern g1="f.sc"
	g2="t.sc"
	k="-5" />
    <hkern g1="f.sc"
	g2="y.sc"
	k="-7" />
    <hkern g1="f.sc"
	g2="bracketright,bracketright.sc"
	k="22" />
    <hkern g1="g.sc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="22" />
    <hkern g1="g.sc"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="70" />
    <hkern g1="g.sc"
	g2="question.sc"
	k="62" />
    <hkern g1="g.sc"
	g2="dollar.sc,s.sc"
	k="14" />
    <hkern g1="g.sc"
	g2="u.sc"
	k="17" />
    <hkern g1="g.sc"
	g2="v.sc"
	k="-21" />
    <hkern g1="g.sc"
	g2="y.sc"
	k="-12" />
    <hkern g1="g.sc"
	g2="X"
	k="-5" />
    <hkern g1="g.sc"
	g2="x.sc"
	k="-5" />
    <hkern g1="g.sc"
	g2="comma,period,quotedblbase,ellipsis"
	k="96" />
    <hkern g1="g.sc"
	g2="z.sc"
	k="-27" />
    <hkern g1="g.sc"
	g2="slash,slash.sc"
	k="13" />
    <hkern g1="j.sc"
	g2="dollar.sc,s.sc"
	k="7" />
    <hkern g1="j.sc"
	g2="t.sc"
	k="11" />
    <hkern g1="j.sc"
	g2="u.sc"
	k="8" />
    <hkern g1="j.sc"
	g2="v.sc"
	k="16" />
    <hkern g1="j.sc"
	g2="w.sc"
	k="7" />
    <hkern g1="j.sc"
	g2="y.sc"
	k="5" />
    <hkern g1="j.sc"
	g2="quotedblright.sc,quoteright.sc"
	k="17" />
    <hkern g1="j.sc"
	g2="z.sc"
	k="11" />
    <hkern g1="j.sc"
	g2="slash,slash.sc"
	k="8" />
    <hkern g1="j.sc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="30" />
    <hkern g1="j.sc"
	g2="b.sc,d.sc,e.sc,eng.sc,f.sc,h.sc,i.sc,k.sc,l.sc,m.sc,n.sc,p.sc,r.sc,thorn.sc"
	k="14" />
    <hkern g1="j.sc"
	g2="braceright,braceright.sc"
	k="23" />
    <hkern g1="j.sc"
	g2="bracketright,bracketright.sc"
	k="5" />
    <hkern g1="k.sc"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="14" />
    <hkern g1="k.sc"
	g2="V"
	k="23" />
    <hkern g1="k.sc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="k.sc"
	g2="X"
	k="16" />
    <hkern g1="k.sc"
	g2="quotedblright.sc,quoteright.sc"
	k="5" />
    <hkern g1="k.sc"
	g2="comma,period,quotedblbase,ellipsis"
	k="33" />
    <hkern g1="l.sc"
	g2="j.sc"
	k="5" />
    <hkern g1="l.sc"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="8" />
    <hkern g1="l.sc"
	g2="question.sc"
	k="8" />
    <hkern g1="l.sc"
	g2="dollar.sc,s.sc"
	k="16" />
    <hkern g1="l.sc"
	g2="slash,slash.sc"
	k="-3" />
    <hkern g1="l.sc"
	g2="bracketright,bracketright.sc"
	k="-3" />
    <hkern g1="l.sc"
	g2="parenright,parenright.sc"
	k="-5" />
    <hkern g1="l.sc"
	g2="quotedbl,quotesingle"
	k="28" />
    <hkern g1="d.sc,o.sc,q.sc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-3" />
    <hkern g1="d.sc,o.sc,q.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="-5" />
    <hkern g1="d.sc,o.sc,q.sc"
	g2="a.sc"
	k="28" />
    <hkern g1="d.sc,o.sc,q.sc"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="22" />
    <hkern g1="d.sc,o.sc,q.sc"
	g2="dollar.sc,s.sc"
	k="14" />
    <hkern g1="p.sc"
	g2="j.sc"
	k="19" />
    <hkern g1="p.sc"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="16" />
    <hkern g1="p.sc"
	g2="question.sc"
	k="22" />
    <hkern g1="p.sc"
	g2="t.sc"
	k="-17" />
    <hkern g1="p.sc"
	g2="u.sc"
	k="23" />
    <hkern g1="p.sc"
	g2="w.sc"
	k="17" />
    <hkern g1="p.sc"
	g2="y.sc"
	k="91" />
    <hkern g1="p.sc"
	g2="x.sc"
	k="78" />
    <hkern g1="p.sc"
	g2="ae.sc"
	k="57" />
    <hkern g1="p.sc"
	g2="quotedblright.sc,quoteright.sc"
	k="96" />
    <hkern g1="p.sc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="20" />
    <hkern g1="r.sc"
	g2="u.sc"
	k="5" />
    <hkern g1="r.sc"
	g2="v.sc"
	k="8" />
    <hkern g1="r.sc"
	g2="w.sc"
	k="8" />
    <hkern g1="r.sc"
	g2="y.sc"
	k="14" />
    <hkern g1="r.sc"
	g2="X"
	k="16" />
    <hkern g1="r.sc"
	g2="x.sc"
	k="17" />
    <hkern g1="r.sc"
	g2="z.sc"
	k="20" />
    <hkern g1="r.sc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="25" />
    <hkern g1="r.sc"
	g2="b.sc,d.sc,e.sc,eng.sc,f.sc,h.sc,i.sc,k.sc,l.sc,m.sc,n.sc,p.sc,r.sc,thorn.sc"
	k="13" />
    <hkern g1="r.sc"
	g2="braceright,braceright.sc"
	k="29" />
    <hkern g1="r.sc"
	g2="bracketright,bracketright.sc"
	k="11" />
    <hkern g1="r.sc"
	g2="parenright,parenright.sc"
	k="20" />
    <hkern g1="r.sc"
	g2="quotedbl,quotesingle"
	k="28" />
    <hkern g1="dollar.sc,s.sc"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="13" />
    <hkern g1="dollar.sc,s.sc"
	g2="V"
	k="29" />
    <hkern g1="dollar.sc,s.sc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="11" />
    <hkern g1="dollar.sc,s.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="20" />
    <hkern g1="dollar.sc,s.sc"
	g2="a.sc"
	k="28" />
    <hkern g1="dollar.sc,s.sc"
	g2="ae.sc"
	k="25" />
    <hkern g1="dollar.sc,s.sc"
	g2="quotedblright.sc,quoteright.sc"
	k="5" />
    <hkern g1="dollar.sc,s.sc"
	g2="comma,period,quotedblbase,ellipsis"
	k="3" />
    <hkern g1="dollar.sc,s.sc"
	g2="z.sc"
	k="11" />
    <hkern g1="dollar.sc,s.sc"
	g2="slash,slash.sc"
	k="76" />
    <hkern g1="dollar.sc,s.sc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="71" />
    <hkern g1="dollar.sc,s.sc"
	g2="parenright,parenright.sc"
	k="17" />
    <hkern g1="t.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="17" />
    <hkern g1="t.sc"
	g2="j.sc"
	k="5" />
    <hkern g1="t.sc"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="3" />
    <hkern g1="t.sc"
	g2="question.sc"
	k="28" />
    <hkern g1="t.sc"
	g2="dollar.sc,s.sc"
	k="16" />
    <hkern g1="t.sc"
	g2="t.sc"
	k="50" />
    <hkern g1="t.sc"
	g2="u.sc"
	k="107" />
    <hkern g1="t.sc"
	g2="w.sc"
	k="13" />
    <hkern g1="t.sc"
	g2="y.sc"
	k="20" />
    <hkern g1="t.sc"
	g2="braceright,braceright.sc"
	k="8" />
    <hkern g1="t.sc"
	g2="bracketright,bracketright.sc"
	k="5" />
    <hkern g1="t.sc"
	g2="parenright,parenright.sc"
	k="11" />
    <hkern g1="thorn.sc"
	g2="V"
	k="8" />
    <hkern g1="thorn.sc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="5" />
    <hkern g1="thorn.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="11" />
    <hkern g1="thorn.sc"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="10" />
    <hkern g1="thorn.sc"
	g2="t.sc"
	k="15" />
    <hkern g1="thorn.sc"
	g2="v.sc"
	k="8" />
    <hkern g1="thorn.sc"
	g2="w.sc"
	k="5" />
    <hkern g1="thorn.sc"
	g2="y.sc"
	k="28" />
    <hkern g1="u.sc"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="7" />
    <hkern g1="u.sc"
	g2="question.sc"
	k="8" />
    <hkern g1="u.sc"
	g2="dollar.sc,s.sc"
	k="5" />
    <hkern g1="u.sc"
	g2="t.sc"
	k="14" />
    <hkern g1="u.sc"
	g2="u.sc"
	k="7" />
    <hkern g1="u.sc"
	g2="x.sc"
	k="17" />
    <hkern g1="u.sc"
	g2="quotedblright.sc,quoteright.sc"
	k="8" />
    <hkern g1="u.sc"
	g2="comma,period,quotedblbase,ellipsis"
	k="5" />
    <hkern g1="u.sc"
	g2="z.sc"
	k="25" />
    <hkern g1="u.sc"
	g2="slash,slash.sc"
	k="11" />
    <hkern g1="u.sc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="16" />
    <hkern g1="u.sc"
	g2="b.sc,d.sc,e.sc,eng.sc,f.sc,h.sc,i.sc,k.sc,l.sc,m.sc,n.sc,p.sc,r.sc,thorn.sc"
	k="11" />
    <hkern g1="v.sc"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="11" />
    <hkern g1="v.sc"
	g2="w.sc"
	k="19" />
    <hkern g1="v.sc"
	g2="y.sc"
	k="-3" />
    <hkern g1="v.sc"
	g2="X"
	k="-3" />
    <hkern g1="v.sc"
	g2="ae.sc"
	k="67" />
    <hkern g1="v.sc"
	g2="quotedblright.sc,quoteright.sc"
	k="54" />
    <hkern g1="v.sc"
	g2="comma,period,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="v.sc"
	g2="slash,slash.sc"
	k="12" />
    <hkern g1="v.sc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="-20" />
    <hkern g1="v.sc"
	g2="braceright,braceright.sc"
	k="-8" />
    <hkern g1="v.sc"
	g2="bracketright,bracketright.sc"
	k="-6" />
    <hkern g1="v.sc"
	g2="parenright,parenright.sc"
	k="-12" />
    <hkern g1="w.sc"
	g2="V"
	k="-8" />
    <hkern g1="w.sc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-6" />
    <hkern g1="w.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="-12" />
    <hkern g1="w.sc"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="96" />
    <hkern g1="w.sc"
	g2="question.sc"
	k="-20" />
    <hkern g1="w.sc"
	g2="dollar.sc,s.sc"
	k="16" />
    <hkern g1="w.sc"
	g2="u.sc"
	k="27" />
    <hkern g1="w.sc"
	g2="comma,period,quotedblbase,ellipsis"
	k="16" />
    <hkern g1="w.sc"
	g2="z.sc"
	k="14" />
    <hkern g1="w.sc"
	g2="slash,slash.sc"
	k="11" />
    <hkern g1="w.sc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="19" />
    <hkern g1="w.sc"
	g2="b.sc,d.sc,e.sc,eng.sc,f.sc,h.sc,i.sc,k.sc,l.sc,m.sc,n.sc,p.sc,r.sc,thorn.sc"
	k="16" />
    <hkern g1="w.sc"
	g2="braceright,braceright.sc"
	k="29" />
    <hkern g1="x.sc"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="16" />
    <hkern g1="x.sc"
	g2="V"
	k="29" />
    <hkern g1="x.sc"
	g2="question.sc"
	k="14" />
    <hkern g1="x.sc"
	g2="dollar.sc,s.sc"
	k="11" />
    <hkern g1="x.sc"
	g2="t.sc"
	k="36" />
    <hkern g1="x.sc"
	g2="w.sc"
	k="65" />
    <hkern g1="x.sc"
	g2="bracketright,bracketright.sc"
	k="14" />
    <hkern g1="y.sc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="14" />
    <hkern g1="y.sc"
	g2="j.sc"
	k="5" />
    <hkern g1="y.sc"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="31" />
    <hkern g1="y.sc"
	g2="question.sc"
	k="22" />
    <hkern g1="y.sc"
	g2="x.sc"
	k="5" />
    <hkern g1="y.sc"
	g2="ae.sc"
	k="8" />
    <hkern g1="y.sc"
	g2="quotedblright.sc,quoteright.sc"
	k="8" />
    <hkern g1="y.sc"
	g2="comma,period,quotedblbase,ellipsis"
	k="50" />
    <hkern g1="z.sc"
	g2="dollar.sc,s.sc"
	k="32" />
    <hkern g1="z.sc"
	g2="t.sc"
	k="-3" />
    <hkern g1="z.sc"
	g2="u.sc"
	k="-3" />
    <hkern g1="z.sc"
	g2="v.sc"
	k="-3" />
    <hkern g1="z.sc"
	g2="w.sc"
	k="56" />
    <hkern g1="z.sc"
	g2="y.sc"
	k="67" />
    <hkern g1="z.sc"
	g2="X"
	k="27" />
    <hkern g1="z.sc"
	g2="ae.sc"
	k="5" />
    <hkern g1="z.sc"
	g2="quotedblright.sc,quoteright.sc"
	k="-8" />
    <hkern g1="z.sc"
	g2="z.sc"
	k="-3" />
    <hkern g1="z.sc"
	g2="slash,slash.sc"
	k="-6" />
    <hkern g1="z.sc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="-6" />
    <hkern g1="z.sc"
	g2="bracketright,bracketright.sc"
	k="92" />
    <hkern g1="z.sc"
	g2="parenright,parenright.sc"
	k="-20" />
    <hkern g1="z.sc"
	g2="quotedbl,quotesingle"
	k="10" />
    <hkern g1="lcaron.sc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="92" />
    <hkern g1="lcaron.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="-20" />
    <hkern g1="lcaron.sc"
	g2="a.sc"
	k="10" />
    <hkern g1="lcaron.sc"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="31" />
    <hkern g1="lcaron.sc"
	g2="X"
	k="27" />
    <hkern g1="lcaron.sc"
	g2="x.sc"
	k="-3" />
    <hkern g1="lcaron.sc"
	g2="ae.sc"
	k="-3" />
    <hkern g1="lcaron.sc"
	g2="quotedblright.sc,quoteright.sc"
	k="-3" />
    <hkern g1="lcaron.sc"
	g2="comma,period,quotedblbase,ellipsis"
	k="48" />
    <hkern g1="lcaron.sc"
	g2="z.sc"
	k="48" />
    <hkern g1="lcaron.sc"
	g2="slash,slash.sc"
	k="13" />
    <hkern g1="lcaron.sc"
	g2="b.sc,d.sc,e.sc,eng.sc,f.sc,h.sc,i.sc,k.sc,l.sc,m.sc,n.sc,p.sc,r.sc,thorn.sc"
	k="3" />
    <hkern g1="lcaron.sc"
	g2="braceright,braceright.sc"
	k="-6" />
    <hkern g1="lcaron.sc"
	g2="parenright,parenright.sc"
	k="-6" />
    <hkern g1="lcaron.sc"
	g2="quotedbl,quotesingle"
	k="-3" />
    <hkern g1="at.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="16" />
    <hkern g1="at.case"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="16" />
    <hkern g1="at.case"
	g2="V"
	k="16" />
    <hkern g1="at.case"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="22" />
    <hkern g1="at.case"
	g2="X"
	k="6" />
    <hkern g1="at.case"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="6" />
    <hkern g1="at.case"
	g2="v"
	k="10" />
    <hkern g1="at.case"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="6" />
    <hkern g1="at.case"
	g2="x"
	k="13" />
    <hkern g1="at.case"
	g2="J,Jcircumflex"
	k="30" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="13" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="a.sc"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="kcommaaccent"
	k="22" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="b,m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="14" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="uni1E9E"
	k="34" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="ae.sc"
	k="10" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="j.sc"
	k="6" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="6" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="dollar.sc,s.sc"
	k="6" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="13" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="10" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="AE"
	k="22" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,uni1EE4"
	k="14" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,uni01EB,oslashacute,uni1EB9,uni1EBD,uni1ECD"
	k="34" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="t.sc"
	k="6" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="6" />
    <hkern g1="guillemotleft.case,guilsinglleft.case"
	g2="v.sc"
	k="6" />
    <hkern g1="guillemotright,guilsinglright"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="14" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="34" />
    <hkern g1="guillemotright,guilsinglright"
	g2="j,jcircumflex,uni0237"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="J,Jcircumflex"
	k="6" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="6" />
    <hkern g1="guillemotright,guilsinglright"
	g2="X"
	k="6" />
    <hkern g1="guillemotright,guilsinglright"
	g2="z.sc"
	k="26" />
    <hkern g1="guillemotright,guilsinglright"
	g2="germandbls.sc"
	k="26" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="26" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="26" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="f,f_f,f_f_i,f_f_l"
	k="19" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="a.sc"
	k="41" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="kcommaaccent"
	k="41" />
    <hkern g1="guillemotright.case,guilsinglright.case"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,uni1ECB,i.loclTRK"
	k="14" />
    <hkern g1="hyphen,endash,emdash"
	g2="x"
	k="26" />
    <hkern g1="hyphen,endash,emdash"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="26" />
    <hkern g1="hyphen,endash,emdash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="19" />
    <hkern g1="hyphen,endash,emdash"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="41" />
    <hkern g1="hyphen,endash,emdash"
	g2="AE"
	k="41" />
    <hkern g1="hyphen,endash,emdash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="14" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="V"
	k="14" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="dollar.sc,s.sc"
	k="16" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="w.sc"
	k="13" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis"
	g2="x.sc"
	k="22" />
    <hkern g1="questiondown,questiondown.sc"
	g2="v.sc"
	k="16" />
    <hkern g1="questiondown,questiondown.sc"
	g2="y.sc"
	k="13" />
    <hkern g1="questiondown,questiondown.sc"
	g2="z,zacute,zdotaccent,zcaron"
	k="22" />
    <hkern g1="questiondown,questiondown.sc"
	g2="f,f_f,f_f_i,f_f_l"
	k="10" />
    <hkern g1="questiondown,questiondown.sc"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="6" />
    <hkern g1="questiondown,questiondown.sc"
	g2="a.sc"
	k="6" />
    <hkern g1="questiondown,questiondown.sc"
	g2="kcommaaccent"
	k="6" />
    <hkern g1="questiondown,questiondown.sc"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,uni1ECB,i.loclTRK"
	k="-7" />
    <hkern g1="questiondown.case"
	g2="X"
	k="16" />
    <hkern g1="questiondown.case"
	g2="v"
	k="13" />
    <hkern g1="questiondown.case"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="22" />
    <hkern g1="questiondown.case"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="10" />
    <hkern g1="questiondown.case"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="6" />
    <hkern g1="questiondown.case"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="6" />
    <hkern g1="questiondown.case"
	g2="AE"
	k="6" />
    <hkern g1="questiondown.case"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="-7" />
    <hkern g1="quotedbl,quotesingle"
	g2="V"
	k="-7" />
    <hkern g1="quotedbl,quotesingle"
	g2="ae.sc"
	k="14" />
    <hkern g1="quotedbl,quotesingle"
	g2="j.sc"
	k="14" />
    <hkern g1="quotedbl,quotesingle"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="60" />
    <hkern g1="quotedbl,quotesingle"
	g2="w.sc"
	k="27" />
    <hkern g1="quotedbl,quotesingle"
	g2="x.sc"
	k="41" />
    <hkern g1="quotedbl,quotesingle"
	g2="z.sc"
	k="14" />
    <hkern g1="quoteleft,quotedblleft"
	g2="t,uni0163,tcaron,tbar,uni021B"
	k="14" />
    <hkern g1="quoteleft,quotedblleft"
	g2="t.sc"
	k="14" />
    <hkern g1="quoteleft,quotedblleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="60" />
    <hkern g1="quoteleft,quotedblleft"
	g2="y.sc"
	k="27" />
    <hkern g1="quoteleft,quotedblleft"
	g2="z,zacute,zdotaccent,zcaron"
	k="41" />
    <hkern g1="quoteleft,quotedblleft"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="14" />
    <hkern g1="quoteleft,quotedblleft"
	g2="a.sc"
	k="7" />
    <hkern g1="quotedblleft.sc,quoteleft.sc"
	g2="j,jcircumflex,uni0237"
	k="14" />
    <hkern g1="quotedblleft.sc,quoteleft.sc"
	g2="J,Jcircumflex"
	k="14" />
    <hkern g1="quotedblleft.sc,quoteleft.sc"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="quotedblleft.sc,quoteleft.sc"
	g2="v"
	k="27" />
    <hkern g1="quotedblleft.sc,quoteleft.sc"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="41" />
    <hkern g1="quotedblleft.sc,quoteleft.sc"
	g2="x"
	k="14" />
    <hkern g1="quotedblleft.sc,quoteleft.sc"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="7" />
    <hkern g1="quoteright,quotedblright"
	g2="i,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,ij,uni1ECB,i.loclTRK"
	k="39" />
    <hkern g1="quoteright,quotedblright"
	g2="b,m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron"
	k="26" />
    <hkern g1="quoteright,quotedblright"
	g2="uni1E9E"
	k="59" />
    <hkern g1="quoteright,quotedblright"
	g2="j.sc"
	k="55" />
    <hkern g1="quoteright,quotedblright"
	g2="c.sc,cent.sc,g.sc,o.sc,oe.sc,q.sc"
	k="60" />
    <hkern g1="quoteright,quotedblright"
	g2="dollar.sc,s.sc"
	k="52" />
    <hkern g1="quoteright,quotedblright"
	g2="w.sc"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="x.sc"
	k="10" />
    <hkern g1="quoteright,quotedblright"
	g2="z.sc"
	k="33" />
    <hkern g1="quoteright,quotedblright"
	g2="germandbls.sc"
	k="14" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,uni018F,Gcaron,uni01EA,Oslashacute,uni1ECC"
	k="39" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,uni1EE4"
	k="26" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,uni01EB,oslashacute,uni1EB9,uni1EBD,uni1ECD"
	k="59" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="t.sc"
	k="55" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uni1EE5"
	k="60" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="v.sc"
	k="52" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="y.sc"
	k="10" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="z,zacute,zdotaccent,zcaron"
	k="10" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,uni1EB8,uni1EBC,uni1ECA"
	k="33" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute,uni1EA1"
	k="14" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="f,f_f,f_f_i,f_f_l"
	k="33" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent"
	k="14" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="a.sc"
	k="13" />
    <hkern g1="quotedblright.sc,quoteright.sc"
	g2="kcommaaccent"
	k="39" />
    <hkern g1="slash,slash.sc"
	g2="V"
	k="39" />
    <hkern g1="slash,slash.sc"
	g2="W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="26" />
    <hkern g1="slash,slash.sc"
	g2="Y,Yacute,Ycircumflex,Ydieresis,uni0232,Ygrave,uni1EF8"
	k="59" />
    <hkern g1="slash,slash.sc"
	g2="J,Jcircumflex"
	k="55" />
    <hkern g1="slash,slash.sc"
	g2="T,uni0162,Tcaron,Tbar,uni021A"
	k="60" />
    <hkern g1="slash,slash.sc"
	g2="X"
	k="52" />
    <hkern g1="slash,slash.sc"
	g2="v"
	k="10" />
    <hkern g1="slash,slash.sc"
	g2="w,wcircumflex,wgrave,wacute,wdieresis"
	k="10" />
    <hkern g1="slash,slash.sc"
	g2="x"
	k="33" />
    <hkern g1="slash,slash.sc"
	g2="Z,Zacute,Zdotaccent,Zcaron"
	k="14" />
    <hkern g1="slash,slash.sc"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute,uni1EA0"
	k="33" />
    <hkern g1="slash,slash.sc"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent"
	k="14" />
    <hkern g1="slash,slash.sc"
	g2="y,yacute,ydieresis,ycircumflex,uni0233,ygrave,uni1EF9"
	k="13" />
    <hkern g1="slash,slash.sc"
	g2="AE"
	k="39" />
  </font>
</defs></svg>
