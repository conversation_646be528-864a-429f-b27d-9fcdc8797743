/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.5.1 (2020-10-01)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=function(){},o=function(e){return function(){return e}};var n,r,a,c=o(!1),u=o(!0),_=tinymce.util.Tools.resolve("tinymce.util.Tools"),s=tinymce.util.Tools.resolve("tinymce.util.XHR"),i=function(e){return e.getParam("template_mdate_classes","mdate")},l=function(e){return e.getParam("template_replace_values")},f=function(e){return e.getParam("template_mdate_format",e.translate("%Y-%m-%d"))},m=function(e,t){if((e=""+e).length<t)for(var n=0;n<t-e.length;n++)e="0"+e;return e},p=function(e,t,n){var r="Sun Mon Tue Wed Thu Fri Sat Sun".split(" "),a="Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday".split(" "),o="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),c="January February March April May June July August September October November December".split(" ");return n=n||new Date,t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace("%D","%m/%d/%Y")).replace("%r","%I:%M:%S %p")).replace("%Y",""+n.getFullYear())).replace("%y",""+n.getYear())).replace("%m",m(n.getMonth()+1,2))).replace("%d",m(n.getDate(),2))).replace("%H",""+m(n.getHours(),2))).replace("%M",""+m(n.getMinutes(),2))).replace("%S",""+m(n.getSeconds(),2))).replace("%I",""+((n.getHours()+11)%12+1))).replace("%p",n.getHours()<12?"AM":"PM")).replace("%B",""+e.translate(c[n.getMonth()]))).replace("%b",""+e.translate(o[n.getMonth()]))).replace("%A",""+e.translate(a[n.getDay()]))).replace("%a",""+e.translate(r[n.getDay()]))).replace("%%","%")},d=function(t,n){return function(){var e=t.getParam("templates");"function"!=typeof e?"string"==typeof e?s.send({url:e,success:function(e){n(JSON.parse(e))}}):n(e):e(n)}},x=function(n,e){return _.each(e,function(e,t){"function"==typeof e&&(e=e(t)),n=n.replace(new RegExp("\\{\\$"+t+"\\}","g"),e)}),n},g=function(e,t){var r=e.dom,a=l(e);_.each(r.select("*",t),function(n){_.each(a,function(e,t){r.hasClass(n,t)&&"function"==typeof a[t]&&a[t](n)})})},v=function(e,t){return new RegExp("\\b"+t+"\\b","g").test(e.className)},y=function(n,e,t){var r,a=n.dom,o=n.selection.getContent();t=x(t,l(n)),r=a.create("div",null,t);var c=a.select(".mceTmpl",r);c&&0<c.length&&(r=a.create("div",null)).appendChild(c[0].cloneNode(!0)),_.each(a.select("*",r),function(e){var t;v(e,n.getParam("template_cdate_classes","cdate").replace(/\s+/g,"|"))&&(e.innerHTML=p(n,(t=n).getParam("template_cdate_format",t.translate("%Y-%m-%d")))),v(e,i(n).replace(/\s+/g,"|"))&&(e.innerHTML=p(n,f(n))),v(e,n.getParam("template_selected_content_classes","selcontent").replace(/\s+/g,"|"))&&(e.innerHTML=o)}),g(n,r),n.execCommand("mceInsertContent",!1,r.innerHTML),n.addVisual()},h=function(e){e.addCommand("mceInsertTemplate",function(r){for(var a=[],e=1;e<arguments.length;e++)a[e-1]=arguments[e];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=a.concat(e);return r.apply(null,n)}}(y,e))},b=function(){return T},T=(n=function(e){return e.isNone()},{fold:function(e,t){return e()},is:c,isSome:c,isNone:u,getOr:a=function(e){return e},getOrThunk:r=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:o(null),getOrUndefined:o(undefined),or:a,orThunk:r,map:b,each:t,bind:b,exists:c,forall:u,filter:b,equals:n,equals_:n,toArray:function(){return[]},toString:o("none()")}),M=function(n){var e=o(n),t=function(){return a},r=function(e){return e(n)},a={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:u,isNone:c,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return M(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?a:T},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(c,function(e){return t(n,e)})}};return a},O={some:M,none:b,from:function(e){return null===e||e===undefined?T:M(e)}},P=function(e,t){return function(e,t,n){for(var r=0,a=e.length;r<a;r++){var o=e[r];if(t(o,r))return O.some(o);if(n(o,r))break}return O.none()}(e,t,c)},S=tinymce.util.Tools.resolve("tinymce.Env"),w=tinymce.util.Tools.resolve("tinymce.util.Promise"),D=Object.hasOwnProperty,A=function(e,t){return D.call(e,t)},C={'"':"&quot;","<":"&lt;",">":"&gt;","&":"&amp;","'":"&#039;"},N=function(e){return e.replace(/["'<>&]/g,function(e){return(A(t=C,n=e)?O.from(t[n]):O.none()).getOr(e);var t,n})},I=function(M,t){var e=function(e){return function(e,t){for(var n=e.length,r=new Array(n),a=0;a<n;a++){var o=e[a];r[a]=t(o,a)}return r}(e,function(e){return{text:e.text,value:e.text}})},u=function(e,t){return P(e,function(e){return e.text===t})},i=function(e){M.windowManager.alert("Could not load the specified template.",function(){return e.focus("template")})},l=function(e){return new w(function(t,n){e.value.url.fold(function(){return t(e.value.content.getOr(""))},function(e){return s.send({url:e,success:function(e){t(e)},error:function(e){n(e)}})})})};(function(){if(t&&0!==t.length)return O.from(_.map(t,function(e,t){var n=function(e){return e.url!==undefined};return{selected:0===t,text:e.title,value:{url:n(e)?O.from(e.url):O.none(),content:n(e)?O.none():O.from(e.content),description:e.description}}}));var e=M.translate("No templates defined.");return M.notificationManager.open({text:e,type:"info"}),O.none()})().each(function(o){var b=e(o),T=function(e,t){return{title:"Insert Template",size:"large",body:{type:"panel",items:e},initialData:t,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],onSubmit:function(t){var e=t.getData();u(n,e.template).each(function(e){l(e).then(function(e){y(M,0,e),t.close()})["catch"](function(){t.disable("save"),i(t)})})},onChange:(r=n=o,a=c,function(n,e){var t;"template"===e.name&&(t=n.getData().template,u(r,t).each(function(t){n.block("Loading..."),l(t).then(function(e){a(n,t,e)})["catch"](function(){a(n,t,""),n.disable("save"),i(n)})}))})};var r,a,n},c=function(e,t,n){var r,a,o,c,u,i,l,s,f,m,p,d,g,v=(r=M,-1===(a=n).indexOf("<html>")&&(o="",(c=r.getParam("content_style","","string"))&&(o+='<style type="text/css">'+c+"</style>"),u=r.getParam("content_css_cors",!1,"boolean")?' crossorigin="anonymous"':"",_.each(r.contentCSS,function(e){o+='<link type="text/css" rel="stylesheet" href="'+r.documentBaseURI.toAbsolute(e)+'"'+u+">"}),i=-1===(g=(p=r).getParam("body_class","","string")).indexOf("=")?g:(d=p).getParam("body_class","","hash")[d.id]||"",l=r.dom.encode,s='<script>document.addEventListener && document.addEventListener("click", function(e) {for (var elm = e.target; elm; elm = elm.parentNode) {if (elm.nodeName === "A" && !('+(S.mac?"e.metaKey":"e.ctrlKey && !e.altKey")+")) {e.preventDefault();}}}, false);<\/script> ",m=(f=r.getBody().dir)?' dir="'+l(f)+'"':"",a='<!DOCTYPE html><html><head><base href="'+l(r.documentBaseURI.getURI())+'">'+o+s+'</head><body class="'+l(i)+'"'+m+">"+a+"</body></html>"),x(a,r.getParam("template_preview_replace_values"))),y=[{type:"selectbox",name:"template",label:"Templates",items:b},{type:"htmlpanel",html:'<p aria-live="polite">'+N(t.value.description)+"</p>"},{label:"Preview",type:"iframe",name:"preview",sandboxed:!1}],h={template:t.text,preview:v};e.unblock(),e.redial(T(y,h)),e.focus("template")},t=M.windowManager.open(T([],{template:"",preview:""}));t.block("Loading..."),l(o[0]).then(function(e){c(t,o[0],e)})["catch"](function(){c(t,o[0],""),t.disable("save"),i(t)})})},k=function(t){return function(e){I(t,e)}};e.add("template",function(e){var t,r;(t=e).ui.registry.addButton("template",{icon:"template",tooltip:"Insert template",onAction:d(t,k(t))}),t.ui.registry.addMenuItem("template",{icon:"template",text:"Insert template...",onAction:d(t,k(t))}),h(e),(r=e).on("PreProcess",function(e){var t=r.dom,n=f(r);_.each(t.select("div",e.node),function(e){t.hasClass(e,"mceTmpl")&&(_.each(t.select("*",e),function(e){t.hasClass(e,i(r).replace(/\s+/g,"|"))&&(e.innerHTML=p(r,n))}),g(r,e))})})})}();