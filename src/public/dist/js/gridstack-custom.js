class initGrid {
    constructor () {
        this.gridData = null
        this.gridHeights ()
        this.loadDragGrid(function(output) {
            initGrid.prototype.setGrid(this, output)
        })

        $(window).resize(function() {
            if (sessionStorage.getItem('doNotRefresh') === '1') {
                sessionStorage.removeItem('doNotRefresh');

                return;
            }

            // location.reload()
        })
    }

    gridHeights () {
        let gridElements = document.getElementsByClassName("grid-stack-item")
        let changeHiddenOverview = false;
        if ( $('#overview-tab').attr('aria-selected') == 'false') {
            $('#overview').css('display', 'block')
            changeHiddenOverview = true
        }

        for (const item of gridElements) {
            let element = $("#" + item.id + " .card-body")
            // for low resolutions when there is a scroller we subtract its width
            element.width( element.width() - 20 )
            $("#" + item.id).attr( "gs-h", Math.round( (element.outerHeight())/10) + 2)
            element.width('')
        }

        if (changeHiddenOverview == true) {
            $('#overview').css('display', '')
        }
    }

    setGrid (element, handleData) {
        const clientCardToggle = $('#toggle-client-card');
        const options = {
            column: 3,
            cellHeight: 10,
            callWidth: 'auto',
            margin: 0,
            alwaysShowResizeHandle: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
        }

        if (handleData) {
            handleData.forEach(item => {
                $("#" + item.id).attr({
                    "gs-x": item.x,
                    "gs-y":  item.y * 100
                })
            })
        }

        const grid = GridStack.init(options)
        grid.setStatic(true)

        $('.grid-stack').css('opacity', 1)

        grid.on('dragstop', () => {
            initGrid.prototype.saveDragGrid(grid)
        });

        clientCardToggle.bootstrapToggle('on');

        clientCardToggle.change(e => {
            const widgetTitle   = $('.grid-stack-item .widget-title');
            const toggleState   = e.target.checked
            const gridStackItem = $('.grid-stack-item');

            if (toggleState === true) {
                grid.setStatic(true)
                widgetTitle.hide()
                gridStackItem.removeClass('mouse-move')

                return;
            }

            widgetTitle.show()
            gridStackItem.addClass('mouse-move')
            grid.setStatic(false);
        })
    }

    loadDragGrid (handleData) {
        return $.ajax({
            url: urlLoad,
            data: {
                "_token": csrfToken,
                "type": cardView
            },
            type: 'post',
            headers: {
                "Accept": "application/json",
            },
            error: xhr => {
                handleAjaxError(xhr)
            },
            success: data => {
                if (jQuery.isEmptyObject(data) === false) {
                    let serializedDataN = JSON.parse(data.layout);
                    handleData(serializedDataN)
                }
            }
        })
    }

    saveDragGrid(gridElement) {
        let serializedData = gridElement.save();

        for (const item of serializedData) {
            item.content = '';
        }

        $.ajax({
            url: urlSave,
            data: {
                "_token": csrfToken,
                "data": JSON.stringify(
                    serializedData
                ),
                type: cardView
            },
            type: 'post',
            headers: {
                "Accept": "application/json",
            },
            error: xhr => {
                handleAjaxError(xhr)
            },
            success: data => {
                console.log('saveDragGrid: saved')
            }
        })
    }
}
