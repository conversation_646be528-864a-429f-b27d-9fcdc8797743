let interestTerms = [];

// let countOffice = $('#countOffices').text();
// if (countOffice == 1) {
//     $('#offices option').last().attr('selected', true);
//     $('#offices').attr("disabled", "disabled");
//     $("#offices").after("<input type='hidden' id='hidden-office-id' name='loan[office_id]' value=" + $("#offices").val() + ">");
//     $('#offices').addClass('hideArrowFromSelect');
//
//     $('#nav-tabContent').show('slow');
//
//     officeAjax(urlOffice, $("#offices").val(), $("#productsByOffice"));
// }

function reDrawProductSelect(products, productId) {
    //let productByOffice = $("#nav-tab");
    //productByOffice.empty();

    // if (products === undefined || products.length === 0) {
    //     $('#loan-sliders').hide();
    //     $('#loan-sliders').hide();
    //     $("#loan-products-warning").show('slow');
    //     $("#loan-products-warning").html('<div id="loan-products-warning" ' +
    //         'class="alert alert-warning alert-dismissible bg-warning text-white border-0 fade show" ' +
    //         ' role="alert"> <button type="button" class="close" data-dismiss="alert" ' +
    //         'aria-label="Close"> <span aria-hidden="true">×</span>' +
    //         ' </button><strong>Няма създадени продукти за този офис</strong></div>')
    // } else {
    //     $('#loan-sliders').show();
    //     $('#loan-products-warning').hide();
    // }

    // console.log(products); return;

    // $.each(products, function (index, product) {
    //     if (index === 0) {
    //         productByOffice.prepend($('<a/>', {
    //             text: product.name,
    //             'class': 'nav-item nav-link active loan',
    //             'data-toggle': 'tab',
    //             'role': 'tab',
    //             product_id: product.product_id
    //         }));
    //         //interestTerms = product.interest_terms;
    //     } else {
    //         productByOffice.append($('<a/>', {
    //             text: product.name,
    //             'class': 'nav-item nav-link loan',
    //             'data-toggle': 'tab',
    //             'role': 'tab',
    //             product_id: product.product_id,
    //         }));
    //     }
    //     $("#loan_product_id").val(product.product_id);
    // });

    // if (products.length > 2) {
    //     let calcWidthTabs = ($('#nav-tab').width() / 2) - 2;
    //     $('.nav-link.loan').innerWidth((calcWidthTabs / (products.length - 1)) - 2);
    //     $('.nav-link.loan').first().innerWidth(calcWidthTabs);
    //     let windowWidth = $(window).width();
    //     if (windowWidth < 1600) {
    //         $('.nav-link.loan').innerWidth(calcWidthTabs);
    //     }
    // } else {
    //     let calcWidthTabs = ($('#nav-tab').width() / products.length) - 2;
    //     $('.nav-link.loan').innerWidth(calcWidthTabs);
    // }

    // $('.nav-item.nav-link.loan').click(function (e) {
    //     if (products.length > 2) {
    //         let calcWidthTabs = ($('#nav-tab').width() / 2) - 2;
    //         $('.nav-link.loan').innerWidth((calcWidthTabs / (products.length - 1)) - 2);
    //         $(this).innerWidth(calcWidthTabs);
    //     }
    // });
    // productByOffice.show();

    // $(".loan").click(function () {
    //     let productId = $(this).attr('product_id');
    //     let pin = $("#pin").val();
    //
    //     $.ajax({
    //         url: productSettingsUrl,
    //         method: 'GET',
    //         data: {productId: productId},
    //         dataType: 'json',
    //         success: function (data) {
    //             interestTerms = data.interestTerms;
    //
    //             let labelSumMin = data.loanSettings.sumMin;
    //             let labelSumMax = data.loanSettings.sumMax;
    //             let labelPeriodMin = data.loanSettings.periodMin;
    //             let labelPeriodMax = data.loanSettings.periodMax;
    //             let labelPeriod = data.loanSettings.periodLabel;
    //             let step = data.loanSettings.step;
    //             let defaultAmount = data.loanSettings.default_amount;
    //             let defaultPeriod = data.loanSettings.default_period;
    //
    //             let paymentsSum = $("#paymentsSum");
    //             let paymentsPeriod = $("#loanPeriod");
    //             let loanSum = $("#loan_sum");
    //             let periodSum = $("#loan_period");
    //
    //             if (pin === undefined) {
    //                 $("#payment_sum").html(defaultAmount);
    //                 $("#loanPeriod").html(defaultPeriod);
    //                 loanSum.attr('min', defaultAmount);
    //                 loanSum.attr('max', defaultAmount);
    //                 loanSum.val(defaultAmount);
    //                 periodSum.attr('min', defaultAmount);
    //                 periodSum.attr('max', defaultAmount);
    //                 periodSum.val(defaultPeriod);
    //             } else {
    //                 $(".salaryPeriodText").html(labelPeriodMax);
    //                 paymentsSum.attr('min', labelSumMin);
    //                 paymentsSum.attr('max', labelSumMax);
    //                 paymentsSum.val(labelSumMax);
    //                 paymentsSum.attr('step', step);
    //                 paymentsPeriod.attr('min', labelPeriodMin);
    //                 paymentsPeriod.attr('max', labelPeriodMax);
    //                 paymentsPeriod.val(labelPeriodMax);
    //
    //                 $("#payment_sum").html(labelSumMax);
    //                 $("#loanPeriod").html(labelPeriodMax);
    //                 $("#loan_period").html(labelPeriodMax);
    //                 $("#paymentsPeriod").html(labelPeriodMax);
    //                 $("#paymentsPeriod").attr('max', labelPeriodMax);
    //                 $("#paymentsPeriod").val(labelPeriodMax);
    //
    //                 loanSum.attr('min', labelSumMin);
    //                 loanSum.attr('max', labelSumMax);
    //                 loanSum.val(labelSumMax);
    //
    //                 periodSum.attr('min', labelPeriodMin);
    //                 periodSum.attr('max', labelPeriodMax);
    //                 periodSum.val(labelPeriodMax);
    //
    //
    //                 if ($("#min-sum").length) {
    //                     $("#min-sum").remove();
    //                 }
    //                 if ($("#min-period").length) {
    //                     $("#min-period").remove();
    //                 }
    //
    //                 $("#loan_product_id").val(productId);
    //                 if (labelPeriod == "days") {
    //                     $("#periodTypeSpan").text(loanSettingDay);
    //                 } else {
    //                     $("#periodTypeSpan").text(loanSettingMonth);
    //                 }
    //             }
    //         }
    //     })
    // });
}

// function officeAjax(urlOffice, officeVal, productByOffice) {
//     $.ajax({
//         url: urlOffice,
//         method: 'GET',
//         data: {officeId: officeVal, clientId: $("#clientId").val()},
//         dataType: 'json',
//         success: function (data) {
//             if (data.error) {
//                 $("#office-danger-alert").html(data.error);
//                 return;
//             }
//
//             productByOffice.empty();
//             if (data.length != 0) {
//                 reDrawProductSelect(
//                     data['products'],
//                     data['selectedProductId']
//                 );
//             } else {
//                 productByOffice.hide();
//             }
//         }
//     })
// }

// $(".loan").click(function () {
//     let productId = $(this).data('product-selection');
//
//     $.ajax({
//         url: productSettingsUrl,
//         method: 'GET',
//         data: {productId: productId},
//         dataType: 'json',
//         success: function (data) {
//             let labelSumMin = data.sumMin;
//             let labelSumMax = data.sumMax;
//             let labelPeriodMin = data.loanSettings.periodMin;
//             let labelPeriodMax = data.loanSettings.periodMax;
//             let labelPeriod = data.loanSettings.periodLabel;
//             let step = data.loanSettings.step;
//             let paymentsSum = $("#paymentsSum");
//             let paymentsPeriod = $("#paymentsPeriod");
//             let loanSum = $("#loan_sum");
//
//             paymentsSum.attr('min', labelSumMin);
//             paymentsSum.attr('max', labelSumMax);
//             paymentsSum.val(labelSumMax);
//             paymentsSum.attr('step', step);
//
//             paymentsPeriod.attr('min', labelPeriodMin);
//             paymentsPeriod.attr('max', labelPeriodMax);
//             paymentsPeriod.val(labelPeriodMax);
//
//             $("#payment_sum").html(labelSumMax);
//             $("#loanPeriod").html(labelPeriodMax);
//
//             $("#loan_period").attr('max', labelPeriodMax);
//             $("#loan_period").val(labelPeriodMax);
//
//             loanSum.attr('min', labelSumMin);
//             loanSum.attr('max', labelSumMax);
//             loanSum.val(labelSumMax);
//
//             if ($("#min-sum").length) {
//                 $("#min-sum").remove();
//             }
//             if ($("#min-period").length) {
//                 $("#min-period").remove();
//             }
//
//             if (labelPeriod == "days") {
//                 $("#periodTypeSpan").text(loanSettingDay);
//             } else {
//                 $("#periodTypeSpan").text(loanSettingMonth);
//             }
//         }
//     })
//
// });

// $('#loan_sum').on('change', function (ev) {
//     setTimeout(function () {
//         let loanSum = $("#loan_sum");
//         let paymentsSum = $("#paymentsSum");
//         let paymentsSumLabel = $("#payment_sum");
//         let value = parseInt(loanSum.val());
//         let maxValue = parseInt(loanSum.attr('max'));
//         let minValue = parseInt(paymentsSum.attr('min'));
//         if ($("#min-sum").length) {
//             $("#min-sum").remove();
//         }
//
//         if (value > maxValue) {
//             // Change range input, number input and label value
//             loanSum.val(maxValue);
//             paymentsSum.val(maxValue);
//             paymentsSumLabel.html(maxValue);
//
//             showErrorMessage(
//                 $('#loanAmountErrorContainer'),
//                 `Максималната сума за този продукт е: ${maxValue} лв.`,
//                 3000
//             );
//
//             return false;
//         }
//
//         if (value < minValue || isNaN(value)) {
//             // Change range input, number input and label value
//             loanSum.val(minValue);
//             paymentsSum.val(minValue);
//             paymentsSumLabel.html(minValue);
//
//             showErrorMessage(
//                 $('#loanAmountErrorContainer'),
//                 `Минималната сума за този продукт е: ${minValue} лв.`,
//                 3000
//             );
//
//             return false;
//         }
//
//         paymentsSum.val(value);
//         paymentsSumLabel.html(value);
//         alert(33333);
//         //calculateSomeLoan('Payments');
//     }, 1);
// })

// $('#loan_period').on('change', function (event) {
//     if (event.keyCode == 13) {
//         event.preventDefault();
//         return false;
//     }
//
//     setTimeout(function () {
//         let loanPeriod = $("#loan_period");
//         let paymentsPeriod = $("#paymentsPeriod");
//         let loanPeriodLabel = $("#loanPeriod");
//         let value = parseInt(loanPeriod.val());
//         let maxValue = parseInt(loanPeriod.attr('max'));
//         let minValue = parseInt(paymentsPeriod.attr('min'));
//
//         if ($("#min-period").length) {
//             $("#min-period").remove();
//         }
//
//         if (value > maxValue) {
//             loanPeriod.val(maxValue);
//             paymentsPeriod.val(maxValue);
//             loanPeriodLabel.html(maxValue);
//             return false;
//         }
//
//         if (value < minValue || isNaN(value)) {
//             loanPeriod.after('<div id="min-period" class="text-danger font-10">' + loanMinPeriod + ' : ' + minValue + '</div>');
//             if (isNaN(value)) {
//                 loanPeriod.val(minValue);
//                 paymentsPeriod.val(minValue);
//                 loanPeriodLabel.html(minValue);
//             }
//             return false;
//         } else {
//             $("#min-period").remove();
//         }
//
//         paymentsPeriod.val(value);
//         loanPeriodLabel.html(value);
//
//         calculateSomeLoan('Payments');
//     }, 1);
// })

// $('#loan_discount').on('change', function (event) {
//     if (event.keyCode === 13) {
//         event.preventDefault();
//         return false;
//     }
//
//     setTimeout(function () {
//         let loanDiscount = $("#loan_discount");
//         let discount = $("#discount");
//         let discountPercentLabel = $("#discountPercent");
//         let value = parseFloat(loanDiscount.val());
//         let maxValue = parseFloat(loanDiscount.attr('max'));
//         let minValue = parseFloat(discount.attr('min'));
//         let minDiscount = $("#min-discount");
//
//         if (minDiscount.length) {
//             minDiscount.remove();
//         }
//
//         if (value > maxValue) {
//             loanDiscount.val(maxValue);
//             discount.val(maxValue);
//             discountPercentLabel.html(maxValue);
//             return false;
//         }
//
//         if (value < minValue || isNaN(value)) {
//             loanDiscount.after('<div id="min-discount" class="text-danger font-10">' + loanMinDiscount + ' : ' + minValue + '</div>');
//             if (isNaN(value)) {
//                 discount.val(minValue);
//                 discountPercentLabel.html(minValue);
//             }
//             return false;
//         } else {
//             minDiscount.remove();
//         }
//
//         discount.val(value);
//         discountPercentLabel.html(value);
//
//         calculateSomeLoan('Payments');
//     }, 1);
// });

// $('.pin-btn').click(function (evt) {
//     const clientPin = $('#pin').val();
//     const validationData = validateClientParams({clientPin});
//
//     if (!validationData.status) {
//         const $errorContainer = $('#cstm-danger-alert');
//
//         showErrorMessage(
//             $errorContainer,
//             validationData.message,
//             3000
//         );
//
//         return;
//     }
//
//     ajaxFields(ajaxUrl, clientPin);
// });

// function validateClientParams(data) {
//     let message = 'successfulValidation';
//     let status = true;
//
//     if (data.clientPin.toString().length !== 10) {
//         message = 'Моля въведете ЕГН. Дължината трябва да е 10 символа.';
//         status = false;
//     }
//
//     return {message, validationStatus: status};
// }

function showErrorMessage($container, message, milliseconds = null) {
    $container.html(message);
    $container.show('slow');

    if (!milliseconds) {
        $container.hide('slow');

        return;
    }

    setTimeout(
        function () {
            $container.hide('slow');
        },
        milliseconds
    );
}

function ajaxFields(url, pin) {
    $.ajax({
        url: url,
        method: 'GET',
        data: {
            pin: pin,
            loanId: $('#loanId').val()
        },
        dataType: 'json',
        success: function (data) {
            if (data.error) {
                showErrorMessage(
                    errorContainer,
                    data.error,
                    3000
                );

                return;
            }

            let client = data.client;
            let clientIdCard = data.idCard;
            let productGroupVal = data.isInstallmentGroup;
            let loan = data.loan;

            if (client) {
                $("#clientId").val(client.client_id).trigger('change');
                $("#first_name").val(client.first_name);
                $("#middle_name").val(client.middle_name);
                $("#last_name").val(client.last_name);
                $("#id_card_number").val(client.idcard_number);
                $("#phone").val(client.phone);


                $("#email").val(client.email);
                $("#client_id").val(client.client_id);
                $("#issued_by").val(client.idcard_issued_id);
            }

            if (data.clientNames) {
                $("#first_name").val(data.clientNames.first_name);
                $("#middle_name").val(data.clientNames.middle_name);
                $("#last_name").val(data.clientNames.last_name);
            }

            if (data.clientEmail) {
                $("#email").val(data.clientEmail.email);
            }


            if (clientIdCard) {
                $('[name="client_idcard[city_id]"]').val(clientIdCard.city_id);
                $('[name="client_idcard[address]"]').val(clientIdCard.address);
                $('[name="client_idcard[post_code]"]').val(clientIdCard.post_code);
                $('[name="client_idcard[issue_date]"]').val(clientIdCard.issue_date);
                $('[name="client_idcard[valid_date]"]').val(clientIdCard.valid_date);
                $('[name="client_idcard[sex]"]').val(clientIdCard.sex);
            }

            if (true == productGroupVal) {
                $("#periodTypeSpan").text(loanSettingMonth);
            }

            let address = data.address;
            let contact = data.contact;
            let guarant = data.guarant;
            let products = data.products;
            let employer = data.employer;
            let countOffice = $('#countOffices').text();

            if (address) {
                $('[name="client_address[city_id]"]').not('.noFill').val(address.city_id);
                $('[name="client_address[address]"]').not('.noFill').val(address.address);
                $('[name="client_address[post_code]"]').not('.noFill').val(address.post_code);
            }

            if (employer) {
                $('[name="client_employer[name]"]').val(employer.name);
                $('[name="client_employer[bulstat]"]').val(employer.bulstat);
                $('[name="client_employer[city_id]"]').val(employer.city_id);
                $('[name="client_employer[address]"]').val(employer.address);
                $('[name="client_employer[position]"]').val(employer.position);
                $('[name="client_employer[experience]"]').val(employer.experience);
                $('[name="client_employer[salary]"]').val(employer.salary);
                $('[name="client_employer[details]"]').val(employer.details);
            }

            if (guarant) {
                $("#guarant_pin").val(guarant.pin);
                $("#guarant_first_name").val(guarant.first_name);
                $("#guarant_middle_name").val(guarant.middle_name);
                $("#guarant_last_name").val(guarant.last_name);
                $("#guarant_phone").val(guarant.phone);
                $("#guarant_idcard_number").val(guarant.idcard_number);
                $("#guarant_address").val(guarant.address);
                $("#guarant_idcard_issued_id").val(guarant.idcard_issued_id);
                $("#guarantIdClientId").val(client.client_id);
                $(".guarantId").val(guarant.guarant_id);

            }

            if (contact) {
                $('#contact_type option[value="' + contact.contact_type_id + '"]').prop('selected', true);
                $("#contact_name").val(contact.name);
                $("#contact_phone").val(contact.phone);
                $(".contactId").val(contact.contact_id);
            }

            if (loan) {
                let loanSum = data.loan.amount_approved;
                let loanPeriod = data.loan.period_approved;
                let loanOfficeId = data.loan.office_id;

                $("#loan_product_id").val(loan.product_id);
                $("#loan_sum").val(loanSum);
                $("#paymentsSum").val(loanSum);
                $("#payment_sum").html(loanSum);

                $("#loan_period").val(loanPeriod);
                $("#paymentsPeriod").val(loanPeriod);
                $("#loanPeriod").html(loanPeriod);
                if ($("#offices option[value=" + loanOfficeId + "]").length > 0) {
                    $('#offices').val(loanOfficeId);
                }

                // $('#payment-method').val(loan.payment_method_id);
                // if (loan.payment_method_id == PAYMENT_METHOD_BANK_ID) {
                //     $("#iban").val(data.bank.iban);
                //     $("#iban").show();
                // } else {
                //     $("#iban").hide();
                // }

                if (products.length > 0 && loan.product_id) {
                    reDrawProductSelect(
                        products,
                        loan.product_id
                    );
                }
            }

            if (clientIdCard.city_id != address.city_id || clientIdCard.address != address.address || clientIdCard.post_code != address.post_code) {
                $('#toggle-address').bootstrapToggle('off');
                offToggleShowAddressValue(address.address, address.post_code, address.city_id);
            } else {
                onToggleHideAddress();
            }

            $('[name="client_address[city_id]"]').selectpicker('val', address.city_id);
            $('[name="client_idcard[city_id]"]').selectpicker('val', clientIdCard.city_id);

            if (countOffice == 1) {
                $('#offices option').last().attr('selected', true);
                $('#offices').attr("disabled", "disabled");
                if ($("#hidden-office-id").length > 0) {
                    $("#hidden-office-id").val($("#offices").val());
                } else {
                    $("#offices").after("<input type='hidden' id='hidden-office-id' name='loan[office_id]' value=" + $("#offices").val() + ">");
                }
                $('#offices').addClass('hideArrowFromSelect');
            }
            $('#nav-tabContent').show('slow');

            interestTerms = data.interestTerms;
            //modifyDiscountSlider(data);
            //calculateSomeLoan('Payments');
        },
        error: function (error) {
            handleAjaxError(error);
        },
    })
}

// function modifyDiscountSlider(data) {
//     let discountArr = data.discountArr;
//
//     let $discountRangeInput = $('#discount');
//     let $discountNumberInput = $('#loan_discount');
//     let $discountPercentLabel = $('#discountPercent');
//
//     $discountRangeInput.attr('min', discountArr.minDiscount);
//     $discountNumberInput.attr('min', discountArr.minDiscount)
//     $discountRangeInput.attr('max', discountArr.maxDiscount);
//     $discountNumberInput.attr('max', discountArr.maxDiscount)
//     $discountRangeInput.val(discountArr.currentDiscount);
//     $discountNumberInput.val(discountArr.currentDiscount);
//     $discountPercentLabel.html(discountArr.currentDiscount);
//
//     // Additional client discount from sale task
//     let $saleTaskDiscountPercent = $('#saleTaskDiscountPercent');
//     $saleTaskDiscountPercent.hide();
//     let span = $('#saleTaskDiscountPercent span');
//     span.text('');
//
//     if (discountArr.saleTaskDiscount) {
//         span.text(discountArr.saleTaskDiscount + ' %');
//         $saleTaskDiscountPercent.show();
//     }
// }

