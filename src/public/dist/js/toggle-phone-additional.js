$(document).ready(function () {
    let phoneAdditionalButton = $('#toggle-phone-additional');
    let phoneAdditionalContainer = $('#toggle-phone-additional-input-container');
    let phoneList = $('#toggle-phone-additional-input-container div');
    let phoneCounter = phoneList.length + 1;

    phoneAdditionalButton.click(function () {
        let seqnum = phoneList.last().find('input').attr('data-seqnum');
        if (isNaN(parseFloat(seqnum))) {
            seqnum = 1;
        }
        seqnum++

        if (phoneCounter >= 6) {
            phoneAdditionalButton.prop('disabled', true);
            phoneAdditionalButton.html('<i class=\'fas fa-times font-10\'></i>');
            phoneAdditionalContainer.append('<p class="phoneAdditionalWarning font-10 text-danger">Достигнат максимум от телефонни номера</p>');
        } else {
            phoneAdditionalContainer.append('' +
                '<div id="phoneAdditional' + seqnum + '" class="phoneAdditionalInputContainer">' +
                '<input name="client[phone][' + seqnum + ']" data-seqnum="' + seqnum + '" class="form-control w-75 mt-3" minlength="7" /> ' +
                '<button class="btn btn-secondary mt-3 w-15 phoneAdditionalDelete" type="button">' +
                '<i class="fas fa-trash-alt font-10"></i>' +
                '</button>' +
                '</div>');
            phoneCounter++;
            phoneAdditionalButton.removeAttr('disabled');
        }

    })

    $(phoneAdditionalContainer).on("click", ".phoneAdditionalDelete", function (e) {
        e.preventDefault();
        $(this).parent('div').hide();
        $(this).parent('div').find('input').val('');
        $('.phoneAdditionalWarning').remove();
        phoneCounter--;
        phoneAdditionalButton.removeAttr('disabled');
        phoneAdditionalButton.html('<i class=\'fas fa-plus font-10\'></i>');
    })

});
