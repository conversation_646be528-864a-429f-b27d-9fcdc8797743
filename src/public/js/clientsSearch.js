window.searchConfigObject = {
    timeout: {
        handle: null,
        time: 500,
    },
    lastQuery: '',
    searchIsRunning: false,
};

function fetchSearchedClients(url, query = '', translation, listId, listClass, type = null) {

    if (query === '' || query === null) {
        $(listId).hide();
        return;
    }

    if (
        // window.searchConfigObject.lastQuery === query ||
        window.searchConfigObject.searchIsRunning
    ) {
        return;
    }

    window.searchConfigObject.lastQuery = query;
    window.searchConfigObject.searchIsRunning = true;

    $.ajax({
        url: url,
        method: 'GET',
        data: {query: query},
        dataType: 'json',
        success: function (data) {
            $(listClass).text('');

            if (data?.status === false) {
                $(listId).show();
                $(listClass).text(translation.notFoundClient + ' (Error)');
                return;
            }

            if (Array.isArray(data)) {
                $(listId).show();

                if (!data.length) {
                    $(listClass).text(translation.notFoundClient);
                    return;
                }
                let html = '';

                $.each(data, function (index, value) {
                    if (value.name?.trim() && value.pin) value.name += ' - ';
                    if (value.phone) value.pin += ' - ';
                    if (value.email) value.phone += ' - ';
                    if (value.office_name) value.email += ' - ';

                    let LoanUrlPart = '';
                    if (value.loan_id &&  parseInt(value.loan_id) > 0) {
                        LoanUrlPart = '/' + value.loan_id;
                    }

                    html += `<a data-clientId="${value.client_id}" class="list-type-${type}" href="${urlClientProfile}/${value.client_id}${LoanUrlPart}">
                                ${value.name} ${value.pin} ${value.phone} ${value.email || ''} ${value.office_name || ''}
                            </a>`
                });
                $(listId).html(html);

                if (type === 'input') {
                    loadJqClickEvent();
                }
                return;
            }

            hideUserSearch('onKeyup');
        },
        complete: function(data) {

            window.searchConfigObject.searchIsRunning = false;
        }
    })
}

function fetchSearchedDiscountClients(url, query = '', translation, listId, listClass, type = null) {
    if (query === '' || query === null) {
        $(listId).hide();
        return;
    }

    $.ajax({
        url: url,
        method: 'GET',
        data: {query: query},
        dataType: 'json',
        success: function (data) {
            if ($.trim(data)) {

                $(listClass).text('');
                $(listId).show();
                if (!data.length) {
                    $(listClass).text(translation.notFoundClient);
                }

                let html = '';
                $.each(data, function (index, value) {
                    html += '<a data-clientId="' + value.client_id + '" class="list-type-' + type + '" href="#">';
                    html += '#' + value.client_id + ' ';
                    html += value.name + ' ';
                    html += value.pin;
                    html += '</a>';
                });
                $(listId).html(html);

                if (type === 'input') {
                    loadJqClickEventDiscount();
                }

            } else {
                hideUserSearch('onKeyup');
            }
        }
    })
}

function hideUserSearch(action) {
    let element = document.getElementById("clients");

    if (element.innerHTML.trim().length != 0) {
        element.style = "display:none";
        element.innerHTML = "";
    }
    if (action === 'onClick') {
        $("#search").val("");
    }
}

$("#search").on('keyup', function () {
    const query = $(this).val();

    if (!window.searchConfigObject.timeout.handle) {

        window.searchConfigObject.timeout.handle = setTimeout(() => {

            fetchSearchedClients(url, query, translation, '#clients', '.clients-list', 'link');

            clearTimeout(window.searchConfigObject.timeout.handle);
            window.searchConfigObject.timeout.handle = null;
        }, window.searchConfigObject.timeout.time);
    }
}).on('keydown', function () {
    if (window.searchConfigObject.timeout.handle) {
        clearTimeout(window.searchConfigObject.timeout.handle);
        window.searchConfigObject.timeout.handle = null;
    }
});
window.onclick = function (event) {
    hideUserSearch('onClick');
}

function loadJqClickEvent() {
    $(".list-type-input").on('click', function () {
        $("#searchClients").val(this.getAttribute('data-clientId'));
        $('.discount-clients-list').hide();
        return false;
    });
}

function loadJqClickEventDiscount() {
    $(".list-type-input").on('click', function(event) {
        event.preventDefault();
        let text = jQuery(this).text();
        $("#searchClients").val(text);
        $('.discount-clients-list').hide();
        return false;
    });
}

window.onload = function() {
    let searchElement = document.getElementById("search");
    if (searchElement) {
        if (searchElement.hasAttribute("readonly")) {
            searchElement.removeAttribute("readonly");
        }
    }
};
