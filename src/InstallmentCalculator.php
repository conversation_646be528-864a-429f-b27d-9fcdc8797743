<?php

namespace StikCredit\Calculators;

use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Modules\Admin\Traits\AdminTrait;
use Modules\Core\Exceptions\ProblemException;
use Modules\Head\Repositories\InstallmentRepository;
use Modules\Head\Services\LoanService;
use Modules\Common\Models\Loan;
use Modules\Common\Models\ProductGroup;

/**
 * @description Old version will be remove
 */
class InstallmentCalculator
{
    use AdminTrait;

    private const THREE_MONTHS = 3;
    private const FIVE_MONTHS = 5;
    private const SEVEN_MONTHS = 7;
    private const TWELVE_MONTHS = 12;

    private const FEBRUARY = 2;
    private const FIRST_DAY = 1;
    private const TWENTY_EIGHTH_DAY = 28;
    private const TWENTY_NINTH_DAY = 29;
    private const THIRTIETH_DAY = 30;
    private const THIRTY_FIRST_DAY = 31;

    /**
     * @param Carbon $createdAt
     * @param int $period
     * @param string $periodType
     *
     * @return Carbon|\Carbon\CarbonInterface|\Carbon\Traits\Date|int
     */
    public static function getInstallmentDate(
        Carbon $createdAt,
        int    $period,
        string $periodType = 'months' // the different type is considered 'days'
    )
    {
        $loanStart = $createdAt->copy();
        $loanEnd = $periodType === 'months'
            ? $loanStart->copy()->addMonths($period)
            : $loanStart->copy()->addDays($period);

        if ($periodType === 'months') {
            $isFebInRange = InstallmentCalculator::checkIfMonthIsBetweenTwoDates(
                $loanStart,
                $loanEnd,
                self::FEBRUARY
            );

            $createdAtDay = $createdAt->day;

            if ($createdAtDay === self::TWENTY_NINTH_DAY && $isFebInRange) {
                return self::TWENTY_EIGHTH_DAY;
            }

            if (
                ($createdAtDay === self::THIRTIETH_DAY || $createdAtDay === self::THIRTY_FIRST_DAY)
                && $isFebInRange
            ) {
                return self::FIRST_DAY;
            }

            if ($createdAtDay === self::THIRTY_FIRST_DAY && !$isFebInRange) {
                return self::THIRTIETH_DAY;
            }

            return $createdAtDay;
        }

        return $loanEnd;
    }

    /**
     * @param Carbon $start
     * @param Carbon $end
     * @param int $month
     *
     * @return bool
     */
    public static function checkIfMonthIsBetweenTwoDates(
        Carbon $start,
        Carbon $end,
        int    $month
    ): bool
    {
        return in_array(
            $month,
            InstallmentCalculator::getMonthsBetweenTwoDates($start, $end)
        );
    }

    /**
     * @param Carbon $start
     * @param Carbon $end
     *
     * @return array
     */
    private static function getMonthsBetweenTwoDates(Carbon $start, Carbon $end): array
    {
        $period = CarbonPeriod::create($start, $end);
        $months = [];

        foreach ($period as $date) {
            $month = $date->month;

            if (!in_array($month, $months)) {
                $months[] = $month;
            }
        }

        return $months;
    }

    public static function generateInstallments(Loan $loan)
    {
        $productGroupId = $loan->product_group_id;

        switch ($productGroupId) {
            case ProductGroup::ID_PAYDAY:
                return InstallmentCalculator::payday($loan);
            case ProductGroup::ID_INSTALLMENT:
                return InstallmentCalculator::installment($loan);
            case ProductGroup::ID_COMMON:
                return InstallmentCalculator::common($loan);
            default:
                throw new ProblemException('Invalid product group!');
        }
    }

    private static function payday(Loan $loan, float $forfeitPercent = 0)
    {
        $amountApproved = (float)$loan->amount_approved;
        $periodApproved = (float)$loan->period_approved;

        InstallmentCalculator::checkLoanParams($periodApproved, $amountApproved);

        // TODO: are the checks needed? its payday loan
        if ($forfeitPercent) {
            $forfeitPercentPerYear = InstallmentCalculator::getCalcRulePayday('months') * $forfeitPercent;
        } else {
            if (true /* Auth::user() && Auth::user()->isAgent() && Auth::user()->agent == 'leasing2' */) {
                if ($amountApproved <= InstallmentCalculator::getCalcRulePayday('underTwoHundredAmount')) {
                    $forfeitPercentPerYear =
                        InstallmentCalculator::getCalcRulePayday('months')
                        * InstallmentCalculator::getCalcRulePayday('forfeitPercentPerMonthUnderTwoHundred');
                } else {
                    $forfeitPercentPerYear =
                        InstallmentCalculator::getCalcRulePayday('months')
                        * InstallmentCalculator::getCalcRulePayday('forfeitPercentPerMonthOverOrEqualTwoHundred');
                }
            } else {
                $forfeitPercentPerYear = InstallmentCalculator::getCalcRulePayday('months')
                    * InstallmentCalculator::getCalcRulePayday('forfeitPercentPerMonthUserTypeIsNotLeasingTwo');
            }
        }

        $interestTerms = $loan->product->interestTerms;
        $interestRate = LoanService::getYearInterestRate(
            $amountApproved,
            $periodApproved,
            $interestTerms
        );

        if ($interestRate) {
            $loanInterestPerYear = InstallmentCalculator::getCalcRulePayday('months') * $interestRate;
        } else {
            $loanInterestPerYear = config('TODO_GET_FROM_CONFIG'); // TODO GET FROM CONFIG
        }

        $clientLoansCount = $loan->client_loans_count;
        $withForfeit = false;

        // TODO: in other service
        if ($clientLoansCount === 1 || $clientLoansCount === 4 /* $clientLoansCount % 4 === 0 */) {
            $loanInterestPerDay = 0;
            $loanTaxPerDay = 0;
            $loanInterestPerYear = 0;
            $forfeitPercentPerYear = 0;
            $forfeit = 0;
        } else {
            $loanInterestPerDay = $loanInterestPerYear / InstallmentCalculator::getCalcRulePayday('days');
            $loanTaxPerDay = InstallmentCalculator::getCalcRulePayday('loanTaxPerYear')
                / InstallmentCalculator::getCalcRulePayday('days');
            $withForfeit = true;
        }

        $interestAmount = $amountApproved
            * $loanInterestPerDay
            * $periodApproved
            / InstallmentCalculator::getCalcRulePayday('percentFactor');

        if ($withForfeit) {
            $forfeitPercentPerDay = $forfeitPercentPerYear / InstallmentCalculator::getCalcRulePayday('days');
            $forfeit = $amountApproved
                * $forfeitPercentPerDay
                * $periodApproved
                / InstallmentCalculator::getCalcRulePayday('percentFactor');
        }

        $loanTotalAmount = $amountApproved + $interestAmount;

        $GPR = InstallmentCalculator::getRate(
            $loanTotalAmount,
            $amountApproved,
            $periodApproved
        );

        if (!empty($loan->plan_start_date)) { // TODO CREATE FIELD
            $plannedDate = Carbon::parse($loan->plan_start_date)->timezone('Europe/Sofia');
        } else {
            $plannedDate = Carbon::now('Europe/Sofia');
        }

        $loanTotalAmountForfeit = (!empty($forfeit) ? $forfeit : 0) + $loanTotalAmount;

        $result = [
            'price' => number_format($amountApproved, 2, '.', ''),
            'installmentAmount' => number_format($loanTotalAmount, 2, '.', ''),
            'period' => $periodApproved,
            'endDate' => $plannedDate->addDays($periodApproved)->toDateTimeString(),
            'totalInterest' => number_format($interestAmount, 2, '.', ''),
            'GPR' => number_format($GPR, 2, '.', ''),
            'loanTotalAmount' => number_format($loanTotalAmount, 2, '.', ''),
            'forfeit' => number_format((!empty($forfeit) ? $forfeit : 0), 2, '.', ''),
            'loanTotalAmountForfeit' => number_format(
                $loanTotalAmountForfeit,
                2,
                '.',
                ''
            ),
            'forfeitPercentPerDay' => number_format(
                $forfeitPercentPerYear / InstallmentCalculator::getCalcRulePayday('days'),
                2,
                '.',
                ''
            ),
            'loanInterestPerDay' => number_format($loanInterestPerDay, 2, '.', ''),
            'loanTaxPerDay' => number_format($loanTaxPerDay, 2, '.', ''),
            'loanInterestPerYear' => $loanInterestPerYear,
            'forfeitPercentPerYear' => $forfeitPercentPerYear,
        ];

        $newInstallmentData = [
            'loan_id' => $loan->loan_id,
            'seq_num' => 1, // Only one installment
            'due_date' => now()->addDays($periodApproved),
            'installment_amount' => $loanTotalAmount,
            'status' => 1,
            'overdue_days' => 10,
            'overdue_amount' => 1000,
            'max_overdue_days' => 60,
            'max_overdue_amount' => 2000,
            'created_at' => now(),
            'created_by' => self::getCurrentAdminId(),
        ];

        //return $result;
        return InstallmentCalculator::createNewInstallment($newInstallmentData);
    }

    private static function installment(Loan $loan, float $forfeitPercent = 0)
    {
        $amountApproved = (float)$loan->amount_approved;
        $periodApproved = (float)$loan->period_approved;

        InstallmentCalculator::checkLoanParams($periodApproved, $amountApproved);

        $forfeitPercentPerYear = self::getForfeitPercentPerYear($forfeitPercent, $periodApproved);

        $interestTerms = $loan->product->interestTerms;
        $interestRate = LoanService::getYearInterestRate(
            $amountApproved,
            $periodApproved,
            $interestTerms
        );

        if ($interestRate) {
            $loanInterestPerYear = InstallmentCalculator::getCalcRuleInstallment('months') * $interestRate;
        } else {
            $loanInterestPerYear = config('TODO_GET_FROM_CONFIG'); // TODO GET FROM CONFIG
        }

        $capitalCurrentBalance = $amountApproved;
        $forfeit = 0;
        $capitalCurrent = 0;
        $capitalInstallment = 0;
        $interestCurrent = 0;
        $interestA = 0;
        $loanTotalForfeit = 0;
        $sum = $loanInterestPerYear + $forfeitPercentPerYear;
        $installmentAmount = InstallmentCalculator::getPmt(
            $sum / InstallmentCalculator::getCalcRuleInstallment('rateFactor'),
            $periodApproved,
            -$amountApproved
        );

        if (!empty($loan->plan_start_date)) {
            $plannedDate = Carbon::parse($loan->plan_start_date)
                ->timezone('Europe/Sofia');
        } else {
            $plannedDate = Carbon::now('Europe/Sofia');
        }

        $loanIssueDate = $plannedDate->copy();
        $installmentsForMassInsert = [];
        $result = [];

        for ($i = 1; $i <= $periodApproved; $i++) {
            $interestCurrent = $capitalCurrentBalance
                * ($loanInterestPerYear / InstallmentCalculator::getCalcRuleInstallment('rateFactor'));

            if ($periodApproved != $i) {
                $forfeitCurrent = ($capitalCurrentBalance * ($sum / InstallmentCalculator::getCalcRuleInstallment(
                                'rateFactor'
                            )))
                    - $interestCurrent;
                $capitalInstallment = $installmentAmount - $interestCurrent - $forfeitCurrent;
                $capitalCurrent += $capitalInstallment;
            } else {
                $lastInstallment = $amountApproved - $capitalCurrent;
                $forfeitCurrent = $installmentAmount - $lastInstallment - $interestCurrent;
                $capitalInstallment = $installmentAmount - $interestCurrent - $forfeitCurrent;
            }

            $capitalCurrentBalance -= $capitalInstallment;
            $forfeit += $forfeitCurrent;

            $plannedDate->addMonthNoOverflow();
            $plannedDate->day(min($loanIssueDate->day, $plannedDate->daysInMonth));

            $installmentsForMassInsert[] = [
                'loan_id' => $loan->loan_id,
                'seq_num' => $i,
                'due_date' => $plannedDate->addMonth(),
                'installment_amount' => $installmentAmount,
                'status' => 1,
                'overdue_days' => 10,
                'overdue_amount' => 1000,
                'max_overdue_days' => 60,
                'max_overdue_amount' => 2000,
                'created_at' => now(),
                'created_by' => self::getCurrentAdminId(),
            ];

            $result['data'][] = [
                'period' => $i,
                'interestAmount' => number_format($interestCurrent, 2, '.', ''),
                'principal' => $interestCurrent + $capitalInstallment,
                'date' => $plannedDate->toDateTimeString(),
                'installmentAmount' => number_format($installmentAmount, 2, '.', ''),
                'forfeit' => number_format($forfeitCurrent, 2, '.', ''),
                'balance' => number_format($capitalCurrentBalance, 2, '.', ''),
            ];

            $loanTotalForfeit = $forfeit;
            $interestA += $interestCurrent;
        }

        $interestAmount = $interestA;
        $loanTotalAmount = $amountApproved + $interestAmount;
        $loanTotalAmountForfeit = $amountApproved + $interestAmount + $forfeit;
        $GPR = InstallmentCalculator::getRate(
            $loanTotalAmount,
            $amountApproved,
            $periodApproved
        );

//        if ($withForfeit != true) {
//            $GPR = ((1 + PHPExcel_Calculation_Financial::Rate(
//                            $creditPeriod,
//                            $creditTotalAmount / $creditPeriod,
//                            -$creditAmount
//                        )) ** 12 - 1) * 100;
//        } else {
//            $GPR = getPlansMonths($creditPeriod, $creditAmount, false, $credit, $withPromoCode)['GPR'];
//        }

        $result['price'] = number_format($amountApproved, 2, '.', '');
        $result['installmentAmount'] = number_format($installmentAmount, 2, '.', '');
        $result['period'] = $periodApproved;
        $result['endDate'] = $plannedDate->toDateTimeString();
        $result['totalInterest'] = number_format($interestAmount, 2, '.', '');
        $result['GPR'] = number_format($GPR, 2, '.', '');
        $result['loanTotalAmount'] = number_format($loanTotalAmount, 2, '.', '');
        $result['loanTotalForfeit'] = number_format($loanTotalForfeit, 2, '.', '');
        $result['loanTotalAmountForfeit'] = number_format($loanTotalAmountForfeit, 2, '.', '');
        $result['forfeitPercentPerYear'] = $forfeitPercentPerYear;
        $result['loanInterestPerYear'] = $loanInterestPerYear;

        //return $result;

        return InstallmentCalculator::createNewInstallment($installmentsForMassInsert);
    }

    private static function common(Loan $loan)
    {
        //TODO: ???
        dd('Common');
    }

    /**
     * @param float $period
     * @param float $amount
     *
     * @throws ProblemException
     */
    private static function checkLoanParams(float $period, float $amount)
    {
        if (empty($period) || $period < 1 || empty($amount) || $amount < 1) {
            throw new ProblemException('Empty period or amount!');
        }
    }

    /**
     * @param $key
     *
     * @return int|float|null
     */
    private static function getCalcRulePayday($key)
    {
        $rules = [
            'percentFactor' => 100,
            'days' => 360,
            'months' => 12,
            'underTwoHundredAmount' => 199,
            'forfeitPercentPerMonthUnderTwoHundred' => 17,
            'forfeitPercentPerMonthOverOrEqualTwoHundred' => 12,
            'forfeitPercentPerMonthUserTypeIsNotLeasingTwo' => 27,
            'loanTaxPerYear' => 319.68,
        ];

        return array_key_exists($key, $rules) ? $rules[$key] : null;
    }

    /**
     * @param $key
     *
     * @return int|float|null
     */
    private static function getCalcRuleInstallment($key)
    {
        $rules = [
            'months' => 12,
            'baseForfeitPercent' => 177.35,
            'forfeitPercentDiscount' => 6.64,
            'forfeitPercentPerYearUpToThreeMonths' => 146,
            'forfeitPercentPerYearBetweenThreeAndSevenMonths' => 136,
            'forfeitPercentPerYearOverSevenMonths' => 126,
            'rateFactor' => 360 / 100 * 30,
        ];

        return array_key_exists($key, $rules) ? $rules[$key] : null;
    }

    /**
     * @param array $data
     *
     * @return mixed|null
     */
    private static function createNewInstallment(array $data)
    {
        if (empty($data)) {
            return null;
        }

        return (new InstallmentRepository)->bulkCreate($data);
    }

    private static function getRate($loanTotalAmount, $loanAmount, $loanPeriod)
    {
//        $GPR = ((1 + PHPExcel_Calculation_Financial::Rate(
//                        1,
//                        $creditTotalAmount,
//                        -$creditAmount
//                    )) ** (12 * 30 / $creditPeriod) - 1) * 100;
        return 1; // TODO USE FORMULA
    }

    private static function getPmt($interest, $period, $amount)
    {
//    $PMT = (-$fv - $pv * pow(1 + $rate, $nper)) /
//    (1 + $rate * $type) /
//    ((pow(1 + $rate, $nper) - 1) / $rate);
        return 1; // TODO USE FORMULA
    }

    protected static function getForfeitPercentPerYear($forfeitPercent, $periodApproved)
    {
        if ($forfeitPercent) {
            $forfeitPercentPerYear = InstallmentCalculator::getCalcRuleInstallment('months')
                * $forfeitPercent;
        } else {
            if (true /* Auth::check() && Auth::user()->isAgent() && Auth::user()->agent == 'leasing2' */) {
                if ($periodApproved <= self::THREE_MONTHS) {
                    $forfeitPercentPerYear = InstallmentCalculator::getCalcRuleInstallment(
                        'forfeitPercentPerYearUpToThreeMonths'
                    );
                } elseif ($periodApproved > self::THREE_MONTHS && $periodApproved <= self::SEVEN_MONTHS) {
                    $forfeitPercentPerYear = InstallmentCalculator::getCalcRuleInstallment(
                        'forfeitPercentPerYearBetweenThreeAndSevenMonths'
                    );
                } else { // Over seven months
                    $forfeitPercentPerYear = InstallmentCalculator::getCalcRuleInstallment(
                        'forfeitPercentPerYearOverSevenMonths'
                    );
                }
            } else {
                if ($periodApproved <= self::FIVE_MONTHS) {
                    $forfeitPercentPerYear =
                        InstallmentCalculator::getCalcRuleInstallment('baseForfeitPercent');
                } elseif (
                    $periodApproved > self::FIVE_MONTHS
                    && $periodApproved <= self::TWELVE_MONTHS
                ) {
                    $forfeitPercentPerYear = InstallmentCalculator::getCalcRuleInstallment('baseForfeitPercent')
                        - ($periodApproved - self::FIVE_MONTHS) * InstallmentCalculator::getCalcRuleInstallment(
                            'forfeitPercentDiscount'
                        );
                } else {
                    $forfeitPercentPerYear = InstallmentCalculator::getCalcRuleInstallment('baseForfeitPercent')
                        - self::SEVEN_MONTHS * InstallmentCalculator::getCalcRuleInstallment('forfeitPercentDiscount');
                }
            }
        }

        return $forfeitPercentPerYear;
    }

    /**
     * @param Carbon $date1
     * @param Carbon $date2
     *
     * @return int
     */
    public static function simpleDateDiff(Carbon $date1, Carbon $date2): int
    {
        $date1->hour(00);
        $date1->minute(00);
        $date1->second(00);

        $date2->hour(00);
        $date2->minute(00);
        $date2->second(00);

        return $date1->diffInDays($date2);
    }

    /**
     * @param Carbon $today
     * @param Carbon $nextDueDate
     * @param Carbon $previousDueDate
     * @param float $installmentInterest
     *
     * @return float
     */
    public static function calcAccruedInterest(
        Carbon $today,
        Carbon $nextDueDate,
        Carbon $previousDueDate,
        float  $installmentInterest
    )
    {
        return Calculator::round(
            (self::simpleDateDiff($today, $previousDueDate)
                / self::simpleDateDiff($nextDueDate, $previousDueDate)
                * $installmentInterest),
            2
        );
    }

    /**
     * @param Carbon $today
     * @param Carbon $previousDueDate
     * @param float $forfeitYear
     * @param float $installmentAmount
     *
     * @return float
     */
    public static function calcLateInterest(
        Carbon $today,
        Carbon $previousDueDate,
        float  $forfeitYear,
        float  $installmentAmount
    )
    {
        return Calculator::round(
            (self::simpleDateDiff($today, $previousDueDate) * ($forfeitYear / 100) / 360 * $installmentAmount),
            2
        );
    }

    public static function calcLatePenalty(
        Carbon $today,
        Carbon $previousDueDate,
        float  $forfeitYear,
        float  $installmentAmount
    )
    {
        return Calculator::round(
            (self::simpleDateDiff($today, $previousDueDate) * ($forfeitYear / 100) / 360 * $installmentAmount),
            2
        );
    }
}
