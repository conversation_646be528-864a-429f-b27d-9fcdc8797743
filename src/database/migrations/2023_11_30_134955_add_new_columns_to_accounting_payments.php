<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('accounting_payments', function (Blueprint $table) {
            $table->integer('creditAmount')->nullable();
            $table->integer('debitAmount')->nullable();
            $table->smallInteger('isRefinance')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('accounting_payments', function (Blueprint $table) {
            $table->dropColumn([
                'creditAmount',
                'debitAmount',
                'isRefinance',
            ]);
        });
    }
};
