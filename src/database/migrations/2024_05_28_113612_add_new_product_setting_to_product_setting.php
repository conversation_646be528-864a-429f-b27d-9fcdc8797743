<?php

use Illuminate\Database\Migrations\Migration;
use Modules\Common\Models\ProductSetting;

return new class extends Migration {
    public function up(): void
    {
        $row = \Modules\Common\Models\ProductSetting::create([
            'name' => ProductSetting::EXTEND_LOAN_FEE_PERCENT_KEY,
            'type' => ProductSetting::PRODUCT_SETTING_TYPE_COMMON,
            'value_type' => ProductSetting::PRODUCT_SETTING_VALUE_TYPE_NUMBER,
            'default_value' => 0,
        ]);

        $products = \Modules\Common\Models\Product::all();
        $products->each(function (\Modules\Common\Models\Product $product) use ($row) {
            $product->productSettings()->create([
                'name' => ProductSetting::EXTEND_LOAN_FEE_PERCENT_KEY,
                'value' => $row->default_value,
                'product_setting_id' => $row->getKey(),
            ]);
        });
    }

    public function down(): void
    {
        $products = \Modules\Common\Models\Product::all();
        $products->each(function (\Modules\Common\Models\Product $product) {
            $product->productSettings()->where([
                'name' => ProductSetting::EXTEND_LOAN_FEE_PERCENT_KEY
            ])->forceDelete();
        });

        ProductSetting::where('name', ProductSetting::EXTEND_LOAN_FEE_PERCENT_KEY)->forceDelete();
    }
};
