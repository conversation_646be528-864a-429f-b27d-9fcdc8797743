<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up()
    {
        /**
         * Fix to drop schemas, because laravel ofter migrate:fresh only drop tables
         */
        $sql = <<<SQL
CREATE OR REPLACE FUNCTION drop_all ()
    RETURNS VOID  AS
$$
DECLARE rec RECORD;
BEGIN
    -- Get all the schemas
    FOR rec IN
        SELECT n.nspname                              AS "name"
        FROM pg_catalog.pg_namespace n
        WHERE n.nspname !~ '^pg_'
          AND n.nspname <> 'information_schema'
          AND n.nspname != 'public'
        LOOP
            EXECUTE 'DROP SCHEMA ' || rec.name || ' CASCADE';
        END LOOP;
    RETURN;
END;
$$ LANGUAGE plpgsql
SQL;
        \DB::unprepared($sql);
        \DB::unprepared('SELECT drop_all()');
        /**
         * Remove procedure - for exclude people factor
         */
        \DB::unprepared('DROP FUNCTION drop_all');
    }

    public function down()
    {
    }
};