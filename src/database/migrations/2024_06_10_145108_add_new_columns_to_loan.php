<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    public function up(): void
    {
        Schema::table('loan', function (Blueprint $table) {
            $table->smallInteger('skip_auto_process')
                ->default(\Modules\Common\Enums\BooleanCasesEnum::NO->value)
                ->index();

            $table->smallInteger('skip_ref_amount_check')
                ->default(\Modules\Common\Enums\BooleanCasesEnum::NO->value)
                ->index();
        });
    }

    public function down(): void
    {
        Schema::table('loan', function (Blueprint $table) {
            $table->dropColumn(['skip_auto_process', 'skip_ref_amount_check']);
        });
    }
};
