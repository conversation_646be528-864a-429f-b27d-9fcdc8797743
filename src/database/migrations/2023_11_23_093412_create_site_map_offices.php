<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        $this->dropOldColumns();

        Schema::create('site_map_offices', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->softDeletes();

            $table->foreignIdFor(\Modules\Common\Models\City::class, 'city_id')
                ->constrained(\Modules\Common\Models\City::getTableName(), 'city_id');

            $table->string('name');
            $table->string('phone');
            $table->text('working_time');
            $table->text('description');
            $table->text('address');
            $table->text('lat');
            $table->text('lng');
            $table->text('slug');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('site_map_offices');
    }

    private function dropOldColumns(): void
    {
        if (Schema::hasColumn('office', 'trade_name')) {
            Schema::table('office', function (Blueprint $table) {
                $table->dropColumn('trade_name');
            });
        }

        if (Schema::hasColumn('office', 'lat')) {
            Schema::table('office', function (Blueprint $table) {
                $table->dropColumn('lat');
            });
        }

        if (Schema::hasColumn('office', 'lng')) {
            Schema::table('office', function (Blueprint $table) {
                $table->dropColumn('lng');
            });
        }

        if (Schema::hasColumn('office', 'working_time')) {
            Schema::table('office', function (Blueprint $table) {
                $table->dropColumn('working_time');
            });
        }
    }
};
