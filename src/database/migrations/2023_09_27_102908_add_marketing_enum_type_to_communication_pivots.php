<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        $oldRows = \Modules\Common\Entities\CommunicationPivot::all();

        Schema::table('communication_pivots', function (Blueprint $table) {
            $table->dropColumn('classification');
        });

        Schema::table('communication_pivots', function (Blueprint $table) {
            $table
                ->enum('classification', \Modules\Communication\Application\Enums\CommunicationClassificationEnum::toArray())
                ->default(\Modules\Communication\Application\Enums\CommunicationClassificationEnum::COMMENT->value)
                ->index();
        });


        $oldRows->map(function (\Modules\Common\Entities\CommunicationPivot $communicationPivot) {
            $row = \Modules\Common\Entities\CommunicationPivot::where('id', $communicationPivot->getKey())->first();

            $row->setAttribute('classification', $communicationPivot->classification);
            $row->save();
        });
    }
};
