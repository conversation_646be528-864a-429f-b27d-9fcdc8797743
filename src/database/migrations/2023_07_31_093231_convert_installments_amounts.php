<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        $installments = \Modules\Common\Models\Installment::all();

        $installments->map(function (\Modules\Common\Models\Installment $installment) {
            $installment->accrued_total_amount = intToFloat($installment->accrued_total_amount);
            $installment->total_amount = intToFloat($installment->total_amount);
            $installment->principal = intToFloat($installment->principal);
            $installment->paid_principal = intToFloat($installment->paid_principal);
            $installment->interest = intToFloat($installment->interest);
            $installment->rest_interest = intToFloat($installment->rest_interest);
            $installment->accrued_interest = intToFloat($installment->accrued_interest);
            $installment->late_interest = intToFloat($installment->late_interest);
            $installment->paid_accrued_interest = intToFloat($installment->paid_accrued_interest);
            $installment->paid_interest = intToFloat($installment->paid_interest);
            $installment->paid_late_interest = intToFloat($installment->paid_late_interest);
            $installment->accrued_penalty = intToFloat($installment->accrued_penalty);
            $installment->penalty = intToFloat($installment->penalty);
            $installment->late_penalty = intToFloat($installment->late_penalty);
            $installment->paid_accrued_penalty = intToFloat($installment->paid_accrued_penalty);
            $installment->paid_penalty = intToFloat($installment->paid_penalty);
            $installment->rest_penalty = intToFloat($installment->rest_penalty);
            $installment->paid_late_penalty = intToFloat($installment->paid_late_penalty);
            $installment->overdue_amount = intToFloat($installment->overdue_amount);
            $installment->max_overdue_amount = intToFloat($installment->max_overdue_amount);

            $installment->save();

            dump('Update installment: ' . $installment->getKey());
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        $installments = \Modules\Common\Models\Installment::all();

        $installments->map(function (\Modules\Common\Models\Installment $installment) {
            $installment->accrued_total_amount = floatToInt($installment->accrued_total_amount);
            $installment->total_amount = floatToInt($installment->total_amount);
            $installment->principal = floatToInt($installment->principal);
            $installment->paid_principal = floatToInt($installment->paid_principal);
            $installment->interest = intToFloat($installment->interest);
            $installment->rest_interest = floatToInt($installment->rest_interest);
            $installment->accrued_interest = floatToInt($installment->accrued_interest);
            $installment->late_interest = floatToInt($installment->late_interest);
            $installment->paid_accrued_interest = floatToInt($installment->paid_accrued_interest);
            $installment->paid_interest = floatToInt($installment->paid_interest);
            $installment->paid_late_interest = floatToInt($installment->paid_late_interest);
            $installment->accrued_penalty = floatToInt($installment->accrued_penalty);
            $installment->penalty = floatToInt($installment->penalty);
            $installment->late_penalty = floatToInt($installment->late_penalty);
            $installment->paid_accrued_penalty = floatToInt($installment->paid_accrued_penalty);
            $installment->paid_penalty = floatToInt($installment->paid_penalty);
            $installment->rest_penalty = floatToInt($installment->rest_penalty);
            $installment->paid_late_penalty = floatToInt($installment->paid_late_penalty);
            $installment->overdue_amount = floatToInt($installment->overdue_amount);
            $installment->max_overdue_amount = floatToInt($installment->max_overdue_amount);


            $installment->save();
        });
    }
};
