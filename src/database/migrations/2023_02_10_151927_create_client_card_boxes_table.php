<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;
use Modules\Common\Database\Seeders\ClientCardBoxesSeeder;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('client_card_boxes', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->softDeletes();

            $table->string('name')->unique();
        });

        /// after create table seed with default data
        Artisan::call('db:seed', ['--class' => ClientCardBoxesSeeder::class]);
    }

    public function down(): void
    {
        Schema::dropIfExists('client_card_boxes');
    }
};
