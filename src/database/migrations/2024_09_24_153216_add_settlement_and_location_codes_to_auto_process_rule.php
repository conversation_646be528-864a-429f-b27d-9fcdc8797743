<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('auto_process_rule', function (Blueprint $table) {
            $table->json('settlement_code')->nullable();
            $table->json('location_code')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('auto_process_rule', function (Blueprint $table) {
            $table->dropColumn([
                'settlement_code',
                'location_code',
            ]);
        });
    }
};
