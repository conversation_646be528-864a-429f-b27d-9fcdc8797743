<?php

namespace App\Console;

use Carbon\Carbon;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Artisan;
use Modules\Accounting\Jobs\GenerateDailyAccountingXmlReport;
use Modules\Common\Jobs\GenerateAffiliateMonthlyReport;

final class Kernel extends ConsoleKernel
{
    private const MAIN_TIMEZONE = 'Europe/Sofia';

    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     *
     * @return void
     */
    protected function schedule(Schedule $schedule): void
    {
        // ----- every 1 min
        $schedule->command('script:unsigned-loans:check')
            ->timezone(self::MAIN_TIMEZONE)
            ->everyMinute()
            ->withoutOverlapping();

        $schedule->command('script:tmp-request:check')
            ->timezone(self::MAIN_TIMEZONE)
            ->everyMinute()
            ->withoutOverlapping();

        $schedule->command('terminal-log:set-unconfirmed')
            ->timezone(self::MAIN_TIMEZONE)
            ->everyMinute()
            ->withoutOverlapping();

        // ----- hourly
        $schedule->command('script:refresh-module-stats')
            ->timezone(self::MAIN_TIMEZONE)
            ->hourly()
            ->withoutOverlapping();

        $schedule->command('script:payment-sources-check')
            ->timezone(self::MAIN_TIMEZONE)
            ->hourly()
            ->withoutOverlapping();

        $schedule->command('script:communication:send-email-no-interest')
            ->hourly()
            ->timezone(self::MAIN_TIMEZONE)
            ->withoutOverlapping()
            ->when(function () {
                return now()->hour >= 9 && now()->hour < 19;
            });

        $schedule->command('script:referral-sms-tracker')
            ->everyThirtyMinutes()
            ->timezone(self::MAIN_TIMEZONE)
            ->withoutOverlapping()
            ->when(function () {
                return now()->hour >= 9 && now()->hour < 18;
            });

        // ----- daily

        // move tmp_request & sale_task(deleted/done) to history
        $schedule->command('script:daily-archive')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('00:05')
            ->withoutOverlapping();

        $schedule->command('script:grace-loan-reset')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('00:30')
            ->withoutOverlapping();

        $schedule->command('script:daily-installment-refresh')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('01:00')
            ->withoutOverlapping();

        $schedule->command('script:new-day-active-loan')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('02:00')
            ->withoutOverlapping();

        $schedule->command('script:new-day-active-client')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('02:30')
            ->withoutOverlapping();

        $schedule->command('script:raw-request-location-update')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('03:00')
            ->withoutOverlapping();

        $schedule->command('script:loan-stats-location-update')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('03:10')
            ->withoutOverlapping();

        $schedule->command('script:client-download-docs-logger')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('03:20')
            ->withoutOverlapping();

        $schedule->command('script:client-visit-logger')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('03:40')
            ->withoutOverlapping();

        $schedule->command('script:clv-tracker')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('03:50')
            ->withoutOverlapping();

        $schedule->command('script:assign-loans-to-buckets')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('04:00')
            ->withoutOverlapping();

        $schedule->command('script:create-easy-pay-refund-tasks')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('04:30')
            ->withoutOverlapping();

        $schedule->command('script:create-collector-fee')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('04:50')
            ->withoutOverlapping();

        $schedule->job(new GenerateDailyAccountingXmlReport())
            ->timezone(self::MAIN_TIMEZONE)
            ->daily()->at('05:00')
            ->withoutOverlapping();


        $schedule->command('script:accounting:delete-old-stats-reports')
            ->timezone(self::MAIN_TIMEZONE)
            ->daily()
            ->at('05:00');

        $schedule->command('script:discounts:process-old-discounts')
            ->timezone(self::MAIN_TIMEZONE)
            ->daily()
            ->at('05:15');

        // remove outdated sale tasks related to discounts
        $schedule->command('script:sales:process-old-tasks')
            ->timezone(self::MAIN_TIMEZONE)
            ->daily()
            ->at('05:20');

        // remove outdated(7d passed) sale/payment tasks related to unreceived money via Easypay
        $schedule->command('script:process-unreceived-money-sale-tasks')
            ->timezone(self::MAIN_TIMEZONE)
            ->daily()
            ->at('05:25');

        // make sale tasks DONE which are older than 3d
        $schedule->command('script:clear-sale-tasks')
            ->dailyAt('05:30')
            ->timezone(self::MAIN_TIMEZONE)
            ->withoutOverlapping();

        $schedule->command('script:cancel-loans-waiting-too-long')
            ->timezone(self::MAIN_TIMEZONE)
            ->daily()
            ->at('05:45');

        $schedule->command('script:common:process-old-loan-refinance')
            ->timezone(self::MAIN_TIMEZONE)
            ->daily()
            ->at('05:55');

        $schedule->command('script:lost-veriff-offers')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('06:00')
            ->withoutOverlapping();

        $schedule->command('script:update-referral-stats')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('06:02')
            ->withoutOverlapping();

        $schedule->command('script:logs:monitor')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('07:00')
            ->withoutOverlapping();

        $schedule->command('script:index-clients')
            ->timezone(self::MAIN_TIMEZONE)
            ->daily()
            ->at('06:20');

	/* Stopped by Antonia request, on 30.05.2025
        if (config('app.project') === 'stikcredit') {
            $schedule->command('script:third-party:submit-report-on-new-clients')
                ->timezone(self::MAIN_TIMEZONE)
                ->daily()
                ->at('06:45');
	}
        */

        // during working time
        if (isProd()) {
//            $schedule->command('script:third-party-report-health')
//                ->timezone(self::MAIN_TIMEZONE)
//                ->at('07:30')
//                ->withoutOverlapping();
            $schedule->command('script:noi-call-status-four-report-health')
                ->timezone(self::MAIN_TIMEZONE)
                ->at('07:30')
                ->withoutOverlapping();

            $schedule->command('script:direct-service:upload')
                ->timezone(self::MAIN_TIMEZONE)
                ->at('07:55')
                ->withoutOverlapping();
        }

        $schedule->command('script:coming-due-date-reminder')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('08:05')
            ->withoutOverlapping();

        $schedule->command('script:overdue-notification')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('08:10')
            ->withoutOverlapping();

        $schedule->command('script:daily-checker-command')
            ->timezone(self::MAIN_TIMEZONE)
            ->at('08:20')
            ->withoutOverlapping();

        $schedule->command('script:send-outer-collector-report aps daily')
            ->timezone(self::MAIN_TIMEZONE)
            ->daily()->at('09:00')
            ->withoutOverlapping();

        $schedule->command('script:send-outer-collector-report akpz daily')
            ->timezone(self::MAIN_TIMEZONE)
            ->daily()->at('09:00')
            ->withoutOverlapping();

        $schedule->command('script:communication:prepare-marketing-tasks')
            ->timezone(self::MAIN_TIMEZONE)
            ->daily()
            ->at('09:05');

        $schedule->command('script:communication:exec-marketing-tasks')
            ->timezone(self::MAIN_TIMEZONE)
            ->daily()
            ->at('09:15');

        $schedule->command('script:communication:update-manual-mailings-stats')
            ->timezone(self::MAIN_TIMEZONE)
            ->between('8:00', '22:00')
            ->everyFiveMinutes();

        // Offer loans on incomplete applications
        $schedule->command('script:discounts:offer-loans-on-incomplete-applications first')
            ->timezone(self::MAIN_TIMEZONE)
            ->dailyAt('10:00');

        $schedule->command('script:discounts:offer-loans-on-incomplete-applications second')
            ->timezone(self::MAIN_TIMEZONE)
            ->dailyAt('10:15');

        $schedule->command('script:send-failed-login-attempt-report')
            ->dailyAt('08:05')
            ->timezone(self::MAIN_TIMEZONE)
            ->withoutOverlapping();

        $schedule->command('script:automatic-removal-from-the-black-list')
            ->dailyAt('06:00')
            ->timezone(self::MAIN_TIMEZONE)
            ->withoutOverlapping();

        $schedule->command('script:easypay-auto-refund')
            ->dailyAt('08:30')
            ->timezone(self::MAIN_TIMEZONE)
            ->withoutOverlapping();

        /// communication for clients which never had loan.
        $schedule->command('script:communication:reminder-for-client-which-never-have-loan')
            ->dailyAt('10:30')
            ->timezone(self::MAIN_TIMEZONE)
            ->withoutOverlapping();

        // ----- weekly
        $schedule->command('script:cron-log:clear')
            ->timezone(self::MAIN_TIMEZONE)
            ->weekly()
            ->at('06:00')
            ->withoutOverlapping();

        $schedule->command('script:logs:clear')
            ->timezone(self::MAIN_TIMEZONE)
            ->weekly()
            ->at('06:30')
            ->withoutOverlapping();

        $schedule->command('script:clear-raw-requests')
            ->timezone(self::MAIN_TIMEZONE)
            ->weeklyOn(Schedule::SUNDAY, '05:00');

        $schedule->command('script:send-outer-collector-report aps weekly')
            ->mondays()
            ->at('08:25');

        $schedule->command('script:send-outer-collector-report akpz weekly')
            ->mondays()
            ->at('08:25');

        $schedule->command('script:send-outer-collector-report lmf weekly loans')
            ->mondays()
            ->at('08:30');

        $schedule->command('script:send-outer-collector-report lmf weekly payments')
            ->mondays()
            ->at('08:35');

        // $schedule->command('script:send-outer-collector-report melon weekly payments')
        //     ->mondays()
        //     ->at('08:40');

        // $schedule->command('script:send-outer-collector-report melon weekly loans')
        //     ->mondays()
        //     ->at('08:43');

        $schedule->command('script:communication:process-old-marketing-tasks')
            ->timezone(self::MAIN_TIMEZONE)
            ->saturdays()
            ->at('23:00');

        $schedule->command('script:discounts:process-old-phone-discounts')
            ->timezone(self::MAIN_TIMEZONE)
            ->sundays()
            ->at('23:00');

        $schedule->command('script:docs:clean-document-pdf-folder')
            ->timezone(self::MAIN_TIMEZONE)
            ->sundays()
            ->at('23:00');

        $schedule->command('script:weekly-cleaner-sms-login-code')
            ->sundays()
            ->at('23:10');

        // ----- monthly
        $schedule->command('script:ccr-report-out:generate cucr')
            ->timezone(self::MAIN_TIMEZONE)
            ->lastDayOfMonth('23:30')
            ->withoutOverlapping();

        // prepare data
        $schedule->command('script:portfolio-snapshot 0')
            ->timezone(self::MAIN_TIMEZONE)
            ->lastDayOfMonth('23:50')
            ->withoutOverlapping()
            ->onSuccess(function () {
                Artisan::call('script:portfolio-snapshot 1');
            });

        $schedule->command('script:send-outer-collector-report aps monthly')
            ->monthlyOn(1, '09:05')
            ->timezone(self::MAIN_TIMEZONE)
            ->withoutOverlapping();

        $schedule->command('script:send-outer-collector-report akpz monthly')
            ->monthlyOn(1, '09:05')
            ->timezone(self::MAIN_TIMEZONE)
            ->withoutOverlapping();

        // $schedule->command('script:send-outer-collector-report melon monthly payments')
        //     ->monthlyOn(1, '09:10')
        //     ->timezone(self::MAIN_TIMEZONE)
        //     ->withoutOverlapping();

        //// update consultant stats
        $schedule->command('script:create-consultant-stats-for-month ' . Carbon::now()->format('m-Y'))
            ->monthlyOn(1, '04:00')
            ->timezone(self::MAIN_TIMEZONE)
            ->withoutOverlapping();

        $schedule->command('script:update-consultant-stats ' . Carbon::now()->format('m-Y'))
            ->lastDayOfMonth('23:59')
            ->timezone(self::MAIN_TIMEZONE)
            ->withoutOverlapping();

        $schedule->job(new GenerateAffiliateMonthlyReport())
            ->monthlyOn(1, '09:00')
            ->timezone(self::MAIN_TIMEZONE)
            ->withoutOverlapping();


        if ($this->app->environment(['local', 'stage'])) {
            $schedule->command('telescope:prune --hours=48')->daily();
        }
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
