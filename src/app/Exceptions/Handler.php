<?php

namespace App\Exceptions;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Support\Arr;
use Illuminate\View\ViewException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Redirect;
use Modules\Api\Domain\Exceptions\ClientAlreadyExists;
use Modules\Api\Domain\Exceptions\ClientAlreadyExistsWithAnotherPhone;
use Modules\Api\Domain\Exceptions\ClientRequestHasAlreadyBeenProcessed;
use Modules\Api\Domain\Exceptions\EasyPay\ClientHasNoActiveLoans;
use Modules\Api\Domain\Exceptions\EasyPay\InvalidPinProvided;
use Modules\Api\Domain\Exceptions\EasyPay\NotExistingClient;
use Modules\Api\Domain\Exceptions\Login\CustomMessage;
use Modules\Api\Domain\Exceptions\Login\SmsCodeNotFoundByCode;
use Modules\Api\Domain\Exceptions\MvrReportIsNotCorrect;
use Modules\Approve\Domain\Exceptions\LoanHasIncorrectStatus;
use Modules\Common\Domain\Exceptions\DomainException;
use Modules\Common\Exceptions\ShouldBeReportedToUser;
use Modules\Common\Exceptions\AccessDeniedApiException;
use Modules\Common\Exceptions\PrincipleEqualToZeroException;
use Modules\Common\Exceptions\RuntimeWarningException;
use Modules\Common\Interfaces\BaseExceptionInterface;
use Modules\Sales\Domain\Exceptions\ClientHasUnprocessedLoans;
use Modules\Sales\Domain\Exceptions\NewLoanAmountTooSmall;
use Modules\Sales\Exceptions\AmountForRefinanceIsLargerThanProductMaxAmount;
use Modules\Sales\Exceptions\NewAppValidatorException;
use Modules\Common\Exceptions\JsonException;
use Psr\Log\LogLevel;
use Throwable;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;

/**
 * Class Handler
 * @package App\Exceptions
 */
class Handler extends ExceptionHandler
{
    protected $dontReport = [
        JsonException::class,
        AccessDeniedApiException::class,
        CustomMessage::class,
        ClientAlreadyExists::class,
        ClientAlreadyExistsWithAnotherPhone::class,
        PrincipleEqualToZeroException::class,
        SmsCodeNotFoundByCode::class,
        ClientRequestHasAlreadyBeenProcessed::class,
        ClientHasNoActiveLoans::class,
        ClientHasUnprocessedLoans::class,
        InvalidPinProvided::class,
        NotExistingClient::class,
        NewLoanAmountTooSmall::class,
        MvrReportIsNotCorrect::class,
    ];

    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    protected $levels = [
        LoanHasIncorrectStatus::class => LogLevel::WARNING,
        AmountForRefinanceIsLargerThanProductMaxAmount::class => LogLevel::WARNING,
        RuntimeWarningException::class => LogLevel::WARNING,
    ];

    public function render(
        $request,
        Throwable $e
    ): Response|JsonResponse|RedirectResponse|\Symfony\Component\HttpFoundation\Response
    {

        // if ("Unauthenticated." != $e->getMessage() && getAdmin()->isSuperAdmin()) dd('DUMP:', $e);

        $msgForDev = '';
        if (
            $e instanceof ThrottleRequestsException
            && preg_match('/(sms-restart)/', $request->url())
        ) {
            /**  @phpstan-ignore-next-line */
            die('Прекалил си с рестартиране, почини малко (˵ ͠ಥ‿ ͠ಥ˵)');
        }


        //// all front end messages will extend this exception (with redirect)
        if (
            $e instanceof FrontEndExceptions
            || $e instanceof DomainException
            || $e instanceof ViewException
        ) {

            return to_route('home')->with('fail',
                (method_exists($e, 'getFrontEndMessage') ? $e->getFrontEndMessage() : $e->getMessage())
            );
        }


        if ($e instanceof NewAppValidatorException) {
            return response()->json([
                'success' => false,
                'messages' => json_decode($e->getMessage()),
                'modalSelector' => '#errorModal',
            ]);
        }


        if ($e instanceof BaseExceptionInterface) {
            $additionalMessage = $e->getAdditionalMessage();

            $file = $e->getFile();
            $line = $e->getLine();

            if (isProd()) {
                $e->saveToLog($file, $line, $e->getMessage() . ' ' . $additionalMessage, []);
            } else {
                $msgForDev = $e->getMessage() . ", $additionalMessage, $file, $line";
            }
        }

        if ($request->wantsJson() || $request->hasHeader('OnErrorJson')) {
            $msg = !empty($msgForDev) ? $msgForDev : $e->getMessage();

            if ($e instanceof ShouldBeReportedToUser) {
                return response()->json([
                        'status' => false,
                        'message' => $msg,
                    ],
                    $e->getCode()
                );
            }

            /// for website logout
            if ($e instanceof AuthenticationException) {
                return response()->json([
                    'status' => false,
                    'message' => $e->getMessage(),
                ]);
            }

            // \Illuminate\Foundation\Exceptions\Handler::convertExceptionToArray
            return response()->json(config('app.debug') ? [
                'status' => false,
                'message' => $e->getMessage(),
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => collect($e->getTrace())->map(fn ($trace) => Arr::except($trace, ['args']))->all(),
            ] : [
                'status' => false,
                'message' => $this->isHttpException($e) ? $e->getMessage() : __('System error'),
            ]);
        }


        if ($e instanceof AuthenticationException) {
            return Redirect::to(route('login'));
        }


        // tmp to get all info for QA v.rizov
        // if (isStage() && "Unauthenticated." != $e->getMessage() && getAdminId() === 121) {
        //     /** @phpstan-ignore-next-line  */
        //     dd($e);
        // }


        return parent::render($request, $e);
    }

    public function report(Throwable $e)
    {
        if ($this->shouldReport($e) && !array_key_exists($e::class, $this->levels) && !app()->runningInConsole()) {
            $this->logExceptionDetails($e);
        }

        parent::report($e);
    }

    protected function logExceptionDetails(Throwable $e): void
    {
        $url = Request::fullUrl();
        $method = Request::method();
        $ip = Request::ip();
        $userAgent = Request::header('User-Agent');

        Log::error('Exception occurred', [
            'exception' => $e->getMessage(),
            'url' => $url,
            'method' => $method,
            'ip' => $ip,
            'user_agent' => $userAgent,
            'trace' => $e->getTraceAsString(),
        ]);
    }
}
