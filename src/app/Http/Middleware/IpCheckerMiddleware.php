<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class IpCheckerMiddleware
{

    public function handle(Request $request, Closure $next)
    {
        return $next($request);

        // if (app()->environment(['local', 'testing'])) {
        //     return $next($request);
        // }

        // $availableIps = config('available-ips');
        // $userIp = $request->getClientIp();

        // if (!in_array($userIp, $availableIps)) {
        //     return redirect('/access-denied');
        // }

        // return $next($request);
    }
}
