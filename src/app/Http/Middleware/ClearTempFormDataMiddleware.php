<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class ClearTempFormDataMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        if ($request->routeIs([
            'sales.newAppFromSaleTask',
            'sales.newApplication',
            'sales.storeNewCompanyApplication',
            'common.slider.*',
            'loans.cities',
        ])
        ) {
            return $next($request);
        }

        Session::remove('tempSliderData');
        Session::remove('tempFormData');
        Session::remove('currentOfficeId');
        Session::remove('newLoanType');
        Session::remove('currentOfficeId');

        return $next($request);
    }
}
