<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Modules\Common\Models\Loan;
use Modules\Common\Models\PaymentTask;
use Modules\Head\Policies\LoanStatusPolicy;
use Modules\Payments\Policies\PaymentTaskPolicy;

class AuthServiceProvider extends ServiceProvider
{
    protected $policies = [
        Loan::class => LoanStatusPolicy::class,
        PaymentTask::class => PaymentTaskPolicy::class
    ];

    public function boot(): void
    {
        $this->registerPolicies();
        Gate::define('viewTelescope', function ($user = null) {
            return true;
        });
    }
}
