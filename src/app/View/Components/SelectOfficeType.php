<?php

namespace App\View\Components;

use Illuminate\Contracts\Foundation\Application;
use Illuminate\View\Component;

/**
 * Class SelectOfficeType
 *
 * @package App\View\Components
 */
class SelectOfficeType extends Component
{
    /**
     * @var string
     */
    public string $cacheKey;

    /**
     * @var mixed
     */
    public $officeTypes;

    /**
     * SelectOfficeType constructor.
     *
     * @param $officeTypes
     * @param $cacheKey
     */
    public function __construct($officeTypes, $cacheKey)
    {
        $this->officeTypes = $officeTypes;
        $this->cacheKey = $cacheKey;
    }

    /**
     * @return Application|\Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.select-office-type');
    }
}
