<?php

namespace App\View\Components;

use App\Exceptions\FrontEndExceptions;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Session;
use Illuminate\View\Component;
use Illuminate\View\View;
use Modules\Admin\Repositories\OfficeRepository;
use Modules\Common\Models\Administrator;
use Modules\Common\Models\Loan;
use Modules\Common\Models\Office;
use Modules\Common\Models\Product;
use Modules\Common\Models\ProductSetting;
use Modules\Product\Services\ProductService;

class CreateLoanCalculator extends Component
{
    public int $currentOfficeId = 0;

    public ?Collection $offices = null;

    public ?Collection $products = null;


    public function __construct(
        public ?Collection $loansForRefinance,
        public ?string $clientPin = '',
        public ?string $phone = '',
    ) {
        $this->fillOffices();
        $this->fillProducts();
    }

    public function getDynamicProductId(): int
    {
        $dynamicProductId = 0;
        $this->products->each(function (Product $product) use (&$dynamicProductId) {
            if (0 === $product->isFixed) {
                $dynamicProductId = $product->getKey();

                return false;
            }
        });

        return $dynamicProductId;
    }

    public function getInstallmentProductId(): int
    {
        $installmentProductId = 0;

        $this->products->each(function (Product $product) use (&$installmentProductId) {
            if (2 === $product->product_type_id) {
                $installmentProductId = $product->getKey();

                return false;
            }
        });

        return $installmentProductId;
    }

    public function isOnlineOffice(int $officeId): bool
    {
        return Office::whereOfficeId($officeId)->first()->isWeb();
    }

    public function getRefinanceData(Loan $loan, bool $forWeb = false): array
    {
        $data = [
            'lastInstallmentDate' => formatDate($loan->getLastInstallment()->due_date),
            'earlyRepaidAmount' => intToFloat($loan->getAmountForRefinance($forWeb)),
        ];

        return $data;
    }

    public function hasTmpProductId(): int
    {
        return (int) Session::get('tempSliderData.product_id', 0);
    }

    /**
     * This action is used in new app page with blade component <x-create-loan-calculator/>
     * @return void
     * @throws \Exception
     */
    protected function fillOffices(): void
    {
        /**
         * @var Administrator $administrator
         */
        $administrator = auth()->user();

        if (!$this->offices) {
            $this->offices = $administrator->offices;
        }

        /// when agent select loan type for company
        /// we need to reject online office from all offices list because loan_type=juridical
        /// allowed only for physical offices
        if (session('newLoanType', 'individual') == 'company') {
            $this->offices = $this->offices->reject(function (Office $office) {
                return $office->getKey() == Office::OFFICE_ID_WEB;
            });

            /// if is selected online office reset for company
            if (session('currentOfficeId') === Office::OFFICE_ID_WEB) {
                $office = $this->offices->first();
                Session::put([
                    'currentOfficeId' => $office->getKey(),
                    'isWebOffice' => $office->isWeb(),
                ]);
            }
        }

        if ($this->offices->isEmpty()) {
            throw new \Exception('No available offices');
        }

        if (!session('currentOfficeId', false)) {
            /**
             * @var Office $office
             */
            $office = $this->offices->first();
            Session::put([
                'currentOfficeId' => $office->getKey(),
                'isWebOffice' => $office->isWeb(),
            ]);
        }
        $this->currentOfficeId = session('currentOfficeId');
    }

    protected function fillProducts(): void
    {
        /**
         * @var Office $office
         */
        $officeRepo = app(OfficeRepository::class);

        $office = $officeRepo->getById($this->currentOfficeId);

//        $filters['migrated'] = 0;
        $filters['active'] = 1;
        $filters['legal_status'] = session('newLoanType', 'individual');
        $this->products = $office->products()->where('migrated', 0)->filterBy($filters)->get();
    }


    public function productSettings(Product $product): array
    {
        $productService = app()->make(ProductService::class);

        try {
            /// get default amounts
            $defAmount = $productService
                ->getProductSettingByKey($product, ProductSetting::DEFAULT_AMOUNT_KEY);

            /// get default periods
            $defPeriod = $productService
                ->getProductSettingByKey($product, ProductSetting::DEFAULT_PERIOD_KEY);
        } catch (\Throwable $e) {
            throw new \Exception('Липсват продуктови настройки за избран офис');
        }


        return [
            'productId' => $product->getKey(),
            'defAmount' => $defAmount,
            'defPeriod' => $defPeriod,
            'isJuridical' => $product->legal_status
        ];
    }

    public function render(): View
    {
        return view('components.create-loan-calculator');
    }
}
