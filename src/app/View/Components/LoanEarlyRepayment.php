<?php

namespace App\View\Components;

use Illuminate\View\Component;
use Modules\Common\Exceptions\ProblemException;
use Modules\Common\Models\Loan;

class LoanEarlyRepayment extends Component
{
    public Loan $loan;

    /**
     * Create a new component instance.
     *
     * @param Loan $loan
     *
     * @throws ProblemException
     */
    public function __construct(Loan $loan)
    {
        if (!$loan->exists) {
            throw new ProblemException('NO!');
        }

        $this->loan = $loan;
    }

    public function render()
    {
        return view('components.early-repayment');
    }
}
