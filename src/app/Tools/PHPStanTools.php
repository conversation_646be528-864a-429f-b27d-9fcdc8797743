<?php

namespace App\Tools;

use Illuminate\Support\Collection;

class PHPStanTools
{
    /**
     * @throws \JsonException
     */
    public function errorsFromJson(): Collection
    {
        $filePath = storage_path('/logs/phpstan.json');
        if (!file_exists($filePath)) {
            throw new \RuntimeException('File does not exist');
        }
        $json = json_decode(file_get_contents(storage_path('/logs/phpstan.json')), true, 512, JSON_THROW_ON_ERROR);
        return collect($json['files'] ?? [])->flatMap(function ($data, $filename) {
            return array_map(static function (array $errorInfo) use ($filename) {
                $errorInfo['filename'] = $filename;
                $errorInfo['message_hash'] = hash('md5', $errorInfo['message']);
                return $errorInfo;
            }, $data['messages']);
        });
    }

    private function groupErrors(Collection $errors): Collection
    {
        return $errors
            ->groupBy('message')
            ->map(function (Collection $errors, string $message) {
                return collect([
                    'errors_count' => $errors->count(),
                    'message' => $message,
                    'message_hash' => hash('md5', $message),
                    'errors' => $errors,
                ]);
            });
    }

    private function removeErrors(Collection $array): Collection
    {
        return collect($array)->map(function (Collection $arr) {
            unset($arr['errors']);
            return $arr;
        });
    }

    /**
     * @throws \JsonException
     */
    public function errorsToArrayLite($limit = 10): Collection
    {
        $errors = $this->groupErrors($this->errorsFromJson())->sortByDesc('errors_count');
        $errors = $this->removeErrors($errors);
        return $errors->slice(0, $limit);
    }

    /**
     * @throws \JsonException
     */
    public function errorsToTextLite($limit = 10): Collection
    {
        $errors = $this->groupErrors($this->errorsFromJson())
            ->sortByDesc('errors_count')
            ->map(function (Collection $arr) {
                return sprintf(
                    "[h: %s][e: %s] %s",
                    $arr->get('message_hash'),
                    $arr->get('errors_count'),
                    $arr->get('message')
                );
            });
        return $errors->values()->slice(0, $limit);
    }

    public function getDetailsByHash($hash): Collection
    {
        return $this->errorsFromJson()->where('message_hash', $hash);
    }
}